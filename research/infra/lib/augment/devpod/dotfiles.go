package devpod

import (
	"archive/tar"
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"io"
	"io/fs"
	"os"
	"os/user"
	"strings"
)

var (
	// DefaultDotfiles are copied from the local user. All items are relative to $HOME.
	// Directories with be copied recursively. Missing entries are ignored.
	DefaultDotfiles = []string{
		".augment",
		".bashrc",
		".config/bash.d",
		".bash_profile",
		".config/profile.d",
		".profile",
		".bash_history",
		".gitconfig",
		".config/git",
		".git_prompt",
		".kube",
		".netrc",
		".python_history",
		".inputrc",
		".ssh",
		".s3cfg",
		".tmux.conf",
		".config/tmux/tmux.conf",
		".vimrc",
		".vim/vimrc",
		".vim/pack",
		".config/pod-init.d",
	}

	// IgnoredFiles are filenames that are ignored, at any depth in the tree.
	IgnoredFiles = map[string]bool{
		"cache":                         true,
		".cache":                        true,
		"ssh_auth_sock":                 true,
		".launch_pod_dotfiles_unpacked": true,
	}
)

// Dotfiles is a dotfiles builder.
type Dotfiles struct {
	home     string
	defaults []string
	ignored  map[string]bool
	deref    bool

	buf *bytes.Buffer
	b64 io.WriteCloser
	gz  *gzip.Writer
	tar *tar.Writer

	tLstat    func(string) (os.FileInfo, error)
	tStat     func(string) (os.FileInfo, error)
	tReadFile func(string) ([]byte, error)
	tReadDir  func(string) ([]os.DirEntry, error)
	tReadlink func(string) (string, error)
	tUser     func() (*user.User, error)
}

// NewDotfiles creates a new dotfiles builder. Callers must call Close() before
// retrieving the base64-encoded, gzip-compressed, tar archive.
func NewDotfiles() Dotfiles {
	buf := &bytes.Buffer{}
	b64 := base64.NewEncoder(base64.StdEncoding, buf)
	gz := gzip.NewWriter(b64)
	t := tar.NewWriter(gz)

	return Dotfiles{
		home:     "",
		defaults: DefaultDotfiles,
		ignored:  IgnoredFiles,

		buf: buf,
		b64: b64,
		gz:  gz,
		tar: t,

		tLstat:    os.Lstat,
		tStat:     os.Stat,
		tReadFile: os.ReadFile,
		tReadDir:  os.ReadDir,
		tReadlink: os.Readlink,
		tUser:     user.Current,
	}
}

// NewDotfiles creates a new dotfiles builder from an existing tar archive, with
// the ability to add and remove files. Callers must call Close() before
// retrieving the base64-encoded, gzip-compressed, tar archive.
func NewDotfilesFromString(tgzb64 string, add, remove map[string]bool) (Dotfiles, error) {
	buf := &bytes.Buffer{}
	b64 := base64.NewEncoder(base64.StdEncoding, buf)
	gz := gzip.NewWriter(b64)
	t := tar.NewWriter(gz)

	d := Dotfiles{
		home:     "",
		defaults: DefaultDotfiles,
		ignored:  IgnoredFiles,

		buf: buf,
		b64: b64,
		gz:  gz,
		tar: t,

		tLstat:    os.Lstat,
		tStat:     os.Stat,
		tReadFile: os.ReadFile,
		tReadDir:  os.ReadDir,
		tReadlink: os.Readlink,
		tUser:     user.Current,
	}

	// Copy the existing archive, skipping the files to be removed.
	tr, err := ReaderFromArchive(tgzb64)
	if err != nil {
		return d, err
	}

	for hdr, err := tr.Next(); err == nil; hdr, err = tr.Next() {
		if _, exists := remove[hdr.Name]; exists {
			fmt.Println("Removing", hdr.Name)
			delete(remove, hdr.Name)
			continue
		}
		if _, exists := add[hdr.Name]; exists {
			return d, fmt.Errorf("cannot add %s, it already exists", hdr.Name)
		}
		if err := d.tar.WriteHeader(hdr); err != nil {
			return d, err
		}
		buf, err := io.ReadAll(tr)
		if err != nil {
			return d, err
		}
		if _, err := d.tar.Write(buf); err != nil {
			return d, err
		}
	}
	if len(remove) != 0 {
		return d, fmt.Errorf("failed to remove %v", remove)
	}

	for fname := range add {
		if _, exists := IgnoredFiles[fname]; exists {
			return d, fmt.Errorf("cannot add %s, it is ignored", fname)
		}
		fmt.Println("Adding", fname)
		if err := d.Add(fname); err != nil {
			return d, err
		}
	}

	return d, nil
}

func ReaderFromArchive(archive string) (*tar.Reader, error) {
	b64r := base64.NewDecoder(base64.StdEncoding, strings.NewReader(archive))
	gzr, err := gzip.NewReader(b64r)
	if err != nil {
		return nil, err
	}
	tr := tar.NewReader(gzr)

	return tr, nil
}

// BuildAndClose adds defaults, additional files, closes the underlying writers
// and returns the results.
func (d Dotfiles) BuildAndClose(extra ...string) (string, error) {
	if err := d.AddDefaults(); err != nil {
		return "", err
	}
	if err := d.Add(extra...); err != nil {
		return "", err
	}
	if err := d.Close(); err != nil {
		return "", err
	}
	return d.String(), nil
}

// AddDefaults adds the `DefaultDotfiles`. Items which do not exist are ignored.
func (d Dotfiles) AddDefaults() error {
	for _, fname := range d.defaults {
		if err := d.add(fname); err != nil && !os.IsNotExist(err) {
			return err
		}
	}
	return nil
}

// Add adds content to the tar. If `fname` is a directory, it is added recursively.
func (d Dotfiles) Add(fnames ...string) error {
	for _, fname := range fnames {
		if err := d.add(fname); err != nil {
			return err
		}
	}
	return nil
}

func (d Dotfiles) add(fname string) error {
	// Get homedir if needed.
	if d.home == "" {
		if user, err := d.tUser(); err != nil {
			return err
		} else {
			d.home = user.HomeDir
		}
	}

	// A DirEntry extended with the original path, relative to $HOME
	type HomeEnt struct {
		fs.DirEntry
		Path string
	}

	// Like tar, copy symlinks as symlinks by default, but allow derefrencing as an option.
	stat := d.tLstat
	if d.deref {
		stat = d.tStat
	}

	// The original fname might be a file or directory, so build a queue for walking the FS
	queue := []HomeEnt{}
	if fi, err := stat(d.home + "/" + fname); err != nil {
		return err
	} else {
		queue = append(queue, HomeEnt{
			DirEntry: fs.FileInfoToDirEntry(fi),
			Path:     fname,
		})
	}

	for len(queue) > 0 {
		// pop the queue
		ent := queue[0]
		queue = queue[1:]

		absPath := d.home + "/" + ent.Path

		if d.ignored[ent.Name()] {
			continue
		} else if ent.IsDir() {
			children, err := d.tReadDir(absPath)
			if err != nil {
				return err
			}
			for _, child := range children {
				queue = append(queue, HomeEnt{
					DirEntry: child,
					Path:     ent.Path + "/" + child.Name(),
				})
			}

		} else if ent.Type() == fs.ModeSymlink { // Will only be true when using Lstat.
			if fi, err := ent.Info(); err != nil {
				return err
			} else if link, err := d.tReadlink(absPath); err != nil {
				return err
			} else if hdr, err := tar.FileInfoHeader(fi, link); err != nil {
				return fmt.Errorf("%s: %w", absPath, err)
			} else {
				hdr.Name = ent.Path
				if err := d.tar.WriteHeader(hdr); err != nil {
					return fmt.Errorf("%s: %w", absPath, err)
				}
			}
		} else {
			fi, err := ent.Info()
			if err != nil {
				return err
			}
			hdr, err := tar.FileInfoHeader(fi, "")
			if err != nil {
				return fmt.Errorf("%s: %w", absPath, err)
			}
			hdr.Name = ent.Path

			buf, err := d.tReadFile(absPath)
			if err != nil {
				return err
			}

			if err := d.tar.WriteHeader(hdr); err != nil {
				return fmt.Errorf("%s: %w", absPath, err)
			}
			if _, err := d.tar.Write(buf); err != nil {
				return fmt.Errorf("%s: %w", absPath, err)
			}
		}
	}

	return nil
}

// Close finalizes the tar, gzip, b64.
func (d Dotfiles) Close() error {
	if err := d.tar.Close(); err != nil {
		return err
	}
	if err := d.gz.Close(); err != nil {
		return err
	}
	if err := d.b64.Close(); err != nil {
		return err
	}
	return nil
}

func (d Dotfiles) String() string {
	return d.buf.String()
}

func (d Dotfiles) List() []string {
	return []string{"foo", "bar"}
}
