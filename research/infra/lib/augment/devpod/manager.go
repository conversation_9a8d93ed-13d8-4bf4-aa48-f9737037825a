package devpod

import (
	"context"
	"errors"
	"fmt"
	"io"
	"iter"
	"net"
	"os"
	"os/exec"
	"os/signal"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/pmezard/go-difflib/difflib"
	"golang.org/x/sync/errgroup"

	corev1 "k8s.io/api/core/v1"

	"github.com/augmentcode/augment/infra/lib/k8s"
	"github.com/augmentcode/augment/infra/lib/logger"
	"github.com/augmentcode/augment/infra/lib/utils"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/crd/devpodv1"
	"github.com/augmentcode/augment/research/infra/lib/augment/devpod/spec"
)

var resolver = &net.Resolver{
	PreferGo: true, // avoid system resolver and local caching
	Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
		d := &net.Dialer{
			Timeout: time.Minute,
		}
		return d.DialContext(ctx, network, "*******:53")
	},
}

func FindSpec(ctx context.Context, k *k8s.Client, c *clusters.Cluster, user, name string) (*devpodv1.DevPod, error) {
	var specNS, specNoNS *devpodv1.DevPod
	var errNS, errNoNS error

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		ak := k.InNamespace(c.MainNamespace)
		k := devpodv1.WrappedClient(&ak)
		specNoNS, errNoNS = k.GetDevPod(gctx, name)
		return k8s.NotFoundOK(errNoNS)
	})
	grp.Go(func() error {
		ak := k.InNamespace("aug-user-" + user)
		k := devpodv1.WrappedClient(&ak)
		specNS, errNS = k.GetDevPod(gctx, name)
		return k8s.NotFoundOK(errNS)
	})

	if err := grp.Wait(); err != nil {
		return nil, err
	} else if specNS == nil && specNoNS == nil {
		return nil, errNoNS
	} else if specNS != nil && specNoNS != nil {
		return nil, fmt.Errorf("devpod/%s exists in both namespaces: %s and %s", name, specNS.Namespace(), specNoNS.Namespace())
	} else if specNS != nil {
		return specNS, nil
	} else {
		return specNoNS, nil
	}
}

// Manager pairs a k8s client and DevPod Builder to implement all of the
// operations on a DevPod (create, reboot, update, ...).
type Manager struct {
	logger.Logger

	k8s  *devpodv1.Client
	clst *clusters.Cluster
	spec *devpodv1.DevPod
	bld  *Builder
	dns  *net.Resolver
}

func NewManager(ctx context.Context, k *k8s.Client, c *clusters.Cluster, user, name string) (*Manager, error) {
	if spec, err := FindSpec(ctx, k, c, user, name); err != nil {
		return nil, err
	} else {
		return NewManagerFromSpec(k, c, spec), nil
	}
}

func NewManagerFromSpec(k *k8s.Client, c *clusters.Cluster, spec *devpodv1.DevPod) *Manager {
	nk := k.InNamespace(spec.Namespace())
	m := &Manager{
		Logger: logger.New(nil),

		k8s:  devpodv1.WrappedClient(&nk),
		clst: c,
		spec: spec,
		bld:  NewBuilder(c, spec.DevPod()),
		dns:  resolver,
	}
	if err := m.spec.Validate(); err != nil {
		m.LogWarn("devpod/%s: building Manager with Spec validation error(s): %v.", m.Name(), err)
	}
	return m
}

func (m Manager) Cluster() *clusters.Cluster {
	return m.clst
}

func (m Manager) Spec() *devpodv1.DevPod {
	return m.spec
}

func (m Manager) Builder() *Builder {
	return m.bld
}

func (m Manager) Name() string {
	return m.Spec().DevPod().DevPodName
}

func (m Manager) K8sContext() string {
	return m.k8s.Config().Context()
}

func (m Manager) IsLocal(ctx context.Context) bool {
	if db, err := clusters.New(); err != nil {
		m.LogWarn("IsLocal(): Unable to detect or get current cluster: %v.", err)
		return false
	} else if cur, err := db.DetectCluster(ctx); err != nil {
		m.LogWarn("IsLocal(): Unable to detect or get current cluster: %v.", err)
		return false
	} else {
		return cur == m.Cluster().Name
	}
}

////////////////////////////////////////////////////////////////////////////////
//
// Getter Utils
//

func (m Manager) GetConfigMap(ctx context.Context) (*k8s.ConfigMap, error) {
	return m.k8s.GetConfigMap(ctx, m.bld.ConfigMapName())
}

// getIngressService may return nil without err on NotFound.
func (m Manager) getIngressService(ctx context.Context) (*k8s.Service, error) {
	if o, err := m.k8s.GetService(ctx, m.bld.IngressServiceName()); err != nil {
		return nil, k8s.NotFoundOK(err)
	} else {
		return o, nil
	}
}

func (m Manager) getService(ctx context.Context) (*k8s.Service, error) {
	return m.k8s.GetService(ctx, m.bld.ServiceName())
}

func (m Manager) getDeployment(ctx context.Context) (*k8s.Deployment, error) {
	return m.k8s.GetDeployment(ctx, m.bld.DeploymentName())
}

// getPod may return nil, it will map a NotFound error to nil error.
func (m Manager) getPod(ctx context.Context, d *k8s.Deployment) (*k8s.Pod, error) {
	if d == nil {
		var err error
		d, err = m.getDeployment(ctx)
		if err != nil {
			return nil, err
		}
	}
	pod, err := d.GetPod(ctx, m.k8s.Client)
	if k8s.IsNotFound(err) {
		return nil, nil
	}
	return pod, err
}

// getSSHHKSecret may return nil without err on NotFound.
func (m Manager) getSSHHKSecret(ctx context.Context) (*k8s.Secret, error) {
	if o, err := m.k8s.GetSecret(ctx, m.bld.SSHHostKeysSecretName()); err != nil {
		return nil, k8s.NotFoundOK(err)
	} else {
		return o, nil
	}
}

// getHomePVC may return nil without error on NotFound.
func (m Manager) getHomePVC(ctx context.Context, deploy *k8s.Deployment) (*k8s.PVC, error) {
	if deploy == nil {
		var err error
		if deploy, err = m.getDeployment(ctx); err != nil {
			return nil, err
		}
	}

	// A little hacky, but use a "sparse Set" to get the HomePVCName() implementation.
	if name := (Set{bld: m.Builder(), deployment: deploy}).HomePVCName(); name == "" {
		return nil, nil
	} else {
		return m.k8s.GetPVC(ctx, name)
	}
}

func (m Manager) GetSet(ctx context.Context) (*Set, error) {
	set := &Set{
		spec: m.Spec(),
		bld:  m.Builder(),
	}

	grp, ctx := errgroup.WithContext(ctx)

	grp.Go(func() error {
		if obj, err := m.GetConfigMap(ctx); err != nil {
			return fmt.Errorf("GetSet()/ConfigMap: %w.", err)
		} else if ac, err := obj.ApplyConfig(m.k8s.DefaultFieldManager()); err != nil {
			return fmt.Errorf("GetSet()/ConfigMap/ApplyConfig: %w.", err)
		} else {
			set.cm = obj
			set.cmAC = ac
			return nil
		}
	})
	grp.Go(func() error {
		if obj, err := m.getIngressService(ctx); err != nil {
			return fmt.Errorf("GetSet()/IngressService: %w.", err)
		} else if obj == nil {
			return nil
		} else if ac, err := obj.ApplyConfig(m.k8s.DefaultFieldManager()); err != nil {
			return fmt.Errorf("GetSet()/IngressService/ApplyConfig: %w.", err)
		} else {
			set.ingressSvc = obj
			set.ingressSvcAC = ac
			return nil
		}
	})
	grp.Go(func() error {
		if obj, err := m.getService(ctx); err != nil {
			return fmt.Errorf("GetSet()/Service: %w.", err)
		} else if ac, err := obj.ApplyConfig(m.k8s.DefaultFieldManager()); err != nil {
			return fmt.Errorf("GetSet()/Service/ApplyConfig: %w.", err)
		} else {
			set.service = obj
			set.serviceAC = ac
			if hn := set.service.ExternalDNSHostname(); hn != "" {
				set.dnsIPs, _ = m.LookupIP(ctx, hn)
			}
			return nil
		}
	})
	grp.Go(func() error {
		if obj, err := m.getDeployment(ctx); err != nil {
			return fmt.Errorf("GetSet()/Deployment: %w.", err)
		} else if ac, err := obj.ApplyConfig(m.k8s.DefaultFieldManager()); err != nil {
			return fmt.Errorf("GetSet()/Deployment/ApplyConfig: %w.", err)
		} else {
			set.deployment = obj
			set.deploymentAC = ac
		}

		if obj, err := m.getPod(ctx, set.deployment); err != nil {
			return fmt.Errorf("GetSet()/Pod: %w.", err)
		} else {
			set.pod = obj // may be nil
			return nil
		}
	})
	grp.Go(func() error {
		if obj, err := m.getSSHHKSecret(ctx); err != nil {
			return fmt.Errorf("GetSet()/SSHHKSecret: %w.", err)
		} else if ac, err := obj.ApplyConfig(m.k8s.DefaultFieldManager()); err != nil {
			return fmt.Errorf("GetSet()/SSHHKSecret/ApplyConfig: %w.", err)
		} else {
			set.sshHKSec = obj
			set.sshHKSecAC = ac
			return nil
		}
	})
	grp.Go(func() error {
		if obj, err := m.getHomePVC(ctx, nil); err != nil {
			return fmt.Errorf("GetSet()/HomePVC: %w.", err)
		} else if obj == nil {
			return nil
		} else if ac, err := obj.ApplyConfig(m.k8s.DefaultFieldManager()); err != nil {
			return fmt.Errorf("GetSet()/HomePVC/ApplyConfig: %w.", err)
		} else {
			set.homePVC = obj // may be nil
			set.homePVCAC = ac
			return nil
		}
	})

	if err := grp.Wait(); err != nil {
		return nil, err
	}

	return set, nil
}

func (m Manager) GetUserServiceAccount(ctx context.Context) (*k8s.ServiceAccount, error) {
	return m.k8s.GetServiceAccount(ctx, m.bld.UserServiceAccountName())
}

func (m Manager) GetUserServiceAccountToken(ctx context.Context) (*k8s.Secret, error) {
	if sa, err := m.GetUserServiceAccount(ctx); err != nil {
		return nil, err
	} else {
		return sa.TokenSecret(ctx, m.k8s.Client)
	}
}

func (m Manager) SetUserServiceAccountToken(ctx context.Context) error {
	if sec, err := m.GetUserServiceAccountToken(ctx); err != nil {
		return err
	} else {
		return m.Builder().UserServiceAccountTokenSet(sec)
	}
}

func (m Manager) LookupIP(ctx context.Context, name string) ([]net.IP, error) {
	return m.dns.LookupIP(ctx, "ip4", name)
}

// WriteSpec updates the DevPod CRD. It also updates the DevPod ConfigMap's `devpod.spec.json` and `devpod.spec.yaml` for convenience.
func (m *Manager) WriteSpec(ctx context.Context) error {
	if err := m.spec.Validate(); err != nil {
		m.LogWarn("WriteSpec(): writing spec with validation errors: %v.", err)
	}
	if obj2, err := m.k8s.ReplaceDevPod(ctx, m.spec); err != nil {
		m.LogInfo("WriteSpec(): Replace: %v", err)
		return err
	} else {
		m.spec = obj2
		m.bld.spec = obj2.DevPod()
	}

	// Best effort configmap updates. Errors are logged.

	data := map[string]string{}

	if y, err := m.Spec().YAML(); err != nil {
		m.LogWarn("WriteSpec(): unable to marshal YAML: %v.", err)
	} else {
		data["devpod.spec.yaml"] = y
	}

	if j, err := m.Spec().JSON(); err != nil {
		m.LogWarn("WriteSpec(): unable to marshal JSON: %v.", err)
	} else {
		data["devpod.spec.json"] = j
	}

	if cm, err := m.GetConfigMap(ctx); err != nil {
		m.LogWarn("WriteSpec(): error getting ConfigMap: %v.", err)
	} else if _, err := cm.SetData(ctx, m.k8s.Client, data); err != nil {
		m.LogWarn("WriteSpec(): error updating ConfigMap: %v.", err)
	}

	return nil
}

// UpdateSpec calls f(), then WriteSpec().
func (m *Manager) UpdateSpec(ctx context.Context, f func(*spec.DevPod) error) error {
	if err := f(m.Spec().DevPod()); err != nil {
		return err
	} else if err := m.WriteSpec(ctx); err != nil {
		return err
	} else {
		return nil
	}
}

// SwapSpec is meant to be used during the Create CLI, nowhere else.
func (m *Manager) SwapSpec(s *spec.DevPod) {
	s2 := m.Spec().DeepCopy()
	s2.Raw().Spec = *s
	m.spec = s2
	m.bld.spec = s2.DevPod()
}

////////////////////////////////////////////////////////////////////////////////
//
// Basic Lifecycle Commands
//

// ListMulti lists against multiple clusters in parallel with unified results. Errors are logged,
// we return as many results as we can.
func ListMulti(ctx context.Context, user string, augiPath bool, cs ...clusters.Cluster) (Sets, error) {
	multisets := make([]Sets, len(cs))

	wg := sync.WaitGroup{}
	wg.Add(len(cs))
	for i, c := range cs {
		go func() {
			defer wg.Done()
			if k, err := c.NewK8s(); err != nil {
				l := logger.New(nil)
				l.LogErr("ListMulti()[%s]: %v.", c.Name, err)
			} else if sets, err := List(ctx, k, &c, user, augiPath); err != nil {
				k.LogErr("ListMulti()[%s]: %v.", c.Name, err)
			} else {
				multisets[i] = sets
			}
		}()
	}
	wg.Wait()

	sets := Sets{}
	for _, uniset := range multisets {
		sets = append(sets, uniset...)
	}
	sets.Sort()

	return sets, nil
}

// List returns Sets with a Deploy and/or a Pod filled out. When a user is
// given, the sets returned will always have a Deployment and a Pod if the
// DevPod is "Running". When a user isn't given we return for all users; in this
// case DevPods with only a pod can be returned for older, plain Pods.
func List(ctx context.Context, k *k8s.Client, c *clusters.Cluster, user string, augiPath bool) (Sets, error) {
	ak := k.InNamespace("")
	k = &ak

	// These label selectors will be used for most list calls below.
	labels := map[string]string{
		"aug.devpod": "true",
	}
	if user != "" {
		labels["aug.user"] = user
	}

	// Perform all RPC reads up front, in parallel.
	specs := map[string]*devpodv1.DevPod{}
	cms := map[string]*k8s.ConfigMap{}
	deploys := map[string]*k8s.Deployment{}
	pods := map[string]*k8s.Pod{}
	services := map[string]*k8s.Service{}
	secs := map[string]*k8s.Secret{}
	pvcs := map[string]*k8s.PVC{}

	grp, gctx := errgroup.WithContext(ctx)
	grp.Go(func() (err error) {
		opts := []k8s.ListOpt{}
		if user != "" {
			opts = append(opts, k8s.ListMatchLabels(map[string]string{"aug.user": user}))
		}
		lst, err := devpodv1.WrappedClient(k).ListDevPods(gctx, opts...)
		for _, obj := range lst {
			specs[obj.Namespace()+"/"+obj.Name()] = obj
		}
		return err
	})
	grp.Go(func() (err error) {
		lst, err := k.ListConfigMaps(gctx, k8s.ListMatchLabels(labels))
		for _, obj := range lst {
			cms[obj.Namespace()+"/"+obj.Name()] = obj
		}
		return err
	})
	grp.Go(func() (err error) {
		lst, err := k.ListDeployments(gctx, k8s.ListMatchLabels(labels))
		for _, obj := range lst {
			deploys[obj.Namespace()+"/"+obj.Name()] = obj
		}
		return err
	})
	grp.Go(func() (err error) {
		lst, err := k.ListPods(gctx, k8s.ListMatchLabels(labels))
		for _, obj := range lst {
			pods[obj.Namespace()+"/"+obj.Name()] = obj
		}
		return err
	})
	grp.Go(func() (err error) {
		lst, err := k.ListServices(gctx, k8s.ListMatchLabels(labels))
		for _, obj := range lst {
			services[obj.Namespace()+"/"+obj.Name()] = obj
		}
		return err
	})
	grp.Go(func() (err error) {
		// NOTE(mattm): Listing secrets across all namespaces isn't feasible. Luckily,
		// we don't need these in the tabular, tree, etc defails when listing sets.
		if false {
			lst, err := k.ListSecrets(gctx, k8s.ListMatchLabels(labels))
			for _, obj := range lst {
				secs[obj.Namespace()+"/"+obj.Name()] = obj
			}
			return err
		}
		return nil
	})
	grp.Go(func() (err error) {
		lst, err := k.ListPVCs(gctx, k8s.ListMatchLabels(labels))
		for _, obj := range lst {
			pvcs[obj.Namespace()+"/"+obj.Name()] = obj
		}
		return err
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	sets := map[string]*Set{}

	// First, init sets from Specs.

	for _, s := range specs {
		key := s.Namespace() + "/" + s.Name()
		if sets[key] != nil {
			k.LogWarn("Duplicate DevPod %s detected.", s.Name())
			continue
		}
		sets[key] = &Set{
			spec: s,
			bld:  NewBuilder(c, s.DevPod()),
		}
	}

	// Deployments (may generate a new set).

	for _, d := range deploys {
		name := d.Name()
		key := d.Namespace() + "/" + name
		if lname := d.Labels()["aug.devpod.name"]; lname != name {
			k.LogWarn("Deployment name mismatch '%s' != '%s'.", lname, name)
		}

		set := sets[key]
		if set == nil {
			k.LogWarn("%s: without a spec", d.ShortName())
			s := devpodv1.NewDevPodFromNames(c.Name, d.Label("aug.user"), name, d.Namespace())
			set = &Set{
				spec: s,
				bld:  NewBuilder(c, s.DevPod()),
			}
			sets[key] = set
		}

		set.deployment = d
	}

	// Then, join pods that match the above labels. This is cheaper than
	// following owner references one at a time.  The list of pods will
	// have some that match a previously listed deployment and some that
	// don't. And, technically we can have a race condition where someone
	// creates a deployment in between listing deployments and listing
	// pods.
	for _, p := range pods {
		name := p.Label("aug.devpod.name")
		if name == "" {
			name = p.Name()
		}
		key := p.Namespace() + "/" + name

		// We have a plain pod (most likely), a manually mislabeld pod (not likely), or a race condition (not that likely).
		// Or we have multiple pods because we're in the middle of an upgrade; in this case create additional entries
		// for the additonal pods.
		if set := sets[key]; set == nil || (set.pod != nil && set.pod.Name() != p.Name()) {
			s := devpodv1.NewDevPodFromNames(c.Name, p.Label("aug.user"), name, p.Namespace())
			sets[key] = &Set{
				spec: s,
				bld:  NewBuilder(c, s.DevPod()),
				pod:  p,
			}
		} else if set.pod == nil {
			// We're joining a pod to it's matching deployment.
			set.pod = p
		} else {
			// We have a dup from the two list calls.
			continue
		}
	}

	// Add in Spec, ConfigMap, Secret(s), PVC(s), and Service(s)
	for _, set := range sets {
		if cm := cms[set.Namespace()+"/"+set.bld.ConfigMapName()]; cm != nil {
			set.cm = cm
		}
		if sec := secs[set.Namespace()+"/"+set.bld.SSHHostKeysSecretName()]; sec != nil {
			set.sshHKSec = sec
		}
		if pvc := pvcs[set.Namespace()+"/"+set.HomePVCName()]; pvc != nil {
			set.homePVC = pvc
		}
		if svc := services[set.Namespace()+"/"+set.bld.ServiceName()]; svc != nil {
			set.service = svc
		}
		if svc := services[set.Namespace()+"/"+set.bld.IngressServiceName()]; svc != nil {
			set.ingressSvc = svc
		}
	}

	// Add in DNS (best-effort, ignore errors); and augiPath (optional)
	grp, gctx = errgroup.WithContext(ctx)
	for _, set := range sets {
		if svc := set.service; svc != nil {
			if hn := svc.ExternalDNSHostname(); hn != "" {
				grp.Go(func() error {
					set.dnsIPs, _ = resolver.LookupIP(gctx, "ip4", hn)
					return nil
				})
			}
		}
	}
	if augiPath {
		for _, set := range sets {
			if pod := set.pod; pod != nil {
				// NOTE(mattm): Hack together a Manager. Alternatively we could provide AugiPath outside of a manager.
				mgr := NewManagerFromSpec(k, c, set.spec)
				set.augi, set.augiErr = mgr.AugiPath(gctx, pod.Name())
			}
		}
	}
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	// Flatten final list.

	ret := Sets{}
	for _, s := range sets {
		ret = append(ret, s)
	}
	ret.Sort()

	return ret, nil
}

// AssertNotExists checks that deployment or legacy simple pod do not exist.
func (m Manager) AssertNotExists(ctx context.Context) error {
	bld := m.Builder()

	// Check for the devpod/{bld.Name()} CRD object.
	if spec, err := m.k8s.GetDevPod(ctx, m.Name()); err == nil && spec != nil {
		return fmt.Errorf("devpod/%s already exists", spec.Name())
	} else if err := k8s.NotFoundOK(err); err != nil {
		return err
	}

	// This check for pod/{bld.Name()} is for objects from when launch_pod.py created raw pods.
	if pod, err := m.k8s.GetPod(ctx, bld.Name()); err == nil && pod != nil {
		return fmt.Errorf("pod/%s already exists", pod.Name())
	} else if err := k8s.NotFoundOK(err); err != nil {
		return err
	}

	// The deploy/{bld.DeploymentName()} is the main object we care about not overwriting by accident.
	if deploy, err := m.getDeployment(ctx); err == nil && deploy != nil {
		return fmt.Errorf("deploy/%s already exists", deploy.Name())
	} else if err := k8s.NotFoundOK(err); err != nil {
		return err
	}

	return nil
}

func (m Manager) BuildSet(ctx context.Context, set0 *Set) (*Set, error) {
	set, err := m.Builder().BuildSet(m.Spec())
	if err != nil {
		return nil, err
	}
	if set0 != nil {
		if cm0, cmAC := set0.cm, set.cmAC; cm0 != nil && cmAC != nil {
			if dot := set0.cm.Key("dotfiles.tgz.b64"); len(dot) > 0 {
				if len(cmAC.Data()["dotfiles.tgz.b64"]) == 0 {
					cmAC.WithData(map[string]string{"dotfiles.tgz.b64": dot})
				}
			}
		}
		if hk0, hkAC := set0.sshHKSec, set.sshHKSecAC; hk0 != nil && hkAC != nil {
			hkAC.SecretApplyConfiguration.Data = nil
			hkAC.WithData(hk0.Raw().Data)
		}
		// Silently increase home PVC AC size if live PVC is already larger.
		if pvc0, pvcAC := set0.homePVC, set.homePVCAC; pvc0 != nil && pvcAC != nil {
			if raw := pvcAC.Raw(); raw == nil {
				// pass
			} else if spec := raw.Spec; spec == nil {
				// pass
			} else if res := spec.Resources; res == nil {
				// pass
			} else if req := res.Requests; req == nil {
				// pass
			} else if qac, q0 := (*req)[corev1.ResourceStorage], pvc0.Raw().Spec.Resources.Requests[corev1.ResourceStorage]; qac.Cmp(q0) < 0 {
				(*req)[corev1.ResourceStorage] = q0
			}
		}
	}
	// Fill in ephemeral PVC from VolumeSnapshot storage request.
	if d := set.deploymentAC; d != nil {
		grp, gctx := errgroup.WithContext(ctx)
		for _, vol := range d.Volumes() {
			if src := vol.Ephemeral; src == nil {
				continue
			} else if tmpl := src.VolumeClaimTemplate; tmpl == nil {
				continue
			} else if pvc := tmpl.Spec; pvc == nil {
				continue
			} else if res := pvc.Resources; res == nil {
				continue
			} else if reqp := res.Requests; reqp == nil {
				continue
			} else if req := *reqp; req == nil {
				continue
			} else if q := req[corev1.ResourceStorage]; !q.IsZero() {
				continue
			} else if ds := pvc.DataSourceRef; ds == nil {
				continue
			} else if kind := ds.Kind; kind == nil || *kind != "VolumeSnapshot" {
				continue
			} else if name := ds.Name; name == nil || *name == "" {
				continue
			} else {
				// we now have a VolumeSnapshot name, with no value set
				grp.Go(func() error {
					vs, err := m.k8s.GetVolumeSnapshot(gctx, *name)
					if err != nil {
						return err
					}
					if rdy, err := vs.ReadyToUse(); !rdy || err != nil {
						m.LogWarn("%s: ready:%t; error: %v.", vs.ShortName(), err)
					}
					if q := vs.RestoreSize(); q == nil {
						return fmt.Errorf("%s has no RestoreSize", vs.ShortName())
					} else {
						req[corev1.ResourceStorage] = *q
						return nil
					}
				})
			}
		}
		if err := grp.Wait(); err != nil {
			return nil, err
		}
	}

	return set, nil
}

func (m Manager) Apply(ctx context.Context, set *Set, dryrun bool) error {
	/// Apply DevPod Spec first, to be used as OnwerReference for the others.

	if !dryrun {
		m.LogInfo("Applying devpod/%s...", m.Name())
		if err := m.Spec().Validate(); err != nil {
			m.LogWarn("Applying devpod/%s with validation errors: %v...", m.Name(), err)
		}
		if obj2, err := m.k8s.ReplaceDevPod(ctx, m.Spec()); err != nil {
			return err
		} else {
			m.spec = obj2
			m.bld.spec = obj2.DevPod()
		}
	}

	/// (Re)Set Ownership

	set.SetOwners(m.Spec())

	/// Apply, collect all errors.

	errs := []error{}
	opts := []k8s.ApplyOpt{
		k8s.ApplyForce(true),
		k8s.ApplyDryRun(dryrun),
	}

	/// Apply ConfigMap
	if ac := set.cmAC; ac != nil {
		if !dryrun {
			m.LogInfo("Applying cm/%s...", ac.Name())
		}
		if obj2, err := m.k8s.ApplyConfigMap(ctx, ac, opts...); err != nil {
			errs = append(errs, err)
		} else {
			set.cm = obj2
		}
	}

	/// Apply Home PVC
	if ac := set.homePVCAC; ac == nil {
		if !dryrun {
			m.LogInfo("NOT Applying a homedir PVC, home data will be lost with the Pod.")
		}
	} else {
		if !dryrun {
			m.LogInfo("Applying pvc/%s (HomeDir)...", ac.Name())
		}
		if obj2, err := m.k8s.ApplyPVC(ctx, ac, opts...); err != nil {
			errs = append(errs, err)
		} else {
			set.homePVC = obj2
		}
	}

	/// Apply SSH HostKeys Secret
	if ac := set.sshHKSecAC; ac == nil {
		if !dryrun {
			m.LogWarn("NOT Applying a SSH HostKeys Secret.")
		}
	} else {
		if !dryrun {
			m.LogInfo("Applying secret/%s...", ac.Name())
		}
		if obj2, err := m.k8s.ApplySecret(ctx, ac, opts...); err != nil {
			errs = append(errs, err)
		} else {
			set.sshHKSec = obj2
		}
	}

	/// Apply SSH Service
	if ac := set.serviceAC; ac != nil {
		if !dryrun {
			m.LogInfo("Applying service/%s (SSH Service)...", ac.Name())
		}
		if obj2, err := m.k8s.ApplyService(ctx, ac, opts...); err != nil {
			errs = append(errs, err)
		} else {
			set.service = obj2
		}
	}

	/// Apply "Expose" Service
	if ac := set.ingressSvcAC; ac != nil {
		if !dryrun {
			m.LogInfo("Applying service/%s (Exposed Ports)...", ac.Name())
		}
		if obj2, err := m.k8s.ApplyService(ctx, ac, opts...); err != nil {
			errs = append(errs, err)
		} else {
			set.ingressSvc = obj2
		}
	}

	/// Apply Deployment (if no existing errors since this may be killed quickly as a self update).
	if err := errors.Join(errs...); err != nil {
		return err
	}
	if ac := set.deploymentAC; ac != nil {
		if !dryrun {
			m.LogInfo("Applying deploy/%s...", ac.Name())
		}
		if obj2, err := m.k8s.ApplyDeployment(ctx, ac, opts...); err != nil {
			return err
		} else {
			set.deployment = obj2
		}
	}

	return nil
}

// Delete all objects. The ConfigMap is the owner and should delete
// everything else except home, but try to delete everything anyway.
// All delete calls are triggered in parallel, errors do not cancel
// inflight calls.
func (m Manager) Delete(ctx context.Context, deleteHome bool) error {
	wg := sync.WaitGroup{}
	errch := make(chan error, 32)
	run := func(f func(context.Context, string) error, s string) {
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := f(ctx, s); err != nil {
				errch <- err
			}
		}()
	}

	m.LogInfo("Deleting DevPod %s (the following log lines cover individual K8s resources)...", m.Name())
	run(m.k8s.DeleteIfDevPod, m.Name())
	run(m.k8s.DeleteIfConfigMap, m.Builder().ConfigMapName())
	run(m.k8s.DeleteIfService, m.Builder().IngressServiceName())
	run(m.k8s.DeleteIfService, m.Builder().ServiceName())
	run(m.k8s.DeleteIfDeployment, m.Builder().DeploymentName())
	run(m.k8s.DeleteIfPod, m.Builder().Name())
	run(m.k8s.DeleteIfSecret, m.Builder().SSHHostKeysSecretName())
	if deleteHome {
		run(m.k8s.DeleteIfPVC, m.Builder().HomePVCNameDefault())
	}

	wg.Wait()
	close(errch)

	errs := []error{}
	for err := range errch {
		errs = append(errs, err)
	}
	if err := errors.Join(errs...); err != nil {
		return err
	}

	m.LogInfo("Deleted DevPod %s.", m.Name())
	return nil
}

func (m Manager) Status(ctx context.Context) (*Set, error) {
	return m.GetSet(ctx)
}

func (m Manager) WaitReady(pctx context.Context, waitDNS bool) (*Set, error) {
	ctx, cancel := context.WithCancel(pctx)
	defer cancel()

	sigintch := make(chan os.Signal, 1)
	signal.Notify(sigintch, os.Interrupt)

	errch := make(chan error, 1)
	go func() {
		errch <- m.waitReadyInternal(ctx, waitDNS)
	}()

	select {
	case <-sigintch:
		m.LogWarn("Caught <ctrl-c>. DevPod status may be incomplete!")
		cancel()
	case err := <-errch:
		if err != nil {
			return nil, err
		}
	}

	return m.GetSet(pctx)
}

func (m Manager) waitReadyInternal(ctx context.Context, waitDNS bool) error {
	m.LogInfo("Waiting for DevPod %s to be ready... (Press <ctrl-c> to stop waiting. The DevPod will continue to boot, but may not be ready yet.)", m.Name())
	sleep := func() {
		select {
		case <-ctx.Done():
		case <-time.After(5 * time.Second):
		}
	}

	deploy, err := m.getDeployment(ctx)
	if err != nil {
		return err
	}

	/// Pod: Wait for Scheduled.
	/// With StorageClass.volumeBindingMode == waitForFirstConsumer, the Pod must be scheduled before the PVC is provisioned.
	for {
		cond := corev1.PodScheduled
		stat := corev1.ConditionTrue

		if r := deploy.Replicas(); r == 0 {
			m.LogInfo("%s is powered off (%d replicas), so not waiting for pod %v.", deploy.ShortName(), r, cond)
			break
		}

		if pod, err := m.getPod(ctx, deploy); err != nil {
			return err
		} else if pod == nil {
			m.LogInfo("pod/??? does not yet exist...")
			sleep()
			continue
		} else if c := pod.Status().Condition(cond); c.Status != stat {
			m.LogInfo("%s %v %v != %v...: %s...", pod.ShortName(), c.Type, c.Status, stat, c.String())
			sleep()
			continue
		} else {
			m.LogInfo("%s %v %v == %v...: %s...", pod.ShortName(), c.Type, c.Status, stat, c.String())
			break
		}
	}

	/// Home PVC: Wait for Bound, this is often the longest delay so wait for it explicitly.
	for {
		target := corev1.ClaimBound

		if pvc, err := m.getHomePVC(ctx, nil); err != nil {
			return err
		} else if pvc == nil {
			break
		} else if r := deploy.Replicas(); r == 0 {
			m.LogInfo("%s is powered off (%d replicas), so not waiting for PVC.", deploy.ShortName(), r)
			break
		} else if cur := pvc.Raw().Status.Phase; cur != target {
			m.LogInfo("pvc/%s Phase %v != %v...", pvc.Name(), cur, target)
			sleep()
			continue
		} else {
			m.LogInfo("pvc/%s Phase == %v.", pvc.Name(), cur)
			break
		}
	}

	/// Pod: Wait for Running. TODO(mattm): Consider adding a ready signal, e.g., sshd is healthy
	for {
		target := corev1.PodRunning

		if r := deploy.Replicas(); r == 0 {
			m.LogInfo("%s is powered off (%d replicas), so not waiting for pod %v.", deploy.ShortName(), r, target)
			break
		}

		if pod, err := m.getPod(ctx, deploy); err != nil {
			return err
		} else if pod == nil {
			m.LogInfo("pod/??? does not yet exist...")
			sleep()
			continue
		} else if cur := pod.Status().Phase; cur != target {
			m.LogInfo("%s Phase %v != %v...: %s...", pod.ShortName(), cur, target, pod.Status().LatestCondition().String())
			sleep()
			continue
		} else {
			m.LogInfo("%s Phase == %v. %s.", pod.ShortName(), cur, pod.Status().LatestCondition().String())
			break
		}
	}

	/// Service (SSH): Wait for LoadBalancer IP
	for {
		if svc, err := m.getService(ctx); err != nil {
			return err
		} else if typ := svc.Raw().Spec.Type; typ == corev1.ServiceTypeClusterIP {
			m.LogInfo("svc/%s Listening on ClusterIP %s.", svc.Name(), svc.Raw().Spec.ClusterIP)
			break
		} else if typ != corev1.ServiceTypeLoadBalancer {
			break
		} else if statuses := svc.Raw().Status.LoadBalancer.Ingress; len(statuses) == 0 {
			m.LogInfo("svc/%s waiting for LoadBalancer Public IP...", svc.Name())
			sleep()
			continue
		} else if ip := statuses[0].IP; ip == "" {
			m.LogInfo("svc/%s waiting for LoadBalancer Public IP...", svc.Name())
			sleep()
			continue
		} else {
			m.LogInfo("svc/%s Listening on PublicIP %s.", svc.Name(), ip)
			m.LogInfo("You may now SSH using the IP address (but a hostname will be available shortly): ssh -p%d augment@%s. ", m.Builder().SSHNonStdPort(), ip)
			break
		}
	}

	/// Service (SSH): Wait for Public DNS using CloudFlare's ******* (CW uses CloudFlare for DNS).
	if !waitDNS {
		// pass
	} else if svc, err := m.getService(ctx); err != nil {
		return err
	} else if hostname := svc.ExternalDNSHostname(); hostname != "" {
		m.LogInfo("Waiting for DNS, this may take a while. You may <ctrl-c> to exit and use the above IP.")
		for {
			if ips, err := m.LookupIP(ctx, hostname); err != nil {
				m.LogInfo("%s waiting for DNS: %v...", hostname, err)
				sleep()
				continue
			} else if len(ips) == 0 {
				m.LogInfo("%s waiting for DNS...", hostname, err)
				sleep()
				continue
			} else {
				m.LogInfo("%s DNS resolved to: %v", hostname, ips)
				m.LogInfo("You may now SSH using: ssh -p%d augment@%s.", m.Builder().SSHNonStdPort(), hostname)
				break
			}
		}
	}

	m.LogInfo("DevPod %s is ready \\o/ !!1", m.Name())
	return nil
}

// Logs returns an iterator which streams the pod log lines. The lines to not
// include the newline. If an error is returned, it will be the last item
// yielded by the iterator.
func (m Manager) Logs(ctx context.Context, opts ...k8s.PodLogOpt) iter.Seq2[string, error] {
	pod, err := m.k8s.GetDeploymentPod(ctx, m.Builder().DeploymentName())
	if err != nil {
		return func(yield func(string, error) bool) {
			yield("", err)
		}
	}
	return pod.Logs(ctx, m.k8s.Client, opts...)
}

// AugiPath returns the path to `augi` in the DevPod which is a good approximation for its version. (Note that this
// calls pod.Exec() which in turn currently forks `kubectl`). `podname` is optional and is resolved if needed.
func (m Manager) AugiPath(ctx context.Context, podname string) (string, error) {
	if podname == "" {
		pod, err := m.k8s.GetDeploymentPod(ctx, m.Builder().DeploymentName())
		if err != nil {
			return "", err
		}
		podname = pod.Name()
	}
	stdoutb, stderrb, err := m.k8s.ExecPodIO(ctx, podname, "", nil, "/bin/sh", "-c", strings.Join([]string{
		"if [ -e /usr/local/bin/augi ]; then",
		"  readlink -n -e /usr/local/bin/augi",
		"else",
		"  echo '<no-augi>'",
		"fi",
	}, "\n"))
	if len(stderrb) > 0 {
		err = fmt.Errorf("%w: %s", err, string(stderrb))
	}
	stdout := utils.TrimOneLine(string(stdoutb))
	if stdout == "" && err == nil {
		stdout = "<no-augi>"
	}
	return stdout, err
}

////////////////////////////////////////////////////////////////////////////////
//
// Power (On/Off) Commands
//

// PowerOn is implemented by setting the deployment replicas to 1.
func (m Manager) PowerOn(ctx context.Context) error {
	if err := m.UpdateSpec(ctx, func(s *spec.DevPod) error {
		s.SetPowerOn()
		return nil
	}); err != nil {
		m.LogErr("PowerOn(): Error updating spec: %v.", err)
	}
	_, err := m.k8s.PatchDeploymentReplicas(ctx, m.bld.DeploymentName(), 1)
	return err
}

// PowerOff is implemented by setting the deployment replicas to 0.
func (m Manager) PowerOff(ctx context.Context) error {
	if err := m.UpdateSpec(ctx, func(s *spec.DevPod) error {
		s.SetPowerOff()
		return nil
	}); err != nil {
		m.LogErr("PowerOn(): Error updating spec: %v.", err)
	}
	_, err := m.k8s.PatchDeploymentReplicas(ctx, m.bld.DeploymentName(), 0)
	return err
}

// Reboot is implemented by mapping the deployment to all pods (typically 1)
// and deleting them.
func (m Manager) Reboot(ctx context.Context) error {
	/// Get the Deployment

	d, err := m.getDeployment(ctx)
	if err != nil {
		return err
	}

	/// Get Pods owned by the Deployment (hopefully 1 usually)

	pods, err := d.GetPods(ctx, m.k8s.Client)
	if err != nil {
		return err
	}

	/// Delete all in parallel

	wg := sync.WaitGroup{}
	errch := make(chan error, len(pods))
	wg.Add(len(pods))

	for _, pod := range pods {
		go func() {
			defer wg.Done()
			if err := m.k8s.DeletePod(ctx, pod.Name()); err != nil {
				errch <- err
			}
		}()
	}

	wg.Wait()
	close(errch)

	/// Return joined errors

	errs := []error{}
	for err := range errch {
		errs = append(errs, err)
	}
	return errors.Join(errs...)
}

////////////////////////////////////////////////////////////////////////////////
//
// Connectivity Commands
//

func (m Manager) ExposePorts(ctx context.Context, set bool, ports ...any) error {
	if err := m.UpdateSpec(ctx, func(s *spec.DevPod) error {
		return s.ExposePorts(set, ports...)
	}); err != nil {
		return err
	}
	return m.applyIngressService(ctx)
}

func (m Manager) HidePorts(ctx context.Context, ports ...int32) error {
	if err := m.UpdateSpec(ctx, func(s *spec.DevPod) error {
		s.HidePorts(ports...)
		return nil
	}); err != nil {
		return err
	}
	return m.applyIngressService(ctx)
}

func (m Manager) applyIngressService(ctx context.Context) error {
	if len(m.Spec().DevPod().Cxn.ExposedPorts) == 0 {
		return m.k8s.DeleteIfService(ctx, m.Builder().IngressServiceName())
	}
	if ac, err := m.Builder().BuildIngressService(); err != nil {
		return err
	} else if ac == nil {
		return nil
	} else {
		ac.WithOwner(&m.spec.Object)
		// TODO(mattm): Log a diff.
		m.LogInfo("Applying service/%s (Exposed Ports)...", ac.Name())
		_, err := m.k8s.ApplyService(ctx, ac, k8s.ApplyForce(true))
		return err
	}
}

// SSHHostname determines the best hostname to use for the `SSH()` command.
// Note that this isn't necessarily the best `Hostname` to include in an
// `ssh_config`. It prefers in-cluster addressing and is robust against
// propagation delays (may return IP when DNS eventually becomes available).
func (m Manager) SSHHostname(ctx context.Context) string {
	// If we can confirm IsLocal(), prefer the local service name from kube-dns.
	if m.IsLocal(ctx) {
		return m.Builder().ServiceName()
	}

	// Otherwise, get the service so we can read live details.
	svc, err := m.getService(ctx)
	if err != nil {
		m.LogWarn("SSHHostName(): Unable to get service (will guess hostname): %v.", err)
		return m.Builder().SSHServiceHostname()
	}

	// Prefer DNS name if DNS resolves.
	hn := svc.ExternalDNSHostname()
	if hn != "" {
		if ips, _ := m.LookupIP(ctx, hn); len(ips) > 0 {
			return hn
		}
	}

	// Public IP, but warn if we're not using an External DNS.
	if ip := svc.PublicIP(); ip != "" {
		if hn != "" {
			m.LogWarn("SSHHostName(): Using %s because %s does not yet resolve from DNS.", ip, hn)
		}
		return ip
	}

	// No Public IP?! Use un-resolved DNS name.
	if hn != "" {
		m.LogWarn("SSHHostName(): Using %s but DNS doesn't result and no public IP found.", hn)
		return hn
	}

	// Hail Mary.
	m.LogWarn("SSHHostName(): Unable to get hostname from service, guessing: %s.", m.Builder().ServiceName())
	return m.Builder().ServiceName()
}

func (m Manager) SSH(ctx context.Context, args ...string) error {
	bld := m.Builder()
	hn := m.SSHHostname(ctx)
	cmdline := []string{
		"ssh",
		"-A",
		"-oStrictHostKeyChecking=accept-new", // accept new without asking, block connections on changes
		fmt.Sprintf("-p%d", bld.SSHNonStdPort()),
		fmt.Sprintf("augment@%s", hn),
	}
	cmdline = append(cmdline, args...)

	cmd := exec.CommandContext(ctx, cmdline[0], cmdline[1:]...)
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	m.LogInfo("Running: %s.", strings.Join(cmdline, " "))
	return cmd.Run()
}

func (m Manager) Exec(ctx context.Context, asRoot bool, args ...string) error {
	bld := m.Builder()

	if asRoot {
		if len(args) == 0 {
			m.LogWarn("Exec(%s): No command was given, guessing `/bin/bash`.", bld.Name())
			args = append(args, "/bin/bash")
		}
	} else {
		sudo := []string{"/bin/sudo", "-u", "augment"}
		if len(args) == 0 {
			args = append(sudo, "-i")
		} else {
			sudo = append(sudo, "--")
			args = append(sudo, args...)
		}
	}

	pod, err := m.k8s.GetDeploymentPod(ctx, bld.DeploymentName())
	if err != nil {
		return err
	}
	return m.k8s.ExecPod(ctx, pod.Name(), "", true, true, args...)
}

////////////////////////////////////////////////////////////////////////////////
//
// Update Commands
//

func (m Manager) UpdateImage(ctx context.Context, newImage string, dryRun bool, init bool, initSpec string) error {
	/// Update init.sh (by default) along with the image.
	if init {
		if err := m.UpdateInit(ctx, initSpec, dryRun); err != nil {
			return err
		}
	}

	/// Get the Deployment -> Container -> Image
	depl, err := m.getDeployment(ctx)
	if err != nil {
		return err
	}
	containers := depl.Raw().Spec.Template.Spec.Containers
	if l := len(containers); l != 1 {
		return fmt.Errorf("UpdateImage() only supported on pods with a single container, got %d", l)
	}
	container := &containers[0]
	curImage := container.Image

	/// Get the latest version of the existing image, if available.
	if newImage == "" {
		var err error
		if newImage, err = m.Cluster().LatestImageFromCurrent(curImage); err != nil {
			return err
		}
	}

	/// Noop, Dry-Run, or Update
	if newImage == curImage {
		m.LogInfo("Deployment %s already using image %s", depl.Name(), curImage)
		return nil
	} else if dryRun {
		m.LogInfo("[dry-run] Would update Deployment %s:\n\t- %s\n\t+ %s", depl.Name(), curImage, newImage)
		return nil
	} else {
		m.LogInfo("Patching Deployment %s...:\n\t- %s\n\t+ %s", depl.Name(), curImage, newImage)
		if _, err := m.k8s.PatchDeploymentImage(ctx, depl.Name(), container.Name, newImage); err != nil {
			return err
		}
		m.LogInfo("Patched Deployment %s.", depl.Name())
		return nil
	}
}

func (m Manager) UpdateInit(ctx context.Context, spec string, dryrun bool) error {
	/// Get new init.sh from filesystem.
	newInit, err := Init{}.FromSpec(spec)
	if err != nil {
		return err
	}

	/// Get existing init.sh from k8s.
	cm, err := m.GetConfigMap(ctx)
	if err != nil {
		return err
	}
	curInit := cm.Key("init.sh")

	/// Early exit if equal, or log unified diff.
	if curInit == newInit {
		m.LogInfo("ConfigMap %s/init.sh is up to date", cm.Name())
		return nil
	} else if diff, err := difflib.GetUnifiedDiffString(difflib.UnifiedDiff{
		FromFile: "cur/init.sh",
		A:        difflib.SplitLines(curInit),
		ToFile:   "new/init.sh",
		B:        difflib.SplitLines(newInit),
		Context:  3,
	}); err != nil {
		return err
	} else {
		m.LogInfo("ConfigMap %s/init.sh diff:\n%s", cm.Name(), diff)
	}

	/// Update (or dry-run)
	if dryrun {
		m.LogInfo("[dry-run] Would update ConfigMap %s/init.sh.", cm.Name())
		return nil
	} else {
		m.LogInfo("Updating ConfigMap %s/init.sh...", cm.Name())
		if _, err := cm.SetKey(ctx, m.k8s.Client, "init.sh", newInit); err != nil {
			return err
		}
		m.LogInfo("Updated ConfigMap %s/init.sh. Affects won't take place until the next reboot.", cm.Name())
		return nil
	}
}

func (m Manager) UpdateConfigMap(ctx context.Context, updates map[string]string) error {
	cm, err := m.GetConfigMap(ctx)
	if err != nil {
		return err
	}
	cm, err = cm.SetData(ctx, m.k8s.Client, updates)
	if err != nil {
		return err
	}
	m.LogInfo("Updated ConfigMap %s.", cm.Name())
	return nil
}

// Resize is not implemented.
func (m Manager) Resize(ctx context.Context) error {
	return fmt.Errorf("Resize(%s): Not Implemented", m.bld.Name())
}

func (m Manager) AttachVolume(ctx context.Context) error {
	return fmt.Errorf("AttachVolume(%s): Not Implemented", m.bld.Name())
}

func (m Manager) DetachVolume(ctx context.Context) error {
	return fmt.Errorf("DetachVolume(%s): Not Implemented", m.bld.Name())
}

////////////////////////////////////////////////////////////////////////////////
//
// Home Directory Commands
//

// GrowHome increases the size of a Home PVC. It is an error if the DevPod
// does use a Home PVC, or if the new size would not be an increase. GrowHome
// takes a string arg in the form "[+]<int>[Gi]"; with a "+" the argument represents
// the delta increase; without a "+" the argument represents the new absolute size.
// <int> is always in Gi, but an optional Gi can be included.
func (m Manager) GrowHome(ctx context.Context, arg string) error {
	pvc, err := m.getHomePVC(ctx, nil)
	if err != nil {
		return err
	}
	if pvc == nil {
		return fmt.Errorf("Home PVC not found or one isn't configured for this DevPod")
	}

	if err := m.UpdateSpec(ctx, func(s *spec.DevPod) error {
		return s.GrowHome(arg)
	}); err != nil {
		return err
	}

	if _, err := pvc.PatchSize(ctx, m.k8s.Client, m.Spec().DevPod().Home.Size()); err != nil {
		return err
	}
	m.LogWarn("If your homedir is block-based (the default) a reboot is now required; fs-based homedir updates are immediate.")
	return nil
}

// GoHome replaces (or adds) the volume used for /home.
func (m Manager) GoHome(ctx context.Context, hname string) error {
	return fmt.Errorf("GoHome(%s): Not Implemented", m.bld.Name())
}

// GoNewHome builds a new PVC and then calls GoHome().
func (m Manager) GoNewHome(ctx context.Context, hname, htype, hclass string, hsize int) error {
	return fmt.Errorf("GoHome(%s): Not Implemented", m.bld.Name())
}

////////////////////////////////////////////////////////////////////////////////
//
// Dotfile Commands
//

func (m Manager) GetDotFilesArchive(ctx context.Context) (string, error) {
	cm, err := m.GetConfigMap(ctx)
	if err != nil {
		return "", err
	}
	return cm.Key("dotfiles.tgz.b64"), nil
}

func (m Manager) GetDotFilesFormatted(ctx context.Context) (string, error) {
	tgzb64, err := m.GetDotFilesArchive(ctx)
	if err != nil {
		return "", err
	}
	return formatTarArchive(tgzb64)
}

func (m Manager) UpdateDotFiles(ctx context.Context, addFiles, rmFiles map[string]bool, dryrun, diff, vimdiff bool) error {
	tgzb64, err := m.GetDotFilesArchive(ctx)
	if err != nil {
		return err
	}

	d, err := NewDotfilesFromString(tgzb64, addFiles, rmFiles)
	if err != nil {
		return err
	}
	if err := d.Close(); err != nil {
		return err
	}

	if dryrun {
		fmt.Println("Dry-run. Printing file lists.")
		updated, err := formatTarArchive(d.String())
		if err != nil {
			return err
		}

		if diff || vimdiff {
			fmt.Println("Showing diff.")
			orig, err := m.GetDotFilesFormatted(ctx)
			if err != nil {
				return err
			}

			if diff {
				return DpDiffStrings(ctx, "diff", orig, "dotfiles", updated, "-u")
			}
			return DpDiffStrings(ctx, "vimdiff", orig, "dotfiles", updated, "-R")

		} else {
			fmt.Print(updated)
		}
		return nil
	}

	updates := map[string]string{
		"dotfiles.tgz.b64": d.String(),
	}
	if err := m.UpdateConfigMap(ctx, updates); err != nil {
		return err
	}

	return nil
}

func formatTarArchive(tgbz64 string) (string, error) {
	tr, err := ReaderFromArchive(tgbz64)
	if err != nil {
		return "", err
	}
	var list strings.Builder
	m := make(map[string]string)

	for {
		hdr, err := tr.Next()
		if err == io.EOF {
			break
		} else if err != nil {
			return "", err
		}
		m[hdr.Name] = fmt.Sprintf("%7d %o %s\n", hdr.Size, hdr.Mode, hdr.Name)
	}

	// Sort by filename, especially useful for diff output
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		fmt.Fprint(&list, m[k])
	}
	return list.String(), nil
}
