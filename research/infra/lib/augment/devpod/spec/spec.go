package spec

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	yaml "github.com/goccy/go-yaml"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	corev1a "k8s.io/client-go/applyconfigurations/core/v1"

	"github.com/augmentcode/augment/infra/lib/distribution"
	"github.com/augmentcode/augment/research/infra/cfg/clusters"
)

// DevPod is a declarative spec. Most fields have good defaults, a minimal spec has
// just UserName, DevPodName, NodeType, and CPUCount or GPUCount. Other common fields are
// PowerState, ExposePorts, EternalTerminal and MoshStart, Image.Tag, and Home.Size.
//
// Many of the `augi devpod create` flags are used to fill out a Spec. The Spec is stored
// in the ConfigMap `r.augmentcode.com/devpod/spec` annotation, which is then used by
// `augi devpod create --replace`.
//
// The DevPod Spec could eventually be defined as a proto. Or it can be be used
// for a CRD with a custom controller.
type DevPod struct {
	/// The DevPod ID. DevPodName **must** start with "${UserName}-".
	ClusterName string `json:"cluster_name"`
	UserName    string `json:"user_name"`
	DevPodName  string `json:"devpod_name"`

	PowerOff_p *bool `json:"power_off,omitempty"`

	/// Main Sub Specs
	Resources Resources `json:"resources,omitempty"`
	Cxn       Cxn       `json:"cxn,omitempty"`
	Image     Image     `json:"image,omitempty"`
	Home      Home      `json:"home,omitempty"`
	Volumes   Volumes   `json:"volumes,omitempty"`

	/// Misc
	DockerMode bool `json:"docker_mode,omitempty"`
	Privileged bool `json:"privileged,omitempty"`
}
type Resources struct {
	NodeType string             `json:"node_type,omitempty"`
	GPU      *int32             `json:"gpu,omitempty"`
	CPU      *resource.Quantity `json:"cpu,omitempty"`
	RAM      *resource.Quantity `json:"ram,omitempty"`
	Disk     *resource.Quantity `json:"disk,omitempty"`

	NodeName    string              `json:"node_name,omitempty"`
	PoolType    string              `json:"pool_type,omitempty"`
	Tolerations []corev1.Toleration `json:"tolerations,omitempty"`

	IgnoreLimits     bool `json:"ignore_limits,omitempty"`
	IgnoreValidation bool `json:"ignore_validations,omitempty"`
}
type Cxn struct {
	ExposedPorts ExposedPorts `json:"exposed_ports,omitempty"`

	SSHStdPort                bool `json:"ssh_standard_port,omitempty"`
	SSHDisableCentralizedKeys bool `json:"ssh_disable_centralized_keys,omitempty"`

	EternalTerminal bool  `json:"eternal_terminal,omitempty"`
	MoshStart       int32 `json:"mosh_start,omitempty"`
	MoshCount       int32 `json:"mosh_count,omitempty"`

	PublicIP_p *bool `json:"public_ip,omitempty"` // Defaults to true, for now
}
type (
	ExposedPorts []ExposedPort
	ExposedPort  struct {
		Name   string `json:"name,omitempty"`   // optional
		Port   int32  `json:"port,omitempty"`   // required
		Target int32  `json:"target,omitempty"` // optional
	}
)

type Image struct {
	// None of these are normally needed.
	Registry string `json:"registry,omitempty"` // Default auto based on cluster.
	Path     string `json:"path,omitempty"`     // Default auto detect 'devpod_cpu' or 'devpod_gpu' based on resources.
	Tag      string `json:"tag,omitempty"`      // Default auto from current augment.git.
}
type Home struct {
	CustomName string             `json:"name,omitempty"`  // Default to "${DevPodName}-home"
	Class      string             `json:"class,omitempty"` // Storage Class, defaults based on cluster. Can also be "ephemeral"|"local"
	Mode       string             `json:"mode,omitempty"`  // Volume Mode, defaults to rwo or rwx based on class.
	Size_p     *resource.Quantity `json:"size,omitempty"`  // Defaults based on Cluster (256Gi). Will grow to this size.
}
type Volumes struct {
	ExcludeClusterDefaults bool               `json:"exclude_cluster_defaults,omitempty"` // Exclude all cluster defaults.
	Exclude                []string           `json:"exclude,omitempty"`                  // Exclude individual cluster defaults.
	VolMounts              clusters.VolMounts `json:"volmounts,omitempty"`                // Add arbitrary additional mounts.
	Volumes                []string           `json:"volumes,omitempty"`                  // Add additional mounts (in the PVC_NAME:PATH format).
	Snapshots              []string           `json:"snapshots,omitempty"`                // Mount previous snapshots read-only to /mnt/snapshots.
}

func FromJSON(str string) (*DevPod, error) {
	spec := &DevPod{}
	if err := json.Unmarshal([]byte(str), spec); err != nil {
		return nil, err
	}
	return spec, nil
}

func FromYAML(str string) (*DevPod, error) {
	spec := &DevPod{}
	if err := yaml.UnmarshalWithOptions([]byte(str), spec, yaml.UseJSONUnmarshaler()); err != nil {
		return nil, err
	}
	return spec, nil
}

func ExposedPortFromString(str string) (ExposedPort, error) {
	ep := ExposedPort{}
	z := ExposedPort{}

	port, target := "", ""
	if parts := strings.Split(str, ":"); len(parts) == 1 {
		port = parts[0]
	} else if len(parts) == 2 {
		port, target = parts[0], parts[1]
	} else if len(parts) == 3 {
		ep.Name, port, target = parts[0], parts[1], parts[2]
	} else {
		return z, fmt.Errorf("%s: must be in the form PORT[:TARGET] or [NAME:]PORT:[TARGET]", str)
	}

	if i, err := strconv.ParseInt(port, 10, 32); err != nil {
		return z, fmt.Errorf("%s: port must be an int32: %w", str, err)
	} else {
		ep.Port = int32(i)
	}

	if target != "" {
		if i, err := strconv.ParseInt(target, 10, 32); err != nil {
			return z, fmt.Errorf("%s: target must be an int32: %w", str, err)
		} else {
			ep.Target = int32(i)
		}
	}

	return ep, nil
}

func ExposedPortFrom(a any) (ExposedPort, error) {
	switch v := a.(type) {
	case string:
		return ExposedPortFromString(v)
	case int32:
		return ExposedPort{Port: v}, nil
	default:
		return ExposedPort{}, fmt.Errorf("%+v of type %T: unsupported type", a, a)
	}
}

func ExposedPortsFrom[T string | int32 | any](a ...T) (ExposedPorts, error) {
	ret := ExposedPorts{}
	errs := []error{}
	for _, v := range a {
		if ep, err := ExposedPortFrom(v); err != nil {
			errs = append(errs, err)
		} else {
			ret = append(ret, ep)
		}
	}
	return ret, errors.Join(errs...)
}

////////////////////////////////////////////////////////////////////////////////
//
// Misc READ-ONLY receiver methods.
//

func (s DevPod) EqualIDs(other DevPod) bool {
	if s.ClusterName != other.ClusterName {
		return false
	}
	if s.UserName != other.UserName {
		return false
	}
	if s.DevPodName != other.DevPodName {
		return false
	}
	return true
}

func (s DevPod) ID() DevPod {
	return DevPod{
		ClusterName: s.ClusterName,
		UserName:    s.UserName,
		DevPodName:  s.DevPodName,
	}
}

func (s DevPod) JSON() (string, error) {
	buf, err := json.MarshalIndent(s, "", "  ")
	return string(buf) + "\n", err
}

func (s DevPod) YAML() (string, error) {
	buf, err := yaml.MarshalWithOptions(s, yaml.UseJSONMarshaler())
	return string(buf), err
}

func (s DevPod) HasPowerOff() bool {
	return s.PowerOff_p != nil
}

func (s DevPod) PowerOff() bool {
	return s.PowerOff_p != nil && *s.PowerOff_p
}

func (s DevPod) PowerOn() bool {
	return s.PowerOff_p == nil || !*s.PowerOff_p
}

func (s Resources) TolerationApplyConfigs() []*corev1a.TolerationApplyConfiguration {
	ret := []*corev1a.TolerationApplyConfiguration{}
	for _, t := range s.Tolerations {
		cfg := corev1a.Toleration()
		cfg.WithKey(t.Key)
		cfg.WithOperator(t.Operator)
		cfg.WithValue(t.Value)
		cfg.WithEffect(t.Effect)
		if t.TolerationSeconds != nil {
			cfg.WithTolerationSeconds(*t.TolerationSeconds)
		}
		ret = append(ret, cfg)
	}
	return ret
}

func (s ExposedPort) ServicePort() corev1.ServicePort {
	svcPort := corev1.ServicePort{
		Name: s.Name,
		Port: s.Port,
	}
	if svcPort.Name == "" {
		svcPort.Name = fmt.Sprintf("%d", svcPort.Port)
	}
	if s.Target > 0 {
		svcPort.TargetPort = intstr.FromInt32(s.Target)
	}
	return svcPort
}

func (s ExposedPort) ServicePortApplyConfig() *corev1a.ServicePortApplyConfiguration {
	cfg := corev1a.ServicePort()
	cfg.WithPort(s.Port)
	cfg.WithProtocol(corev1.Protocol("TCP"))

	if s.Name == "" {
		cfg.WithName(fmt.Sprintf("%d", s.Port))
	} else {
		cfg.WithName(s.Name)
	}
	if s.Target > 0 {
		cfg.WithTargetPort(intstr.FromInt32(s.Target))
	} else {
		cfg.WithTargetPort(intstr.FromInt32(s.Port))
	}
	return cfg
}

func (s Cxn) ServicePorts() []corev1.ServicePort {
	return s.ExposedPorts.ServicePorts()
}

func (s Cxn) ServicePortsApplyConfig() []*corev1a.ServicePortApplyConfiguration {
	return s.ExposedPorts.ServicePortsApplyConfig()
}

func (s ExposedPorts) ServicePorts() []corev1.ServicePort {
	ret := []corev1.ServicePort{}
	for _, p := range s {
		ret = append(ret, p.ServicePort())
	}
	return ret
}

func (s ExposedPorts) ServicePortsApplyConfig() []*corev1a.ServicePortApplyConfiguration {
	ret := []*corev1a.ServicePortApplyConfiguration{}
	for _, p := range s {
		ret = append(ret, p.ServicePortApplyConfig())
	}
	return ret
}

func (s Cxn) PublicIP() bool {
	return s.PublicIP_p == nil || *s.PublicIP_p
}

func (s Image) Image(c clusters.Cluster, gpu bool) string {
	reg, path, tag := distribution.ParseImage(func() string {
		if gpu {
			return c.Images["devpod_gpu"]
		} else {
			return c.Images["devpod_cpu"]
		}
	}())
	if r := s.Registry; r != "" {
		reg = r
	}
	if p := s.Path; p != "" {
		path = p
	}

	// When using the default image (cpu/gpu), use 'main' as the default tag. And let '@main' trigger
	// the old behavior of using the tag baked into the repo.
	if s.Path == "" { // default image
		if t := s.Tag; t == "@main" {
			// pass
		} else if t != "" {
			tag = t
		} else {
			tag = "main"
		}
	} else { // non-default image
		if t := s.Tag; t != "" {
			tag = t
		}
	}

	return reg + "/" + path + ":" + tag
}

// HomePVCName formats the default when DevPod.Home.Name is empty.
func (s DevPod) HomePVCName() string {
	return s.Home.Name(s)
}

// Name returns the requested or default HomeDir PVC name. The default requires information from the overall spec.
func (sh Home) Name(s DevPod) string {
	if n := sh.CustomName; n != "" {
		return n
	}
	return s.DevPodName + "-home"
}

func (sh Home) Size() resource.Quantity {
	if sh.Size_p != nil {
		return *sh.Size_p
	}
	return *resource.NewQuantity(256*1024*1024*1024, resource.BinarySI)
}

// CombinedUserVolMounts builds `Volumes` and `Snapshots`, and then merges with `VolMounts`.
func (s Volumes) CombinedUserVolMounts(username string) (clusters.VolMounts, error) {
	errs := []error{}
	ret := s.VolMounts
	for _, v := range s.Volumes {
		if vm, err := clusters.VolMountFromString(v); err != nil {
			errs = append(errs, err)
		} else {
			ret = append(ret, vm)
		}
	}
	for _, ss := range s.Snapshots {
		nameNoDots := strings.ReplaceAll(ss, ".", "-")
		ret = append(ret, clusters.VolMount{
			Name: nameNoDots,
			Vol: corev1.Volume{
				VolumeSource: corev1.VolumeSource{
					Ephemeral: &corev1.EphemeralVolumeSource{
						VolumeClaimTemplate: &corev1.PersistentVolumeClaimTemplate{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									"aug.devpod": "true",
									"aug.user":   username,
								},
							},
							Spec: corev1.PersistentVolumeClaimSpec{
								AccessModes: []corev1.PersistentVolumeAccessMode{corev1.ReadOnlyMany},
								DataSourceRef: &corev1.TypedObjectReference{
									APIGroup: func() *string { s := "snapshot.storage.k8s.io"; return &s }(),
									Kind:     "VolumeSnapshot",
									Name:     ss,
								},
								Resources: corev1.VolumeResourceRequirements{
									Requests: corev1.ResourceList{
										corev1.ResourceStorage: *resource.NewQuantity(0, resource.BinarySI), // This is a TODO that we epect to update later.
									},
								},
							},
						},
					},
				},
			},
			Mounts: []corev1.VolumeMount{{
				MountPath: "/mnt/snapshots/" + ss,
				ReadOnly:  true,
			}},
		})
	}
	if err := errors.Join(errs...); err != nil {
		return nil, err
	}
	return ret, nil
}

// CombinedAllVolMounts combines all of `Volumes` and `VolMounts` (via `CombinedUserVolmounts()`) with
// the cluster-defined mounts, and filters out excluded VolMounts.
func (s Volumes) CombinedAllVolMounts(c clusters.Cluster, username string) (clusters.VolMounts, error) {
	// Start with user-defined VolMounts.
	vms, err := s.CombinedUserVolMounts(username)
	if err != nil {
		return nil, err
	}
	// Add cluster-defined VolMounts.
	if !s.ExcludeClusterDefaults {
		vms = append(vms, c.VolMounts...)
	}
	// Filter out excluded VolMounts.
	exclude := map[string]bool{}
	for _, e := range s.Exclude {
		exclude[e] = true
	}
	fltr := clusters.VolMounts{}
	for _, vm := range vms {
		if !exclude[vm.Name] {
			fltr = append(fltr, vm)
		}
	}
	return fltr, nil
}
