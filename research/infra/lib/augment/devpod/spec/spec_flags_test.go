package spec

import (
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/spf13/pflag"
	corev1 "k8s.io/api/core/v1"

	"github.com/augmentcode/augment/research/infra/cfg/clusters"
)

func TestFromFlags(t *testing.T) {
	t.<PERSON>()
	tests := map[string]struct {
		origSpec  *DevPod
		inFlags   map[string]string
		inCluster string
		inUser    string
		inName    string
		wantErr   string
		want      *DevPod
	}{
		/// Tests for new spec.

		"no-flags": {
			inFlags:   map[string]string{},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources:   Resources{},
				Cxn:         Cxn{},
				Image:       Image{},
				Home:        Home{},
				Volumes:     Volumes{},
			},
		},
		"poweroff-both-flags-err": {
			inFlags: map[string]string{
				"power-on":  "true",
				"power-off": "true",
			},
			wantErr: "--power-on and --power-off are mutually exclusive.",
		},
		"power-on": {
			inFlags: map[string]string{
				"power-on": "true",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				PowerOff_p:  func() *bool { b := false; return &b }(),
				Resources:   Resources{},
				Cxn:         Cxn{},
				Image:       Image{},
				Home:        Home{},
				Volumes:     Volumes{},
			},
		},
		"power-off": {
			inFlags: map[string]string{
				"power-off": "true",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				PowerOff_p:  func() *bool { b := true; return &b }(),
				Resources:   Resources{},
				Cxn:         Cxn{},
				Image:       Image{},
				Home:        Home{},
				Volumes:     Volumes{},
			},
		},
		"cpu-and-gpu-type-flag": {
			inFlags: map[string]string{
				"cpu-type": "cpu0",
				"gpu-type": "gpu0",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "cannot specify both --gpu-type and --cpu-type",
		},
		"just-cpu-type": {
			inFlags: map[string]string{
				"cpu-type": "cpu0",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					NodeType: "cpu0",
				},
				Cxn:     Cxn{},
				Image:   Image{},
				Home:    Home{},
				Volumes: Volumes{},
			},
		},
		"just-gpu-type": {
			inFlags: map[string]string{
				"gpu-type": "gpu0",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					NodeType: "gpu0",
				},
				Cxn:     Cxn{},
				Image:   Image{},
				Home:    Home{},
				Volumes: Volumes{},
			},
		},
		"all-flags": {
			inFlags: map[string]string{
				"gpu-type":                     "cpu0",
				"gpu-count":                    "3",
				"cpu-count":                    "11",
				"ram":                          "23", // Also tests auto-G suffix.
				"disk":                         "29", // Also tests auto-Gi suffix.
				"node-name":                    "node0",
				"pool-type":                    "pool0",
				"tolerations":                  "key0,key1=val1,key2:PreferNoSchedule,key3=val3:PreferNoSchedule",
				"ignore-limits":                "true",
				"ignore-validation":            "true",
				"exposed-ports":                "8080,90:9090,test0:7777:",
				"ssh-std-port":                 "true",
				"ssh-disable-centralized-keys": "true",
				"eternal-terminal":             "true",
				"mosh-start":                   "100",
				"mosh-count":                   "10",
				"public-ip":                    "false",
				"custom-image":                 "reg0/path/to/img0:tag0",
				"image-tag":                    "tag1",
				"home-name":                    "myhome0",
				"home-class":                   "class0",
				"home-mode":                    "mode0",
				"home-size":                    "1024", // Also tests auto-Gi suffix.
				"vol-exclude-defaults":         "true",
				"vol-exclude":                  "vol0,vol1",
				"vols":                         "vol1:/path1,vol2:/path2",
				"docker":                       "true",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					NodeType: "cpu0",
					GPU:      func() *int32 { i := int32(3); return &i }(),
					CPU:      quanT(t, "11"),
					RAM:      quanT(t, "23G"),
					Disk:     quanT(t, "29Gi"),
					NodeName: "node0",
					PoolType: "pool0",
					Tolerations: []corev1.Toleration{
						{Key: "key0", Operator: "Exists", Effect: "NoSchedule"},
						{Key: "key1", Operator: "Equal", Value: "val1", Effect: "NoSchedule"},
						{Key: "key2", Operator: "Exists", Effect: "PreferNoSchedule"},
						{Key: "key3", Operator: "Equal", Value: "val3", Effect: "PreferNoSchedule"},
					},
					IgnoreLimits:     true,
					IgnoreValidation: true,
				},
				Cxn: Cxn{
					ExposedPorts: []ExposedPort{
						{"", 8080, 0},
						{"", 90, 9090},
						{"test0", 7777, 0},
					},
					SSHStdPort:                true,
					SSHDisableCentralizedKeys: true,
					EternalTerminal:           true,
					MoshStart:                 100,
					MoshCount:                 10,
					PublicIP_p:                func() *bool { b := false; return &b }(),
				},
				Image: Image{
					Registry: "reg0",
					Path:     "path/to/img0",
					Tag:      "tag1",
				},
				Home: Home{
					CustomName: "myhome0",
					Class:      "class0",
					Mode:       "mode0",
					Size_p:     quanT(t, "1Ti"),
				},
				Volumes: Volumes{
					ExcludeClusterDefaults: true,
					Exclude:                []string{"vol0", "vol1"},
					VolMounts: clusters.VolMounts{
						{
							Name:   "vol1",
							Mounts: []corev1.VolumeMount{{MountPath: "/path1"}},
						},
						{
							Name:   "vol2",
							Mounts: []corev1.VolumeMount{{MountPath: "/path2"}},
						},
					},
				},
				DockerMode: true,
			},
		},
		"ignore-default-home-size": {
			inFlags: map[string]string{
				"home-size": "256",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources:   Resources{},
				Cxn:         Cxn{},
				Image:       Image{},
				Home:        Home{},
				Volumes:     Volumes{},
			},
		},
		"cpu-parse-err": {
			inFlags: map[string]string{
				"cpu-count": "_bad_",
			},
			wantErr: "--cpu-count=_bad_: quantities must match",
		},
		"ram-parse-err": {
			inFlags: map[string]string{
				"ram": "_bad_",
			},
			wantErr: "--ram=_bad_: quantities must match",
		},
		"disk-parse-err": {
			inFlags: map[string]string{
				"disk": "_bad_",
			},
			wantErr: "--disk=_bad_: quantities must match",
		},
		"exposed-port-err": {
			inFlags: map[string]string{
				"exposed-ports": "bad:bad:bad",
			},
			wantErr: "--exposed-ports=bad:bad:bad",
		},
		"home-size-parse-err": {
			inFlags: map[string]string{
				"home-size": "_bad_",
			},
			wantErr: "--home-size=_bad_: quantities must match",
		},
		"vols-parse-err": {
			inFlags: map[string]string{
				"vols": "good0:path0,_bad_,good1:path1",
			},
			wantErr: `--vols=_bad_: volmount "_bad_" must be in the format`,
		},

		/// Tests from orig spec.

		"simple-orig/no-flags": {
			origSpec: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			inFlags:   map[string]string{},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources:   Resources{},
				Cxn:         Cxn{},
				Image:       Image{},
				Home:        Home{},
				Volumes:     Volumes{},
			},
		},
		"orig/cluster-name-mismatch": {
			origSpec: &DevPod{
				ClusterName: "cluster1",
				UserName:    "user0",
				DevPodName:  "name0",
			},
			inFlags:   map[string]string{},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "cluster_name mismatch",
		},
		"orig/user-name-mismatch": {
			origSpec: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user1",
				DevPodName:  "name0",
			},
			inFlags:   map[string]string{},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "user_name mismatch",
		},
		"orig/devpod-name-mismatch": {
			origSpec: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name1",
			},
			inFlags:   map[string]string{},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			wantErr:   "devpod_name mismatch",
		},
		"full-orig/no-flags": {
			origSpec: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					NodeType: "cpu0",
					GPU:      func() *int32 { i := int32(3); return &i }(),
					CPU:      quanT(t, "11"),
					RAM:      quanT(t, "23G"),
					Disk:     quanT(t, "29Gi"),
					NodeName: "node0",
					PoolType: "pool0",
					Tolerations: []corev1.Toleration{
						{Key: "key0", Operator: "Exists", Effect: "NoSchedule"},
						{Key: "key1", Operator: "Equal", Value: "val1", Effect: "NoSchedule"},
						{Key: "key2", Operator: "Exists", Effect: "PreferNoSchedule"},
						{Key: "key3", Operator: "Equal", Value: "val3", Effect: "PreferNoSchedule"},
					},
					IgnoreLimits:     true,
					IgnoreValidation: true,
				},
				Cxn: Cxn{
					ExposedPorts: []ExposedPort{
						{"", 8080, 0},
						{"", 90, 9090},
						{"test0", 7777, 0},
					},
					SSHStdPort:                true,
					SSHDisableCentralizedKeys: true,
					EternalTerminal:           true,
					MoshStart:                 100,
					MoshCount:                 10,
					PublicIP_p:                func() *bool { b := false; return &b }(),
				},
				Image: Image{
					Registry: "reg0",
					Path:     "path/to/img0",
					Tag:      "tag1",
				},
				Home: Home{
					CustomName: "myhome0",
					Class:      "class0",
					Mode:       "mode0",
					Size_p:     quanT(t, "1Ti"),
				},
				Volumes: Volumes{
					ExcludeClusterDefaults: true,
					Exclude:                []string{"vol0", "vol1"},
					VolMounts: clusters.VolMounts{
						{
							Name:   "vol1",
							Mounts: []corev1.VolumeMount{{MountPath: "/path1"}},
						},
						{
							Name:   "vol2",
							Mounts: []corev1.VolumeMount{{MountPath: "/path2"}},
						},
					},
				},
				DockerMode: true,
			},
			inFlags:   map[string]string{},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					NodeType: "cpu0",
					GPU:      func() *int32 { i := int32(3); return &i }(),
					CPU:      quanT(t, "11"),
					RAM:      quanT(t, "23G"),
					Disk:     quanT(t, "29Gi"),
					NodeName: "node0",
					PoolType: "pool0",
					Tolerations: []corev1.Toleration{
						{Key: "key0", Operator: "Exists", Effect: "NoSchedule"},
						{Key: "key1", Operator: "Equal", Value: "val1", Effect: "NoSchedule"},
						{Key: "key2", Operator: "Exists", Effect: "PreferNoSchedule"},
						{Key: "key3", Operator: "Equal", Value: "val3", Effect: "PreferNoSchedule"},
					},
					IgnoreLimits:     true,
					IgnoreValidation: true,
				},
				Cxn: Cxn{
					ExposedPorts: []ExposedPort{
						{"", 8080, 0},
						{"", 90, 9090},
						{"test0", 7777, 0},
					},
					SSHStdPort:                true,
					SSHDisableCentralizedKeys: true,
					EternalTerminal:           true,
					MoshStart:                 100,
					MoshCount:                 10,
					PublicIP_p:                func() *bool { b := false; return &b }(),
				},
				Image: Image{
					Registry: "reg0",
					Path:     "path/to/img0",
					Tag:      "tag1",
				},
				Home: Home{
					CustomName: "myhome0",
					Class:      "class0",
					Mode:       "mode0",
					Size_p:     quanT(t, "1Ti"),
				},
				Volumes: Volumes{
					ExcludeClusterDefaults: true,
					Exclude:                []string{"vol0", "vol1"},
					VolMounts: clusters.VolMounts{
						{
							Name:   "vol1",
							Mounts: []corev1.VolumeMount{{MountPath: "/path1"}},
						},
						{
							Name:   "vol2",
							Mounts: []corev1.VolumeMount{{MountPath: "/path2"}},
						},
					},
				},
				DockerMode: true,
			},
		},
		"full-orig/cleared-flags": {
			origSpec: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					NodeType: "cpu0",
					GPU:      func() *int32 { i := int32(3); return &i }(),
					CPU:      quanT(t, "11"),
					RAM:      quanT(t, "23G"),
					Disk:     quanT(t, "29Gi"),
					NodeName: "node0",
					PoolType: "pool0",
					Tolerations: []corev1.Toleration{
						{Key: "key0", Operator: "Exists", Effect: "NoSchedule"},
						{Key: "key1", Operator: "Equal", Value: "val1", Effect: "NoSchedule"},
						{Key: "key2", Operator: "Exists", Effect: "PreferNoSchedule"},
						{Key: "key3", Operator: "Equal", Value: "val3", Effect: "PreferNoSchedule"},
					},
					IgnoreLimits:     true,
					IgnoreValidation: true,
				},
				Cxn: Cxn{
					ExposedPorts: []ExposedPort{
						{"", 8080, 0},
						{"", 90, 9090},
						{"test0", 7777, 0},
					},
					SSHStdPort:                true,
					SSHDisableCentralizedKeys: true,
					EternalTerminal:           true,
					MoshStart:                 100,
					MoshCount:                 10,
					PublicIP_p:                func() *bool { b := false; return &b }(),
				},
				Image: Image{
					Registry: "reg0",
					Path:     "path/to/img0",
					Tag:      "tag1",
				},
				Home: Home{
					CustomName: "myhome0",
					Class:      "class0",
					Mode:       "mode0",
					Size_p:     quanT(t, "1Ti"),
				},
				Volumes: Volumes{
					ExcludeClusterDefaults: true,
					Exclude:                []string{"vol0", "vol1"},
					VolMounts: clusters.VolMounts{
						{
							Name:   "vol1",
							Mounts: []corev1.VolumeMount{{MountPath: "/path1"}},
						},
						{
							Name:   "vol2",
							Mounts: []corev1.VolumeMount{{MountPath: "/path2"}},
						},
					},
				},
				DockerMode: true,
			},
			inFlags: map[string]string{
				"gpu-type":                     "",
				"gpu-count":                    "0",
				"cpu-count":                    "",
				"ram":                          "",
				"disk":                         "",
				"node-name":                    "",
				"pool-type":                    "",
				"tolerations":                  "",
				"ignore-limits":                "false",
				"ignore-validation":            "false",
				"exposed-ports":                "",
				"ssh-std-port":                 "false",
				"ssh-disable-centralized-keys": "false",
				"eternal-terminal":             "false",
				"mosh-start":                   "0",
				"mosh-count":                   "0",
				"public-ip":                    "true",
				"custom-image":                 "",
				"image-tag":                    "",
				"home-name":                    "",
				"home-class":                   "",
				"home-mode":                    "",
				"home-size":                    "",
				"vol-exclude-defaults":         "false",
				"vol-exclude":                  "",
				"vols":                         "",
				"docker":                       "false",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources:   Resources{},
				Cxn:         Cxn{},
				Image:       Image{},
				Home:        Home{},
				Volumes:     Volumes{},
			},
		},
		"append": {
			origSpec: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					Tolerations: []corev1.Toleration{
						{Key: "key0", Operator: "Exists", Effect: "NoSchedule"},
					},
				},
				Cxn: Cxn{
					ExposedPorts: []ExposedPort{
						{"", 8080, 0},
					},
				},
				Image: Image{},
				Home:  Home{},
				Volumes: Volumes{
					Exclude: []string{"vol0"},
					VolMounts: clusters.VolMounts{
						{
							Name:   "vol3",
							Mounts: []corev1.VolumeMount{{MountPath: "/path3"}},
						},
					},
				},
			},
			inFlags: map[string]string{
				"tolerations":   "+key1,key2",
				"exposed-ports": "+8081,p8082:8082:82",
				"vol-exclude":   "+vol1,vol2",
				"vols":          "+vol4:/path4,vol5:/path5",
			},
			inCluster: "cluster0",
			inUser:    "user0",
			inName:    "name0",
			want: &DevPod{
				ClusterName: "cluster0",
				UserName:    "user0",
				DevPodName:  "name0",
				Resources: Resources{
					Tolerations: []corev1.Toleration{
						{Key: "key0", Operator: "Exists", Effect: "NoSchedule"},
						{Key: "key1", Operator: "Exists", Effect: "NoSchedule"},
						{Key: "key2", Operator: "Exists", Effect: "NoSchedule"},
					},
				},
				Cxn: Cxn{
					ExposedPorts: []ExposedPort{
						{"", 8080, 0},
						{"", 8081, 0},
						{"p8082", 8082, 82},
					},
				},
				Image: Image{},
				Home:  Home{},
				Volumes: Volumes{
					Exclude: []string{"vol0", "vol1", "vol2"},
					VolMounts: clusters.VolMounts{
						{
							Name:   "vol3",
							Mounts: []corev1.VolumeMount{{MountPath: "/path3"}},
						},
						{
							Name:   "vol4",
							Mounts: []corev1.VolumeMount{{MountPath: "/path4"}},
						},
						{
							Name:   "vol5",
							Mounts: []corev1.VolumeMount{{MountPath: "/path5"}},
						},
					},
				},
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			flags := pflag.NewFlagSet("test0", pflag.ContinueOnError)
			fn := FromFlags(flags)
			for k, v := range tc.inFlags {
				if err := flags.Set(k, v); err != nil {
					t.Fatalf("flags.Set(%s, %s): %v.", k, v, err)
				}
			}
			if err := flags.Parse(nil); err != nil {
				t.Fatalf("flags.Parse(): %v.", err)
			}
			got, gotErr := fn(tc.inCluster, tc.inUser, tc.inName, tc.origSpec)
			if tc.wantErr == "" && gotErr != nil {
				t.Errorf("got error %v, want no error.", gotErr)
			}
			if tc.wantErr != "" && (gotErr == nil || !strings.Contains(gotErr.Error(), tc.wantErr)) {
				t.Errorf("got error %v, want error containing '%v'.", gotErr, tc.wantErr)
			}
			if diff := cmp.Diff(tc.want, got.DeepCopy(), cmpopts.EquateEmpty()); diff != "" {
				t.Errorf("FromFlags(): -want +got:\n%s", diff)
			}
		})
	}
}
