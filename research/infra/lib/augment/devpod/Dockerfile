# BASE_IMG is the augment_cpu (ubuntu) or augment_gpu (detai -> nvidia -> ubuntu)
ARG BASE_IMG
FROM $BASE_IMG AS layer0

USER root
WORKDIR /

RUN add-apt-repository ppa:jgmath2000/et  # for `et` below

# https://cloud.google.com/sdk/docs/install#deb
# For google-cloud-* below
# NOTE(mattm): This should be a noop on GPU images as it's already done on the nvidia or determined base image. We also only
# want google-cloud-* packages from this repo, but it also at least is known to provide kubectl.
RUN curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo gpg --dearmor > /usr/share/keyrings/cloud.google.gpg \
 && printf "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main\n" > /etc/apt/sources.list.d/google-cloud-sdk.list \
 && printf "Package: google-cloud-*\n"  >  /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin: release o=cloud-sdk\n" >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin-Priority: 500\n"        >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "\n"                         >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Package: kubectl\n"         >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin: release o=cloud-sdk\n" >> /etc/apt/preferences.d/99-augment-google-cloud \
 && printf "Pin-Priority: -1\n"         >> /etc/apt/preferences.d/99-augment-google-cloud

# For helm, below.
RUN curl -s https://baltocdn.com/helm/signing.asc | gpg --dearmor > /usr/share/keyrings/helm.gpg \
 && printf "deb [arch=%s signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main\n" "$(dpkg --print-architecture)" > /etc/apt/sources.list.d/helm-stable-debian.list

# Update for latest PostgreSQL client. Our servers are at v17, stock ubuntu client at v14, and are incompatible without client >= server.
# https://wiki.postgresql.org/wiki/Apt
RUN curl -s https://www.postgresql.org/media/keys/ACCC4CF8.asc | sudo gpg --dearmor > /usr/share/keyrings/pgdg.gpg \
 && printf "deb [signed-by=/usr/share/keyrings/pgdg.gpg] https://apt.postgresql.org/pub/repos/apt %s-pgdg main\n" "$(lsb_release -sc)" > /etc/apt/sources.list.d/pgdg.list

RUN --mount=type=cache,target=/var/cache/apt,sharing=locked apt-get update \
 && yes | unminimize \
 && apt-get dist-upgrade -y \
 && apt-get install -y \
      bash-completion \
      bind9-dnsutils \
      docker \
      docker-buildx \
      dumb-init \
      et \
      google-cloud-cli \
      google-cloud-cli-gke-gcloud-auth-plugin \
      git \
      helm \
      man-db \
      mosh \
      postgresql-client \
      rsync \
      ssh \
      sudo \
      tmux \
      tree \
      # items from dev_vm/01_apt.sh \
      unzip \
      zip \
      gcc \
      g++ \
      libasound2 \
      libatk1.0-0 \
      libatspi2.0-0 \
      libatk-bridge2.0-0 \
      libcairo2 \
      libgtk-3-0 \
      libgbm1 \
      libpango-1.0-0 \
      libxcomposite1 \
      libxdamage1 \
      libxkbcommon0 \
      libxrandr2 \
      libssl-dev \
      libtinfo5 \
      libhwloc-dev \
      libssl-dev \
      libstdc++6-12-dbg \
      pkg-config \
      xvfb \
      tzdata \
 && apt-get autoremove -y --purge \
 && apt-get autoclean

### Vim 9.x
FROM layer0 AS vim9
RUN --mount=type=cache,target=/root/dl :\
 && apt-get update \
 && DEBIAN_FRONTEND=noninteractive apt-get install -y \
      libpython3-dev \
      libgpm-dev \
      libsodium-dev \
 && VIM_VERSION='9.1.1199' \
 && VIM_SHA256='fc71b4cd30e55cd02c3f4147ea9c678e53fefc3f016eab368881bada72d18d4b' \
 && VIM_URL="https://github.com/vim/vim/archive/refs/tags/v${VIM_VERSION}.tar.gz" \
 && VIM_TGZ="vim-v${VIM_VERSION}.tar.gz" \
 && VIM_DL="/root/dl/${VIM_TGZ}" \
 && test -e "$VIM_DL" || curl -fsSL "$VIM_URL" -o "$VIM_DL" \
 && printf "%s %s\n" "$VIM_SHA256" "$VIM_DL" | sha256sum -c \
 && mkdir -p /tmp/vim9 \
 && tar -xz --no-same-owner -C /tmp/vim9 --strip-components=1 -f "$VIM_DL" \
 && cd /tmp/vim9/src/ \
 && make \
      CONF_OPT_PYTHON3='--enable-python3interp --with-python3-command=/usr/bin/python3'

FROM layer0
RUN --mount=type=bind,from=vim9,source=/tmp/vim9,target=/tmp/vim9,rw : \
 && cd /tmp/vim9/src/ \
 && make install

RUN curl -sL "$(curl -s https://api.github.com/repos/bitnami-labs/sealed-secrets/releases | jq -r 'last(sort_by(.published_at) | .[] | select(.draft or .prerelease|not) | select(.name | match("^sealed-secrets"))) | .assets[] | select(.name | match("^kubeseal-.*-linux-amd64.tar.gz$")) | .browser_download_url')" \
  | tar -xzO kubeseal \
  | install -oroot -groot -m0755 -D /dev/stdin /usr/local/bin/kubeseal

# Install Node and pnpm for extension development
# https://www.notion.so/Augment-VSCode-Extension-238053cf1149449e9004e29dcd44e33e?pvs=4#62afda0b78d04240911cdf75bd336e15
# https://github.com/nodesource/distributions?tab=readme-ov-file#using-ubuntu-1
# NOTE(mattm): The dai_environments base GPU image adds config to /etc/bash.bashrc which masks our version, so remove it.
RUN --mount=type=cache,target=/var/cache/apt,sharing=locked : \
 && sed '\#^source /usr/local/nvm/nvm.sh#d' -i /etc/bash.bashrc \
 && curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
 && apt-get install -y nodejs \
 && npm install -g pnpm@latest-9

# Install `augi`
COPY --chmod=755 augi.build /usr/local/bin/augi.live
RUN /usr/local/bin/augi.live release select live

# Install jsonnet toolchain
ARG GOBIN=/usr/local/bin _JSONNET_VER=latest _JB_VER=latest
RUN \
 --mount=type=cache,target=/root/.cache/go-build \
 --mount=type=cache,target=/root/go \
 : \
 && go install github.com/google/go-jsonnet/cmd/jsonnet@"$_JSONNET_VER" \
 && go install github.com/google/go-jsonnet/cmd/jsonnetfmt@"$_JSONNET_VER" \
 && go install github.com/google/go-jsonnet/cmd/jsonnet-lint@"$_JSONNET_VER" \
 && go install github.com/jsonnet-bundler/jsonnet-bundler/cmd/jb@"$_JB_VER"

# Install `kubecfg`
ARG _KUBECFG_VER=latest
RUN \
 --mount=type=cache,target=/root/.cache/go-build \
 --mount=type=cache,target=/root/go \
 go install github.com/kubecfg/kubecfg@"$_KUBECFG_VER"

# Install updated `protoc` (from dev_vm/03_protoc.sh). The ubuntu version is 3.x
RUN --mount=type=cache,target=/var/cache : \
 && PROTOC_RELEASE=v25.0-rc2 \
 && PROTOC_VERSION=25.0-rc-2 \
 && PROTOC_SHA256SUM=d64fc42971396bcd8b68bc6b3272bdf2b62c085906d950404ed05f49b9e6ae93 \
 && PROTOC_ZIP=protoc-"$PROTOC_VERSION"-linux-x86_64.zip \
 && curl -sL -C - https://github.com/protocolbuffers/protobuf/releases/download/"$PROTOC_RELEASE/$PROTOC_ZIP" -o /var/cache/"$PROTOC_ZIP" \
 && printf "%s %s" "$PROTOC_SHA256SUM" "/var/cache/$PROTOC_ZIP" | sha256sum -c \
 && unzip -o /var/cache/"$PROTOC_ZIP" -d /usr/local -x readme.txt

# Install rustup system-wide (from dev_vm/15_rust.sh, but done a bit differently)
# This is inspired by the files available in the ubuntu:24.04 rustup package AND by looking
# at the Archlinux rustup PKGBUILD. When we update to ubuntu:24.04 we can switch to the upstream apt install.
# rustup-init installs all of the rust components (like rustc) as copies of itself; instead we can install just one `rustct` and
# install the other components as symlinks which is much clearer.
RUN --mount=type=cache,target=/var/cache : \
 && curl -sL -C - https://static.rust-lang.org/rustup/dist/x86_64-unknown-linux-gnu/rustup-init -o /var/cache/rustup-init \
 && install -oroot -groot -m0755 -D /var/cache/rustup-init /usr/local/bin/ \
 && RUSTUP_HOME=/tmp/rustup/rustup CARGO_HOME=/tmp/rustup/cargo /usr/local/bin/rustup-init -v --no-update-default-toolchain --no-modify-path -y \
 && install -oroot -groot -m0755 -D /tmp/rustup/cargo/bin/rustup /usr/local/bin \
 && (cd /tmp/rustup/cargo/bin && for f in $(ls -1 * | grep -v '^rustup'); do ln -sf rustup "/usr/local/bin/$f"; done) \
 && printf 'export PATH="$HOME/.cargo/bin:$PATH"\n' > /etc/profile.d/90-cargo.sh

# Install click (from dev_vm/20_click.sh).
RUN --mount=type=cache,target=/mnt/cache/rust : \
 && mkdir -p /mnt/cache/rust/cargo /mnt/cache/rust/rustup \
 && export RUSTUP_HOME=/mnt/cache/rust/rustup \
 && export CARGO_HOME=/mnt/cache/rust/cargo \
 && rustup toolchain list \
 && rustup default stable \
 && rustup update --no-self-update \
 && cargo install click --bin=click --root="/usr/local/"

# gcloud-login helper/wrapper
COPY --chown=root:root --chmod=0755 gcloud-login /usr/local/bin/

# Bake the init script in and use as the default entrypoint. However, note that
# the Devpod ignores this and pulls in the init script again (via ConfigMap); mainly
# so that it works with vanilla images, but also allows on-they-fly adjustments.
COPY --chmod=0755 init.sh /usr/local/bin/init.sh
ENTRYPOINT ["/usr/bin/dumb-init", "--version", "--", "/usr/local/bin/init.sh"]
