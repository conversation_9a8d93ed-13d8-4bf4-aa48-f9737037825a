from typing import Optional, Sequence

from base.diff_utils.changes import Modified, T, Changed
from base.diff_utils.diff_utils import File
from base.prompt_format_autofix.common import PromptChunkWithLines
from pydantic.dataclasses import dataclass, rebuild_dataclass, ConfigDict  # type: ignore

from research.core.types import Document
from research.retrieval.types import DocumentId
from research.utils.repo_change_utils import RepoChange, PyrMap, FileTuple
from pathlib import Path
from pyrsistent import PVector as PyrVector

context_retriever_config = {
    "scorer": {
        "name": "dense_scorer_v2_ffwd",
        "checkpoint_path": "/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468",
        "cache_dir": "/tmp/augment/chatanol_cache",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 2000,
    },
    "query_formatter": {
        "name": "base:chatanol6-singleturnisspecial",
        "tokenizer_name": "rogue",
        "max_tokens": 1024,
    },
    "document_formatter": {
        "name": "base:chatanol6-embedding-with-path-key",
        "tokenizer_name": "rogue",
    },
}

localization_config = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/autofix-location/v17",
        "cache_dir": "/tmp/augment/localization_cache",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 2000,
    },
    "query_formatter": {
        "name": "next_edit_location_query",
        "tokenizer": "starcoder",
        "max_prompt_tokens": 8192,
        "max_diff_tokens": 4096,
        "max_instruction_tokens": 0,
        "max_command_output_tokens": 4096,
        "use_smart_header": True,
        "deduplicate_identical_paths": True,
        "truncate_instructions_tail": False,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer": "starcoder",
        "max_tokens": 999,
    },
}


@dataclass
class AutofixPredictEditLocationsInput:
    command_output: str
    recent_changes: Sequence[Modified[File]]
    top_k: int = 64
    doc_ids: Optional[list[DocumentId]] = None
    new_docs: Optional[list[Document]] = None
    instructions: Optional[str] = None


# Workaround for handling generic types
rebuild_dataclass(AutofixPredictEditLocationsInput)  # type: ignore


@dataclass(config=ConfigDict(arbitrary_types_allowed=True))
class AutofixContextRetrieveInput:
    command: str
    command_output: str
    edit_locations: Sequence[PromptChunkWithLines]
    recent_changes: RepoChange
    top_k: int = 32
    doc_ids: Optional[list[DocumentId]] = None
    new_docs: Optional[list[Document]] = None


# Workaround for handling generic types
rebuild_dataclass(AutofixContextRetrieveInput)  # type: ignore


@dataclass
class AutofixContextOutput:
    generated_query: str
    retrieved_chunks: Sequence[PromptChunkWithLines]


rebuild_dataclass(AutofixContextOutput)  # type: ignore
