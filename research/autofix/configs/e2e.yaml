
system:
  name: autofix
  use_gold_locations: False
  use_localization: True
  top_k_edit_locations: 64
  context_retrieval: True
  max_concurrent: 10

task:
  name: autofix
  dataset_path: /mnt/efs/augment/user/colin/autofix_e2e_eval/datasets/v17.pkl.gz
  limit: 1000

podspec: 1xH100-gcp.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: eval-e2e-v17-localize-v17-with-context
  project: autofix
  workspace: Dev
