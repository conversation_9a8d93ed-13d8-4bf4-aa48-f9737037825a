import re
import torch
from transformers import AutoModelForCausal<PERSON>, AutoTokenizer
from research.utils.repo_change_utils import <PERSON><PERSON><PERSON><PERSON><PERSON>, patchset_from_repo_change

torch.cuda.empty_cache()
torch.random.manual_seed(0)


class LogExtractor:
    PROMPT_TEMPLATE = """<|system|>You are an expert AI software engineer, capable of responding with only 'Yes' or 'No'<|end|><|user|>
Here are my recent changes:
```
{recent_changes}
```
Here is a excerpt from a CI build log:
```
{log}
```
Are there any errors or warnings related to my recent changes in this log excerpt?<|end|><|assistant|>"""

    def __init__(self, max_log_tokens: int = 4096, max_diff_tokens: int = 4096):
        # Load model and tokenizer
        self._model = AutoModelForCausalLM.from_pretrained(
            "/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-32B-Instruct",
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True,
            attn_implementation="flash_attention_2",
        )
        self._tokenizer = AutoTokenizer.from_pretrained(
            "/mnt/efs/augment/checkpoints/qwen25-coder/Qwen2.5-Coder-32B-Instruct",
            trust_remote_code=True,
        )
        self._tokenizer.truncation_side = "left"
        self._max_log_tokens = max_log_tokens
        self._max_diff_tokens = max_diff_tokens

    def _chunk_log(self, log: str) -> list[str]:
        # Remove timestamps as they are very token expensive
        datetime_regex = r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z"
        log = re.sub(datetime_regex, "", log)

        # Remove all non ascii characters
        log = log.encode("ascii", "ignore").decode("ascii")

        # Remove ANSI escape sequences
        ansi_escape_regex = r"\x1B[@-_][0-?]*[ -/]*[@-~]"
        log = re.sub(ansi_escape_regex, "", log)

        # Split log into chunks by prepending '[command]'
        logs = re.split(r"(?=\[command\].*)", log)

        # Drop [command], ##[group], ##[endgroup]
        substrs_to_drop = [
            "##[group]",
            "##[endgroup]",
            "[command]",
            "##[notice]",
            "##[error]",
            "##[warning]",
        ]
        for substr in substrs_to_drop:
            logs = [log.replace(substr, "") for log in logs]

        return logs

    def _predict_batch(
        self, breaking_change: RepoChange, log_excerpts: list[str], batch_size=8
    ) -> list[float]:
        patchset = patchset_from_repo_change(
            breaking_change, num_context_lines=3, ignore_whitespace=True
        )
        breaking_diff = str(patchset)
        breaking_diff = self._tokenizer.decode(
            self._tokenizer.encode(
                breaking_diff, truncation=True, max_length=self._max_diff_tokens
            )
        )

        all_probs = []
        for i in range(0, len(log_excerpts), batch_size):
            batch = log_excerpts[i : i + batch_size]
            # truncate the log excerpt to the max length
            batch = [
                self._tokenizer.decode(
                    self._tokenizer.encode(
                        excerpt, truncation=True, max_length=self._max_log_tokens
                    )
                )
                for excerpt in batch
            ]

            prompts = [
                self.PROMPT_TEMPLATE.format(recent_changes=breaking_diff, log=excerpt)
                for excerpt in batch
            ]

            inputs = self._tokenizer(
                prompts,
                return_tensors="pt",
                padding=True,
            )
            input_ids = inputs.input_ids.cuda()
            attention_mask = inputs.attention_mask.cuda()

            with torch.no_grad():
                logits = (
                    self._model(input_ids, attention_mask=attention_mask)
                    .logits[:, -1, :]
                    .cpu()
                )

            probs = torch.softmax(logits, dim=-1)

            yes_token_id = self._tokenizer.encode("Yes")[0]
            no_token_id = self._tokenizer.encode("No")[0]

            true_probs = probs[:, yes_token_id]
            false_probs = probs[:, no_token_id]

            batch_probs = true_probs / (true_probs + false_probs)
            batch_probs = torch.where(
                (true_probs == 0) & (false_probs == 0), 0.5, batch_probs
            )

            all_probs.extend(batch_probs.tolist())

            # Explicitly free memory after each batch
            del (
                input_ids,
                attention_mask,
                logits,
                probs,
                true_probs,
                false_probs,
                batch_probs,
            )
            torch.cuda.empty_cache()

        return all_probs

    def score(
        self, log_content: str, breaking_change: RepoChange, batch_size=8
    ) -> list[tuple[float, str]]:
        log_chunks = self._chunk_log(log_content)

        probabilities = self._predict_batch(breaking_change, log_chunks, batch_size)
        scored_chunks = list(zip(probabilities, log_chunks))
        return sorted(scored_chunks, key=lambda x: x[0], reverse=True)

    def extract(
        self, log_content: str, breaking_change: RepoChange, batch_size=1
    ) -> str:
        scored_chunks = self.score(log_content, breaking_change, batch_size)
        return scored_chunks[0][1]
