from asyncio import CancelledError
import asyncio
import os
from pathlib import Path
import random
from anthropic.types import TextBlock
from anthropic import (
    APIConnectionError,
    AsyncAnthropic,
    InternalServerError,
    RateLimitError,
)
from unidiff import PatchSet
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from research.utils.repo_change_utils import (
    RepoChange,
    get_after,
    patchset_from_repo_change,
)


async def neural_fix_file_selection(
    log: str,
    fixing_change: RepoChange,
    log_tokens: int = 5000,
    diff_tokens: int = 50000,
) -> RepoChange:
    token_counter = ClaudeTokenCounter()
    log_fmt = token_counter.truncate_to_budget(log, log_tokens, reversed=True)
    patch: PatchSet = patchset_from_repo_change(fixing_change, num_context_lines=3)
    file_paths: set[Path] = {Path(file.path) for file in patch}
    if len(file_paths) == 1:
        # Only one file was changed, so no need to filter
        return fixing_change
    diff: str = token_counter.truncate_to_budget(str(patch), diff_tokens, reversed=True)
    prompt = f"""
    Here is a CI log containing an error:
    ```
    {log_fmt}
    ```
    Here is the diff that was applied to fix the error:
    ```
    {diff}
    ```

    Please specify which files are relevant to fixing the error, at least one.
    Respond only the relevant file paths, one per line.
    For example:
    src/foo.py
    src/bar.py
    src/baz.py
    """
    client = AsyncAnthropic(timeout=60, max_retries=5)
    while True:
        try:
            response = await client.messages.create(
                model="claude-3-5-sonnet-latest",
                max_tokens=1024,
                temperature=0,
                system="You are an expert AI software engineer.",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt,
                            }
                        ],
                    }
                ],
            )
            if len(response.content) == 0 or not isinstance(
                response.content[0], TextBlock
            ):
                # Could not get a response
                return fixing_change
            relevant_files: set[Path] = {
                Path(file.strip())
                for file in response.content[0].text.splitlines()
                if file.strip()
            }
            relevant_files = relevant_files.intersection(file_paths)
            if len(relevant_files) == 0:
                # No relevant files were found
                return fixing_change
            for change in fixing_change.changed_files:
                change_path = change.map(lambda x: x.path)
                if get_after(change_path) not in relevant_files:
                    # Drop this change since it is not relevant to the fix
                    fixing_change.drop_change(change_path)
            return fixing_change
        except CancelledError:
            # This is expected to happen when the request is cancelled
            continue
        except (APIConnectionError, InternalServerError, RateLimitError):
            await asyncio.sleep(10 * random.uniform(0.8, 1.2))
            continue
