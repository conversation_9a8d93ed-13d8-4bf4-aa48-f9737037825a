import aiohttp
import json
from dataclasses import asdict
from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File
from base.prompt_format_autofix.contains_errors_prompt_formatter import (
    AutofixContainErrorsOutput,
)
from base.prompt_format_autofix.check_command_prompt_formatter import (
    AutofixCheckCommandOutput,
)
from base.prompt_format_autofix.create_fix_plan_prompt_formatter import (
    ApplyFileFixInput,
    AutofixCreateFixPlanInput,
)
from base.prompt_format_autofix.check_command_prompt_formatter import (
    AutofixCheckCommandInput,
)
from base.prompt_format_autofix.contains_errors_prompt_formatter import (
    AutofixContainErrorsInput,
)
from base.prompt_format_autofix.create_fix_plan_prompt_formatter import (
    AutofixCreateFixPlanOutput,
)


class AutofixClient:
    """
    A client for interacting with the Autofix service via async HTTP requests.

    Attributes:
        base_url (str): The base URL of the Autofix service.
        _session (aiohttp.ClientSession): The session object for making HTTP requests.
    """

    def __init__(self, base_url: str):
        """
        Initialize the AutofixClient with a base URL.

        Args:
            base_url (str): The base URL for the Autofix API.
        """
        self.base_url = base_url

    async def check_command(
        self, input: AutofixCheckCommandInput
    ) -> AutofixCheckCommandOutput:
        """
        Check if a command is code-related.

        Args:
            command (str): The command to check.
            output (str): The output from the command.

        Returns:
            CheckCommand: The result of the check.
        """
        async with aiohttp.ClientSession() as client:
            async with client.post(
                f"{self.base_url}/check_command", json=asdict(input)
            ) as resp:
                response = await resp.json()
        return AutofixCheckCommandOutput(**response)

    async def contain_errors(
        self, input: AutofixContainErrorsInput
    ) -> AutofixContainErrorsOutput:
        """
        Check if a command output contains errors.

        Args:
            command (str): The command to check.
            output (str): The output from the command.

        Returns:
            ContainErrors: The result indicating if errors were found.
        """
        async with aiohttp.ClientSession() as client:
            async with client.post(
                f"{self.base_url}/contain_errors", json=asdict(input)
            ) as resp:
                response = await resp.json()
        return AutofixContainErrorsOutput(**response)

    async def create_fix_plan(
        self, input: AutofixCreateFixPlanInput
    ) -> AutofixCreateFixPlanOutput:
        """
        Create a fix plan based on the root cause, git diff, and other context.

        Args:
            git_diff (str): The git diff to apply fixes to.
            command (str): The command that was executed.
            output (str): The output from the command.
            edit_locations (list[FileChunk]): Locations in the code that need to be edited.

        Returns:
            FixPlan: The generated fix plan.
        """
        async with aiohttp.ClientSession() as client:
            async with client.post(
                f"{self.base_url}/create_fix_plan", json=asdict(input)
            ) as resp:
                response = await resp.json()
        return AutofixCreateFixPlanOutput(**json.loads(response))

    async def apply_file_fix(self, input: ApplyFileFixInput) -> Modified[File]:
        """
        Apply a fix to a file.

        Args:
            file_fix (FileFix): The file fix to apply.
            source_content (str): The source content of the file.

        Returns:
            FilePatch: The generated file patch.
        """
        async with aiohttp.ClientSession() as client:
            async with client.post(
                f"{self.base_url}/apply_file_fix", json=asdict(input)
            ) as resp:
                response = await resp.json()
        return Modified[File](
            before=File(
                path=response["before"]["path"], contents=response["before"]["contents"]
            ),
            after=File(
                path=response["after"]["path"], contents=response["after"]["contents"]
            ),
        )
