import asyncio
from collections import defaultdict
import hashlib
from typing import Sequence
import numpy as np
from base.diff_utils.diff_utils import File
from base.prompt_format_autofix.common import (
    PromptChunkWithLines,
    AutofixSteeringMessage,
)
from base.prompt_format_autofix.create_fix_plan_prompt_formatter import (
    ApplyFileFixInput,
    AutofixFileFix,
    AutofixCreateFixPlanInput,
    AutofixCreateFixPlanOutput,
)
from research.autofix.autofix_client import AutofixClient
from research.autofix.autofix_manager import AutofixManager
from research.core.types import Document
from research.utils.repo_change_utils import (
    FileTuple,
    Modified,
    RepoChange,
    patchset_from_repo_change,
)
from base.ranges.range_types import LineRange
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

DEFAULT_PATH: str = "/mnt/efs/spark-data/user/colin/bugfix_localization_model/v14/stage5_with_filtered_logs_fixed3/"


def repo_change_to_docs(breaking_change: Repo<PERSON>hange) -> list[Document]:
    return [
        Document(
            text=contents,
            id=hashlib.sha256((str(path) + contents).encode("utf-8")).hexdigest(),
            path=str(path),
            meta={},
        )
        for path, contents in breaking_change.after_files.items()
        if len(contents) < 100 * 1024  # Filter out large files
    ]


def location_scores(
    edited_locations: Sequence[PromptChunkWithLines],
    gold_locations: Sequence[PromptChunkWithLines],
) -> tuple[np.float32, np.float32]:
    """
    Calculate overlap and noise scores between edited and gold locations.

    Overlap score: Fraction of gold locations that overlap with at least one edited location.
    Noise score: Fraction of edited locations that do not overlap with any gold location.
    """
    if not edited_locations or not gold_locations:
        return np.float32(0.0), np.float32(0.0)

    overlap_score = np.float32(0.0)
    for gold_loc in gold_locations:
        for edited_loc in edited_locations:
            if edited_loc.path == gold_loc.path:
                edit_range = LineRange(
                    edited_loc.line_offset,
                    edited_loc.line_offset + edited_loc.length_in_lines,
                )
                gold_range = LineRange(
                    gold_loc.line_offset,
                    gold_loc.line_offset + gold_loc.length_in_lines,
                )
                if edit_range.overlaps(gold_range):
                    overlap_score += 1
                    break

    overlap_score /= len(gold_locations)

    noise_score = np.float32(0.0)
    for edited_loc in edited_locations:
        has_overlap = False
        for gold_loc in gold_locations:
            if edited_loc.path == gold_loc.path:
                edit_range = LineRange(
                    edited_loc.line_offset,
                    edited_loc.line_offset + edited_loc.length_in_lines,
                )
                gold_range = LineRange(
                    gold_loc.line_offset,
                    gold_loc.line_offset + gold_loc.length_in_lines,
                )
                if edit_range.overlaps(gold_range):
                    has_overlap = True
                    break
        if not has_overlap:
            noise_score += 1
    noise_score /= len(edited_locations)

    return overlap_score, noise_score


def compute_scores(
    fixing_change: RepoChange, suggested_change: RepoChange
) -> tuple[np.float32, np.float32, np.float32, np.float32]:
    overlap_score, noise_score = location_scores(
        edited_locations=edit_locations_from_repo_change(suggested_change),
        gold_locations=edit_locations_from_repo_change(fixing_change),
    )

    suggested_paths: set[str] = {
        file.path
        for file in patchset_from_repo_change(suggested_change, num_context_lines=3)
    }
    fix_paths: set[str] = {
        file.path
        for file in patchset_from_repo_change(fixing_change, num_context_lines=3)
    }
    both_files: set[str] = fix_paths.union(suggested_paths)
    exact_match_per_file = np.zeros(len(both_files), dtype=np.float32)
    edit_distance_per_file = np.zeros(len(both_files), dtype=np.float32)
    for i, path in enumerate(both_files):
        suggested = suggested_change.after_files[Path(path)]
        target = fixing_change.after_files[Path(path)]
        exact_match_per_file[i] = float(suggested == target)
    return (
        exact_match_per_file.mean(),
        edit_distance_per_file.mean(),
        overlap_score,
        noise_score,
    )


def consolidate_ranges(ranges: Sequence[LineRange]):
    if not ranges:
        return []
    sorted_ranges = sorted(ranges, key=lambda r: r.start)
    consolidated = [sorted_ranges[0]]
    for current in sorted_ranges[1:]:
        if consolidated[-1].overlaps(current):
            consolidated[-1] = consolidated[-1].merge(current)
        else:
            consolidated.append(current)
    return consolidated


def merge_chunks(
    edit_locations: Sequence[PromptChunkWithLines],
    breaking_change: RepoChange,
) -> Sequence[PromptChunkWithLines]:
    loc_map = defaultdict(list[LineRange])
    for loc in edit_locations:
        loc_map[loc.path].append(
            LineRange(loc.line_offset, loc.line_offset + loc.length_in_lines)
        )

    for path, ranges in loc_map.items():
        loc_map[path] = consolidate_ranges(ranges)

    output = []
    for path, ranges in loc_map.items():
        content: str = breaking_change.after_files[Path(path)]
        content_lines = content.splitlines(keepends=True)
        for range in ranges:
            output.append(
                PromptChunkWithLines(
                    text="".join(content_lines[range.start : range.stop]),
                    path=path,
                    line_offset=range.start,
                    length_in_lines=range.stop - range.start,
                )
            )
    return output


def edit_locations_from_repo_change(
    fixing_change: RepoChange, num_context_lines: int = 10
) -> Sequence[PromptChunkWithLines]:
    locations = []
    for file in patchset_from_repo_change(
        fixing_change, num_context_lines=num_context_lines
    ):
        content = fixing_change.before_files[Path(file.path)]
        content_lines = content.splitlines(keepends=True)
        for hunk in file:
            start_line = hunk.source_start - 1
            end_line = hunk.source_start + hunk.source_length - 1
            text = "".join(content_lines[start_line:end_line])
            locations.append(
                PromptChunkWithLines(
                    text=text,
                    path=file.path,
                    line_offset=start_line,
                    length_in_lines=end_line - start_line,
                )
            )
    return locations


async def generate_fix_suggestion(
    client: AutofixClient | AutofixManager,
    edit_locations: Sequence[PromptChunkWithLines],
    breaking_change: RepoChange,
    command_output: str,
    command: str = "./check.sh",  # Placeholder for command given in the log.
    steering_history: Sequence[AutofixSteeringMessage] = [],
) -> tuple[RepoChange, AutofixCreateFixPlanOutput]:
    breaking_change_files = [
        Modified[File](
            before=File(path=str(file.before.path), contents=file.before.code),
            after=File(path=str(file.after.path), contents=file.after.code),
        )
        for file in breaking_change.changed_files
        if isinstance(file, Modified)
    ]

    fix_plan: AutofixCreateFixPlanOutput = await client.create_fix_plan(
        AutofixCreateFixPlanInput(
            breaking_change=breaking_change_files,
            command=command,
            command_output=command_output,
            edit_locations=edit_locations,
            steering_history=steering_history,
        )
    )

    async def generate_edit(change: AutofixFileFix) -> Modified[File]:
        content = breaking_change.after_files[Path(change.path)]
        return await client.apply_file_fix(
            ApplyFileFixInput(file_fix=change, source_content=content)
        )

    # Concurrently apply edits to all files.
    changed_files: list[Modified[File]] = []
    if fix_plan.fix_plan:
        changed_files = await asyncio.gather(
            *[generate_edit(change) for change in fix_plan.fix_plan.changes]
        )

    suggested_change = RepoChange.from_before_and_changes(
        breaking_change.after_files,
        (
            Modified[FileTuple](
                before=FileTuple(path=Path(file.after.path), code=file.before.contents),
                after=FileTuple(path=Path(file.after.path), code=file.after.contents),
            )
            for file in changed_files
            if file.before.contents != file.after.contents
        ),
    )
    return suggested_change, fix_plan
