"""Take hold-out data from localization data pipeline and format it for E2E eval."""

from glob import glob
import os
import pandas as pd
from tqdm import tqdm
from base.diff_utils.changes import Modified
from base.third_party_clients.token_counter.token_counter import (
    TokenizerBasedTokenCounter,
)
from base.tokenizers.llama3_tokenizer import Llama3InstructTokenizer
from research.autofix.autofix_eval_static import (
    repo_change_to_docs,
)
import compress_pickle

from research.eval.harness.systems.autofix_system import AutofixSystemInput
from research.next_edits.edit_localization_stages import EditLocalizationProblem
from research.utils.repo_change_utils import FileTuple
from google.cloud import storage


# Register tqdm with pandas
tqdm.pandas()

# The source path containing parquet files with DataFrame column "pickled_results" containing EditLocalizationProblem objects compressed pickled
INPUT_PATH: str = "/mnt/efs/spark-data/user/colin/bugfix_localization_model/v17/stage8_eval_format_as_tokens"

# The output path for the list of AutofixSystemInput objects compressed pickled output
OUTPUT_PATH: str = "gs://gcp-us1-user/colin/autofix_e2e_eval/datasets/v17.pkl.gz"


def translate_EditLocalizationProblem_to_AutofixSystemInput(
    problems: list[EditLocalizationProblem],
) -> list[AutofixSystemInput]:
    output_items = []

    for problem in tqdm(problems):
        doc_ids = frozenset(
            {doc.id for doc in repo_change_to_docs(problem.past_to_wip_repo_change)}
        )

        # NOTE: commit_meta is the fix commit, so we want the parent, which is the breaking commit.
        # We only take commits from the PR branch, so this is safe to assume len(parents) == 1
        breaking_commit = problem.commit_meta.parents[0]

        assert (
            problem.command_output is not None
        ), "For the autofix task, we require the command output to be provided."
        item = AutofixSystemInput(
            command_output=problem.command_output,  # The log output of the errors extracted from the CI build.
            breaking_change=problem.past_to_wip_repo_change,  # The breaking change that caused the error.
            doc_ids=doc_ids,  # The document ids that are available for retrieval.
            fixing_change=problem.wip_to_future_repo_change,  # The fixing change that fixes the error.
            repo_name=problem.commit_meta.repo_name,  # The name of the repository.
            breaking_commit_sha=breaking_commit,  # The sha of the commit associated with the command output.
            fixing_commit_sha=problem.commit_meta.sha,  # The sha of the commit that fixes the error.
            pr_number=problem.pr_meta.pr_number,  # The number of the pull request.
        )
        output_items.append(item)

    return output_items


def filter_and_log(
    autofix_system_inputs: list[AutofixSystemInput],
    keep_filter: list[bool],
    orig_len: int,
    title: str,
) -> list[AutofixSystemInput]:
    removed = sum(not keep for keep in keep_filter)
    if removed == 0:
        return autofix_system_inputs
    print(
        f"Removed {removed} / {orig_len} "
        f"({removed / orig_len:.2%}) samples ({title})"
    )
    return [
        autofix_system_input
        for autofix_system_input, keep in zip(autofix_system_inputs, keep_filter)
        if keep
    ]


def filter_data(
    autofix_system_inputs: list[AutofixSystemInput], unique_repo: bool = False
) -> list[AutofixSystemInput]:
    orig_len = len(autofix_system_inputs)
    print(f"Starting with {orig_len} samples")
    autofix_system_inputs = filter_and_log(
        autofix_system_inputs,
        [len(x.breaking_change.changed_files) > 0 for x in autofix_system_inputs],
        orig_len,
        "break_diff_empty",
    )
    autofix_system_inputs = filter_and_log(
        autofix_system_inputs,
        [
            len(x.fixing_change.changed_files) > 0
            for x in autofix_system_inputs
            if x.fixing_change
        ],
        orig_len,
        "fix_diff_empty",
    )

    autofix_system_inputs = filter_and_log(
        autofix_system_inputs,
        [
            all(isinstance(file, Modified) for file in x.fixing_change.changed_files)
            for x in autofix_system_inputs
            if x.fixing_change
        ],
        orig_len,
        "only_modified",
    )

    def is_invalid_change(change):
        return any(
            isinstance(file, Modified) and file.after.code == file.before.code
            for file in change.changed_files
        )

    autofix_system_inputs = filter_and_log(
        autofix_system_inputs,
        [
            not is_invalid_change(x.fixing_change)
            for x in autofix_system_inputs
            if x.fixing_change
        ],
        orig_len,
        "invalid fixing_change",
    )
    autofix_system_inputs = filter_and_log(
        autofix_system_inputs,
        [not is_invalid_change(x.breaking_change) for x in autofix_system_inputs],
        orig_len,
        "invalid breaking_change",
    )

    # Smart paste limitation
    def count_max_file_toks(change, max_file_size=100 * 1024):  # 100kb max
        after_files: list[FileTuple] = [
            change.after
            for change in change.changed_files
            if isinstance(change, Modified)
        ]
        return (
            max(
                llama_counter.count_tokens(file.code)
                if len(file.code) < max_file_size
                else float("inf")
                for file in after_files
            )
            if after_files
            else 0
        )

    max_smart_paste_tokens: int = 15000
    llama_counter = TokenizerBasedTokenCounter(Llama3InstructTokenizer())
    autofix_system_inputs = filter_and_log(
        autofix_system_inputs,
        [
            count_max_file_toks(x.fixing_change) < max_smart_paste_tokens
            for x in autofix_system_inputs
            if x.fixing_change
        ],
        orig_len,
        "max_smart_paste_tokens",
    )

    if unique_repo:
        # duplicated keep first
        duplicated_filter = (
            pd.Series([x.repo_name for x in autofix_system_inputs])
            .duplicated(keep="first")
            .to_list()
        )
        autofix_system_inputs = filter_and_log(
            autofix_system_inputs, duplicated_filter, orig_len, "unique_repo"
        )

    print(
        f"Remaining samples {len(autofix_system_inputs)} / {orig_len} "
        f"({len(autofix_system_inputs) / orig_len:.2%})"
    )
    return autofix_system_inputs


def main(
    unique_repo: bool = False,
):
    """Format eval data for E2E eval."""
    print(f"Loading data from {INPUT_PATH}")
    print(f"Saving to {OUTPUT_PATH}")
    print(f"Config: unique_repo={unique_repo}")

    df = pd.concat(
        [pd.read_parquet(f) for f in glob(os.path.join(INPUT_PATH, "*.parquet"))]
    )

    problems = (
        df["pickled_results"]
        .progress_apply(
            lambda pickled_results: [
                result[1]
                for result in compress_pickle.loads(pickled_results, compression="gzip")
            ]
        )
        .tolist()
    )
    problems = [problem for sublist in problems for problem in sublist]

    examples = translate_EditLocalizationProblem_to_AutofixSystemInput(problems)
    print(f"Loaded {len(examples)} autofix system inputs.")
    filter_data(examples, unique_repo=unique_repo)

    bucket_name, blob_name = OUTPUT_PATH[5:].split("/", 1)
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)

    output_pickle = compress_pickle.dumps(examples, compression="gzip")
    blob.upload_from_string(output_pickle)


if __name__ == "__main__":
    main()
