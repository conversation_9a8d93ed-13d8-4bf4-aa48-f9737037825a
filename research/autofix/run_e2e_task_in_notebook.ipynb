#%% md
This notebook is functionally similar to running `python research/eval/eval.py research/autofix/configs/e2e_gold_locations.yaml --local`, but it involves a little less overhead.
#%%
%load_ext autoreload
%autoreload 2
#%%
import os
from research.eval.harness.factories import create_system
from research.eval.harness import tasks
import uuid
import yaml
import pickle

config = yaml.safe_load(
    """
system:
  name: autofix
  use_gold_locations: False
  use_localization: True
  top_k_edit_locations: 64
  context_retrieval: True
  max_concurrent: 10

task:
  name: autofix
  dataset_path: /mnt/efs/augment/user/colin/autofix_e2e_eval/datasets/v17.pkl.gz
  limit: 10

podspec: 1xH100-gcp.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: eval-e2e-v17-localize-v17-with-context
  project: autofix
  workspace: Dev

"""
)

system = create_system(config["system"])
task = tasks.create_task(config["task"])

system.load()
output_path = f"/tmp/{str(uuid.uuid4())}"
os.makedirs(output_path, exist_ok=True)
print(f"Writing output to {output_path}")
result = task.run(system, output_path)
artifacts = pickle.load(
    open(f"{output_path}/_AutofixSystem_AutofixTask_artifacts.pkl", "rb")
)
print(result)
#%%
import numpy as np
from base.prompt_format_autofix.common import PromptChunkWithLines
from research.autofix.autofix_eval_static import location_scores

overlap_scores: list[np.float32] = []
noise_scores: list[np.float32] = []
for a in artifacts:
    x = [PromptChunkWithLines(**x) for x in a.artifacts["edit_locations"]]
    y = [PromptChunkWithLines(**y) for y in a.artifacts["gold_edit_locations"]]

    overlap, noise = location_scores(x, y)
    print(f"Overlap: {overlap}, Noise: {noise}")
    print(
        f"https://github.com/search?q=repo%3A{a.artifacts['model_input']['repo_name']}+{a.artifacts['model_input']['sha']}&type=pullrequests"
    )
    overlap_scores.append(overlap)
    noise_scores.append(noise)
    print()

print(f"Average overlap: {sum(overlap_scores) / len(overlap_scores)}")
print(f"Average noise: {sum(noise_scores) / len(noise_scores)}")