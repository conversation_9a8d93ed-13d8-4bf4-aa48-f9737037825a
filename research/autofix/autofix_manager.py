import asyncio
from collections import defaultdict
from asyncio import CancelledError
import os
import random
import re
from textwrap import dedent
from typing import Sequence
from anthropic.types import ToolUse<PERSON>lock, TextBlock
from anthropic import (
    APIConnectionError,
    AsyncAnthropic,
    AsyncAnthropicVertex,
    InternalServerError,
    RateLimitError,
)
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
)
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from requests import ReadTimeout
from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    ClientException,
    InstructionResponse,
)

import warnings
from google.auth._default import _CLOUD_SDK_CREDENTIALS_WARNING

from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File
from base.prompt_format_autofix.common import (
    PromptChunkWithLines,
    format_chunks,
    format_command_output,
    AutofixFileFix,
    AutofixFixPlan,
)
from base.diff_utils.diff_formatter import format_file_changes_with_ranges
from base.prompt_format_autofix.check_command_prompt_formatter import (
    AutofixCheckCommandOutput,
    AutofixCheckCommandV1PromptFormatter,
    AutofixCheckCommandInput,
    AutofixCheckCommandTokenApportionment,
)
from base.prompt_format_autofix.contains_errors_prompt_formatter import (
    AutofixContainErrorsOutput,
    AutofixContainErrorsV1PromptFormatter,
    AutofixContainErrorsInput,
    AutofixContainErrorsTokenApportionment,
)
from base.prompt_format_autofix.create_fix_plan_prompt_formatter import (
    ApplyFileFixInput,
    AutofixCreateFixPlanInput,
    AutofixCreateFixPlanOutput,
    AutofixCreateFixPlanTokenApportionment,
    AutofixCreateFixPlanV1PromptFormatter,
    parse_fix_plan_xml,
)
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.third_party_clients.anthropic_direct_client import get_tool_definition
from base.third_party_clients.token_counter.token_counter_claude import (
    ClaudeTokenCounter,
)
from research.eval.harness.factories import create_retriever
from research.autofix.retrieval import (
    AutofixContextRetrieveInput,
    AutofixContextOutput,
    localization_config,
    context_retriever_config,
    AutofixPredictEditLocationsInput,
)
from research.retrieval.types import DocumentIndex, RetrievalScore
from research.core.artifacts import collect_artifacts
from research.utils.repo_change_utils import (
    FileTuple,
    RepoChange,
    patchset_from_repo_change,
)
import logging

warnings.filterwarnings("ignore", message=_CLOUD_SDK_CREDENTIALS_WARNING)


logger = logging.getLogger(__name__)


class AutofixManager:
    def _get_model_name(self, name: str) -> str:
        match name.lower():
            case "haiku":
                if isinstance(self._anthropic_client, AsyncAnthropicVertex):
                    return "claude-3-haiku@20240307"
                else:
                    return "claude-3-haiku-20240307"
            case "sonnet":
                if isinstance(self._anthropic_client, AsyncAnthropicVertex):
                    return "claude-3-5-sonnet-v2@20241022"
                else:
                    return "claude-3-5-sonnet-latest"
            case _:
                raise ValueError(f"Unknown model name: {name}")

    def __init__(
        self,
        anthropic_client: AsyncAnthropicVertex | AsyncAnthropic,
        verbose: bool = True,
        context_retrieval: bool = True,
    ):
        self._verbose = verbose
        self._anthropic_client = anthropic_client
        augi_token = os.environ.get("AUGMENT_TOKEN")
        assert augi_token is not None, "AUGMENT_TOKEN is not set"
        augment_client = AugmentClient(
            url="https://staging-shard-0.api.augmentcode.com/", token=augi_token
        )
        self.context_retriever: DocumentIndex | None = None
        if context_retrieval:
            self.context_retriever = create_retriever(context_retriever_config)
            assert self.context_retriever is not None, "RCA retriever is not loaded"
            self.context_retriever.load()
        self.localization_model = create_retriever(localization_config)
        assert self.localization_model is not None, "Localization model is not loaded"
        self.localization_model.load()

        self._claude_tok_counter = ClaudeTokenCounter()
        self._smart_paste_client = AugmentModelClient(
            augment_client, "forger-smart-paste-v2-qwen-8b-32k-edit"
        )
        self._check_command_formatter = AutofixCheckCommandV1PromptFormatter(
            AutofixCheckCommandTokenApportionment(),
            self._claude_tok_counter,
        )
        self._contain_errors_formatter = AutofixContainErrorsV1PromptFormatter(
            AutofixContainErrorsTokenApportionment(),
            self._claude_tok_counter,
        )
        self._create_fix_plan_formatter = AutofixCreateFixPlanV1PromptFormatter(
            AutofixCreateFixPlanTokenApportionment(),
            self._claude_tok_counter,
        )

    async def _ask_model_with_tool(
        self,
        model_name: str,
        prompt: str,
        system_prompt: str | None = None,
        tool_name: str | None = None,
    ) -> dict:
        return (
            await self._ask_model_with_tool_or_text(
                model_name=model_name,
                prompt=prompt,
                system_prompt=system_prompt,
                tool_name=tool_name,
            )
        ).__dict__

    async def _ask_model(
        self,
        model_name: str,
        prompt: str,
        system_prompt: str | None = None,
    ) -> str:
        return str(
            await self._ask_model_with_tool_or_text(
                model_name=model_name,
                prompt=prompt,
                system_prompt=system_prompt,
            )
        )

    async def _ask_model_with_tool_or_text(
        self,
        model_name: str,
        prompt: str,
        system_prompt: str | None = None,
        tool_name: str | None = None,
    ) -> object:
        params = {}
        params["model"] = self._get_model_name(model_name)
        params["max_tokens"] = 8000 if model_name == "sonnet" else 4096
        params["temperature"] = 0
        params["system"] = system_prompt or "You are an expert AI software engineer."
        if tool_name is not None:
            params["tool_choice"] = {"type": "tool", "name": tool_name}
            params["tools"] = [get_tool_definition(tool_name)]
        params["messages"] = [
            {"role": "user", "content": [{"type": "text", "text": prompt}]}
        ]
        resp = None
        while resp is None:
            try:
                resp = await self._anthropic_client.messages.create(**params)
            except CancelledError:
                # This is expected to happen when the request is cancelled
                continue
            except (APIConnectionError, InternalServerError, RateLimitError) as e:
                if self._verbose:
                    print(f"Error: {e}")
                await asyncio.sleep(10 * random.uniform(0.8, 1.2))
                continue
        if self._verbose:
            print(
                f"Input tokens: {resp.usage.input_tokens}, Output tokens: {resp.usage.output_tokens}"
            )
        assert len(resp.content) > 0, f"Expected non-empty response, got {resp.content}"
        resp_block = resp.content[0]
        if isinstance(resp_block, ToolUseBlock):
            return resp_block.input.__dict__
        assert isinstance(resp_block, TextBlock), f"Unknown response type {resp_block}"
        return resp_block.text

    def _copy_trailing_whitespace(self, source: str, target: str) -> str:
        trailing_whitespace_regex = r"[\s\r\n]*$"
        source_trailing = re.search(trailing_whitespace_regex, source)
        source_trailing = source_trailing.group(0) if source_trailing else ""
        target_without_trailing_whitespace = re.sub(
            trailing_whitespace_regex, "", target
        )
        return target_without_trailing_whitespace + source_trailing

    async def context_retrieve(
        self, context_retrieve_input: AutofixContextRetrieveInput
    ) -> AutofixContextOutput:
        assert self.context_retriever is not None, "RCA retriever is not loaded"
        if context_retrieve_input.new_docs is not None:
            self.context_retriever.add_docs(context_retrieve_input.new_docs)

        command_and_output = format_command_output(
            context_retrieve_input.command,
            context_retrieve_input.command_output,
            self._claude_tok_counter,
            500,
            5000,
        )
        chunk_formatted = format_chunks(
            context_retrieve_input.edit_locations,
            self._claude_tok_counter,
            50000,
            command_output=context_retrieve_input.command_output,
        )

        prompt = dedent(
            f"""
            Here are some failure logs from a failed test:
            {command_and_output}

            Below are some relevant code excerpts from the codebase:
            {chunk_formatted}

            What is one critical question you would ask about the codebase to resolve the error below that cannot be answered by the provided code excerpts?
            Specify relevant files, don't specify line numbers, use identifers like classes and functions instead.
            Use no prefix or suffix, respond with the question only.
            """
        )
        generated_query = await self._ask_model(
            prompt=prompt,
            model_name="sonnet",
        )

        input = ChatRetrieverPromptInput(
            prefix="",
            suffix="",
            path="",
            message=generated_query,
            selected_code="",
            chat_history=[],
        )
        chunks, _ = self.context_retriever.query(
            model_input=input,
            doc_ids=context_retrieve_input.doc_ids,
            top_k=context_retrieve_input.top_k,
        )
        prompt_chunks = [
            PromptChunkWithLines(
                text=chunk.text,
                path=chunk.parent_doc.path,
                char_start=chunk.char_offset,
                char_end=chunk.char_offset + chunk.length,
                line_offset=chunk.line_offset,
                length_in_lines=chunk.length_in_lines,
            )
            for chunk in chunks
        ]
        return AutofixContextOutput(generated_query, prompt_chunks)

    async def predict_edit_locations(
        self, predict_edit_locations_input: AutofixPredictEditLocationsInput
    ) -> tuple[
        Sequence[PromptChunkWithLines], Sequence[RetrievalScore], dict[str, str]
    ]:
        assert self.localization_model is not None, "Localization model is not loaded"
        if predict_edit_locations_input.new_docs is not None:
            self.localization_model.add_docs(predict_edit_locations_input.new_docs)
        input = LocalizationNextEditPromptInput(
            current_file=File("", ""),
            edit_region=CharRange(0, 0),
            instruction=predict_edit_locations_input.instructions or "",
            command_output=predict_edit_locations_input.command_output,
            recent_changes=predict_edit_locations_input.recent_changes,
        )

        with collect_artifacts() as collector:
            chunks, scores = self.localization_model.query(
                model_input=input,
                top_k=predict_edit_locations_input.top_k,
                doc_ids=predict_edit_locations_input.doc_ids,
            )
            artifacts = collector.get_flattened_artifacts()
        # we ignore score here, but we could use it to rank the chunks later
        return (
            [
                PromptChunkWithLines(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                    char_start=chunk.char_offset,
                    char_end=chunk.char_offset + chunk.length,
                    line_offset=chunk.line_offset,
                    length_in_lines=chunk.length_in_lines,
                )
                for chunk in chunks
            ],
            scores,
            artifacts,
        )

    async def check_command(
        self, check_command_input: AutofixCheckCommandInput
    ) -> AutofixCheckCommandOutput:
        prompt_output = self._check_command_formatter.format_prompt(check_command_input)
        prompt = prompt_output.message
        system_prompt = prompt_output.system_prompt
        tool = prompt_output.tools[0] if prompt_output.tools else None
        response = await self._ask_model_with_tool(
            prompt=prompt,
            system_prompt=system_prompt,
            model_name="haiku",
            tool_name=tool,
        )
        return AutofixCheckCommandOutput(
            result=response["result"],
            desc=response["desc"],
        )

    async def contain_errors(
        self, contain_errors_input: AutofixContainErrorsInput
    ) -> AutofixContainErrorsOutput:
        prompt_output = self._contain_errors_formatter.format_prompt(
            contain_errors_input
        )
        prompt = prompt_output.message
        system_prompt = prompt_output.system_prompt
        tool = prompt_output.tools[0] if prompt_output.tools else None
        response = await self._ask_model_with_tool(
            prompt=prompt,
            tool_name=tool,
            model_name="haiku",
            system_prompt=system_prompt,
        )
        return AutofixContainErrorsOutput(
            result=response["result"],
            desc=response["desc"],
        )

    def _merge_changes_by_path(
        self, changes: Sequence[AutofixFileFix]
    ) -> Sequence[AutofixFileFix]:
        changes_dict = defaultdict(list)
        for change in changes:
            changes_dict[change.path].append(change)
        return [
            AutofixFileFix(
                path=path,
                change_desc="\n\n".join(
                    map(lambda change: change.change_desc, file_changes)
                ),
                code_block_start_line=min(
                    map(lambda change: change.code_block_start_line or 0, file_changes)
                ),
                code_block="\n...\n".join(
                    map(lambda change: change.code_block, file_changes)
                ),
            )
            for path, file_changes in changes_dict.items()
        ]

    async def create_fix_plan(
        self, fix_plan_input: AutofixCreateFixPlanInput
    ) -> AutofixCreateFixPlanOutput:
        files: set[str] = set([loc.path for loc in fix_plan_input.edit_locations])
        prompt_output = self._create_fix_plan_formatter.format_prompt(fix_plan_input)
        xml_text: str = await self._ask_model(
            model_name="sonnet",
            system_prompt=prompt_output.system_prompt,
            prompt=prompt_output.message,
        )
        xml = parse_fix_plan_xml(xml_text)
        changes: Sequence[AutofixFileFix] = []
        fix_desc = ""
        if xml.fix_plan and xml.fix_plan.fix_desc:
            fix_desc = xml.fix_plan.fix_desc
            if xml.fix_plan.changes:
                changes = list(filter(lambda x: x.path in files, xml.fix_plan.changes))
                changes = self._merge_changes_by_path(changes)
        return AutofixCreateFixPlanOutput(
            thinking=xml.thinking,
            critical_thinking=xml.critical_thinking,
            fix_plan=AutofixFixPlan(fix_desc=fix_desc, changes=changes),
            system_prompt=prompt_output.system_prompt,
            prompt=prompt_output.message,
        )

    async def apply_file_fix(
        self, apply_file_fix_input: ApplyFileFixInput
    ) -> Modified[File]:
        file_fix = apply_file_fix_input.file_fix
        source_content = apply_file_fix_input.source_content
        response_chunks: InstructionResponse | None = None
        try:
            response = []
            start_line: int | None = None
            end_line: int | None = None
            for response_chunks in self._smart_paste_client.smart_paste_stream(
                selected_text="",
                prefix="",
                suffix="",
                path="",
                chat_history=[
                    {
                        "request_message": file_fix.change_desc,
                        "response_text": "not used",
                    }
                ],
                code_block=file_fix.code_block or "not used",
                target_file_path=file_fix.path,
                target_file_content=source_content,
            ):
                if response_chunks.replacement_start_line is not None:
                    start_line = response_chunks.replacement_start_line - 1
                if response_chunks.replacement_end_line is not None:
                    end_line = response_chunks.replacement_end_line - 1
                if response_chunks.replacement_text is not None:
                    response.append(response_chunks.replacement_text)

            source_lines = source_content.splitlines(keepends=True)
            source_lines[start_line:end_line] = response
            target_code = "".join(source_lines)
            target_code_lines = len(target_code.splitlines(keepends=True))
            source_code_lines = len(source_content.splitlines(keepends=True))
            if target_code_lines < source_code_lines * 0.5:
                print(
                    "Warning: number of lines in target code is less than 50%"
                    f" of source code, {target_code_lines} < {source_code_lines} * 0.5 = {source_code_lines * 0.5}"
                )
            return Modified[File](
                before=File(path=file_fix.path, contents=source_content),
                after=File(
                    path=file_fix.path,
                    contents=self._copy_trailing_whitespace(
                        source_content, target_code
                    ),
                ),
            )
        except (ReadTimeout, ClientException) as e:
            print(f"Error for {file_fix.path}: {e}")
            return Modified[File](
                before=File(path=file_fix.path, contents=source_content),
                after=File(path=file_fix.path, contents=source_content),
            )
        except Exception as e:
            if response_chunks:
                print(f"Error RI {response_chunks.request_id}: {e}")
            return Modified[File](
                before=File(path=file_fix.path, contents=source_content),
                after=File(path=file_fix.path, contents=source_content),
            )
