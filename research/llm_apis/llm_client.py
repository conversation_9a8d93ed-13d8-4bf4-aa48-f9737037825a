"""Contains client for LLM APIs for the use in agents."""

import json
import random
import re
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, TypeVar, Union, cast

import anthropic
import openai
import requests
from anthropic import (
    NOT_GIVEN as Anthropic_NOT_GIVEN,
)
from anthropic import (
    APIConnectionError as AnthropicAPIConnectionError,
)
from anthropic import (
    InternalServerError as AnthropicInternalServerError,
)
from anthropic import (
    RateLimitError as AnthropicRateLimitError,
)
from anthropic._exceptions import OverloadedError as AnthropicOverloadedError
from anthropic.types import (
    RedactedThinkingBlock as AnthropicRedactedThinkingBlock,
)
from anthropic.types import (
    TextBlock as AnthropicTextBlock,
)
from anthropic.types import (
    ThinkingBlock as AnthropicThinkingBlock,
)
from anthropic.types import ToolParam as AnthropicToolParam
from anthropic.types import (
    ToolResultBlockParam as AnthropicToolResultBlockParam,
)
from anthropic.types import (
    Tool<PERSON>se<PERSON>lock as AnthropicToolUseBlock,
)
from anthropic.types.message_create_params import (
    ToolChoiceToolChoiceAny,
    ToolChoiceToolChoiceAuto,
    ToolChoiceToolChoiceTool,
)
from dataclasses_json import DataClassJsonMixin
from fireworks.client import Fireworks
from google.genai import Client as genai_Client
from google.genai import types as genai_types
from google.genai.types import FunctionCallingConfigMode
from openai import (
    APIConnectionError as OpenAI_APIConnectionError,
)
from openai import (
    InternalServerError as OpenAI_InternalServerError,
)
from openai import (
    RateLimitError as OpenAI_RateLimitError,
)
from openai._types import NOT_GIVEN as OpenAI_NOT_GIVEN
from transformers import AutoTokenizer

from research.core.constants import GCP_PROJECT_ID, GCP_VERTEX_REGION
from research.environments import get_eng_secret
from research.models import GenerationOptions, GenerativeLanguageModel


@dataclass
class ToolParam(DataClassJsonMixin):
    """Internal representation of LLM tool."""

    name: str
    description: str
    input_schema: dict[str, Any]


@dataclass
class ToolCall(DataClassJsonMixin):
    """Internal representation of LLM-generated tool call."""

    tool_call_id: str
    tool_name: str
    tool_input: Any


@dataclass
class ToolResult(DataClassJsonMixin):
    """Internal representation of LLM tool result."""

    tool_call_id: str
    tool_name: str
    tool_output: Any


@dataclass
class ToolFormattedResult(DataClassJsonMixin):
    """Internal representation of formatted LLM tool result."""

    tool_call_id: str
    tool_name: str
    tool_output: str


@dataclass
class TextPrompt(DataClassJsonMixin):
    """Internal representation of user-generated text prompt."""

    text: str


@dataclass
class TextResult(DataClassJsonMixin):
    """Internal representation of LLM-generated text result."""

    text: str


AssistantContentBlock = (
    TextResult | ToolCall | AnthropicRedactedThinkingBlock | AnthropicThinkingBlock
)
UserContentBlock = TextPrompt | ToolFormattedResult
GeneralContentBlock = UserContentBlock | AssistantContentBlock

# Since Python list is invariant, we use a bounded type variable to express covariance
ContentTypeVar = TypeVar("ContentTypeVar", bound=GeneralContentBlock)
LLMMessages = list[list[ContentTypeVar]]


class LLMClient:
    """A client for LLM APIs for the use in agents."""

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """
        raise NotImplementedError


def recursively_remove_invoke_tag(obj):
    """Recursively remove the </invoke> tag from a dictionary or list."""
    result_obj = {}
    if isinstance(obj, dict):
        for key, value in obj.items():
            result_obj[key] = recursively_remove_invoke_tag(value)
    elif isinstance(obj, list):
        result_obj = [recursively_remove_invoke_tag(item) for item in obj]
    elif isinstance(obj, str):
        if "</invoke>" in obj:
            result_obj = json.loads(obj.replace("</invoke>", ""))
        else:
            result_obj = obj
    else:
        result_obj = obj
    return result_obj


class AnthropicVertexClient(LLMClient):
    """Use Anthropic models via Vertex AI.

    Prerequisites:
    > pip3 install --upgrade --user google-cloud-aiplatform
    > gcloud auth application-default login
    > pip3 install -U 'anthropic[vertex]'
    """

    def __init__(
        self,
        region=GCP_VERTEX_REGION,
        project_id=GCP_PROJECT_ID,
        model_name="claude-3-5-sonnet@20240620",
        max_retries=2,
        use_caching: bool = True,
        thinking_tokens: int = 0,
    ):
        """Initialize the Anthropic Vertex AI client."""
        self.client = anthropic.AnthropicVertex(
            region=region,
            project_id=project_id,
            # Disable retries since we are handling retries ourselves.
            max_retries=1,
            timeout=60 * 5,
        )
        self.model_name = model_name
        self.max_retries = max_retries
        self.use_caching = use_caching
        self.prompt_caching_headers: dict[str, str] | None = None
        self.thinking_tokens = thinking_tokens

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """

        # Turn GeneralContentBlock into Anthropic message format
        anthropic_messages = []
        for idx, message_list in enumerate(messages):
            role = "user" if idx % 2 == 0 else "assistant"
            message_content_list = []
            for message in message_list:
                # Check string type to avoid import issues particularly with reloads.
                if str(type(message)) == str(TextPrompt):
                    message = cast(TextPrompt, message)
                    message_content = AnthropicTextBlock(
                        type="text",
                        text=message.text,
                    )
                elif str(type(message)) == str(TextResult):
                    message = cast(TextResult, message)
                    message_content = AnthropicTextBlock(
                        type="text",
                        text=message.text,
                    )
                elif str(type(message)) == str(ToolCall):
                    message = cast(ToolCall, message)
                    message_content = AnthropicToolUseBlock(
                        type="tool_use",
                        id=message.tool_call_id,
                        name=message.tool_name,
                        input=message.tool_input,
                    )
                elif str(type(message)) == str(ToolFormattedResult):
                    message = cast(ToolFormattedResult, message)
                    message_content = AnthropicToolResultBlockParam(
                        type="tool_result",
                        tool_use_id=message.tool_call_id,
                        content=message.tool_output,
                    )
                elif str(type(message)) == str(AnthropicRedactedThinkingBlock):
                    message = cast(AnthropicRedactedThinkingBlock, message)
                    message_content = message
                elif str(type(message)) == str(AnthropicThinkingBlock):
                    message = cast(AnthropicThinkingBlock, message)
                    message_content = message
                else:
                    print(
                        f"Unknown message type: {type(message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                    )
                    raise ValueError(
                        f"Unknown message type: {type(message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                    )
                message_content_list.append(message_content)

            # Anthropic supports up to 4 cache breakpoints, so we put them on the last 4 messages.
            if self.use_caching and idx >= len(messages) - 4:
                if isinstance(message_content_list[-1], dict):
                    message_content_list[-1]["cache_control"] = {"type": "ephemeral"}
                else:
                    message_content_list[-1].cache_control = {"type": "ephemeral"}

            anthropic_messages.append(
                {
                    "role": role,
                    "content": message_content_list,
                }
            )

        if self.use_caching:
            extra_headers = self.prompt_caching_headers
        else:
            extra_headers = None

        # Turn tool_choice into Anthropic tool_choice format
        if tool_choice is None:
            tool_choice_param = Anthropic_NOT_GIVEN
        elif tool_choice["type"] == "any":
            tool_choice_param = ToolChoiceToolChoiceAny(type="any")
        elif tool_choice["type"] == "auto":
            tool_choice_param = ToolChoiceToolChoiceAuto(type="auto")
        elif tool_choice["type"] == "tool":
            tool_choice_param = ToolChoiceToolChoiceTool(
                type="tool", name=tool_choice["name"]
            )
        else:
            raise ValueError(f"Unknown tool_choice type: {tool_choice['type']}")

        if len(tools) == 0:
            tool_params = Anthropic_NOT_GIVEN
        else:
            tool_params = [
                AnthropicToolParam(
                    input_schema=tool.input_schema,
                    name=tool.name,
                    description=tool.description,
                )
                for tool in tools
            ]

        response = None

        if thinking_tokens is None:
            thinking_tokens = self.thinking_tokens
        if thinking_tokens and thinking_tokens > 0:
            extra_body = {
                "thinking": {"type": "enabled", "budget_tokens": thinking_tokens}
            }
            temperature = 1
            assert (
                max_tokens >= 32_000 and thinking_tokens <= 8192
            ), f"As a heuristic, max tokens {max_tokens} must be >= 32k and thinking tokens {thinking_tokens} must be < 8k"
        else:
            extra_body = None

        for retry in range(self.max_retries):
            try:
                response = self.client.messages.create(  # type: ignore
                    max_tokens=max_tokens,
                    messages=anthropic_messages,
                    model=self.model_name,
                    temperature=temperature,
                    system=system_prompt or Anthropic_NOT_GIVEN,
                    tool_choice=tool_choice_param,  # type: ignore
                    tools=tool_params,
                    extra_headers=extra_headers,
                    extra_body=extra_body,
                )
                break
            except (
                AnthropicAPIConnectionError,
                AnthropicInternalServerError,
                AnthropicRateLimitError,
                AnthropicOverloadedError,
            ) as e:
                if retry == self.max_retries - 1:
                    print(f"Failed Anthropic request after {retry + 1} retries")
                    raise e
                else:
                    print(f"Retrying LLM request: {retry + 1}/{self.max_retries}")
                    # Sleep 4-6 seconds with jitter to avoid thundering herd.
                    time.sleep(5 * random.uniform(0.8, 1.2))

        # Convert messages back to Augment format
        augment_messages = []
        assert response is not None
        for message in response.content:
            if "</invoke>" in str(message):
                warning_msg = "\n".join(
                    ["!" * 80, "WARNING: Unexpected 'invoke' in message", "!" * 80]
                )
                print(warning_msg)

            if str(type(message)) == str(AnthropicTextBlock):
                message = cast(AnthropicTextBlock, message)
                augment_messages.append(TextResult(text=message.text))
            elif str(type(message)) == str(AnthropicRedactedThinkingBlock):
                augment_messages.append(message)
            elif str(type(message)) == str(AnthropicThinkingBlock):
                message = cast(AnthropicThinkingBlock, message)
                augment_messages.append(message)
            elif str(type(message)) == str(AnthropicToolUseBlock):
                message = cast(AnthropicToolUseBlock, message)
                augment_messages.append(
                    ToolCall(
                        tool_call_id=message.id,
                        tool_name=message.name,
                        tool_input=recursively_remove_invoke_tag(message.input),
                    )
                )
            else:
                raise ValueError(f"Unknown message type: {type(message)}")

        message_metadata = {
            "raw_response": response,
            "input_tokens": response.usage.input_tokens,
            "output_tokens": response.usage.output_tokens,
            "cache_creation_input_tokens": getattr(
                response.usage, "cache_creation_input_tokens", -1
            ),
            "cache_read_input_tokens": getattr(
                response.usage, "cache_read_input_tokens", -1
            ),
        }

        return augment_messages, message_metadata


class AnthropicDirectClient(AnthropicVertexClient):
    """Use Anthropic models via first party API."""

    def __init__(
        self,
        model_name="claude-3-5-sonnet-v2@20241022",
        max_retries=2,
        use_caching=True,
        use_low_qos_server: bool = False,
        thinking_tokens: int = 0,
    ):
        """Initialize the Anthropic first party client."""
        if not use_low_qos_server:
            api_key = get_eng_secret("seal-research-anthropic-key")
        else:
            api_key = get_eng_secret("seal-research-anthropic-low-qos-key")
        # Disable retries since we are handling retries ourselves.
        self.client = anthropic.Anthropic(
            api_key=api_key, max_retries=1, timeout=60 * 5
        )
        self.model_name = model_name
        self.max_retries = max_retries
        self.use_caching = use_caching
        self.prompt_caching_headers = {"anthropic-beta": "prompt-caching-2024-07-31"}
        self.thinking_tokens = thinking_tokens


class OpenAIDirectClient(LLMClient):
    """Use OpenAI models via first party API."""

    def __init__(self, model_name: str, max_retries=2, cot_model: bool = True):
        """Initialize the OpenAI first party client."""
        api_key = get_eng_secret("research-openai-key")
        self.client = openai.OpenAI(
            api_key=api_key,
            max_retries=1,
        )
        self.model_name = model_name
        self.max_retries = max_retries
        self.cot_model = cot_model

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            system_prompt: A system prompt.
            max_tokens: The maximum number of tokens to generate.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """

        # Turn GeneralContentBlock into OpenAI message format
        openai_messages = []
        if system_prompt is not None:
            if self.cot_model:
                raise NotImplementedError("System prompt not supported for cot model")
            system_message = {"role": "system", "content": system_prompt}
            openai_messages.append(system_message)
        for idx, message_list in enumerate(messages):
            if len(message_list) > 1:
                raise ValueError("Only one entry per message supported for openai")
            augment_message = message_list[0]
            if str(type(augment_message)) == str(TextPrompt):
                augment_message = cast(TextPrompt, augment_message)
                message_content = {"type": "text", "text": augment_message.text}
                openai_message = {"role": "user", "content": [message_content]}
            elif str(type(augment_message)) == str(TextResult):
                augment_message = cast(TextResult, augment_message)
                message_content = {"type": "text", "text": augment_message.text}
                openai_message = {"role": "assistant", "content": [message_content]}
            elif str(type(augment_message)) == str(ToolCall):
                augment_message = cast(ToolCall, augment_message)
                tool_call = {
                    "type": "function",
                    "id": augment_message.tool_call_id,
                    "function": {
                        "name": augment_message.tool_name,
                        "arguments": augment_message.tool_input,
                    },
                }
                openai_message = {
                    "role": "assistant",
                    "tool_calls": [tool_call],
                }
            elif str(type(augment_message)) == str(ToolFormattedResult):
                augment_message = cast(ToolFormattedResult, augment_message)
                openai_message = {
                    "role": "tool",
                    "tool_call_id": augment_message.tool_call_id,
                    "content": augment_message.tool_output,
                }
            else:
                print(
                    f"Unknown message type: {type(augment_message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                )
                raise ValueError(f"Unknown message type: {type(augment_message)}")
            openai_messages.append(openai_message)

        # Turn tool_choice into OpenAI tool_choice format
        if tool_choice is None:
            tool_choice_param = OpenAI_NOT_GIVEN
        elif tool_choice["type"] == "any":
            tool_choice_param = "required"
        elif tool_choice["type"] == "auto":
            tool_choice_param = "auto"
        elif tool_choice["type"] == "tool":
            tool_choice_param = {
                "type": "function",
                "function": {"name": tool_choice["name"]},
            }
        else:
            raise ValueError(f"Unknown tool_choice type: {tool_choice['type']}")

        # Turn tools into OpenAI tool format
        openai_tools = []
        for tool in tools:
            tool_def = {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.input_schema,
            }
            tool_def["parameters"]["strict"] = True
            openai_tool_object = {
                "type": "function",
                "function": tool_def,
            }
            openai_tools.append(openai_tool_object)

        response = None
        for retry in range(self.max_retries):
            try:
                extra_body = {}
                openai_max_tokens = max_tokens
                openai_temperature = temperature
                if self.cot_model:
                    extra_body["max_completion_tokens"] = max_tokens
                    openai_max_tokens = OpenAI_NOT_GIVEN
                    openai_temperature = OpenAI_NOT_GIVEN

                response = self.client.chat.completions.create(  # type: ignore
                    model=self.model_name,
                    messages=openai_messages,
                    temperature=openai_temperature,
                    tools=openai_tools if len(openai_tools) > 0 else OpenAI_NOT_GIVEN,
                    tool_choice=tool_choice_param,  # type: ignore
                    max_tokens=openai_max_tokens,
                    extra_body=extra_body,
                )
                break
            except (
                OpenAI_APIConnectionError,
                OpenAI_InternalServerError,
                OpenAI_RateLimitError,
            ) as e:
                if retry == self.max_retries - 1:
                    print(f"Failed OpenAI request after {retry + 1} retries")
                    raise e
                else:
                    print(f"Retrying OpenAI request: {retry + 1}/{self.max_retries}")
                    # Sleep 8-12 seconds with jitter to avoid thundering herd.
                    time.sleep(10 * random.uniform(0.8, 1.2))

        # Convert messages back to Augment format
        augment_messages = []
        assert response is not None
        openai_response_messages = response.choices
        if len(openai_response_messages) > 1:
            raise ValueError("Only one message supported for OpenAI")
        openai_response_message = openai_response_messages[0].message
        tool_calls = openai_response_message.tool_calls
        content = openai_response_message.content

        # Exactly one of tool_calls or content should be present
        if tool_calls and content:
            raise ValueError("Only one of tool_calls or content should be present")
        elif not tool_calls and not content:
            raise ValueError("Either tool_calls or content should be present")

        if tool_calls:
            if len(tool_calls) > 1:
                raise ValueError("Only one tool call supported for OpenAI")
            tool_call = tool_calls[0]
            try:
                # Parse the JSON string into a dictionary
                tool_input = json.loads(tool_call.function.arguments)
            except json.JSONDecodeError as e:
                print(f"Failed to parse tool arguments: {tool_call.function.arguments}")
                print(f"JSON parse error: {str(e)}")
                raise ValueError(f"Invalid JSON in tool arguments: {str(e)}") from e

            augment_messages.append(
                ToolCall(
                    tool_name=tool_call.function.name,
                    tool_input=tool_input,
                    tool_call_id=tool_call.id,
                )
            )
        elif content:
            augment_messages.append(TextResult(text=content))
        else:
            raise ValueError(f"Unknown message type: {openai_response_message}")

        assert response.usage is not None
        message_metadata = {
            "raw_response": response,
            "input_tokens": response.usage.prompt_tokens,
            "output_tokens": response.usage.completion_tokens,
        }

        return augment_messages, message_metadata


class GeminiVertexClient(LLMClient):
    """Use Gemini models via Vertex AI."""

    def __init__(
        self,
        region="us-central1",
        project_id="augment-research-gsc",
        model_name="gemini-1.5-pro",
        max_retries=2,
    ):
        """Initialize the Gemini client."""
        self.client = genai_Client(vertexai=True, project=project_id, location=region)
        self.model_name = model_name
        self.max_retries = max_retries

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            system_prompt: A system prompt.
            max_tokens: The maximum number of tokens to generate.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """

        # Convert tools to Gemini tool format
        gemini_tool_object = None

        def convert_to_gemini_schema(param_dict: dict) -> dict:
            new_dict = {}
            for key, value in param_dict.items():
                if key == "type":
                    # Convert value to caps
                    new_dict["type"] = value.upper()
                elif isinstance(value, dict):
                    new_dict[key] = convert_to_gemini_schema(value)
                else:
                    new_dict[key] = value
            return new_dict

        if len(tools) > 0:
            gemini_tools = []
            for tool in tools:
                gemini_tool = {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": convert_to_gemini_schema(tool.input_schema),
                }
                gemini_tools.append(gemini_tool)
            gemini_tool_object = genai_types.Tool(function_declarations=gemini_tools)

        # Turn tool_choice into Gemini tool_choice format
        allowed_function_names = None
        if tool_choice is None:
            function_calling_mode = FunctionCallingConfigMode.AUTO
        elif tool_choice["type"] == "any":
            function_calling_mode = FunctionCallingConfigMode.ANY
        elif tool_choice["type"] == "auto":
            function_calling_mode = FunctionCallingConfigMode.AUTO
        elif tool_choice["type"] == "tool":
            function_calling_mode = FunctionCallingConfigMode.ANY
            allowed_function_names = [tool_choice["name"]]
        else:
            raise ValueError(f"Unknown tool_choice type: {tool_choice['type']}")

        function_calling_config = genai_types.FunctionCallingConfig(
            mode=function_calling_mode, allowed_function_names=allowed_function_names
        )
        tool_config = genai_types.ToolConfig(
            function_calling_config=function_calling_config
        )

        gemini_messages = []
        for message_list in messages:
            parts = []
            role = None
            for message in message_list:
                if str(type(message)) == str(TextPrompt):
                    message = cast(TextPrompt, message)
                    role = "user"
                    parts.append(genai_types.Part(text=message.text))
                elif str(type(message)) == str(TextResult):
                    message = cast(TextResult, message)
                    role = "model"
                    parts.append(genai_types.Part(text=message.text))
                elif str(type(message)) == str(ToolCall):
                    message = cast(ToolCall, message)
                    if message.tool_call_id == "":
                        tool_call_id = None
                    else:
                        tool_call_id = message.tool_call_id
                    function_call = genai_types.FunctionCall(
                        id=tool_call_id,
                        name=message.tool_name,
                        args=message.tool_input,  # type: ignore
                    )
                    role = "model"
                    parts.append(genai_types.Part(function_call=function_call))
                elif str(type(message)) == str(ToolFormattedResult):
                    message = cast(ToolFormattedResult, message)
                    response_dict = {
                        "output": message.tool_output,
                    }
                    function_response = genai_types.FunctionResponse(
                        id=message.tool_call_id,
                        name=message.tool_name,
                        response=response_dict,  # type: ignore
                    )
                    role = "user"
                    parts.append(genai_types.Part(function_response=function_response))
            gemini_message = genai_types.Content(role=role, parts=parts)
            if parts:
                assert role
                gemini_messages.append(gemini_message)

        gemini_config = genai_types.GenerateContentConfig(
            system_instruction=system_prompt,
            max_output_tokens=max_tokens,
            temperature=temperature,
            safety_settings=[],
            tools=[gemini_tool_object] if gemini_tool_object is not None else None,
            tool_config=tool_config,
        )

        response = None
        for retry in range(self.max_retries):
            try:
                response = self.client.models.generate_content(
                    model=self.model_name,
                    contents=gemini_messages,
                    config=gemini_config,
                )
                break
            except Exception as e:
                if retry == self.max_retries - 1:
                    print(f"Failed Gemini request after {retry + 1} retries")
                    raise e
                else:
                    print(
                        f"Retrying Gemini request: {retry + 1}/{self.max_retries} for {e}"
                    )
                    # Sleep 4-6 seconds with jitter to avoid thundering herd.
                    time.sleep(5 * random.uniform(0.8, 1.2))

        # Convert messages back to Augment format
        augment_messages = []
        if (
            response
            and response.candidates
            and response.candidates[0].content
            and response.candidates[0].content.parts
        ):
            for part in response.candidates[0].content.parts:
                if part.text is not None:
                    augment_messages.append(TextResult(text=part.text))
                elif part.function_call is not None:
                    # If the model tries to make more than one tool call, just ignore the rest.
                    if len(augment_messages) > 0 and isinstance(
                        augment_messages[-1], ToolCall
                    ):
                        continue
                    function_call = part.function_call
                    tool_call_id = function_call.id
                    tool_name = function_call.name
                    assert isinstance(tool_name, str)
                    augment_message = ToolCall(
                        tool_call_id=tool_call_id or "",
                        tool_name=tool_name,
                        tool_input=function_call.args,
                    )
                    augment_messages.append(augment_message)
                else:
                    raise ValueError(f"Unknown message type: {part}")

        input_tokens = 0
        output_tokens = 0
        if (
            response
            and response.usage_metadata
            and response.usage_metadata.prompt_token_count
            and response.usage_metadata.candidates_token_count
        ):
            input_tokens = response.usage_metadata.prompt_token_count
            output_tokens = response.usage_metadata.candidates_token_count
        message_metadata = {
            "raw_response": response,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
        }

        return augment_messages, message_metadata


class QwenFastForwardClient(LLMClient):
    """Use Qwen fastbackward models."""

    def __init__(
        self,
        model: GenerativeLanguageModel,
    ):
        self.seq_length = 32768

        self.model = model
        self.tokenizer = AutoTokenizer.from_pretrained(
            pretrained_model_name_or_path="Qwen/Qwen2.5-Coder-32B-Instruct"
        )

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses.

        Args:
            messages: A list of messages.
            system_prompt: A system prompt.
            max_tokens: The maximum number of tokens to generate.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.

        Returns:
            A generated response.
        """

        # Turn GeneralContentBlock into Qwen message format
        qwen_messages = []
        system_message = {
            "role": "system",
            "content": system_prompt or "You are a helpful assistant.",
        }
        qwen_messages.append(system_message)
        for idx, message_list in enumerate(messages):
            message_content_list = []
            for message in message_list:
                if str(type(message)) == str(TextPrompt):
                    message = cast(TextPrompt, message)
                    message_dict = {
                        "role": "user",
                        "content": message.text,
                    }
                elif str(type(message)) == str(TextResult):
                    message = cast(TextResult, message)
                    message_dict = {
                        "role": "assistant",
                        "content": message.text,
                    }
                elif str(type(message)) == str(ToolCall):
                    message = cast(ToolCall, message)
                    message_dict = {
                        "role": "assistant",
                        "content": "",
                        "function_call": {
                            "name": message.tool_name,
                            "arguments": message.tool_input,
                        },
                    }
                elif str(type(message)) == str(ToolFormattedResult):
                    message = cast(ToolFormattedResult, message)
                    message_dict = {
                        "role": "tool",
                        "name": message.tool_name,
                        "content": message.tool_output,
                    }
                else:
                    print(
                        f"Unknown message type: {type(message)}, expected one of {str(TextPrompt)}, {str(TextResult)}, {str(ToolCall)}, {str(ToolFormattedResult)}"
                    )
                    raise ValueError(f"Unknown message type: {type(message)}")
                message_content_list.append(message_dict)
                qwen_messages.extend(message_content_list)

        # Format messages into prompt
        chat_template_tools = []
        for tool in tools:
            chat_template_tools.append(
                {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.input_schema,
                    },
                }
            )
        formatted_messages = self.tokenizer.apply_chat_template(
            qwen_messages,
            tools=chat_template_tools,
            tokenize=False,
        )
        # https://huggingface.co/Qwen/Qwen2.5-Coder-32B-Instruct/blob/main/tokenizer_config.json
        im_start_id = 151644
        im_end_id = 151645
        tool_start_id = 151657
        tool_end_id = 151658

        # Tokenize prompt
        prompt_tokens = []
        tokenized_prompt_object = self.tokenizer(formatted_messages)  # type: ignore
        prompt_tokens = tokenized_prompt_object["input_ids"]

        effective_seq_length = self.seq_length - max_tokens
        prompt_tokens = cast(list[int], prompt_tokens)
        if len(prompt_tokens) > effective_seq_length:
            raise ValueError(
                f"Prompt is too long: {len(prompt_tokens)} > {effective_seq_length}"
            )

        prefill_tokens = [im_start_id] + self.tokenizer.encode("assistant\n")
        tool_tokens = []
        # Add tool choice
        force_tool = False
        if tool_choice is not None:
            if tool_choice["type"] == "auto":
                pass
            elif tool_choice["type"] == "any":
                force_tool = True
                prefill_tokens.append(tool_start_id)
                tool_tokens.extend(self.tokenizer.encode('\n{"name": "'))
                prefill_tokens.extend(tool_tokens)
            elif tool_choice["type"] == "tool":
                force_tool = True
                prefill_tokens.extend(
                    self.tokenizer.encode(
                        f"I should use the {tool_choice['name']} tool.\n"
                    )
                )
                prefill_tokens.append(tool_start_id)
                tool_tokens.extend(
                    self.tokenizer.encode(
                        f'\n{{"name": "{tool_choice["name"]}", "arguments": {{"'
                    )
                )
                prefill_tokens.extend(tool_tokens)

        prompt_tokens.extend(prefill_tokens)

        assert isinstance(prompt_tokens, list)

        # Generate tokens
        max_messages = 10
        max_tokens_left = max_tokens
        generated_messages = []
        generated_tokens = []
        raw_inputs = []
        raw_responses = []
        for message_idx in range(max_messages):
            generation_options = GenerationOptions(
                temperature=temperature,
                max_generated_tokens=max_tokens_left,
                stop_tokens=[im_end_id, tool_end_id],
            )
            input_tokens = prompt_tokens + generated_tokens
            raw_inputs.append(input_tokens)
            print(f"Generating at most {max_tokens_left} tokens")
            print(f"Prompt tokens: {len(prompt_tokens)}")
            print("ATTEMPTING TO GENERATE TOKENS FROM QWEN")

            response = self.model.raw_generate_tokens(
                prompt_tokens=input_tokens,
                options=generation_options,
            )
            print("GENERATED TOKENS FROM QWEN")
            raw_responses.append(response)
            generated_tokens.extend(response.tokens)
            max_tokens_left -= len(response.tokens)  # type: ignore

            print(f"Generated {len(response.tokens)} tokens")

            # Check if it's a tool message
            if force_tool:
                tool_message_tokens = tool_tokens + response.tokens[:-1]
                # Note: this is a horrible hack because function calling doesn't seem to close tool bracket
                assert response.tokens[-1] in (tool_end_id, im_end_id)
                tool_message = self.tokenizer.decode(tool_message_tokens)
                # Parse tool message
                try:
                    tool_message = json.loads(tool_message)
                except json.JSONDecodeError:
                    print("Failed to parse tool message:", tool_message)
                    raise
                tool_name = tool_message["name"]
                tool_input = tool_message["arguments"]
                tool_call = ToolCall(
                    tool_name=tool_name, tool_input=tool_input, tool_call_id=""
                )
                generated_messages.append(tool_call)
            # Function calling seems a bit bugged, so disabling this for now
            elif tool_start_id in response.tokens:
                print("Tool message detected outside of forced tool mode")
                raise ValueError("Tool message detected outside of forced tool mode")
            else:
                assert response.tokens[-1] == im_end_id
                text = self.tokenizer.decode(response.tokens)
                message = TextResult(text=text)
                generated_messages.append(message)

            if max_tokens_left <= 0 or response.tokens[-1] == im_end_id:
                break

        message_metadata = {
            "raw_inputs": raw_inputs,
            "raw_responses": raw_responses,
        }

        return generated_messages, message_metadata


class ToolSupportProvider:
    """Adapter class to enable tool usage with models that don't natively support tools.

    This class provides methods to:
    1. Create a system prompt that includes tool details
    2. Parse tool calls from text responses
    3. Convert parsed tool calls to the format expected by generate()
    """

    @staticmethod
    def create_tool_system_prompt(
        tools: List[ToolParam], base_system_prompt: Optional[str] = None
    ) -> str:
        """Create a system prompt that provides tool details for models that don't natively support tools.

        Args:
            tools: List of ToolParam objects
            base_system_prompt: Optional base system prompt to include

        Returns:
            str: A system prompt that instructs the model how to use the provided tools
        """
        tool_descriptions = []

        for tool in tools:
            tool_desc = f"Function Name: '{tool.name}'"
            tool_desc += f"\nPurpose: '{tool.description}'"
            tool_desc += (
                f"\nParameters Schema: {json.dumps(tool.input_schema, indent=4)}"
            )
            tool_descriptions.append(tool_desc)

        tools_text = "\n\n".join(tool_descriptions)

        # Start with the base system prompt if provided
        system_prompt_parts = []
        if base_system_prompt:
            system_prompt_parts.append(base_system_prompt)

        # Add the tool instructions
        tool_instructions = f"""\
You have access to the following functions:

{tools_text}

Instructions for Using Functions:
1. When a user's request requires using a function, reply ONLY in the following format:
   <function=function_name>{{"param1": "value1", "param2": "value2"}}</function>
2. Adhere strictly to the parameters schema. Ensure all required fields are provided.
3. Use the function only when you cannot directly answer using general knowledge.
4. If no function is necessary, respond to the query directly without mentioning the function.

Examples:
- For a query like "What is the temperature in Toronto?" respond with:
  <function=get_temperature>{{"location": "Toronto"}}</function>
- For "What is the capital of France?" respond with general knowledge and do NOT use the function.
"""
        system_prompt_parts.append(tool_instructions)

        return "\n\n".join(system_prompt_parts)

    @staticmethod
    def convert_messages_to_prompt(messages: LLMMessages) -> str:
        """Convert LLMMessages to a single text prompt.

        This method takes the messages parameter from generate() and converts
        all the list[UserContentBlock] pieces into a single text prompt.
        It handles both TextPrompt and ToolFormattedResult, clearly describing
        which parts are the tool results.

        Args:
            messages: A list of messages from generate()

        Returns:
            str: A single text prompt that includes both user messages and tool results
        """
        prompt_parts = []

        for i, message_list in enumerate(messages):
            # Determine if this is a user or assistant message based on index
            role = "User" if i % 2 == 0 else "Assistant"
            message_parts = []

            for message in message_list:
                # Check the type of message using isinstance
                if isinstance(message, TextPrompt) or isinstance(message, TextResult):
                    # This is a text message (either user prompt or assistant response)
                    text_message = cast(Union[TextPrompt, TextResult], message)
                    message_parts.append(f"{text_message.text}")
                elif isinstance(message, ToolCall):
                    # This is a tool call from the assistant
                    tool_call_message = cast(ToolCall, message)
                    tool_call = (
                        f"[Tool Call: {tool_call_message.tool_name}]\n"
                        f"Arguments: {json.dumps(tool_call_message.tool_input, indent=2)}"
                    )
                    message_parts.append(tool_call)
                elif isinstance(message, ToolFormattedResult):
                    # This is a formatted tool result
                    tool_result_message = cast(ToolFormattedResult, message)
                    tool_result = (
                        f"[Tool Result: {tool_result_message.tool_name}]\n"
                        f"{tool_result_message.tool_output}"
                    )
                    message_parts.append(tool_result)
                else:
                    # For any other type, try to extract useful information
                    message_type = type(message).__name__
                    if hasattr(message, "text"):
                        message_parts.append(f"{getattr(message, 'text')}")
                    else:
                        message_parts.append(f"[Unknown message type: {message_type}]")

            # Join all parts of this message
            if message_parts:
                prompt_parts.append(f"{role}: {' '.join(message_parts)}")

        # Join all messages into a single prompt
        return "\n\n".join(prompt_parts)

    @staticmethod
    def convert_to_assistant_content_blocks(
        text: str,
    ) -> Tuple[List[AssistantContentBlock], Dict[str, Any]]:
        """Convert text response to assistant content blocks.

        This method parses the text response, extracts any tool calls, and returns
        the content blocks in the format expected by generate().

        The method preserves the order of text and function calls in the original response.
        It can handle multiple function calls and mixed text/function content.

        Args:
            text: Text response from the model

        Returns:
            Tuple[List[AssistantContentBlock], Dict[str, Any]]:
                - List of assistant content blocks
                - Metadata dictionary
        """
        # Find all function calls and their positions in the text
        pattern = r"<function=([^>]+)>({.*?})</function>"
        matches = list(re.finditer(pattern, text, re.DOTALL))

        # If no function calls, just return the text as a single block
        if not matches:
            return [TextResult(text=text)], {
                "tool_support_provider": "used",
                "original_text": text,
            }

        # Create a list of segments (text or function call) in the order they appear
        segments = []
        last_end = 0

        for match in matches:
            # If there's text before this function call, add it as a text segment
            if match.start() > last_end:
                text_segment = text[last_end : match.start()].strip()
                if text_segment:
                    segments.append({"type": "text", "content": text_segment})

            # Add the function call as a segment
            try:
                tool_name = match.group(1)
                tool_args = json.loads(match.group(2))
                segments.append(
                    {
                        "type": "function",
                        "name": tool_name,
                        "arguments": tool_args,
                        "id": f"call_{tool_name}_{abs(hash(json.dumps(tool_args)))}".replace(
                            "-", "_"
                        )[:32],  # Generate a deterministic ID with only underscores
                    }
                )
            except json.JSONDecodeError:
                print(f"Error parsing tool arguments: {match.group(2)}")
                # Add as text if we can't parse it
                segments.append({"type": "text", "content": match.group(0)})

            last_end = match.end()

        # If there's text after the last function call, add it
        if last_end < len(text):
            text_segment = text[last_end:].strip()
            if text_segment:
                segments.append({"type": "text", "content": text_segment})

        # Convert segments to AssistantContentBlock objects
        assistant_content_blocks: List[AssistantContentBlock] = []

        for segment in segments:
            if segment["type"] == "text":
                assistant_content_blocks.append(TextResult(text=segment["content"]))
            else:  # function call
                assistant_content_blocks.append(
                    ToolCall(
                        tool_call_id=segment["id"],
                        tool_name=segment["name"],
                        tool_input=segment["arguments"],
                    )
                )

        # Create metadata
        metadata = {
            "tool_support_provider": "used",
            "original_text": text,
            "segments_count": len(segments),
        }

        return assistant_content_blocks, metadata


class FireworksClient(LLMClient):
    """Use Fireworks AI models via their API."""

    # Model constants for easy access
    MODEL_DEEPSEEK_R1 = "accounts/fireworks/models/deepseek-r1"
    MODEL_DEEPSEEK_V3 = "accounts/fireworks/models/deepseek-v3"
    MODEL_DEEPSEEK_V3_0324 = "accounts/fireworks/models/deepseek-v3-0324"
    MODEL_QWEN2_5_CODER_32B = "accounts/fireworks/models/qwen2p5-coder-32b-instruct"
    MODEL_QWQ_32B = "accounts/fireworks/models/qwq-32b"

    def __init__(
        self,
        model_name: str,
        max_retries=2,
        api_key: str | None = None,
    ):
        """Initialize the Fireworks AI client.

        Args:
            model_name: The model name to use, e.g., "accounts/fireworks/models/deepseek-v3"
            max_retries: Maximum number of retries for API calls.
            api_key: Optional API key. If not provided, will try to get it from eng-secrets.
        """
        if api_key is None:
            api_key = get_eng_secret("fireworks-api-key")
        self.api_key = api_key  # Store the API key in the instance
        self.client = Fireworks(
            api_key=api_key
            # We handle retries ourselves in the generate method
        )
        self.model_name = model_name
        self.max_retries = max_retries
        self._supports_tools_cache = None  # Cache for supports_tools check

    @property
    def supports_tools(self) -> bool:
        """Check if the model supports tools.

        This property lazily evaluates whether the model supports tools by making an API call
        the first time it's accessed, then caches the result.

        Returns:
            bool: True if the model supports tools, False otherwise.
        """
        if self._supports_tools_cache is None:
            self._supports_tools_cache = self._check_model_supports_tools()
        return self._supports_tools_cache

    def _check_model_supports_tools(self) -> bool:
        """Check if the model supports tools by querying the Fireworks API.

        This method makes a direct API call to get model information and checks
        the supportsTools parameter in the response.

        Returns:
            bool: True if the model supports tools, False otherwise.
        """
        try:
            # Extract account_id and model_id from the model name
            parts = self.model_name.split("/")
            if len(parts) != 4 or parts[0] != "accounts":
                # If model name doesn't follow the expected format, assume it doesn't support tools
                return False

            account_id = parts[1]
            model_id = parts[3]

            # Make a direct API call to get model information
            headers = {"Authorization": f"Bearer {self.api_key}"}
            url = f"https://api.fireworks.ai/v1/accounts/{account_id}/models/{model_id}"

            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                print(
                    f"Error getting model information: {response.status_code} {response.text}"
                )
                return False

            model_info = response.json()

            # Check if the model supports tools
            return model_info.get("supportsTools", False)
        except Exception as e:
            # If there's an error, log it and assume the model doesn't support tools
            print(f"Error checking if model supports tools: {e}")
            return False

    def generate(
        self,
        messages: LLMMessages,
        max_tokens: int,
        system_prompt: str | None = None,
        temperature: float = 0.0,
        tools: list[ToolParam] = [],
        tool_choice: dict[str, str] | None = None,
        thinking_tokens: int | None = None,
    ) -> Tuple[list[AssistantContentBlock], dict[str, Any]]:
        """Generate responses using Fireworks AI models.

        Args:
            messages: A list of messages.
            max_tokens: The maximum number of tokens to generate.
            system_prompt: A system prompt.
            temperature: The temperature.
            tools: A list of tools.
            tool_choice: A tool choice.
            thinking_tokens: Optional number of tokens to use for thinking.

        Returns:
            A generated response and metadata.

        Raises:
            ValueError: If tools are provided but the model doesn't support tools.
        """
        # If tools are provided and the model doesn't support them, raise an error
        if tools and not self.supports_tools:
            raise ValueError(
                f"Model {self.model_name} does not support tools, but tools were provided. "
                f"To use tools with this model, wrap it with ToolSupportWrapper."
            )

        # Convert messages to Fireworks format
        fireworks_messages = []
        if system_prompt is not None:
            fireworks_messages.append({"role": "system", "content": system_prompt})

        for idx, message_list in enumerate(messages):
            # Special case: Handle a message list with both TextResult and ToolCall
            if len(message_list) > 1:
                # Check if this is a TextResult + ToolCall combination
                text_result = None
                tool_calls = []

                for message in message_list:
                    if isinstance(message, TextResult):
                        text_result = message
                    elif isinstance(message, ToolCall):
                        tool_calls.append(message)
                    else:
                        # If we have any other type, we can't handle this combination
                        print("message_list:")
                        print(message_list)
                        raise ValueError(
                            "Unsupported message combination for Fireworks"
                        )

                # If we have both a TextResult and at least one ToolCall, create a combined message
                if text_result is not None and tool_calls:
                    # Create a message with both text content and tool calls
                    combined_message = {
                        "role": "assistant",
                        "content": text_result.text,
                        "tool_calls": [
                            {
                                "type": "function",
                                "id": tool_call.tool_call_id,
                                "function": {
                                    "name": tool_call.tool_name,
                                    "arguments": json.dumps(tool_call.tool_input),
                                },
                            }
                            for tool_call in tool_calls
                        ],
                    }
                    fireworks_messages.append(combined_message)
                    continue
                else:
                    # If it's not a TextResult + ToolCall combination, we can't handle it
                    print("message_list:")
                    print(message_list)
                    raise ValueError(
                        "Only one entry per message supported for Fireworks, unless it's a TextResult + ToolCall combination"
                    )

            # Standard case: Handle a message list with a single message
            message = message_list[0]
            if isinstance(message, TextPrompt):
                fireworks_messages.append({"role": "user", "content": message.text})
            elif isinstance(message, TextResult):
                fireworks_messages.append(
                    {"role": "assistant", "content": message.text}
                )
            elif isinstance(message, ToolCall):
                fireworks_messages.append(
                    {
                        "role": "assistant",
                        "content": "",
                        "tool_calls": [
                            {
                                "type": "function",
                                "id": message.tool_call_id,
                                "function": {
                                    "name": message.tool_name,
                                    "arguments": json.dumps(message.tool_input),
                                },
                            }
                        ],
                    }
                )
            elif isinstance(message, ToolFormattedResult):
                fireworks_messages.append(
                    {
                        "role": "tool",
                        "tool_call_id": message.tool_call_id,
                        "content": message.tool_output,
                    }
                )
            else:
                print(
                    f"Unknown message type: {type(message)}, expected one of TextPrompt, TextResult, ToolCall, ToolFormattedResult"
                )
                raise ValueError(f"Unknown message type: {type(message)}")

        # Convert tools to Fireworks format
        fireworks_tools = []
        for tool in tools:
            fireworks_tools.append(
                {
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.input_schema,
                    },
                }
            )

        # Convert tool_choice to Fireworks format
        if tool_choice is None:
            fireworks_tool_choice = "auto" if fireworks_tools else "none"
        elif tool_choice["type"] == "any":
            fireworks_tool_choice = "required"
        elif tool_choice["type"] == "auto":
            fireworks_tool_choice = "auto"
        elif tool_choice["type"] == "tool":
            fireworks_tool_choice = {
                "type": "function",
                "function": {"name": tool_choice["name"]},
            }
        else:
            raise ValueError(f"Unknown tool_choice type: {tool_choice['type']}")

        # Make API call with retries
        response: Any = None
        for retry in range(self.max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=fireworks_messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    tools=fireworks_tools if fireworks_tools else [],
                    tool_choice=fireworks_tool_choice,
                )
                break
            except Exception as e:
                if retry == self.max_retries - 1:
                    print(f"Failed Fireworks request after {retry + 1} retries")
                    raise e
                else:
                    print(f"Retrying Fireworks request: {retry + 1}/{self.max_retries}")
                    # Sleep 4-6 seconds with jitter to avoid thundering herd
                    time.sleep(5 * random.uniform(0.8, 1.2))

        # Convert response back to our format
        augment_messages = []
        assert response is not None
        message = response.choices[0].message

        # Standard processing for models that natively support tools
        # Handle tool calls
        if message.tool_calls:
            for tool_call in message.tool_calls:
                try:
                    tool_input = json.loads(tool_call.function.arguments)
                except json.JSONDecodeError as e:
                    print(
                        f"Failed to parse tool arguments: {tool_call.function.arguments}"
                    )
                    print(f"JSON parse error: {str(e)}")
                    raise ValueError(f"Invalid JSON in tool arguments: {str(e)}") from e

                augment_messages.append(
                    ToolCall(
                        tool_name=tool_call.function.name,
                        tool_input=tool_input,
                        tool_call_id=tool_call.id,
                    )
                )
        # Handle text content
        elif message.content:
            augment_messages.append(TextResult(text=message.content))
        else:
            raise ValueError(f"Unknown message type: {message}")

        # Prepare metadata
        assert response.usage is not None
        message_metadata = {
            "raw_response": response,
            "input_tokens": response.usage.prompt_tokens,
            "output_tokens": response.usage.completion_tokens,
        }

        return augment_messages, message_metadata


def get_client(client_name: str, **kwargs) -> LLMClient:
    """Get a client for a given client name."""
    if client_name == "anthropic-vertex":
        return AnthropicVertexClient(**kwargs)
    elif client_name == "anthropic-direct":
        return AnthropicDirectClient(**kwargs)
    elif client_name == "openai-direct":
        return OpenAIDirectClient(**kwargs)
    elif client_name == "gemini-vertex":
        return GeminiVertexClient(**kwargs)
    elif client_name == "qwen-fastforward":
        return QwenFastForwardClient(**kwargs)
    elif client_name == "fireworks":
        return FireworksClient(**kwargs)
    else:
        raise ValueError(f"Unknown client name: {client_name}")
