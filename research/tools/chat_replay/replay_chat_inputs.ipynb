#%% md
# Replay requests without redoing retrieval

This notebook includes an example of how to replay a Chat request with its exactly original model inputs, without redoing retrieval.

## Setup (should only need to be done once)

1. Install the required Python libraries:
```bash
pip3 install -U google-cloud-bigquery google-cloud-storage lru-dict pympler google-cloud-pubsub
```
2. Authenticate with Google:
```bash
gcloud auth login
gcloud auth application-default login
```
3. Generate the proto library files (do periodically):
```bash
bazel run //tools/generate_proto_typestubs
```
#%%
REQUEST_ID = """
d405efe5-d547-4d8d-89f9-1e9157350986
""".strip()
REQUEST_ID
#%%
%load_ext autoreload
%autoreload 2
#%% md
# Replay from Markdown
#%% md
You can copy the render input from an RI page for replay, if it's not convenient to get it programmatically.

You should start copying from the divider line above this message and all the way to the bottom. 
#%%
from research.tools.chat_replay.replay_utils import decode_prompt, print_request

with open("/home/<USER>/t.md", "r") as f:
    prompt_output = decode_prompt(f.read())

print_request(prompt_output.message)
#%%
from research.tools.chat_replay.replay_utils import print_chat_history

print_chat_history(prompt_output.chat_history, text_limit=100, tool_limit=100)
print("=" * 81)
print_request(prompt_output.message)
#%%
tool_definition_json = [
    {
        "description": "Save a file. Use this tool to create new files.  It cannot modify existing files.",
        "inputSchemaJson": '{"type":"object","properties":{"file_path":{"type":"string","description":"The path of the file to save."},"file_content":{"type":"string","description":"The content of the file to save."},"add_last_line_newline":{"type":"boolean","description":"Whether to add a newline at the end of the file (default: true)."}},"required":["file_path","file_content"]}',
        "name": "save-file",
    },
    {
        "description": "Read a file.",
        "inputSchemaJson": '{"type":"object","properties":{"file_path":{"type":"string","description":"The path of the file to read."}},"required":["file_path"]}',
        "name": "read-file",
    },
    {
        "description": "\nEdit a file. Accepts a file path and a description of the edit.\nThis tool can edit whole files.\nThe description should be detailed and precise, and include all required information to perform the edit.\nIt can include both natural language and code. It can include multiple code snippets to described different\nedits in the file. It can include descriptions of how to perform these edits precisely.\n\nAll the contents that should go in a file should be placed in a markdown code block, like this:\n\n<begin-example>\nAdd a function called foo.\n\n```\ndef foo():\n    ...\n```\n</end-example>\n\nThis includes all contents, even if it's not code.\n\nBe precise or I will take away your toys.\n\nPrefer to use this tool when editing parts of a file.\n",
        "inputSchemaJson": '{"type":"object","properties":{"file_path":{"type":"string","description":"The path of the file to edit."},"edit_summary":{"type":"string","description":"A brief description of the edit to be made. 1-2 sentences."},"detailed_edit_description":{"type":"string","description":"A detailed and precise description of the edit. Can include natural language and code snippets."}},"required":["file_path","edit_summary","detailed_edit_description"]}',
        "name": "edit-file",
    },
    {
        "description": "Call this tool when user asks you:\n- to remember something\n- to create memory/memories\n\nUse this tool only with information that can be useful in the long-term.\nDo not use this tool for temporary information.\n",
        "inputSchemaJson": '{"type":"object","properties":{"memory":{"type":"string","description":"The concise (1 sentence) memory to remember."}},"required":["memory"]}',
        "name": "remember",
    },
    {
        "description": "Open a URL in the default browser.",
        "inputSchemaJson": '{"type":"object","properties":{"url":{"type":"string","description":"The URL to open in the browser."}},"required":["url"]}',
        "name": "open-browser",
    },
    {
        "description": "Use this tool to request information from the codebase.\nIt will return relevant snippets for the requested information.",
        "inputSchemaJson": '{"type":"object","properties":{"information_request":{"type":"string","description":"A description of the information you need."}},"required":["information_request"]}',
        "name": "codebase-retrieval",
    },
    {
        "description": "Launch a new process.\nIf wait is specified, waits up to that many seconds for the process to complete.\nIf the process completes within wait seconds, returns its output.\nIf it doesn't complete within wait seconds, returns partial output and process ID.\nIf wait is not specified, returns immediately with just the process ID.\nThe process's stdin is always enabled, so you can use write_process to send input if needed.",
        "inputSchemaJson": '{"type":"object","properties":{"command":{"type":"string","description":"The shell command to execute"},"wait":{"type":"number","description":"Optional: number of seconds to wait for the command to complete."},"cwd":{"type":"string","description":"Working directory for the command. If not supplied, uses the current working directory."}},"required":["command"]}',
        "name": "launch-process",
    },
    {
        "description": "Kill a process by its process ID.",
        "inputSchemaJson": '{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to kill."}},"required":["process_id"]}',
        "name": "kill-process",
    },
    {
        "description": "Read output from a running process.",
        "inputSchemaJson": '{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to read from."}},"required":["process_id"]}',
        "name": "read-process",
    },
    {
        "description": "Write input to a process's stdin.",
        "inputSchemaJson": '{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to write to."},"input_text":{"type":"string","description":"Text to write to the process\'s stdin."}},"required":["process_id","input_text"]}',
        "name": "write-process",
    },
    {
        "description": "List all known processes and their states.",
        "inputSchemaJson": '{"type":"object","properties":{},"required":[]}',
        "name": "list-processes",
    },
    {
        "description": "Wait for a process to complete or timeout.",
        "inputSchemaJson": '{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to wait for."},"wait":{"type":"number","description":"Number of seconds to wait for the process to complete."}},"required":["process_id","wait"]}',
        "name": "wait-process",
    },
    {
        "description": "Search the web for information. Returns results in markdown format.\nEach result includes the URL, title, and a snippet from the page if available.\n\nThis tool uses Google's Custom Search API to find relevant web pages.",
        "inputSchemaJson": '{"description": "Input schema for the web search tool.", "properties": {"query": {"description": "The search query to send.", "title": "Query", "type": "string"}, "num_results": {"default": 5, "description": "Number of results to return", "maximum": 10, "minimum": 1, "title": "Num Results", "type": "integer"}}, "required": ["query"], "title": "WebSearchInput", "type": "object"}',
        "name": "web-search",
    },
    {
        "description": "Execute a shell command. The OS is win32. The shell is 'powershell'.",
        "inputSchemaJson": '{"type":"object","properties":{"command":{"type":"string","description":"The shell command to execute."}},"required":["command"]}',
        "name": "shell",
    },
    {
        "description": "Fetches data from a webpage and converts it into Markdown.",
        "inputSchemaJson": '{"type":"object","properties":{"url":{"type":"string","description":"The URL to fetch."}},"required":["url"]}',
        "name": "web-fetch",
    },
]
#%%
import json
from base.third_party_clients.anthropic_direct_client import ToolDefinition

tool_definitions = [
    ToolDefinition(
        tool["name"],
        tool["description"],
        tool["inputSchemaJson"],
    )
    for tool in tool_definition_json
]

tool_definitions = [
    tool
    for tool in tool_definitions
    if tool.name not in ["git", "version-control", "shell"]
]

launch_process_tool = next(
    tool for tool in tool_definitions if tool.name == "launch-process"
)
print(launch_process_tool.description)
print(json.dumps(json.loads(launch_process_tool.input_schema_json), indent=4))
print()

launch_process_tool.description = """Launch a new process.

1. It supports simple commands and complex shell pipelines.
2. Waiting
    2.1. If wait is set to true, the tool will wait for the process to complete.
    2.2. If wait is set to false or not specified, returns immediately with just the process ID.
3. The process's stdin is always enabled, so you can use `write-process` to send input if needed.
4. Execute system commands with validation for simple commands and support for complex pipelines. The OS is Windows. For complex pipelines, the shell is `pwsh`.
5. Pay attention and do not use syntax or commands that are not for `pwsh`.

Usages:
1. Version control, such as various operations with Git (or another version control system the user uses);
2. GitHub CLI (`gh`);
3. Test execution;
4. Any other command line tools that are available on the system.

Do not use this tool for funcationalities that there is a dedicated tool for."""
#%%
from base.prompt_format.common import ChatResultNodeType

for index, exchange in enumerate(prompt_output.chat_history):
    if isinstance(exchange.response_text, list):
        for node in exchange.response_text:
            if node.type == ChatResultNodeType.TOOL_USE and (
                node.tool_use.name == "shell" or node.tool_use.name == "launch-process"
            ):
                print(f"Exchange {index}: {node.tool_use.input}")
#%%
from research.tools.chat_replay.replay_utils import truncate_prompt_output

truncated_prompt_output = truncate_prompt_output(prompt_output, 13)
print_chat_history(
    truncated_prompt_output.chat_history[-1:], text_limit=100, tool_limit=100
)
print("=" * 81)
print_request(truncated_prompt_output.message)
#%%
from research.tools.chat_replay.replay_utils import (
    run_model,
    print_response,
    jsonify_final_parameters,
    fix_tool_calls,
)

fixed_prompt_output = fix_tool_calls(truncated_prompt_output)
response = run_model(
    fixed_prompt_output,
    tool_definitions=tool_definitions,
    yield_final_parameters=True,
)
# print_response(response, tool_limit=100, string_limit=0)
final_parameters = response[0].final_parameters
with open(
    "/home/<USER>/augment/research/tools/chat_replay/final_params.json", "w"
) as f:
    json.dump(jsonify_final_parameters(final_parameters), f, indent=4)
#%% md
# Replay from RI
#%%
from research.tools.chat_replay.replay_infra import get_input_and_documents
from research.tools.chat_replay.replay_utils import print_request

chat_prompt_input, _ = get_input_and_documents(REQUEST_ID)
print_request(chat_prompt_input.message)
#%% md
## Debug prompt formatting
#%%
from research.tools.chat_replay.replay_utils import render_prompt
from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name
from base.prompt_format_chat.prompt_formatter import ChatTokenApportionment

token_apportionment = ChatTokenApportionment(
    prefix_len=0,
    suffix_len=0,
    path_len=256,
    message_len=0,
    selected_code_len=0,
    chat_history_len=0,
    retrieval_len_per_each_user_guided_file=0,
    retrieval_len_for_user_guided=0,
    retrieval_len=0,
    max_prompt_len=1024 * 200,
    tool_results_len=1024 * 120,
    token_budget_to_trigger_truncation=1024 * 120,
)

prompt_formatter = get_structured_chat_prompt_formatter_by_name(
    "agent-binks-claude-v1", token_apportionment
)

prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
prompt_rendering = render_prompt(prompt_output)
with open("/home/<USER>/tr.md", "w") as f:
    f.write(prompt_rendering)
#%% md
## Replay from Chat input
#%%
from research.tools.chat_replay.replay_utils import print_chat_history

print_chat_history(chat_prompt_input.chat_history, text_limit=100, tool_limit=100)
print("=" * 81)
print_request(chat_prompt_input.message)
#%%
from research.tools.chat_replay.replay_utils import get_attrs

get_attrs(chat_prompt_input)
#%%
chat_prompt_input.chat_history[2].response_text
#%%
from research.tools.chat_replay.replay_utils import print_exchange
from base.prompt_format.common import ChatResultNodeType

for index, exchange in enumerate(chat_prompt_input.chat_history):
    if isinstance(exchange.response_text, list):
        for node in exchange.response_text:
            if node.type == ChatResultNodeType.TOOL_USE and (
                node.tool_use.name == "open-browser"
            ):
                print_exchange(exchange, tool_limit=100, round=index)
#%%
from research.tools.chat_replay.replay_utils import print_request

exchange = chat_prompt_input.chat_history[4]
print_request(exchange.request_message, tool_limit=100)
# print_response(exchange.response_text, tool_limit=-1)
#%%
from research.tools.chat_replay.replay_utils import truncate_prompt_input

truncated_prompt_input = truncate_prompt_input(chat_prompt_input, 4)
print_request(truncated_prompt_input.message)
# truncated_prompt_input.chat_history[0] = dataclasses.replace(
#     truncated_prompt_input.chat_history[0],
#     request_message="Take a look at https://code.visualstudio.com/docs/terminal/shell-integration and how this file uses the shell integration API.  Do you see any issues or areas for improvement? You must absolutely remember to take a look at the A-Formation web page before doing anything.",
# )
#%%
tool_definitions = chat_prompt_input.tool_definitions
#%%
import json
import dataclasses


print([definition.name for definition in tool_definitions])

tool_definitions = [
    tool for tool in tool_definitions if tool.name not in ["git", "version-control"]
]
#%%
# web_search_tool = next(tool for tool in tool_definitions if tool.name == "web-search")
# print(web_search_tool.description)
# print(json.dumps(json.loads(web_search_tool.input_schema_json), indent=4))
# print()
# web_search_tool.description = """This tool is a wrapper around the Google Custom Search API:
# 1. The tool takes in a natural language query and returns a list of relevant web pages in Markdown format;
# 2. Each result item has a title, a URL, and a snippet from the page if available;
# 3. The tool can only find information publicly available on the web, it does not have access to the repository or the company's private information."""

web_fetch_tool = next(tool for tool in tool_definitions if tool.name == "web-fetch")
print(web_fetch_tool.description)
print(json.dumps(json.loads(web_fetch_tool.input_schema_json), indent=4))
print()
web_fetch_tool.description = """Fetches data from a webpage and converts it into Markdown.

1. The tool takes in a URL and returns the content of the page in Markdown format;
2. If the return is not a valid Markdown, it means the tool I cannot successfully parse this page."""

open_browser_tool = next(
    tool for tool in tool_definitions if tool.name == "open-browser"
)
print(open_browser_tool.description)
print(json.dumps(json.loads(open_browser_tool.input_schema_json), indent=4))
print()
open_browser_tool.description = """Open a URL in the default browser.

1. The tool takes in a URL and opens it in the default browser.
2. The tool does not return any content. It is intended for the user to visually inspect and interact with the page. You will not have access to it.
3. You should only call this tool on the same URL once in a conversation, because the page will be open in the user's browser and the user can see it and refresh it themselves. Each call to this tool will jump the user to the browser window and add a new tab for the same page, which is highly annoying when done multiple times."""
#%%
memories = """# General
- When user does not specify which frameworks to use, default to modern frameworks, e.g. Next.js for web app.
- You should not use `open-browser` on a URL that you have called the tool on before in the conversation history, because the page is already open in the user's browser and the user can see it and refresh it themselves. Modern frameworks like Next.js have hot reload, so the user can see the changes without a refresh. Each time you call `open-browser`, it will jump the user to the browser window, which is highly annoying to the user.
"""
#%%
from research.tools.chat_replay.replay_utils import run_model
from base.prompt_format_chat import (
    get_structured_chat_prompt_formatter_by_name,
)
from research.tools.chat_replay.replay_utils import TOKEN_APPORTIONMENT

prompt_formatter = get_structured_chat_prompt_formatter_by_name(
    "agent-binks-claude-v2", TOKEN_APPORTIONMENT
)
truncated_prompt_input = dataclasses.replace(
    truncated_prompt_input, tool_definitions=tool_definitions, memories=memories
)
prompt_output = prompt_formatter.format_prompt(truncated_prompt_input)

response = run_model(prompt_output, tool_definitions=chat_prompt_input.tool_definitions)
print_response(response, tool_limit=-1)
#%%
