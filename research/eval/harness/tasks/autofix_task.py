"""The task for evaluating the autofix system."""

from dataclasses import dataclass

import numpy as np
from research.core.types import Document
from research.eval.harness.systems.autofix_system import (
    AutofixSystemInput,
    AutofixSystemOutput,
)
from research.eval.harness.tasks.abs_task import AbstractTask
from research.autofix.autofix_eval_static import (
    repo_change_to_docs,
    compute_scores,
)
from research.eval.harness.systems import abs_system
import json
import pathlib
import compress_pickle
import pickle
import logging
import time
from tqdm import tqdm

logger = logging.getLogger("tasks.autofix_task")


@dataclass(frozen=True)
class AutofixTaskConfig:
    dataset_path: str
    limit: int | None = None


class AutofixTask(AbstractTask[AutofixSystemInput, AutofixSystemOutput]):
    version: str = "1.0"

    def __init__(self, config: AutofixTaskConfig):
        self.config = config

        self.examples: list[AutofixSystemInput] = compress_pickle.load(
            self.config.dataset_path
        )
        if self.config.limit is not None:
            logger.info(
                f"Limiting from {len(self.examples)} to {self.config.limit} examples."
            )
            self.examples = self.examples[: self.config.limit]

        self.all_docs = [
            doc
            for example in self.examples
            for doc in repo_change_to_docs(example.breaking_change)
        ]

        logger.info(
            f"Loaded {len(self.examples)} examples from {self.config.dataset_path}"
        )
        logger.info(
            f"Loaded {len(self.all_docs)} documents from {self.config.dataset_path}"
        )

    def __getitem__(self, index: int) -> tuple[AutofixSystemInput, list[Document]]:
        example_docs = repo_change_to_docs(self.examples[index].breaking_change)
        return self.examples[index], example_docs

    def __len__(self) -> int:
        return len(self.examples)

    @classmethod
    def from_yaml_config(cls, config: dict) -> "AutofixTask":
        """Returns AutofixTask object constructed using a config dictionary."""

        formatted_config = AutofixTaskConfig(**config)
        return cls(
            formatted_config,
        )

    def execute(
        self,
        model_input: AutofixSystemInput,
        generation: str,
        timeout: float | None = None,
    ) -> dict:
        raise NotImplementedError(
            "Autofix does not support this API because generation is of type string."
        )

    def run(
        self,
        system: abs_system.AbstractSystem[AutofixSystemInput, AutofixSystemOutput],
        output_path: str | pathlib.Path,
        output_prefix: str = "",
    ) -> dict:
        """Return the results from running the task against the model.

        May have side effects; e.g. generating code and saving in a file.

        Args:
          system: The System to run the task against.
          output_path: The path to an existing directory to which artifacts can be written.
          output_prefix: Optional prefix to include with files to prevent collisions.
        """

        system.clear_retriever()
        system.add_docs(self.all_docs)

        scores_items = []
        summary_scores = {}

        time_start = time.time()
        outputs = system.generate_parallel(self.examples)
        time_end = time.time()
        logger.info(
            f"generate_parallel for {len(self.examples)} examples took {time_end - time_start:.2f} seconds"
        )

        for model_input, output in tqdm(list(zip(self.examples, outputs))):
            suggested_change = output.suggested_change

            assert model_input.fixing_change is not None
            exact_match_score, edit_distance_score, overlap_score, noise_score = (
                compute_scores(model_input.fixing_change, suggested_change)
            )
            scores_items.append(
                {
                    "exact_match_score": float(exact_match_score),
                    "edit_distance_score": float(edit_distance_score),
                    "overlap_score": float(overlap_score),
                    "noise_score": float(noise_score),
                }
            )

        avg_exact_match_score = np.mean(
            [item["exact_match_score"] for item in scores_items]
        )
        avg_edit_distance_score = np.mean(
            [item["edit_distance_score"] for item in scores_items]
        )
        avg_overlap_score = np.mean([item["overlap_score"] for item in scores_items])
        avg_noise_score = np.mean([item["noise_score"] for item in scores_items])
        overlap_score_easy = np.mean(
            [item["overlap_score"] > 0.0 for item in scores_items]
        )
        overlap_score_hard = np.mean(
            [item["overlap_score"] == 1.0 for item in scores_items]
        )

        summary_scores["avg_exact_match_score"] = avg_exact_match_score
        summary_scores["avg_edit_distance_score"] = avg_edit_distance_score
        summary_scores["avg_overlap_score"] = avg_overlap_score
        summary_scores["overlap_score_easy"] = overlap_score_easy
        summary_scores["overlap_score_hard"] = overlap_score_hard
        summary_scores["avg_noise_score"] = avg_noise_score

        logger.info(f"Summary scores: {summary_scores}")

        if isinstance(output_path, str):
            output_path = pathlib.Path(output_path)
        if output_path.exists():
            file_name = f"{output_prefix}_{system.name}_{self.name}"
            out_file = output_path / f"{file_name}_scores.json"
            logger.info(f"Writing results to {out_file}")

            with open(out_file, "w") as f:
                json.dump(
                    {"summary_scores": summary_scores, "score_items": scores_items}, f
                )

            logger.info(f"Writing artifacts to {out_file}")
            artifacts_out_file = output_path / f"{file_name}_artifacts.pkl"
            with open(artifacts_out_file, "wb") as f:
                pickle.dump(outputs, f)

        return {
            "summary_scores": summary_scores,
            "score_items": scores_items,
        }
