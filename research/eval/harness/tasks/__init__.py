"""The Task Interface and Implementations."""

import copy
import logging

from research.eval.harness.tasks.abs_task import AbstractTask, DocsType
from research.eval.harness.tasks.api_call_task import SUPPORTED_DATASET2DIR, ApiCallTask
from research.eval.harness.tasks.augment_qa_task import Augment<PERSON>ATask
from research.eval.harness.tasks.aug_human_eval import AugHumanEval
from research.eval.harness.tasks.can_it_edit import CanItEdit
from research.eval.harness.tasks.cceval import CCEval
from research.eval.harness.tasks.chat_eval_task import ChatEvalTask
from research.eval.harness.tasks.edit_eval_task import EditEvalTask, PREditEvalTask
from research.eval.harness.tasks.fimeval import FIMEval
from research.eval.harness.tasks.hindsight import HindsightCompletionTask
from research.eval.harness.tasks.human_eval import HumanEval
from research.eval.harness.tasks.human_eval_fim import HumanEvalFim
from research.eval.harness.tasks.human_eval_instruct import HumanEvalInstruct
from research.eval.harness.tasks.hydra_task import HydraTask
from research.eval.harness.tasks.inficoder_eval_task import Infi<PERSON><PERSON>r<PERSON>valTask
from research.eval.harness.tasks.mbpp import MBPP
from research.eval.harness.tasks.next_edit_classification_task import (
    NextEditClassificationTask,
)
from research.eval.harness.tasks.next_edit_gen_eval_task import NextEditGenEvalTask
from research.eval.harness.tasks.next_edit_location_eval_task import (
    NextEditLocationEvalTask,
)
from research.eval.harness.tasks.pinocchio_instruct import PinocchioInstructTask
from research.eval.harness.tasks.autofix_task import AutofixTask
from research.eval.harness.tasks.swe_bench_task import SWEBenchTask


def create_task(config: dict):
    """The factory function to create the task."""
    logging.debug(f"Creating task {config}")
    config = copy.deepcopy(config)
    if "name" not in config:
        raise ValueError(f"Did not find the name field in config : {config.keys()}")
    name = config.pop("name").lower()
    if name == "humaneval":
        return HumanEval.from_yaml_config(config)
    elif name == "humaneval_fim":
        return HumanEvalFim.from_yaml_config(config)
    elif name == "humaneval_instruct":
        return HumanEvalInstruct.from_yaml_config(config)
    elif name == "aug_humaneval":
        return AugHumanEval.from_yaml_config(config)
    elif name == "hydra":
        return HydraTask.from_yaml_config(config)
    elif name in ("api", "api_call"):
        dataset = config.get("dataset", None)
        return ApiCallTask(
            dataset_dir=SUPPORTED_DATASET2DIR[dataset], limit=config.get("limit", None)
        )
    elif name == "mbpp":
        return MBPP.from_yaml_config(config)
    elif name == "cceval":
        return CCEval.from_yaml_config(config)
    elif name == "pinocchio_instruct":
        return PinocchioInstructTask.from_yaml_config(config)
    elif name == "edit_eval_task":
        return EditEvalTask.from_yaml_config(config)
    elif name == "pr_edit_eval_task":
        return PREditEvalTask.from_yaml_config(config)
    elif name == "can_it_edit":
        return CanItEdit.from_yaml_config(config)
    elif name == "next_edit_location":
        return NextEditLocationEvalTask.from_yaml_config(config)
    elif name == "next_edit_gen":
        return NextEditGenEvalTask.from_yaml_config(config)
    elif name == "next_edit_classification":
        return NextEditClassificationTask.from_yaml_config(config)
    elif name == "chat_eval_task":
        return ChatEvalTask.from_yaml_config(config)
    elif name == "inficoder_eval_task":
        return InfiCoderEvalTask.from_yaml_config(config)
    elif name == "hindsight":
        return HindsightCompletionTask.from_yaml_config(config)
    elif name == "fimeval":
        return FIMEval(**config)
    elif name == "augment_qa":
        return AugmentQATask.from_yaml_config(config)
    elif name == "autofix":
        return AutofixTask.from_yaml_config(config)
    elif name == "swe_bench":
        return SWEBenchTask.from_yaml_config(config)
    else:
        raise ValueError(f"Task name not recognized: {name}")


__all__ = [
    "AbstractTask",
    "AugHumanEval",
    "create_task",
    "HumanEval",
    "HumanEvalFim",
    "HydraTask",
    "ApiCallTask",
    "MBPP",
    "DocsType",
    "CCEval",
    "CanItEdit",
    "NextEditLocationEvalTask",
    "AutofixTask",
    "SWEBenchTask",
]
