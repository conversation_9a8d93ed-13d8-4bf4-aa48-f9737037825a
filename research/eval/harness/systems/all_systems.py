"""This module provides a collection of systems for evaluation harness in research augmentations.

The systems include:
    - BasicSystem: A fundamental evaluation system.
    - GoldSystem: A system that works with gold-standard data for evaluation.
    - ModelServerSystem: A system interfacing with a model server for research evaluations.
    - RAGSystem: A system that utilizes a Retriever-Augmented Generation model.
    - RAGWithRerankerSystem: Extends RAGSystem with additional reranking capabilities.

Additionally, the module provides an abstract base for system implementations, as well as
functions to manage and manipulate registered systems:
    - AbstractSystem: Base class for all evaluation systems.
    - get_system: Fetches a registered system.
    - list_systems: Lists all available systems.
    - register_system: Registers a new system for use.
"""

from research.eval.harness.systems.abs_system import (
    AbstractSystem,
    get_system,
    list_systems,
    register_system,
)
from research.eval.harness.systems.basic_RAG_system import RAGSystem
from research.eval.harness.systems.basic_RAG_system_with_reranker import (
    RAGWithRerankerSystem,
)
from research.eval.harness.systems.basic_system import BasicSystem
from research.eval.harness.systems.chat_RAG_system import ChatRAGSystem
from research.eval.harness.systems.code_edit_system_with_retrieval import (
    RAGCodeEditSystem,
)
from research.eval.harness.systems.comparison_system import ComparisonSystem
from research.eval.harness.systems.droid_code_edit_system import DroidCodeEditSystem
from research.eval.harness.systems.droid_repo_code_edit_system import (
    DroidRepoCodeEditSystem,
)
from research.eval.harness.systems.elden_system import EldenSystem
from research.eval.harness.systems.elden_system_w_prod_formatter import (
    ProdEldenSystem,
)
from research.eval.harness.systems.gold_system import GoldSystem
from research.eval.harness.systems.next_edit_gen_system import (
    NextEditGenSystem,
)
from research.eval.harness.systems.next_edit_location_system import (
    BasicNextEditLocationSystem,
)
from research.eval.harness.systems.next_edit_reranker_system import (
    NextEditRerankerSystem,
)
from research.eval.harness.systems.autofix_system import AutofixSystem
from research.eval.harness.systems.online_rag_system import OnlineRAGSystem
from research.eval.harness.systems.remote_chat_system import RemoteChatSystem
from research.eval.harness.systems.remote_completion_system import (
    RemoteCompletionSystem,
)
from research.eval.harness.systems.remote_edit_system import RemoteEditSystem
from research.model_server.mock_system import MockSystem
from research.model_server.model_server_system import ModelServerSystem
from research.eval.harness.systems.agent_system import AgentSystem

__all__ = [
    "AutofixSystem",
    "AgentSystem",
    "BasicSystem",
    "BasicNextEditLocationSystem",
    "ChatRAGSystem",
    "NextEditGenSystem",
    "NextEditRerankerSystem",
    "GoldSystem",
    "ModelServerSystem",
    "MockSystem",
    "RAGSystem",
    "RAGWithRerankerSystem",
    "ComparisonSystem",
    "AbstractSystem",
    "OnlineRAGSystem",
    "get_system",
    "list_systems",
    "register_system",
    "RAGCodeEditSystem",
    "DroidCodeEditSystem",
    "ProdEldenSystem",
    "DroidRepoCodeEditSystem",
    "RemoteChatSystem",
    "RemoteEditSystem",
    "RemoteCompletionSystem",
]
