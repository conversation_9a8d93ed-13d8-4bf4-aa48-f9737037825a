"""This is the interface for the autofix system."""

from __future__ import annotations

from dataclasses import asdict, dataclass, field
from typing import Collection, Sequence
from anthropic import AsyncAnthropic

from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File
from base.prompt_format_autofix.common import (
    PromptChunkWithLines,
    AutofixSteeringMessage,
)
from research.core.types import Document, DocumentId
from research.eval.harness.systems.abs_system import AbstractSystem, register_system
from research.models import GenerativeLanguageModel
from research.utils.repo_change_utils import RepoChange
from research.autofix.autofix_manager import (
    AutofixManager,
    AutofixPredictEditLocationsInput,
    AutofixContextRetrieveInput,
    AutofixContextOutput,
)
from research.autofix.autofix_eval_static import (
    edit_locations_from_repo_change,
    generate_fix_suggestion,
    repo_change_to_docs,
    merge_chunks,
)
import asyncio
import os
import sys
import nest_asyncio
from tqdm.asyncio import tqdm_asyncio
from research.environments import get_eng_secret
import time
import logging
from tqdm import tqdm

nest_asyncio.apply()

logger = logging.getLogger(__name__)


def handle_exception(loop, context):
    # Extract exception details
    exception = context.get("exception")
    message = context.get("message")

    # Log the full error
    print(f"Caught unhandled exception: {message}", file=sys.stderr)
    if exception:
        print(f"Exception details: {exception}", file=sys.stderr)

    # Force flush stdout/stderr
    sys.stdout.flush()
    sys.stderr.flush()

    # Terminate the process immediately
    os._exit(1)


@dataclass(frozen=True)
class AutofixSystemConfig:
    """Schema for configuring an AutofixSystem."""

    use_gold_locations: bool = False
    """Whether to add gold locations to candidate locations."""

    use_localization: bool = True
    """Whether to use localization model to predict candidate locations."""

    top_k_edit_locations: int = 64
    """The number of edit locations to predict."""

    context_retrieval: bool = False
    """Whether to use context retrieval."""

    max_concurrent: int = 10
    """The maximum number of concurrent requests to make."""


@dataclass(frozen=True)
class AutofixSystemInput:
    """The input to the autofix system."""

    command_output: str
    """The output from the command."""

    breaking_change: RepoChange
    """The breaking change leading up to the error."""

    doc_ids: frozenset[DocumentId]
    """The sequence of document ids that are available for retrieval."""

    fixing_change: RepoChange | None = None
    """Optionally pass the fixing changes to be able to generate from gold locations."""

    repo_name: str | None = None
    """The name of the repository."""

    breaking_commit_sha: str | None = None
    """The sha of the commit associated with the command output."""

    fixing_commit_sha: str | None = None
    """The sha of the commit that fixes the error."""

    pr_number: int | None = None
    """The number of the pull request associated with the input."""

    steering_history: Sequence[AutofixSteeringMessage] = field(default_factory=list)
    """The steering history, from most to least recent."""

    def __setstate__(self, state: dict) -> None:
        # For backwards compatibility with eval
        if "steering_history" not in state:
            state["steering_history"] = []
        self.__dict__.update(state)


@dataclass
class AutofixSystemOutput:
    suggested_change: RepoChange
    """The suggested change to fix the error."""

    artifacts: dict | None = None
    """Any additional artifacts that the system may want to return."""


@register_system("autofix")
@dataclass
class AutofixSystem(AbstractSystem[AutofixSystemInput, AutofixSystemOutput]):
    """Abstract interface that encapsulates the autofix application."""

    def __init__(
        self,
        config: AutofixSystemConfig,
    ):
        if "ANTHROPIC_API_KEY" not in os.environ:
            os.environ["ANTHROPIC_API_KEY"] = get_eng_secret("lior-anthropic-api-key")
        if "AUGMENT_TOKEN" not in os.environ:
            os.environ["AUGMENT_TOKEN"] = get_eng_secret("lior-augment-token")

        self.client: AutofixManager | None = None

        self.config = config
        try:
            self.loop = asyncio.get_event_loop()
        except RuntimeError:
            self.loop = asyncio.new_event_loop()
        self.loop.set_debug(True)
        self.loop.set_exception_handler(handle_exception)
        asyncio.set_event_loop(self.loop)

    def load(self):
        """Load the system."""
        self.client = AutofixManager(
            AsyncAnthropic(),
            verbose=False,
            context_retrieval=self.config.context_retrieval,
        )

    def unload(self):
        """Unload the system."""
        if (
            self.client is not None
            and self.client.context_retriever is not None
            and self.client.localization_model is not None
        ):
            self.client.context_retriever.unload()
            self.client.localization_model.unload()

    def generate_parallel(
        self, model_inputs: list[AutofixSystemInput]
    ) -> list[AutofixSystemOutput]:
        sem = asyncio.Semaphore(self.config.max_concurrent)

        async def atomic_async_generate(model_input: AutofixSystemInput):
            async with sem:
                return await self.generate_async(model_input)

        tasks = [atomic_async_generate(model_input) for model_input in model_inputs]
        # Check if we're in a notebook environment
        try:
            from IPython import get_ipython  # type: ignore

            if get_ipython() is not None:
                # We're in a notebook, use tqdm
                return self.loop.run_until_complete(
                    tqdm_asyncio.gather(*tasks, desc="Generating fixes")
                )
        except ImportError:
            pass

        # Not in a notebook, use regular gather
        return self.loop.run_until_complete(asyncio.gather(*tasks))

    async def generate_async(
        self, model_input: AutofixSystemInput
    ) -> AutofixSystemOutput:
        """Returns an AutofixSystemOutput object.

        This object contains the suggested change to fix the error.
        """
        assert self.client is not None
        repo_docs = repo_change_to_docs(model_input.breaking_change)
        repo_doc_ids = [doc.id for doc in repo_docs]
        recent_changes = [
            Modified(
                File(path=str(file.before.path), contents=file.before.code),
                File(path=str(file.after.path), contents=file.after.code),
            )
            for file in model_input.breaking_change.changed_files
            if isinstance(file, Modified)
        ]

        if self.config.use_localization:
            (
                edit_locations,
                localization_scores,
                localization_artifacts,
            ) = await self.client.predict_edit_locations(
                AutofixPredictEditLocationsInput(
                    command_output=model_input.command_output,
                    recent_changes=recent_changes,
                    doc_ids=repo_doc_ids,
                    new_docs=repo_docs,
                    top_k=self.config.top_k_edit_locations,
                    instructions=None,
                )
            )
        else:
            edit_locations = []
            localization_scores = []
            localization_artifacts = {}

        gold_locations: Sequence[PromptChunkWithLines] = []
        if model_input.fixing_change:
            gold_locations = edit_locations_from_repo_change(model_input.fixing_change)
            if self.config.use_gold_locations:
                edit_locations = [*gold_locations, *edit_locations]
                localization_scores = [-1.0] * len(gold_locations) + list(
                    localization_scores
                )

        retrival_output = AutofixContextOutput(generated_query="", retrieved_chunks=[])

        if (
            self.config.context_retrieval
        ):  # If configured to retrieve root cause codebase context
            retrival_output: AutofixContextOutput = await self.client.context_retrieve(
                AutofixContextRetrieveInput(
                    command="./check.sh",
                    command_output=model_input.command_output,
                    recent_changes=model_input.breaking_change,
                    edit_locations=edit_locations,
                    doc_ids=repo_doc_ids,
                    new_docs=repo_docs,
                    top_k=32,
                )
            )

        # Combine the edit locations and retrieved chunks
        edit_locations = [
            *edit_locations,
            *retrival_output.retrieved_chunks,
        ]

        edit_locations = merge_chunks(
            edit_locations,
            model_input.breaking_change,
        )

        suggested_change, fix_plan = await generate_fix_suggestion(
            self.client,
            edit_locations=edit_locations,
            breaking_change=model_input.breaking_change,
            command_output=model_input.command_output,
            command="./check.sh",
            steering_history=model_input.steering_history,
        )
        edited_locations = edit_locations_from_repo_change(suggested_change)

        artifacts = {
            **asdict(fix_plan),
            "gold_edit_locations": [asdict(loc) for loc in gold_locations],
            "edited_locations": [asdict(loc) for loc in edited_locations],
            "localization_scores": [score for score in localization_scores],
            "edit_locations": [asdict(loc) for loc in edit_locations],
            "localization_artifacts": localization_artifacts,
            "context_retrieval_query": retrival_output.generated_query,
            "context_retrieval_chunks": [
                asdict(chunk) for chunk in retrival_output.retrieved_chunks
            ],
            "model_input": asdict(model_input),
        }

        return AutofixSystemOutput(suggested_change, artifacts)

    def generate(self, model_input: AutofixSystemInput) -> AutofixSystemOutput:
        """Returns an AutofixSystemOutput object.

        This object contains the suggested change to fix the error.
        """
        return self.loop.run_until_complete(self.generate_async(model_input))

    def add_docs(self, src_files: Collection[Document]):
        """Ingest a copy of the source code repository."""
        if (
            self.client
            and self.client.context_retriever
            and self.config.context_retrieval
        ):
            self.client.context_retriever.add_docs(src_files)
        if self.client and self.client.localization_model:
            self.client.localization_model.add_docs(src_files)

    def remove_docs(self, doc_ids: Collection[DocumentId]):
        """Remove documents from the retriever."""
        if (
            self.client
            and self.client.context_retriever
            and self.config.context_retrieval
        ):
            self.client.context_retriever.remove_docs(doc_ids)
        if self.client and self.client.localization_model:
            self.client.localization_model.remove_docs(doc_ids)

    def clear_retriever(self):
        """Clear any documents from the retriever.

        This is useful for clearing out the current repo from the retriever.
        """
        if (
            self.client
            and self.client.context_retriever
            and self.config.context_retrieval
        ):
            self.client.context_retriever.remove_all_docs()
        if self.client and self.client.localization_model:
            self.client.localization_model.remove_all_docs()

    def get_model(self) -> GenerativeLanguageModel:
        """Returns the model."""
        raise NotImplementedError()

    @classmethod
    def from_yaml_config(cls, config: dict) -> "AutofixSystem":
        """Returns a System object constructed using a config dictionary."""
        # TODO: Make system configurable.
        parsed_config = AutofixSystemConfig(**config)
        return AutofixSystem(parsed_config)
