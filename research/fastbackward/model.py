"""Implementation of the Transformer for fastbackward.

Need-to-knows about DataClassJsonMixin (talk to arun,xuanyi if you have questions):
- we use it to reliably convert JSON to the dataclass with validation, etc.
- when you use a nested DataClassJsonMixin, such as GluSpec, please do not set any default value. Otherwise, DataClassJsonMixin might be confused about which dataclass type to be used, if multiple fit.
- please always use YourDataClass.schema().load/dump to convert from/to JSON (see a minimal example on why it is necessary at experimental/dxy/learn/dataclass.ipynb)
"""

import inspect
import logging
import math
import typing
from contextlib import contextmanager
from copy import deepcopy
from dataclasses import dataclass
from functools import partial

import dataclasses_json
import torch
import torch.distributed as dist
import torch.nn.functional as F
import torch.utils.checkpoint
from apex.normalization import FusedRMSNorm
from torch import nn

import research.fastbackward.fs_model_parallel as mpu
import research.fastbackward.sequence_parallel as spu
from base.fastforward.positional_embeddings import (
    DeepSeekV1ExtensionConfig,
    RotaryConfig,
)
from research.fastbackward import functional, rotary
from research.fastbackward.attention import FusedAttn, SimpleCachedAttn
from research.fastbackward.flat_model_state import flatten_model
from research.fastbackward.fs_model_parallel.layers import (
    ColumnParallelLinear,
    ParallelEmbedding,
    RowParallelLinear,
)
from research.fastbackward.mixed_precision_adam import MixedPrecisionAdamW

logger = logging.getLogger(__name__)


def _get_norm_factory(norm_type: str):
    if norm_type == "rmsnorm":
        factory = FusedRMSNorm
    elif norm_type == "layernorm":
        factory = nn.LayerNorm
    else:
        raise ValueError(f"Unsupported norm_type: {norm_type}")
    return factory


@dataclass
class DeepSeekV2MLASpec(dataclasses_json.DataClassJsonMixin):
    """The DeepSeek-V2 MLA Spec."""

    hidden_dim: int = 2048
    """The hidden dimension of the input feature tensor."""

    num_heads: int = 1
    """The number of attention key/value heads."""

    v_head_dim: int = 128
    """The hidden dimension of each attention head."""

    q_lora_rank: int | None = None

    kv_lora_rank: int = 512

    qk_rope_head_dim: int = 64

    qk_nope_head_dim: int = 128

    eps: float = 1e-6

    bias: bool = False

    dropout_attn: float = 0.0
    """The dropout probability for the attention weights."""


class DeepSeekV2Attention(nn.Module):
    """Projection into heads -> QKV -> projection back -> residual."""

    def __init__(
        self,
        config: DeepSeekV2MLASpec,
        use_sequence_parallel: bool = False,
        max_seq_len: int = 2048,
    ):
        super().__init__()
        # TODO(Xuanyi): support sequence parallel.
        self.config = config
        self.use_sequence_parallel = use_sequence_parallel
        assert not use_sequence_parallel, "Sequence parallel is not supported yet."
        model_parallel_size = mpu.get_model_parallel_world_size()
        assert self.config.num_heads % model_parallel_size == 0
        self.n_local_heads = self.config.num_heads // model_parallel_size
        self.q_head_dim = config.qk_rope_head_dim + config.qk_nope_head_dim
        self.v_head_dim = config.v_head_dim
        assert self.q_head_dim - self.v_head_dim > 0, (
            f"{self.q_head_dim=} {self.v_head_dim=}, "
            f"{config.qk_rope_head_dim=} {config.qk_nope_head_dim=}"
        )
        self.attn_func_w_cache = SimpleCachedAttn(
            n_local_heads=self.n_local_heads,
            n_local_kv_heads=self.n_local_heads,
            head_dim=self.q_head_dim,
            max_batch_size=1,
            max_seq_len=max_seq_len,
        )
        self.attn_func_wo_cache = FusedAttn(dropout_p=config.dropout_attn, causal=True)

        # This will be shard across model parallelism group and only result int n_local_heads
        if self.config.q_lora_rank is None:
            self.q_proj = ColumnParallelLinear(
                in_features=self.config.hidden_dim,
                out_features=self.config.num_heads * self.q_head_dim,
                bias=False,
                gather_output=False,
                input_already_tensor_parallel=False,
                init_method=lambda x: x,
            )
        else:
            raise NotImplementedError("LoRA is not supported yet.")

        # Repeat this layer per GPU device.
        self.kv_a_proj_with_mqa = nn.Linear(
            self.config.hidden_dim,
            self.config.kv_lora_rank + self.config.qk_rope_head_dim,
            bias=self.config.bias,
        )
        self.kv_a_layernorm = FusedRMSNorm(self.config.kv_lora_rank, eps=config.eps)
        # This will be shard across model parallelism group and only result int n_local_heads
        self.kv_b_proj = ColumnParallelLinear(
            self.config.kv_lora_rank,
            self.config.num_heads
            * (self.q_head_dim - self.config.qk_rope_head_dim + self.v_head_dim),
            bias=False,
            gather_output=False,
            input_already_tensor_parallel=False,
            init_method=lambda x: x,
        )
        # The input is shard across model parallelism group
        self.o_proj = RowParallelLinear(
            self.config.num_heads * self.v_head_dim,
            self.config.hidden_dim,
            bias=self.config.bias,
            input_is_parallel=True,
            init_method=lambda x: x,
        )

    def forward(
        self,
        inputs: torch.Tensor,
        rotary_freqs: rotary.RotaryFreqs | None = None,
        start_pos: int | None = None,
        mask: torch.Tensor | None = None,
    ) -> torch.Tensor:
        bs, seq, _ = inputs.shape

        if self.config.q_lora_rank is None:
            q_proj = self.q_proj(inputs)
        else:
            raise NotImplementedError("Lora is not supported yet.")
        # [batch, num_heads, seq_len, q_head_dim]
        q_proj = q_proj.view(bs, seq, self.n_local_heads, self.q_head_dim)
        q_nope, q_pe = torch.split(
            q_proj, [self.config.qk_nope_head_dim, self.config.qk_rope_head_dim], dim=-1
        )
        # Process KV
        compressed_kv = self.kv_a_proj_with_mqa(inputs)
        compressed_kv, k_pe = torch.split(
            compressed_kv,
            [self.config.kv_lora_rank, self.config.qk_rope_head_dim],
            dim=-1,
        )

        # The shared key for all heads to carry RoPE
        # [batch, 1, seq_len, qk_rope_head_dim]
        k_pe = k_pe.view(bs, seq, 1, self.config.qk_rope_head_dim)

        # [batch, seq_len, num_heads, qk_nope_head_dim + v_head_dim]
        kv = self.kv_b_proj(self.kv_a_layernorm(compressed_kv)).view(
            bs,
            seq,
            self.n_local_heads,
            self.config.qk_nope_head_dim + self.v_head_dim,
        )
        k_nope, v_states = torch.split(
            kv, [self.config.qk_nope_head_dim, self.v_head_dim], dim=-1
        )

        # Transpose QKV to match [batch, seq_len, num_heads, ...]
        v_pad_states = torch.cat(
            [
                v_states,
                v_states.new_zeros(
                    bs, seq, self.n_local_heads, self.q_head_dim - self.v_head_dim
                ),
            ],
            dim=-1,
        )
        assert rotary_freqs is not None

        # For the lite model, there are 192 dimension and the first 64 dimension will apply the RoPE
        q_states = torch.cat(
            [
                rotary.rotary_embed(q_pe, rotary_freqs.cos, rotary_freqs.sin),
                q_nope,
            ],
            dim=-1,
        )
        ke_after_rope = rotary.rotary_embed(k_pe, rotary_freqs.cos, rotary_freqs.sin)

        # NOTE(Xuanyi): copy_to_model_parallel_region is necessary here as the ke_after_rope
        # is shared across different model parallelism devices in the same group.
        # But d_loss / d_ke_after_ropeon on each device at here is just a local gradient and different
        # from each other, so that we need to sum them up to get the correct gradient.
        k_states = torch.cat(
            [
                mpu.copy_to_model_parallel_region(ke_after_rope).repeat(
                    1, 1, self.n_local_heads, 1
                ),
                k_nope,
            ],
            dim=-1,
        )

        # attn_output should be [batch, seq_len, v_head_dim + padded_zeros]
        if start_pos is not None:
            # Decoding phase
            assert (
                not torch.is_grad_enabled()
            ), "This should not be called during training."
            attn_output = self.attn_func_w_cache(
                q_states, k_states, v_pad_states, start_pos=start_pos, mask=mask
            )
        else:
            # Forward phase
            attn_output = self.attn_func_wo_cache(
                q_states, k_states, v_pad_states, start_pos=start_pos, mask=mask
            )
        assert attn_output is not None
        attn_output = attn_output[..., : self.v_head_dim]
        # attn_output is now [batch, seqlen, nheads, headdim]
        output = self.o_proj(
            attn_output.reshape(bs, seq, self.n_local_heads * self.v_head_dim)
        )
        return output


@dataclass
class GenericAttnSpec(dataclasses_json.DataClassJsonMixin):
    """The Generic Attention Spec."""

    hidden_dim: int
    """The hidden dimension of the input feature tensor."""

    n_heads: int
    """The number of attention heads."""

    n_kv_heads: int
    """The number of attention key/value heads."""

    norm_type: str
    """One of "rmsnorm" or "layernorm"."""

    pos_embed_type: str
    """One of "rope" or "absolute".
    "rope" uses standard LLaMA-style RoPE and "absolute" uses absolute position embeddings."""

    bias: bool
    """Whether to use bias in the linear layers."""

    qkv_bias: bool = False
    """Whether to use bias _only_ for the QKV projection. (Needed for Qwen.)"""

    use_qk_norm: bool = False
    """Whether to use RMSNorm for QK. (Needed for Qwen-3.)"""

    dropout_attn: float = 0.0
    """The dropout probability for the self-attention itself."""

    head_dim: int = 0
    """The dimension of each attention head. If 0, calculated as hidden_dim // n_heads."""

    def __post_init__(self):
        assert self.n_kv_heads > 0, f"{self.n_kv_heads=} must be greater than 0."
        assert not (
            self.bias and self.qkv_bias
        ), f"{self.bias=} and {self.qkv_bias=} cannot be both True."
        if self.head_dim == 0:
            self.head_dim = self.hidden_dim // self.n_heads


class Attention(nn.Module):
    """Full self-attention layer: QKV -> Attention -> Output."""

    def __init__(
        self,
        config: GenericAttnSpec,
        layer_id: int,
        use_sequence_parallel: bool = False,
        max_seq_len: int = 2048,
        norm_eps: float = 1e-5,  # Default value matching ModelArgs default
        kv_cache_batch_size: int = 1,  # Initial size for KV cache
    ):
        super().__init__()
        self.layer_id = layer_id
        self.config = config
        self.n_kv_heads = config.n_kv_heads
        model_parallel_size = mpu.get_model_parallel_world_size()
        assert config.n_heads % model_parallel_size == 0
        self.n_local_heads = config.n_heads // model_parallel_size
        if self.n_kv_heads >= model_parallel_size:
            assert self.n_kv_heads % model_parallel_size == 0
            self.n_local_kv_heads = self.n_kv_heads // model_parallel_size
        else:
            assert model_parallel_size % self.n_kv_heads == 0
            self.n_local_kv_heads = 1
        # Use explicit head_dim if provided, otherwise the default value is hidden_dim // n_heads
        # Qwen3 is an example where in some models, num_head * head_dim != hidden_dim
        self.head_dim = config.head_dim
        self.use_sequence_parallel = use_sequence_parallel
        self.norm_eps = norm_eps
        # NOTE(Xuanyi): we create both attention objects for fowward and decoding,
        # even if one did not need decoding, the additional memory cost is small.
        # For example, a LLAMA-70B model needs 1 * 8K * 8 * 128 * 80 = 640MiB for the cache.
        self.attn_func_w_cache = SimpleCachedAttn(
            n_local_heads=self.n_local_heads,
            n_local_kv_heads=self.n_local_kv_heads,
            head_dim=self.head_dim,
            max_batch_size=kv_cache_batch_size,
            max_seq_len=max_seq_len,
        )
        self.attn_func_wo_cache = FusedAttn(dropout_p=config.dropout_attn, causal=True)
        self.wq = ColumnParallelLinear(
            config.hidden_dim,
            config.n_heads * self.head_dim,
            bias=config.qkv_bias or config.bias,
            gather_output=False,
            input_already_tensor_parallel=use_sequence_parallel,
            init_method=lambda x: x,
        )
        self.kv_are_replicated = self.n_kv_heads < model_parallel_size
        kv_layer_factory = partial(
            ColumnParallelLinear,
            # ColumnParallelLinear automatically divides out_features by world size, so:
            # 1. For fully-sharded KV (no replication), ColumnParallelLinear will
            #    correctly calculate local output features from the total output
            #    features.
            # 2. For replicated KV (partially-sharded or fully-replicated), each local
            #    device should have a single KV head (after replication, each device
            #    logically has 1/replication_size KV head). To account for
            #    ColumnParallelLinear's automatic division by world size, out_features
            #    should be multiplied by MP size.
            out_features=model_parallel_size * self.head_dim
            if self.kv_are_replicated
            else self.n_kv_heads * self.head_dim,
            gather_output=False,
            input_already_tensor_parallel=use_sequence_parallel,
            replication_group_name="kv" if self.kv_are_replicated else None,
            init_method=lambda x: x,
        )
        self.wk = kv_layer_factory(
            config.hidden_dim,
            bias=config.qkv_bias or config.bias,
        )
        self.wv = kv_layer_factory(
            config.hidden_dim,
            bias=config.qkv_bias or config.bias,
        )
        self.wo = RowParallelLinear(
            config.n_heads * self.head_dim,
            config.hidden_dim,
            bias=config.bias,
            input_is_parallel=True,
            custom_output_reducer=spu.tensor2seq if use_sequence_parallel else None,
            init_method=lambda x: x,
        )
        if self.config.use_qk_norm:
            # Use the norm_eps passed from TransformerBlock, which comes from ModelArgs
            self.q_norm = FusedRMSNorm(self.head_dim, eps=self.norm_eps)
            self.k_norm = FusedRMSNorm(self.head_dim, eps=self.norm_eps)

    def forward(
        self,
        x: torch.Tensor,
        rotary_freqs: rotary.RotaryFreqs | None = None,
        start_pos: int | None = None,
        mask: torch.Tensor | None = None,
    ):
        # Dimensions
        #   No sequence parallel (seq2tensor is a no-op)
        #   - x and x_gathered: (bsz, seqlen, dim)
        #   Sequence parallel
        #   - x: (bsz * seqlen // mp_size, dim)
        #   - x_gathered: (bsz, seqlen, dim)
        x_gathered = spu.seq2tensor(x)
        bsz, seqlen, _ = x_gathered.shape
        xq, xk, xv = self.wq(x_gathered), self.wk(x_gathered), self.wv(x_gathered)

        xq = xq.view(bsz, seqlen, self.n_local_heads, self.head_dim)
        xk = xk.view(bsz, seqlen, self.n_local_kv_heads, self.head_dim)
        xv = xv.view(bsz, seqlen, self.n_local_kv_heads, self.head_dim)

        if self.config.use_qk_norm:
            xq = self.q_norm(xq)
            xk = self.k_norm(xk)

        if self.config.pos_embed_type == "rope":
            assert rotary_freqs is not None
            xq = rotary.rotary_embed(xq, rotary_freqs.cos, rotary_freqs.sin)
            xk = rotary.rotary_embed(xk, rotary_freqs.cos, rotary_freqs.sin)

        if start_pos is not None:
            # Decoding phase
            assert (
                not torch.is_grad_enabled()
            ), "This should not be called during training."
            attn_output = self.attn_func_w_cache(
                xq, xk, xv, start_pos=start_pos, mask=mask
            )
        else:
            # Forward phase
            attn_output = self.attn_func_wo_cache(xq, xk, xv, mask=mask)

        assert attn_output is not None
        # attn_output is now (bsz, seqlen, nheads, headdim)
        output = self.wo(attn_output.view(bsz, seqlen, -1))
        return output


@dataclass
class MlpSpec(dataclasses_json.DataClassJsonMixin):
    """The Generic MLP Layer Spec."""

    hidden_dim: int
    """The hidden dimension of the MLP layer."""

    bias: bool
    """Whether to use bias in the MLP layer."""


class Mlp(nn.Module):
    """MLP feedforward module."""

    def __init__(
        self,
        config: MlpSpec,
        use_sequence_parallel: bool = False,
    ):
        super().__init__()
        self.config = config
        self.w1 = ColumnParallelLinear(
            config.hidden_dim,
            config.hidden_dim * 4,
            bias=config.bias,
            gather_output=False,
            input_already_tensor_parallel=use_sequence_parallel,
            init_method=lambda x: x,
        )
        self.w2 = RowParallelLinear(
            config.hidden_dim * 4,
            config.hidden_dim,
            bias=config.bias,
            input_is_parallel=True,
            custom_output_reducer=spu.tensor2seq if use_sequence_parallel else None,
            init_method=lambda x: x,
        )

    def forward(self, x):
        x = spu.seq2tensor(x)
        return self.w2(F.gelu(self.w1(x), approximate="tanh"))


@dataclass
class SwiGLUSpec(dataclasses_json.DataClassJsonMixin):
    """The SwiGLU Layer Spec."""

    hidden_dim: int
    """The hidden dimension of the GLU layer."""

    intermediate_size: int
    """The intermediate size of the GLU layer."""

    bias: bool
    """Whether to use bias in the GLU layer."""


class SwiGLU(nn.Module):
    """GLU feedforward module."""

    def __init__(
        self,
        config: SwiGLUSpec,
        use_sequence_parallel: bool = False,
    ):
        super().__init__()
        self.config = config
        self.w1 = ColumnParallelLinear(
            config.hidden_dim,
            config.intermediate_size,
            bias=config.bias,
            gather_output=False,
            input_already_tensor_parallel=use_sequence_parallel,
            init_method=lambda x: x,
        )
        self.w2 = RowParallelLinear(
            config.intermediate_size,
            config.hidden_dim,
            bias=config.bias,
            input_is_parallel=True,
            custom_output_reducer=spu.tensor2seq if use_sequence_parallel else None,
            init_method=lambda x: x,
        )
        self.w3 = ColumnParallelLinear(
            config.hidden_dim,
            config.intermediate_size,
            bias=config.bias,
            gather_output=False,
            input_already_tensor_parallel=use_sequence_parallel,
            init_method=lambda x: x,
        )

    def forward(self, x):
        x = spu.seq2tensor(x)
        h = functional.swiglu(self.w1(x), self.w3(x))
        return self.w2(h)


@dataclass
class DeepSeekV2MoESpec(dataclasses_json.DataClassJsonMixin):
    """The DeepSeek-V2 MoE layer spec."""

    hidden_dim: int
    """The feature dimension of the input tensor."""

    n_routed_experts: int
    """The number of experts in the MoE layer."""

    routed_scaling_factor: float
    """The scaling factor for the routed experts."""

    num_experts_per_token: int
    """The number of experts per token."""

    intermediate_size: int
    """The intermediate size of each routed expert."""

    n_shared_experts: int
    """The number of shared experts."""

    topk_method: str
    """The topk method of the MoE layer."""

    use_dense_moe: bool
    """Whether to use dense MoE or not."""


class DeepSeekV2MoE(nn.Module):
    """The DeepSeek-V2-MoE layer, a mix of shared experts + routed experts."""

    def __init__(
        self,
        config: DeepSeekV2MoESpec,
        use_sequence_parallel: bool = False,
    ):
        super().__init__()
        self.config = config
        self.topk_method = config.topk_method
        self.n_routed_experts = config.n_routed_experts
        self.num_experts_per_token = config.num_experts_per_token
        self.routed_scaling_factor = config.routed_scaling_factor
        assert config.n_shared_experts >= 1
        assert not use_sequence_parallel, "Sequence parallel is not supported yet."
        self.mp_size = mpu.get_model_parallel_world_size()
        self.mp_rank = mpu.get_model_parallel_rank()
        if config.use_dense_moe:
            # We use the expert parallelism to implement the dense MoE.
            assert config.n_routed_experts % self.mp_size == 0
            self.local_n_experts = config.n_routed_experts // self.mp_size
            self.w1 = nn.Parameter(
                torch.empty(
                    (self.local_n_experts, config.intermediate_size, config.hidden_dim),
                )
            )
            self.w2 = nn.Parameter(
                torch.empty(
                    (self.local_n_experts, config.hidden_dim, config.intermediate_size),
                )
            )
            self.w3 = nn.Parameter(
                torch.empty(
                    (self.local_n_experts, config.intermediate_size, config.hidden_dim),
                )
            )
        else:
            # We use the expert parallelism to implement the sparse MoE.
            # Currently it is the same as dense MoE part, but as we will optimize the logic in future,
            # I intentionally keep two branches here.
            assert config.n_routed_experts % self.mp_size == 0
            self.local_n_experts = config.n_routed_experts // self.mp_size
            self.w1 = nn.Parameter(
                torch.empty(
                    (self.local_n_experts, config.intermediate_size, config.hidden_dim),
                )
            )
            self.w2 = nn.Parameter(
                torch.empty(
                    (self.local_n_experts, config.hidden_dim, config.intermediate_size),
                )
            )
            self.w3 = nn.Parameter(
                torch.empty(
                    (self.local_n_experts, config.intermediate_size, config.hidden_dim),
                )
            )
        # No tensor parallelism for gate_weight
        self.gate_weight = nn.Parameter(
            torch.empty((config.n_routed_experts, config.hidden_dim))
        )
        self.shared_experts = SwiGLU(
            config=SwiGLUSpec(
                hidden_dim=config.hidden_dim,
                intermediate_size=config.intermediate_size * config.n_shared_experts,
                bias=False,
            )
        )

    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        if self.config.use_dense_moe:
            return self.forward_dense_moe(inputs)
        else:
            return self.forward_sparse_moe(inputs)

    def forward_dense_moe(self, inputs: torch.Tensor) -> torch.Tensor:
        bs, seqlen, _ = inputs.shape
        logits = F.linear(
            inputs.type(torch.float32), self.gate_weight.type(torch.float32), None
        )
        scores = logits.softmax(dim=-1, dtype=torch.float32)
        # NOTE(Xuanyi): copy_to_model_parallel_region is necessary for scores and xinputs
        # as they are shared across different model parallelism devices in the same group.
        # In the forward pass, they are a no-op, but in the backward pass, they are summed up.
        scores = mpu.copy_to_model_parallel_region(scores)
        local_expert_indexes = slice(
            self.mp_rank * self.local_n_experts,
            (self.mp_rank + 1) * self.local_n_experts,
        )
        local_scores = scores[..., local_expert_indexes].mT
        xinputs = inputs.view(bs, 1, seqlen, -1)
        xinputs = mpu.copy_to_model_parallel_region(xinputs)
        # shape: [batch, experts, seq, hidden]
        h = functional.swiglu(
            torch.matmul(xinputs, self.w1.mT), torch.matmul(xinputs, self.w3.mT)
        )
        outs_of_experts = torch.matmul(h, self.w2.mT) * local_scores.view(
            bs, self.local_n_experts, seqlen, 1
        )
        outs_of_experts = outs_of_experts.sum(dim=1)
        outs_of_experts = mpu.reduce_from_model_parallel_region(outs_of_experts)
        outs_of_experts = outs_of_experts.to(inputs.dtype)
        outs_of_shared_experts = self.shared_experts(inputs)
        return outs_of_experts + outs_of_shared_experts

    def compute_gate(self, inputs: torch.Tensor):
        logits = F.linear(
            inputs.type(torch.float32), self.gate_weight.type(torch.float32), None
        )
        scores = logits.softmax(dim=-1, dtype=torch.float32)
        if self.topk_method == "greedy":
            topk_weight, topk_idx = torch.topk(
                scores, k=self.num_experts_per_token, dim=-1, sorted=False
            )
        elif self.topk_method == "group_limited_greedy":
            raise NotImplementedError("Group limited greedy is not supported yet.")
        else:
            raise ValueError(f"Unknown topk method: {self.topk_method}")
        # DeepSeek-V2 doesn't normalize the topk prob.
        topk_weight = topk_weight * self.routed_scaling_factor
        return topk_idx, topk_weight

    def forward_sparse_moe(self, inputs: torch.Tensor) -> torch.Tensor:
        # TODO(Xuanyi):
        # - support efficient sparse implementation
        # - support sequence parallelism
        # - support auxiliary loss
        bs, seqlen, _ = inputs.shape
        logits = F.linear(
            inputs.type(torch.float32), self.gate_weight.type(torch.float32), None
        )
        scores = logits.softmax(dim=-1, dtype=torch.float32)
        _, topk_idx = torch.topk(
            scores, k=self.num_experts_per_token, dim=-1, sorted=False
        )
        mask = torch.zeros_like(scores, dtype=torch.float32)
        mask = mask.scatter_(-1, topk_idx, 1.0)
        scores = scores * mask
        # NOTE(Xuanyi): copy_to_model_parallel_region is necessary for scores and xinputs
        # as they are shared across different model parallelism devices in the same group.
        # In the forward pass, they are a no-op, but in the backward pass, they are summed up.
        scores = mpu.copy_to_model_parallel_region(scores)
        local_expert_indexes = slice(
            self.mp_rank * self.local_n_experts,
            (self.mp_rank + 1) * self.local_n_experts,
        )
        local_scores = scores[..., local_expert_indexes].mT
        xinputs = inputs.view(bs, 1, seqlen, -1)
        xinputs = mpu.copy_to_model_parallel_region(xinputs)
        # shape: [batch, experts, seq, hidden]
        h = functional.swiglu(
            torch.matmul(xinputs, self.w1.mT), torch.matmul(xinputs, self.w3.mT)
        )
        outs_of_experts = torch.matmul(h, self.w2.mT) * local_scores.view(
            bs, self.local_n_experts, seqlen, 1
        )
        outs_of_experts = outs_of_experts.sum(dim=1)
        outs_of_experts = mpu.reduce_from_model_parallel_region(outs_of_experts)
        outs_of_experts = outs_of_experts.to(inputs.dtype)
        outs_of_shared_experts = self.shared_experts(inputs)
        return outs_of_experts + outs_of_shared_experts

        # NOTE(Xuanyi): Please keep this comment here as it is a "correct" sparse implementation
        # for inference, however, it will raise some nccl issues during the training.
        #
        # # TODO(Xuanyi):
        # # - support different auxiliary losses
        # # - support greedy grouped top-K for the generation mode
        # bsz, seq_len, h = inputs.shape
        # flat_inputs = inputs.view(-1, h)
        # topk_idx, topk_weight = self.compute_gate(flat_inputs)
        # flat_topk_idx = topk_idx.view(-1)
        # hidden_states = flat_inputs.repeat_interleave(self.num_experts_per_token, dim=0)
        # outs = torch.empty_like(hidden_states)
        # for i, expert in enumerate(self.experts):
        #     outs[flat_topk_idx == i] = expert(hidden_states[flat_topk_idx == i])
        # outs = (outs.view(*topk_weight.shape, -1) * topk_weight.unsqueeze(-1)).sum(
        #     dim=1
        # )
        # outs_of_routed_experts = outs.to(inputs.dtype).view(bsz, seq_len, h)
        # outs_of_shared_experts = self.shared_experts(inputs)
        # return outs_of_routed_experts + outs_of_shared_experts


@dataclass
class ModelArgs(dataclasses_json.DataClassJsonMixin):
    """Model configuration, please read the file docstring on how to properly use DataClassJsonMixin with nested dataclass."""

    # Core architecture arguments
    dim: int = 4096
    n_layers: int = 32
    n_heads: int = 32  # to be deprecated
    n_kv_heads: int = 0
    """Number of KV heads. If 0, will be set to n_heads (multi-head attention)."""
    head_dim: int = 0
    """Dimension of each attention head. If 0, calculated as dim // n_heads.
    Needed for models like Qwen3 where num_head * head_dim != hidden_dim.
    This value is passed to GenericAttnSpec during initialization."""
    rope_theta: float = 10000.0
    # Linear scsaling factor for rotary embeddings
    # See https://github.com/huggingface/transformers/blob/1ac599d90f740ce28f637ad32ff5f59c40cd5a0a/src/transformers/models/llama/modeling_llama.py#L159C14-L159C14
    rope_scaling_factor: float = 1.0
    vocab_size: int = -1  # defined later by tokenizer
    multiple_of: int = 256  # make SwiGLU hidden layer size multiple of large power of 2
    ffn_dim_multiplier: float = 1.0
    """Additional intermediate dimension multiplier for FFN layers."""

    rotary_config: RotaryConfig | None = None

    attn_config: GenericAttnSpec | DeepSeekV2MLASpec | None = None
    """The attention config."""

    ffn_config: MlpSpec | SwiGLUSpec | DeepSeekV2MoESpec | None = None
    """The FFN config."""

    first_layer_ffn_config: MlpSpec | SwiGLUSpec | DeepSeekV2MoESpec | None = None
    """The FFN config for the first layer, this is because some models have a different FFN config for the first layer such as DeepSeek-V2!"""

    norm_eps: float = 1e-5

    # Meta-architecture arguments
    ffn_type: str = "glu"
    """One of "glu" or "mlp"."""
    bias: str = "none"
    """One of "none" or "attn_mlp"."""
    norm_type: str = "rmsnorm"
    """One of "rmsnorm" or "layernorm"."""
    pos_embed_type: str = "rope"
    """One of "rope" or "absolute"."""

    skip_output: bool = False
    """If True, skip the output layer and return the final layer embeddings.

    This is useful for embedding models.
    """

    max_seq_len: int = 2048
    max_generation_batch_size: int = 32
    kv_cache_batch_size: int = 1
    """The batch size to use for the KV cache."""

    use_activation_checkpointing: bool = True
    use_sequence_parallel: bool = False

    def __post_init__(self):
        assert not self.ffn_type or self.ffn_type in {"glu", "mlp"}
        assert not self.bias or self.bias in {"none", "attn_mlp"}
        assert self.norm_type in {"rmsnorm", "layernorm"}
        assert self.pos_embed_type in {"rope", "absolute"}
        self.n_kv_heads = self.n_kv_heads or self.n_heads

    @staticmethod
    def load_from_dict(params: dict) -> "ModelArgs":
        """Load the model args from a JSON file and fix compatibility issues."""
        params = fix_model_arg_params(params)
        return ModelArgs.schema().load(params)

    def dump_as_dict(self) -> dict:
        """Convert the model args to a dict and fix compatibility issues."""
        args = correct_model_args(self)
        return ModelArgs.schema().dump(args)


def fix_model_arg_params(params: dict[str, typing.Any]):
    """Fix stored model args to work with the latest version of fastbackward.

    This function is intended to be the central location to handle all the changes that
    were made to the model args over time.

    Please add a comment describing each change you've made with a date and PR link.

    Args:
        params: The ModelArgs in dict form to fix.

    Returns:
        The fixed model args.
    """
    fixed_params = deepcopy(params)
    # NOTE(zhuoran, 2024-06-07): We deprecated the `starcoder` and `llama` flags in favor
    # of more granular flags, described below.
    # PR: https://github.com/augmentcode/augment/pull/7322
    if "starcoder" in fixed_params and fixed_params["starcoder"]:
        fixed_params.setdefault("ffn_type", "mlp")
        fixed_params.setdefault("pos_embed_type", "absolute")
        fixed_params.setdefault("bias", "attn_mlp")
        fixed_params.setdefault("norm_type", "layernorm")
        fixed_params.setdefault("n_kv_heads", 1)
    fixed_params.pop("starcoder", None)
    if "llama" in fixed_params and fixed_params["llama"]:
        fixed_params.setdefault("ffn_type", "glu")
        fixed_params.setdefault("pos_embed_type", "rope")
        fixed_params.setdefault("bias", "none")
        fixed_params.setdefault("norm_type", "rmsnorm")
        fixed_params.setdefault("n_kv_heads", 0)
    fixed_params.pop("llama", None)

    # NOTE(zhuoran, 2024-06-07): We made `ffn_dim_multiplier` a float from an Optional:
    # a value of 1.0 is the same as the previous default value of None.
    # PR: https://github.com/augmentcode/augment/pull/7322
    if fixed_params.get("ffn_dim_multiplier") is None:
        fixed_params["ffn_dim_multiplier"] = 1.0

    # NOTE(carl, 2024-01-03): We deprecated the `loss_vocab_size` flag.
    # PR: https://github.com/augmentcode/augment/pull/3177
    fixed_params.pop("loss_vocab_size", None)

    # NOTE(xuanyi, 2024-08-13): We renamed GluSpec to SwiGLUSpec.
    if (
        isinstance(fixed_params.get("ffn_config", None), dict)
        and fixed_params["ffn_config"].get("__type", None) == "GluSpec"
    ):
        logger.warning(
            "GluSpec has been renamed to SwiGLUSpec; please update your model configuration by 2024-11-01."
        )
        fixed_params["ffn_config"]["__type"] = "SwiGLUSpec"
    if (
        isinstance(fixed_params.get("first_layer_ffn_config", None), dict)
        and fixed_params["first_layer_ffn_config"].get("__type", None) == "GluSpec"
    ):
        logger.warning(
            "GluSpec has been renamed to SwiGLUSpec; please update your model configuration by 2024-11-01."
        )
        fixed_params["first_layer_ffn_config"]["__type"] = "SwiGLUSpec"
    # NOTE(xuanyi, 2024-10-11): drop generation_mode in https://github.com/augmentcode/augment/pull/13343
    fixed_params.pop("generation_mode", None)
    return fixed_params


def correct_model_args(params: ModelArgs) -> ModelArgs:
    """Correct the model args to work with the latest version of fastbackward."""
    params = deepcopy(params)
    # NOTE(Xuanyi): the following logic is to automatically remove the deprecated arguments
    # and convert them to the new style of configs.
    # Fix the attention config.
    if params.attn_config is None:
        attn_config = GenericAttnSpec(
            hidden_dim=params.dim,
            n_heads=params.n_heads,
            n_kv_heads=params.n_kv_heads or params.n_heads,
            norm_type=params.norm_type,
            pos_embed_type=params.pos_embed_type,
            bias=params.bias in ["attn_mlp"],
            # GenericAttnSpec's __post_init__ will check the validity of head_dim
            # and assign default value if head_dim is not set(0) in ModelArgs
            head_dim=params.head_dim,
        )
    else:
        attn_config = params.attn_config
    params.attn_config = attn_config

    # Fix the FFN config
    if params.ffn_type == "mlp":
        assert params.ffn_config is None
        ffn_config = MlpSpec(
            hidden_dim=params.dim,
            bias=params.bias in ["attn_mlp"],
        )
    elif params.ffn_type == "glu":
        assert params.ffn_config is None
        intermediate_size = int(2 * 4 * params.dim / 3)
        # custom dim factor multiplier
        intermediate_size = int(params.ffn_dim_multiplier * intermediate_size)
        intermediate_size = params.multiple_of * (
            (intermediate_size + params.multiple_of - 1) // params.multiple_of
        )
        ffn_config = SwiGLUSpec(
            hidden_dim=params.dim,
            intermediate_size=intermediate_size,
            bias=params.bias in ["attn_mlp"],
        )
    else:
        assert params.ffn_config is not None
        ffn_config = params.ffn_config
    params.ffn_config = ffn_config

    # Fix the first layer FFN config
    params.first_layer_ffn_config = params.first_layer_ffn_config or params.ffn_config
    # Fix the positional embedding config
    if params.rotary_config is not None:
        rotary_config = params.rotary_config
    else:
        rotary_config = RotaryConfig(
            rotary_ratio=1.0,
            rotary_theta=params.rope_theta,
            max_position_embeddings=params.max_seq_len,
            rotary_interleave=True,
            ext_config=DeepSeekV1ExtensionConfig(
                rotary_scaling_factor=params.rope_scaling_factor,
            ),
        )
    params.rotary_config = rotary_config

    # Explicitly disable the deprecated keys.
    params.ffn_type = ""
    params.bias = ""
    params.n_heads = -1
    params.n_kv_heads = -1
    params.rope_theta = -1.0
    params.rope_scaling_factor = -1.0
    return params


class TransformerBlock(nn.Module):
    """A single transformer block."""

    def __init__(
        self,
        layer_id: int,
        attn_config: GenericAttnSpec | DeepSeekV2MLASpec,
        ffn_config: MlpSpec | SwiGLUSpec | DeepSeekV2MoESpec,
        args: ModelArgs,
    ):
        super().__init__()
        self.n_heads = args.n_heads
        self.dim = args.dim
        self.hidden_dim = args.dim * 4
        self.head_dim = (
            attn_config.head_dim
            if isinstance(attn_config, GenericAttnSpec)
            else args.dim // args.n_heads
        )
        # Build the attention layer
        if isinstance(attn_config, GenericAttnSpec):
            self.attention = Attention(
                attn_config,
                layer_id=layer_id,
                use_sequence_parallel=args.use_sequence_parallel,
                max_seq_len=args.max_seq_len,
                norm_eps=args.norm_eps,  # Pass norm_eps from ModelArgs
                kv_cache_batch_size=args.kv_cache_batch_size,
            )
        elif isinstance(attn_config, DeepSeekV2MLASpec):
            self.attention = DeepSeekV2Attention(
                attn_config,
                use_sequence_parallel=args.use_sequence_parallel,
                max_seq_len=args.max_seq_len,
            )

        # Build the feed-forward layer
        if isinstance(ffn_config, MlpSpec):
            self.feed_forward = Mlp(
                config=ffn_config,
                use_sequence_parallel=args.use_sequence_parallel,
            )
        elif isinstance(ffn_config, SwiGLUSpec):
            self.feed_forward = SwiGLU(
                config=ffn_config,
                use_sequence_parallel=args.use_sequence_parallel,
            )
        elif isinstance(ffn_config, DeepSeekV2MoESpec):
            self.feed_forward = DeepSeekV2MoE(
                ffn_config,
                use_sequence_parallel=args.use_sequence_parallel,
            )
        self.layer_id = layer_id
        norm_factory = _get_norm_factory(args.norm_type)
        self.attention_norm = norm_factory(args.dim, eps=args.norm_eps)
        self.ffn_norm = norm_factory(args.dim, eps=args.norm_eps)

    def forward(
        self,
        x: torch.Tensor,
        rotary_freqs: rotary.RotaryFreqs,
        start_pos: int | None = None,
        mask: torch.Tensor | None = None,
    ):
        attention_norm = self.attention_norm(x)
        h = x + self.attention.forward(attention_norm, rotary_freqs, start_pos, mask)
        ffn_norm = self.ffn_norm(h)
        out = h + self.feed_forward.forward(ffn_norm)
        return out


class Transformer(nn.Module):
    """Full transformer model."""

    def __init__(
        self,
        params: ModelArgs,
        skip_init: bool = False,
    ):
        super().__init__()
        logger.info("Creating a Transformer model.")
        params = correct_model_args(params)
        self.params = params
        self.n_layers = params.n_layers

        self.tok_embeddings = ParallelEmbedding(
            params.vocab_size, params.dim, init_method=lambda x: x
        )

        if params.pos_embed_type == "absolute":
            self.pos_embeddings = ParallelEmbedding(
                params.max_seq_len, params.dim, init_method=lambda x: x
            )
        assert params.attn_config is not None, "attn_config must be specified"
        assert (
            params.first_layer_ffn_config is not None
        ), "first_layer_ffn_config must be specified"
        assert params.ffn_config is not None, "ffn_config must be specified"
        assert params.rotary_config is not None, "rotary_config must be specified"

        logger.info(f"Attention config:\n{params.attn_config}")
        logger.info(f"First layer FFN config:\n{params.first_layer_ffn_config}")
        logger.info(f"Other layers' FFN config:\n{params.ffn_config}")

        self.layers = torch.nn.ModuleList()
        for layer_id in range(params.n_layers):
            if layer_id == 0:
                cur_ffn_config = params.first_layer_ffn_config
            else:
                cur_ffn_config = params.ffn_config
            self.layers.append(
                TransformerBlock(
                    layer_id,
                    params.attn_config,
                    cur_ffn_config,
                    params,
                )
            )

        norm_factory = _get_norm_factory(params.norm_type)
        self.norm = norm_factory(params.dim, eps=params.norm_eps)

        # TODO: move this logic to generic argument validation with updated config logic
        if params.vocab_size % mpu.get_model_parallel_world_size() != 0:
            raise ValueError(
                f"Vocab size ({params.vocab_size}) must be divisible by model parallel size ({mpu.get_model_parallel_world_size()})"
            )

        # Final projection: dim -> vocab_size
        self.output = (
            ColumnParallelLinear(
                params.dim,
                params.vocab_size,
                bias=False,
                gather_output=False,
                init_method=lambda x: x,
            )
            if not params.skip_output
            else None
        )
        # Build the right rotary freqs (sin/cos)
        if isinstance(params.attn_config, GenericAttnSpec):
            # This is for standard RoPE(pos_embed_type == "rope"), and covers "absolute".
            # There's no cost to computing rotary_freqs in the absolute positional embeddings case
            self.rotary_freqs = rotary.precompute_rotary_freqs(
                params.attn_config.head_dim,  # a GenericAttnSpec will guarentee to have a valid head_dim
                params.max_seq_len,
                config=params.rotary_config,
            )
        elif isinstance(params.attn_config, DeepSeekV2MLASpec):
            self.rotary_freqs = rotary.precompute_rotary_freqs(
                params.attn_config.qk_rope_head_dim,
                params.max_seq_len,
                config=params.rotary_config,
            )
        else:
            raise ValueError(f"Unsupported attention config: {params.attn_config}")
        if self.params.use_sequence_parallel:
            self.apply(self._register_sequence_parallel_grad_allreduce)
        if not skip_init:
            self.init_weights()

    def init_weights(self):
        # init all weights
        self.apply(self._init_fn)
        for n, p in self.named_parameters():
            # apply special scaled init to the residual projections, per GPT-2 paper
            if n.endswith(".wo.weight"):
                torch.nn.init.normal_(
                    p, mean=0.0, std=0.02 / math.sqrt(2 * self.params.n_layers)
                )

    def _init_fn(self, module: nn.Module):
        if isinstance(module, (ColumnParallelLinear, RowParallelLinear)):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, ParallelEmbedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, DeepSeekV2MoE):
            torch.nn.init.normal_(module.gate_weight, mean=0.0, std=0.02)
            torch.nn.init.normal_(module.w1, mean=0.0, std=0.02)
            torch.nn.init.normal_(module.w2, mean=0.0, std=0.02)
            torch.nn.init.normal_(module.w3, mean=0.0, std=0.02)

    def _register_sequence_parallel_grad_allreduce(self, module: nn.Module):
        """Register a hook to allreduce grads in sequence-parallel regions.

        Weights in sequence-parallel regions need to be allreduced in the MP group:
        - Norm layers
        - _Output_ biases (output proj in attn and MLP2)

        Args:
            module: the module to register the hook on.
        """
        to_register = []
        if isinstance(module, (FusedRMSNorm, nn.LayerNorm)):
            to_register.extend(p for p in module.parameters() if p is not None)
        if isinstance(module, RowParallelLinear) and module.bias is not None:
            to_register.append(module.bias)

        # NOTE: we need to use the (out-of-place) `register_hook` rather than the simpler
        # `register_post_accumulate_grad_hook`, since post-accumulate, you might be working with
        # _gradient accumulated_ gradients (duh). And if you all_reduce those, you are averaging
        # the _accumulated_ gradients, which is obviously wrong.
        def _allreduce_grad_hook(grad):
            new_grad = torch.empty_like(grad)
            with torch.no_grad():
                new_grad.copy_(grad)
                dist.all_reduce(new_grad, group=mpu.get_model_parallel_group())
            return new_grad

        for p in to_register:
            p.register_hook(_allreduce_grad_hook)

    # NOTE(arun): `create_config` is used to save the model configuration in a
    # consistent way so that we can recursively save the configurations of the more
    # complex retrieval models that e.g. have separate query and document encoders.
    # It must return a dictionary of arguments to the constructor.
    def create_config(self) -> dict:
        """Create a configuration dictionary for this model."""
        return {"params": self.params}

    def embed_tokens(self, tokens: torch.Tensor, start_pos: int | None = None):
        _, seqlen = tokens.shape
        start_pos = start_pos or 0
        # This will move the rotary freqs to the right device at the first time of call embed_tokens
        # and then a no-op for the rest of the time.
        self.rotary_freqs = self.rotary_freqs.to(self.tok_embeddings.weight.device)
        h = self.tok_embeddings(tokens)
        if self.params.pos_embed_type == "absolute":
            embed_range = torch.arange(
                start_pos, start_pos + seqlen, dtype=torch.long, device=h.device
            )
            h = h + self.pos_embeddings(embed_range)
        rotary_freqs = self.rotary_freqs.slice(start_pos, start_pos + seqlen)
        return h, rotary_freqs

    @torch.no_grad()
    def generate(self, tokens: torch.Tensor, start_pos: int):
        """A variant of `forward` specifically for autoregressive generation for inference."""
        assert self.output is not None, "self.output is None"
        assert (
            not self.params.use_sequence_parallel
        ), "Sequence parallel must be disabled."
        bsz, seqlen = tokens.shape
        h, rotary_freqs = self.embed_tokens(tokens, start_pos)
        with spu.sequence_parallel_context(bsz, seqlen, enabled=False):
            h = spu.none2seq(h)
            for layer in self.layers:
                h = layer(h, rotary_freqs, start_pos=start_pos)
            h = self.norm(h)
            h = spu.seq2none(h)
            parallel_output = self.output(h)
            output = mpu.gather_from_model_parallel_region(parallel_output)
        return output.float()

    @contextmanager
    def expand_num_kv_caches(self, num_caches: int):
        orig_num_caches = self.layers[0].attention.attn_func_w_cache.cache_k.size(0)
        for layer in self.layers:
            layer.attention.attn_func_w_cache.reset_max_batch_size(num_caches)
        yield
        for layer in self.layers:
            layer.attention.attn_func_w_cache.reset_max_batch_size(orig_num_caches)

    def replicate_kv_cache_prefix(
        self, prefix_len: int | None = None, src_idx: int = 0
    ):
        """Replicate the K/V cache prefix to all cache lines."""
        for layer in self.layers:
            attn = layer.attention.attn_func_w_cache
            for cache in [attn.cache_k, attn.cache_v]:
                src_line = cache[src_idx, :prefix_len, ...]
                for i in range(cache.size(0)):
                    if i == src_idx:
                        continue
                    cache[i, :prefix_len, ...].copy_(src_line)

    def forward(
        self,
        tokens: torch.Tensor,
        attention_mask: torch.Tensor | None = None,
        skip_output: bool = False,
    ):
        """Implements single forward pass of `Transformer`

        Args:
            tokens: tokens to process. Shape: [batch, seq_length]
            attention_mask: If None, will use causal mask.
                Shape have to be broadcastable to [batch, heads, seq_length, seq_length]
                `FusedAttn` supports both bool and float masks.
                `SimpleCachedAttn` supports only float masks.
            skip_output: if True, skips the final output layer. Useful for chunked cross_entropy.

        Returns:
            output:
            - [batch, seq_length, vocab_size // model_parallel_size] normally
            - [batch, seq_length, hidden_dim] if `skip_output` is True.
        """
        bsz, seqlen = tokens.shape
        h, rotary_freqs = self.embed_tokens(tokens)
        with spu.sequence_parallel_context(
            bsz, seqlen, enabled=self.params.use_sequence_parallel
        ) as sequence_parallel_context:
            h = spu.none2seq(h)
            for layer in self.layers:
                assert isinstance(layer, TransformerBlock)
                if self.training and self.params.use_activation_checkpointing:
                    # NOTE: the way activation checkpointing works is to re-run `forward` per-layer
                    # during backprop. This occurs _outside_ the sequence parallel context defined
                    # above (since it's wherever the user calls `loss.backward()`). As a result, we
                    # need to re-enter the seqpar context _inside_ the layer's `forward`. We do so
                    # here with a wrapper + partial. An alternative would be to pass the context
                    # into the layer, but this keeps all the checkpointing-specific logic in one
                    # place.
                    def layer_wrapper(layer_, *args, **kwargs):
                        with spu.reenter_context(sequence_parallel_context):
                            return layer_(*args, **kwargs)

                    wrapped_layer = partial(layer_wrapper, layer)
                    # Note: all layer args to `checkpoint` function have to be passed via *args, not **kwargs
                    # https://github.com/pytorch/pytorch/blob/4c1dd13ba33d0fcd1f039ea67b026979a09ae00a/torch/utils/checkpoint.py#L478
                    h = torch.utils.checkpoint.checkpoint(
                        wrapped_layer,
                        h,
                        rotary_freqs,
                        None,  # start_pos
                        attention_mask,
                        preserve_rng_state=False,
                        # NOTE: we use the reentrant implementation here, since it's the only one
                        # compatible with _partial_ compilation. (Ie, only some portions of the passed
                        # function are compiled.)
                        use_reentrant=True,
                    )
                else:
                    h = layer(h, rotary_freqs, start_pos=None, mask=attention_mask)
            h = self.norm(h)
            h = spu.seq2none(h)

        # Skip output: useful for embedding models or chunked cross_entropy loss.
        if self.output is None or skip_output:
            return h

        parallel_logits = self.output(h)

        # NOTE(carl): We are returning parallel logits, i.e. this is (vocabulary size
        #  // model parallel size). This is more efficient for a forward pass, but you
        # may need to reduce across model partitions to get the final logits. See
        # the `generate` method for an example.
        return parallel_logits


def num_params(model: torch.nn.Module, matmul_only: bool = False) -> int:
    """Utility function to count the number of parameters on a model.

    If `matmul_only` is True, count only linear layer params (useful for flops counting).
    """
    if matmul_only:
        total = 0
        for m in model.modules():
            if isinstance(m, (RowParallelLinear, ColumnParallelLinear, nn.Linear)):
                total += m.weight.numel()
        return total
    return sum(p.numel() for p in model.parameters())


def configure_fsdp_optimizer(
    *modules: torch.nn.Module,
    weight_decay: float,
    learning_rate: float,
    betas: tuple[float, float],
    eps: float = 1e-8,
):
    """Configure an FSDP optimizer for the given modules.

    The optimizer currently assumes that:
    1. All parameters with dimension >=2 will be decayed.
    2. All parameters with dimension < 2 will *not* be decayed.

    This is a reasonable default for many models, but won't apply for all.

    Args:
        *modules: One or more modules to configure the optimizer for.
        weight_decay: The weight decay to use.
        learning_rate: The learning rate to use.
        betas: The betas to use.

    Returns:
        A tuple containing the optimizer and the flattened model.
    """
    params_with_grads = [p for m in modules for p in m.parameters() if p.requires_grad]
    decay_params = [p for p in params_with_grads if p.dim() >= 2]
    nodecay_params = [p for p in params_with_grads if p.dim() < 2]
    num_decay_params = sum(p.numel() for p in decay_params)
    num_nodecay_params = sum(p.numel() for p in nodecay_params)
    logger.info(
        f"num decayed parameter tensors: {len(decay_params)}, with {num_decay_params:,} parameters"
    )
    logger.info(
        f"num non-decayed parameter tensors: {len(nodecay_params)}, with {num_nodecay_params:,} parameters"
    )
    fms = flatten_model(
        {"decay": decay_params, "nodecay": nodecay_params},
        mpu.get_data_parallel_rank(),
        mpu.get_data_parallel_world_size(),
    )
    # =====
    # NOTE: alternative using the apex FusedAdam optimizer, which is broken for bfloat16
    # (see https://github.com/NVIDIA/apex/issues/1728)
    # optim_groups = [
    #     {'params': fms.dist_param("decay"), 'weight_decay': weight_decay},
    #     {'params': fms.dist_param("nodecay"), 'weight_decay': 0.0},
    # ]
    # optimizer = FusedAdam(optim_groups, lr=learning_rate, betas=betas, set_grad_none=False,
    #                       capturable=True, master_weights=True)
    # OR:
    # optimizer = torch.optim.AdamW(optim_groups, lr=learning_rate, betas=betas, fused=True)
    # =====
    optim_groups = [
        (
            fms.dist_params("decay"),
            fms.dist_grads("decay"),
            {"weight_decay": weight_decay},
        ),
        (
            fms.dist_params("nodecay"),
            fms.dist_grads("nodecay"),
            {"weight_decay": 0.0},
        ),
    ]

    # TODO(carl): When there are multiple param groups, some ranks will get none of the elements
    # of that param group in its slice, leading to empty slices that can cause issues, so we
    # filter them out. It _might_ make sense to instead balance each group across the DP slices,
    # but that complicates a lot of logic for ~eps gain in the common case
    optim_groups = [g for g in optim_groups if g[0].numel() > 0]
    optimizer = MixedPrecisionAdamW(
        optim_groups, lr=learning_rate, betas=betas, eps=eps, weight_decay=weight_decay
    )
    return optimizer, fms


def configure_optimizers(
    model: torch.nn.Module, weight_decay, learning_rate, betas, device_type
):
    # start with all of the candidate parameters
    param_dict = {pn: p for pn, p in model.named_parameters()}
    # filter out those that do not require grad
    param_dict = {pn: p for pn, p in param_dict.items() if p.requires_grad}
    # create optim groups. Any parameters that is 2D will be weight decayed, otherwise no.
    # i.e. all weight tensors in matmuls + embeddings decay, all biases and layernorms don't.
    for n, p in param_dict.items():
        logger.info(f"{n} {p.shape} decay={p.dim() >= 2}")
    decay_params = [p for n, p in param_dict.items() if p.dim() >= 2]
    nodecay_params = [p for n, p in param_dict.items() if p.dim() < 2]
    optim_groups = [
        {"params": decay_params, "weight_decay": weight_decay},
        {"params": nodecay_params, "weight_decay": 0.0},
    ]
    num_decay_params = sum(p.numel() for p in decay_params)
    num_nodecay_params = sum(p.numel() for p in nodecay_params)
    logger.info(
        f"num decayed parameter tensors: {len(decay_params)}, with {num_decay_params:,} parameters"
    )
    logger.info(
        f"num non-decayed parameter tensors: {len(nodecay_params)}, with {num_nodecay_params:,} parameters"
    )
    # Create AdamW optimizer and use the fused version if it is available
    fused_available = "fused" in inspect.signature(torch.optim.AdamW).parameters
    use_fused = fused_available and device_type == "cuda"
    extra_args = dict(fused=True) if use_fused else dict()
    optimizer = torch.optim.AdamW(
        optim_groups, lr=learning_rate, betas=betas, **extra_args
    )
    logger.info(f"using fused AdamW: {use_fused}")

    return optimizer
