"""Configuration for the deploy server."""

import pathlib
from dataclasses import dataclass
from typing import Optional

from dataclasses_json import dataclass_json
import services.lib.grpc.tls_config.tls_config as tls_config


@dataclass_json
@dataclass
class Config:
    """Configuration for the deploy server."""

    allowed_branches: list[str]
    """The branches that are allowed to be deployed."""

    namespace: str
    """The namespace in which the deploy pod runs"""

    image_tag_file: str
    """File containing the image for the deploy pod"""

    cloud: str
    """The cloud the deploy pod runs in"""

    env: str
    """The environment the deploy pod runs in"""

    project_id: str
    """The project id of the bigtable instance"""

    instance_name: str
    """The name of the bigtable instance"""

    table_name: str
    """The name of the bigtable table"""

    server_mtls: bool
    """Whether the server should use mTLS."""

    server_key_path: str
    """The path to the server key."""

    server_cert_path: str
    """The path to the server certificate."""

    server_ca_path: str
    """The path to the server CA certificate."""

    client_mtls: Optional[tls_config.ClientConfig]

    port: int
    """The port to listen on."""

    slack_bot_endpoint: str
    """The slack bot endpoint to use."""

    deploy_viewer_url: str
    """The endpoint to use for the deploy ui."""

    topic_name: str
    """The name of the pub/sub topic to listen to."""

    subscription_batch_size: int
    """The batch size to use for fetching subscription messages."""

    deploy_cron_job_name: str
    """The name of the deploy cron job."""

    shutdown_grace_period_s: float = 20.0
    """The grace period for the server to shutdown."""

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )
