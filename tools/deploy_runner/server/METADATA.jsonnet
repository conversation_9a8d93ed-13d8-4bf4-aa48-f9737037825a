// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'deploy-runner-server',
      kubecfg: {
        target: '//tools/deploy_runner/server:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk', 'costa'],
          slack_channel: '#system-services',
        },
      },
    },
    {
      name: 'deploy-runner-server-shared',
      kubecfg: {
        target: '//tools/deploy_runner/server:kubecfg_shared',
        task: [
          {
            cloud: 'ALL',
          },
        ],
      },
    },
  ],
}
