"""kubecfg based deployment targets."""

# pylint: disable=logging-fstring-interpolation

import json
import logging
import shlex
import subprocess
import tempfile
import time
import typing
from dataclasses import dataclass
import pathlib
import git
import gitdb
from datetime import datetime, timedelta
from pathlib import Path

from prometheus_client import Histogram, Counter

from tools.deploy_runner import deploy_events_pb2
import yaml
from kubernetes.client import V1Deployment

from base.cloud.k8s.kubectl import (
    contains_error as kubectl_contains_error,
    is_immutable_job,
)
import tools.deploy_runner.metadata_pb2 as metadata_pb2  # pylint: disable=no-name-in-module
from tools.deploy_runner.deploy_target import (
    DeploymentInfo,
    DeployNotifier,
    DeployTarget,
    form_bazel_command_line,
)
from tools.kubecfg import kubecfg_diff
from tools.kubecfg.kubecfg import KubeCfg, KubeCfgException, VisitConfig
from tools.bazel_lib import bazel
from tools.kubecfg.kubecfg_test_lib import KubeCfgTestUtil
from tools.kubecfg.pusher import dependency_push_image

INF = float("inf")
LATENCY_BUCKETS = [
    60 * i for i in [0, 1, 2, 4, 6, 8, 10, 12, 14, 16, 24, 32, 64, 128, INF]
]
_push_latency = Histogram(
    "au_kubecfg_deploy_push_latency",
    "Time to push an image",
    ["image_target", "phase"],
    buckets=LATENCY_BUCKETS,
)

_deploy_latency = Histogram(
    "au_kubecfg_deploy_target_latency",
    "Time to deploy a target",
    ["name", "env", "namespace", "phase"],
    buckets=LATENCY_BUCKETS,
)

_skip_reason = Counter(
    "au_kubecfg_deploy_skip",
    "Counter for skipped targets",
    ["name", "env", "namespace", "result"],
)


@dataclass
class Rollout:
    """A rollout."""

    name: str
    namespace: str
    timeout: timedelta


def get_deployment_rollouts(
    yaml_output: str, default_timeout: timedelta
) -> typing.Iterable[Rollout]:
    """Return the deployment names from the given output file."""
    d = list(yaml.safe_load_all(yaml_output))
    for item in d:
        if item.get("kind") == "Deployment":
            logging.debug(f"Found deployment {item['metadata']['name']}")
            timeout = default_timeout
            if "spec" in item and item["spec"].get("progressDeadlineSeconds"):
                logging.debug(
                    "Found timeout: %s", item["spec"]["progressDeadlineSeconds"]
                )
                timeout = timedelta(seconds=item["spec"]["progressDeadlineSeconds"])
            yield Rollout(
                item["metadata"]["name"],
                item["metadata"].get("namespace"),
                timeout=timeout,
            )


def _is_fully_deployed(
    name: str,
    generation: int,
    dep: V1Deployment,
    progress_fn: typing.Callable[[str], None],
) -> bool:
    """Checks if a deployment status is fully deployed.

    based on https://stackoverflow.com/questions/68540187/kubectl-rollout-status-when-the-command-complete:
    Its updated-replica count is at least its desired-replica count (every new pod has been created)
    Its current-replica count is at most its updated-replica count (every old pod has been destroyed)
    Its available-replica count is at least its updated-replica count (every new pod is running)
    """
    if generation > dep.status.observed_generation:  # type: ignore
        logging.info(f"Waiting for deployment {name} update to be observed.")
        return False
    if not dep.status:
        return False
    ur = dep.status.updated_replicas
    if ur is None:
        ur = 0

    assert dep.spec
    if dep.spec.replicas is None or dep.spec.replicas == 0:
        logging.info(f"Deployment {name} has no replicas.")
        return True
    if dep.spec.replicas and ur < dep.spec.replicas:
        progress_fn(
            f"Waiting for deployment {name} rollout to finish: {ur} out of {dep.spec.replicas} new replicas have been updated."
        )
        return False
    sr = dep.status.replicas
    if sr is None:
        sr = 0
    if sr > ur:
        progress_fn(
            f"Waiting for deployment {name} rollout to finish: {sr - ur} old replicas are pending termination."
        )
        return False
    if dep.status.available_replicas is None:
        logging.info(
            f"Waiting for deployment {name} rollout to finish: 0 of {ur} updated replicas are available."
        )
        logging.info(
            f"Waiting for deployment {name} rollout to finish: 0 of {ur} updated replicas are available."
        )
        return False
    ar = dep.status.available_replicas
    if ar is None:
        ar = 0
    if ar < ur:
        progress_fn(
            f"Waiting for deployment {name} rollout to finish: {ar} of {ur} updated replicas are available."
        )
        return False
    progress_fn(f"Deployment {name} successfully rolled out.")
    return True


class KubecfgDeployParent:
    """Target level parent for KubecfgDeployTarget.

    All tasks for the same target share the parent.
    This allows to easily cache and reuse information.
    """

    def __init__(
        self, deployment_info: DeploymentInfo, target: metadata_pb2.Deployment
    ):
        self.deployment_info = deployment_info
        self.target = target
        # cache for pushed images
        # we only build an image and and re-use the hash
        self.image_cache = {}
        # cache for kubecfg json
        self.kubecfg_json: dict | None = None
        # if the dependencies have been checked
        self.dep_checked = False

    def _push_image(self, image_target: str, dst: str) -> str:
        """Push the image to the artifact registry.

        If the image is already pushed, return the digest.
        The image is build and pushed by calling the Bazel target.
        """
        logging.debug("push image target %s, dst %s", image_target, dst)

        with self.deployment_info.with_workspace_lock():
            digest = self.image_cache.get((image_target, dst))
            if digest:
                logging.info(
                    "Image %s already pushed, returning digest %s", image_target, digest
                )
                return digest

        with _push_latency.labels(image_target, "build").time():
            cmd = form_bazel_command_line(
                "build",
                self.deployment_info,
                cmd_args=["-c", "opt"]
                + shlex.split(self.deployment_info.extra_bazel_args or "")
                + [image_target],
            )
            logging.info("Command: %s", shlex.join(cmd))
            env = bazel.get_bazel_env()
            r = subprocess.run(
                cmd,
                cwd=self.deployment_info.workspace,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                encoding="utf-8",
                check=False,
            )
            if r.returncode != 0:
                raise KubeCfgException(
                    "Failed to build image",
                    stdout=r.stdout,
                    stderr=r.stderr,
                    command=cmd,
                )

        with _push_latency.labels(image_target, "push").time():
            digest = dependency_push_image(
                image_target, dst, base_dir=self.deployment_info.workspace / "bazel-bin"
            )
            logging.debug("Digest '%s'", digest)
            self.image_cache[(image_target, dst)] = digest
            return digest

    def get_kubecfg_json(
        self,
    ) -> dict:
        """Return the kubecfg configuration json document as Python dict.

        The kubecfg configuration is created by calling the bazel target with the `_json` suffix and reading the build artifact file.
        """
        with _deploy_latency.labels(
            self.target.name,
            "",
            "",
            "get_kubecfg_json",
        ).time():
            if self.kubecfg_json is not None:
                logging.info("Using cached kubecfg json")
                return self.kubecfg_json

            env = bazel.get_bazel_env()
            # build the json target
            cmd = form_bazel_command_line(
                cmd="build",
                deployment_info=self.deployment_info,
                cmd_args=["-c", "opt", self.target.kubecfg.target + "_json"]
                + shlex.split(self.deployment_info.extra_bazel_args or ""),
            )
            logging.info("Command: %s", shlex.join(cmd))
            r = subprocess.run(
                cmd,
                cwd=self.deployment_info.workspace,
                stderr=subprocess.PIPE,
                stdout=subprocess.PIPE,
                encoding="utf-8",
                env=env,
                check=False,
            )
            if r.returncode != 0:
                raise KubeCfgException(
                    "Failed to build kubecfg json",
                    stdout=r.stdout,
                    stderr=r.stderr,
                    command=cmd,
                )
            p = bazel.get_output_files(self.deployment_info.workspace, r.stderr)
            if len(p) != 1:
                raise KubeCfgException(
                    f"Expected 1 output file, found {len(p)}: {p}",
                    stdout=r.stdout,
                    stderr=r.stderr,
                    command=cmd,
                )

            # this file has to exist at this point
            self.kubecfg_json = json.load(p[0].open("r", encoding="utf-8"))
            return self.kubecfg_json

    def run_kubecfg(
        self, task: metadata_pb2.KubeCfgTask, dry_run: bool, notifier: DeployNotifier
    ) -> str | None:
        """Run the kubecfg command as a subprocess.

        This is used for yaml based kubecfg.
        """
        with _deploy_latency.labels(
            self.target.name,
            metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
            task.namespace if task.namespace else "",
            "run_kubecfg",
        ).time():
            logging.info(
                "Applying to namespace %s (%s/%s)",
                task.namespace,
                metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud),
                metadata_pb2.KubeCfgTask.Env.Name(task.env),
            )
            kubectl = self.deployment_info.kubectl_factory(
                metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud)
            )

            deploy_task = deploy_events_pb2.DeployTask(
                target_name=self.target.name,
                namespace=task.namespace or "",
                cloud=metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud)
                if task.cloud
                else "",
            )
            with (
                self.deployment_info.with_workspace_lock(),
                tempfile.NamedTemporaryFile(mode="w+", encoding="utf-8") as f,
            ):
                target_args = []
                target_args.append("--cloud")
                target_args.append(metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud))
                target_args.append("apply")
                target_args.append("--batch")
                if task.namespace:
                    target_args.extend(["--namespace", task.namespace])
                    if not task.env:
                        raise ValueError("kubecfg deployment target must have an env")
                    env_name = metadata_pb2.KubeCfgTask.Env.Name(task.env)
                    target_args.extend(["--env", env_name])
                if not kubectl.kube_config_file:
                    target_args.append("--kube-config-file=")
                else:
                    # make the path absolute as the kubecfg command might not have the kube config file in its runfiles
                    target_args.append(
                        f"--kube-config-file={pathlib.Path(kubectl.kube_config_file).absolute()}"
                    )
                if self.deployment_info.deployed_by:
                    target_args.append(
                        f"--deployed-by={self.deployment_info.deployed_by}"
                    )
                target_args.append("--stamp")
                target_args.append("--output-file")
                target_args.append(f.name)
                for extra_args in self.target.kubecfg.extra_config_args:  # type: ignore
                    target_args.append("--extra-config-args")
                    target_args.append(f"{extra_args.name}={extra_args.value}")  # type: ignore

                cmd = form_bazel_command_line(
                    "run",
                    self.deployment_info,
                    cmd_args=["-c", "opt"]
                    + shlex.split(self.deployment_info.extra_bazel_args or "")
                    + [self.target.kubecfg.target],
                    target_args=target_args,
                )

                logging.info("Command: %s", shlex.join(cmd))
                if dry_run:
                    return ""

                env = bazel.get_bazel_env()

                logging.info(
                    "Command: %s in %s", shlex.join(cmd), self.deployment_info.workspace
                )
                r = subprocess.run(
                    cmd,
                    cwd=self.deployment_info.workspace,
                    capture_output=True,
                    env=env,
                    encoding="utf-8",
                    check=False,
                )
                notifier.output(
                    deploy_events_pb2.DeployOutput(
                        task=deploy_task,
                        command=cmd,
                        stdout=r.stdout,
                        stderr=r.stderr,
                        return_code=r.returncode,
                    )
                )

                # 0 = success, 4 = skip
                if r.returncode != 0 and r.returncode != 4:
                    logging.warning(
                        f"Deployment {self.target.name} to {metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud)} failed: return code {r.returncode}"
                    )
                    raise KubeCfgException(
                        f"Failed to deploy {self.target.name}",
                        stdout=r.stdout,
                        stderr=r.stderr,
                        command=cmd,
                    )
                if r.returncode == 4:
                    # skip rollout
                    return None

                if not dry_run:
                    config = Path(f.name).read_text(encoding="utf-8")
                    notifier.configuration(
                        deploy_events_pb2.DeployConfiguration(
                            task=deploy_task,
                            configuration=config,
                        )
                    )
                    return config
                return ""

    def get_config(
        self,
        task: metadata_pb2.KubeCfgTask,
        task_kubecfg: KubeCfg,
        visit_config: VisitConfig,
    ):
        """Return a config object for the given task."""
        with _deploy_latency.labels(
            self.target.name,
            metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
            task.namespace if task.namespace else "",
            "get_config",
        ).time():
            return task_kubecfg.get_config(visit_config, None)

    def _check_git_version(self, diff: str, task: metadata_pb2.KubeCfgTask) -> bool:
        """Check if the current git version is deployed.

        We extract the git version from the deployed configuration by parsing the kubectl diff.
        Usually there is a single git version, but there can be multiple in cases like crashes
        or other kinds of drifts.
        We compare these deployed git versions with the git version to deploy.

        We skip the deployment unless git version to deploy is an anchestor (i.e. before in the
        time ordering) of every deployed git version.

        This check is to prevent "go back in time" with deployments, e.g. by adhoc deployments.

        Returns:
            True if the current deploy should proceed, False otherwise.
        """
        with _deploy_latency.labels(
            self.target.name,
            metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
            task.namespace if task.namespace else "",
            "git_version",
        ).time():
            try:
                deployed_versions = kubecfg_diff.extract_git_versions(diff)
                logging.info("Current git versions: %s", ", ".join(deployed_versions))
                if not deployed_versions:
                    # no git version found, so we can't check it
                    return True
                repo = git.Repo(self.deployment_info.workspace)
                current_commit = repo.head.commit
                should_deploy = False
                for deployed_version in deployed_versions:
                    try:
                        deployed_commit = repo.commit(deployed_version)
                    except ValueError as ex:  # type: ignore
                        # AU-3380 - was unable to reproduce locally
                        logging.warning(
                            "Cannot check git version %s: %s", deployed_version, ex
                        )
                        return True
                    # check if the current commit is an ancestor of the git version that is deployed
                    if repo.is_ancestor(current_commit, deployed_commit):
                        logging.warning(
                            "Deployed commit %s is an ancestor of the current commit %s",
                            deployed_version,
                            current_commit,
                        )
                    else:
                        logging.debug(
                            "Deployed commit %s is not an ancestor of the current commit %s",
                            deployed_version,
                            current_commit,
                        )
                        # if any object is outdated, we will deploy
                        should_deploy = True
                        break
                if should_deploy:
                    return True
                if self.deployment_info.allow_rollback:
                    logging.info("Rollbacks are allowed")
                    return True
                return False
            except git.InvalidGitRepositoryError:
                logging.info("Not a git repository, skipping rollback check")
                return True
            except gitdb.exc.BadName as ex:  # type: ignore
                logging.info("Skipping rollback chec: Cannot check git version: %s", ex)
                return True

    def apply_task(
        self,
        task: metadata_pb2.KubeCfgTask,
        dry_run: bool,
        notifier: DeployNotifier,
    ) -> str | None:
        """Run the deployment Bazel target.

        Usually, this will run an K8S apply target, but it can perform
        other operations that logically are deployments, e.g. upload a file to S3.

        Returns True if the deployment succeeded, False otherwise. None if the operation was skipped
        """
        assert self.target.kubecfg

        logging.info(
            "Applying to namespace %s (%s/%s)",
            task.namespace,
            metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud),
            metadata_pb2.KubeCfgTask.Env.Name(task.env),
        )
        kubectl = self.deployment_info.kubectl_factory(
            metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud)
        )

        deploy_task = deploy_events_pb2.DeployTask(
            target_name=self.target.name,
            namespace=task.namespace or "",
            cloud=metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud) if task.cloud else "",
        )

        kubecfg_json = self.get_kubecfg_json()
        logging.debug("Kubecfg JSON: %s", kubecfg_json)
        task_kubecfg = KubeCfg.create_from_json(
            json_data=kubecfg_json,
            base_directory=self.deployment_info.workspace,
            cloud=metadata_pb2.KubeCfgTask.Cloud.Name(task.cloud),
            env=metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else None,
            namespace=task.namespace,
            deployed_by=self.deployment_info.deployed_by,
            deployment_target_name=self.target.name,
            kube_config_file=kubectl.kube_config_file,
            push_fn=self._push_image,
            extra_config_args=[
                (a.name, a.value) for a in self.target.kubecfg.extra_config_args
            ],
        )
        if any(f for f in task_kubecfg.files if not f.endswith(".jsonnet")):
            # for yaml or helm based kubecfg, we need to run kubecfg
            return self.run_kubecfg(task, dry_run, notifier)
        visit_config = VisitConfig(stamp=True, rewrite=not kubecfg_json["norewrite"])
        with self.get_config(task, task_kubecfg, visit_config) as config:
            if task_kubecfg.namespace:
                logging.info(
                    "Applying configuration to namespace '%s' in '%s'",
                    task_kubecfg.namespace,
                    task_kubecfg.env,
                )
            else:
                logging.info("Applying cluster-wide configuration")

            notifier.configuration(
                deploy_events_pb2.DeployConfiguration(
                    task=deploy_task,
                    configuration=config.dump(),
                )
            )

            with _deploy_latency.labels(
                self.target.name,
                metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
                task.namespace if task.namespace else "",
                "diff",
            ).time():
                r = config.diff()
                if r.stdout:
                    logging.info("%s", r.stdout)
                if r.stderr:
                    logging.error("%s", r.stderr)

                notifier.output(
                    deploy_events_pb2.DeployOutput(
                        task=deploy_task,
                        stdout=r.stdout,
                        stderr=r.stderr,
                        return_code=r.returncode,
                        command=r.command or [],
                    )
                )

                if not self._check_git_version(r.stdout, task):
                    logging.info("Skipping apply because of git version")
                    _skip_reason.labels(
                        self.target.name,
                        metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
                        task.namespace if task.namespace else "",
                        "git_version",
                    ).inc()
                    return None

                if not kubectl_contains_error(
                    r.stderr
                ) and not kubecfg_diff.is_meaningful_diff(r.stdout):
                    logging.info("No changes to apply")
                    _skip_reason.labels(
                        self.target.name,
                        metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
                        task.namespace if task.namespace else "",
                        "no_changes",
                    ).inc()
                    return None

                if is_immutable_job(r.stderr):
                    logging.info("Skipping apply to immutable job")
                    _skip_reason.labels(
                        self.target.name,
                        metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
                        task.namespace if task.namespace else "",
                        "immutable_job",
                    ).inc()
                    return None

            if not dry_run:
                with _deploy_latency.labels(
                    self.target.name,
                    metadata_pb2.KubeCfgTask.Env.Name(task.env) if task.env else "",
                    task.namespace if task.namespace else "",
                    "apply",
                ).time():
                    r = config.apply()
                    if r.stdout:
                        logging.info("%s", r.stdout)
                    if r.stderr:
                        logging.error("%s", r.stderr)

                    if kubectl_contains_error(r.stderr) or r.returncode:
                        raise KubeCfgException(
                            f"Failed to apply changes for {self.target.name}",
                            stdout=r.stdout,
                            stderr=r.stderr,
                            command=r.command or [],
                        )
                    notifier.output(
                        deploy_events_pb2.DeployOutput(
                            task=deploy_task,
                            stdout=r.stdout,
                            stderr=r.stderr,
                            return_code=r.returncode,
                            command=[str(c) for c in r.command or []],
                        )
                    )
                    return config.dump()
            else:
                return ""


class KubecfgDeployTarget(DeployTarget):
    """A deployment target that runs a Kubecfg target."""

    def __init__(
        self,
        parent: KubecfgDeployParent,
        task: metadata_pb2.KubeCfgTask,
        default_rollout_timeout=timedelta(minutes=15),
    ):
        self.parent = parent
        self.task = task
        self.default_rollout_timeout = default_rollout_timeout
        self.is_deployed = False
        self.rollouts = []
        self.rollout_start = None
        assert parent.target.bazel

        self.deploy_task = deploy_events_pb2.DeployTask(
            target_name=self.parent.target.name,
            namespace=self.task.namespace or "",
            cloud=metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)
            if self.task.cloud
            else "",
        )

    @property
    def target_name(self):
        return self.parent.target.name

    @property
    def name(self):
        if self.task.namespace:
            return f"{self.task.namespace}/{self.parent.target.name}@{metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)}"
        else:
            return f"/{self.parent.target.name}@{metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)}"

    @property
    def priority(self):
        return self.parent.target.priority

    @property
    def namespace(self):
        if self.task.namespace:
            return self.task.namespace
        return None

    @property
    def env(self):
        if self.task.env:
            return self.task.env
        return None

    @property
    def cloud(self):
        return self.task.cloud

    def __repr__(self):
        return f"KubecfgDeployTarget({self.parent.target, self.task})"

    def apply_task(
        self,
        dry_run: bool,
        notifier: DeployNotifier,
    ) -> bool:
        """Run the deployment Bazel target.

        Usually, this will run an K8S apply target, but it can perform
        other operations that logically are deployments, e.g. upload a file to S3.

        Returns True if the deployment succeeded, False if the operation was skipped
        """
        try:
            assert self.parent.target.kubecfg

            config = self.parent.apply_task(self.task, dry_run, notifier)
            if config is None:
                return False

            self.rollouts.extend(
                get_deployment_rollouts(
                    config, default_timeout=self.default_rollout_timeout
                )
            )
            self.rollout_start = datetime.now()
            return True
        except KubeCfgException as ex:
            if ex.stdout or ex.stderr:
                notifier.output(
                    deploy_events_pb2.DeployOutput(
                        task=self.deploy_task,
                        stdout=ex.stdout or "",
                        stderr=ex.stderr or "",
                        return_code=1,
                        command=ex.command or [],
                    )
                )
            raise

    def validate_task(self, notifier: DeployNotifier):
        assert self.parent.target.kubecfg

        for rollout in self.rollouts:
            self.watch_deployment(
                rollout.name,
                namespace=rollout.namespace,
                cloud=metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud),
                timeout=rollout.timeout,
                notifier=notifier,
            )

    def is_fully_deployed(
        self, name: str, generation: int, dep: V1Deployment, notifier: DeployNotifier
    ) -> bool:
        """Checks if a deployment status is fully deployed.

        based on https://stackoverflow.com/questions/68540187/kubectl-rollout-status-when-the-command-complete:
        Its updated-replica count is at least its desired-replica count (every new pod has been created)
        Its current-replica count is at most its updated-replica count (every old pod has been destroyed)
        Its available-replica count is at least its updated-replica count (every new pod is running)
        """

        def progress_fn(message: str):
            notifier.progress(
                deploy_events_pb2.DeployProgress(
                    task=self.deploy_task,
                    message=message,
                )
            )
            logging.info("%s", message)

        return _is_fully_deployed(name, generation, dep, progress_fn)

    def watch_deployment(
        self,
        deployment_name: str,
        namespace: str,
        cloud: str,
        timeout: timedelta,
        notifier: DeployNotifier,
        pooling_interval: timedelta = timedelta(seconds=10),
    ):
        """Watch for a K8S deployment rollout to finish (or timeout)."""
        logging.info(f"Watch rollout of deployment {namespace}/{deployment_name}")
        notifier.progress(
            deploy_events_pb2.DeployProgress(
                task=self.deploy_task,
                message=f"Watch rollout of deployment {namespace}/{deployment_name}",
            )
        )
        v1 = self.parent.deployment_info.kubernetes_client.get_apps_api(cloud)
        deployment = v1.read_namespaced_deployment(deployment_name, namespace)
        dep = v1.read_namespaced_deployment_status(deployment_name, namespace)
        if not dep:
            raise KubeCfgException(
                f"Deployment {namespace}/{deployment_name} not found"
            )
        start = self.rollout_start or datetime.now()
        while not self.is_fully_deployed(
            deployment_name,
            deployment.metadata.generation,  # type: ignore
            dep,  # type: ignore
            notifier,
        ):
            if start + timeout < datetime.now():
                break
            time.sleep(pooling_interval.total_seconds())
            dep = v1.read_namespaced_deployment_status(deployment_name, namespace)
        else:
            return
        # timeout
        logging.info("Latest deployment status")
        logging.info(f"{dep.status}")  # type: ignore
        logging.warning(
            f"Rollout for deployment {namespace}/{deployment_name} timed out"
        )
        raise KubeCfgException(
            f"Rollout for deployment {namespace}/{deployment_name} timed out"
        )

    def deploy(
        self,
        dry_run: bool,
        notifier: DeployNotifier,
    ) -> bool | None:
        """Run the deployment Bazel target.

        Returns True if the deployment succeeded, False otherwise. None if the operation was skipped
        """
        logging.info(f"Deploying '{self.name}'")

        notifier.started(
            deploy_events_pb2.DeployStarted(
                task=self.deploy_task,
            )
        )
        cloud = metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)
        self.parent.deployment_info.kubernetes_client.login(cloud)
        try:
            r = self.apply_task(dry_run, notifier=notifier)
            if not r:
                notifier.finished(
                    deploy_events_pb2.DeployFinished(
                        task=self.deploy_task,
                        status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_SKIPPED,
                    )
                )
                logging.info(f"Deploying '{self.name}' skipped")
                return None
            logging.info(f"Deploying '{self.name}' succeeded")
            self.is_deployed = True
            notifier.finished(
                deploy_events_pb2.DeployFinished(
                    task=self.deploy_task,
                    status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_SUCCESS,
                )
            )
            return True
        except Exception as ex:  # pylint: disable=broad-except
            logging.exception(ex)
            logging.error(f"Failure during deployment '{self.name}'")
            notifier.finished(
                deploy_events_pb2.DeployFinished(
                    task=self.deploy_task,
                    status=deploy_events_pb2.DeployStatus.DEPLOY_STATUS_FAILED,
                    message=f"Deployment failed: {ex}",
                )
            )
            return False

    def test(self):
        logging.info("Checking kubecfg target %s", self.name)
        assert self.parent.target.kubecfg
        if not self.parent.target.kubecfg.target:
            logging.error("No kubecfg target")
            return False
        # convert bazel target name to file
        t = self.parent.target.kubecfg.target
        if not t.startswith("//"):
            logging.error("No absolute bazel target")
            return False
        t = t[2:]
        t = t.replace(":", "/")
        t += "_json.json"

        if not Path(t).exists():
            logging.error("Missing or invalid kubecfg target: %s", self.target_name)
            return False

        json_data = json.loads(Path(t).read_text(encoding="utf-8"))

        if self.task.cloud not in [
            metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_DEV,
            metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_PROD,
            metadata_pb2.KubeCfgTask.Cloud.GCP_EU_WEST4_PROD,
            metadata_pb2.KubeCfgTask.Cloud.GCP_US_CENTRAL1_GSC_PROD,
            metadata_pb2.KubeCfgTask.Cloud.GCP_AGENT_US_CENTRAL1_PROD,
            metadata_pb2.KubeCfgTask.Cloud.GCP_AGENT_EU_WEST4_PROD,
        ]:
            logging.error("Invalid cloud for kubecfg target")
            return False

        if json_data["cluster_wide"]:
            if self.task.env:
                logging.error("Invalid env for kubecfg target")
                return False
            if self.task.namespace:
                logging.error("Invalid namespace for kubecfg target")
                return False
        else:
            if self.task.env not in [
                metadata_pb2.KubeCfgTask.Env.PROD,
                metadata_pb2.KubeCfgTask.Env.STAGING,
            ]:
                logging.error("Invalid env for kubecfg target")
                return False

            if not self.task.namespace:
                logging.error("Missing namespace for kubecfg target")
                return False

        if not json_data["lint"]:
            logging.error("Lint is disabled for kubecfg target")
            return True

        kubecfg = KubeCfg(
            [json_data["src"]],
            cloud=metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud),
            cluster_wide=json_data["cluster_wide"],
            env=metadata_pb2.KubeCfgTask.Env.Name(self.task.env)
            if not json_data["cluster_wide"]
            else None,
            namespace=self.task.namespace if not json_data["cluster_wide"] else None,
            base_directory=pathlib.Path.cwd().absolute(),
            push_fn=lambda src, dst: f"{dst}@test",
            extra_config_args=[
                (a.name, a.value) for a in self.parent.target.kubecfg.extra_config_args
            ],
        )
        kubecfg_test_util = KubeCfgTestUtil(
            kubecfg,
            allow_multi_apps=json_data["lint_allow_multi_apps"],
            no_rewrite=json_data["norewrite"],
        )

        try:
            kubecfg_test_util.test()
            logging.info(
                "Kubecfg generation test for '%s' succeeded",
                self.name,
            )
        except AssertionError as e:
            logging.error("Kubecfg generation test for '%s' failed", self.name)
            logging.error("%s", e)
            return False
        except KubeCfgException as e:
            logging.error("%s", e)
            logging.error("Kubecfg generation test for '%s' failed", self.name)
            if e.stdout:
                logging.error("%s", e.stdout)
            if e.stderr:
                logging.error("%s", e.stderr)
            return False

        return True

    def validate(self, notifier: DeployNotifier) -> bool:
        if not self.is_deployed:
            # the deployment was either skipped for failed
            logging.info(f"Skipping validation of '{self.name}'")
            return True
        logging.info(f"Validating '{self.name}'")
        cloud = metadata_pb2.KubeCfgTask.Cloud.Name(self.task.cloud)
        self.parent.deployment_info.kubernetes_client.login(cloud)
        try:
            self.validate_task(notifier)
            logging.info(f"Validating deployment of '{self.name}' succeeded")
            notifier.validation_finished(
                deploy_events_pb2.ValidationFinished(
                    task=self.deploy_task,
                    status=deploy_events_pb2.ValidationStatus.VALIDATION_SUCCESS,
                )
            )
            return True
        except KubeCfgException as ex:
            if ex.stdout or ex.stderr:
                notifier.output(
                    deploy_events_pb2.DeployOutput(
                        task=self.deploy_task,
                        stdout=ex.stdout or "",
                        stderr=ex.stderr or "",
                        return_code=0,
                        command=[],
                    )
                )
            else:
                notifier.progress(
                    deploy_events_pb2.DeployProgress(
                        task=self.deploy_task,
                        message=f"Deployment validation failed: {ex}",
                    )
                )
            if ex.stdout:
                logging.info(ex.stdout)
            if ex.stderr:
                logging.info(ex.stderr)
            logging.warning(f"Deployment validation failed: {ex}")
            notifier.validation_finished(
                deploy_events_pb2.ValidationFinished(
                    task=self.deploy_task,
                    status=deploy_events_pb2.ValidationStatus.VALIDATION_FAILED,
                    message=f"Deployment validation failed: {ex}",
                )
            )
            return False
        except Exception as ex:  # pylint: disable=broad-except
            logging.exception(ex)
            logging.error(f"Failure during deployment validation '{self.name}'")
            return False
