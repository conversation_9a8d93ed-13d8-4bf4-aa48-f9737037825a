// see https://www.notion.so/Bazel-based-testing-and-deployment-638c55d03c9a446c884fd0b0d0b25447?pvs=4#74fd85baf83f43fd97c47bb4485d218e for details
{
  deployment: [
    {
      name: 'slack-bot',
      kubecfg: {
        target: '//tools/bot:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_DEV',
            env: 'PROD',
            namespace: 'devtools',
          },
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
          {
            cloud: 'GCP_US_CENTRAL1_GSC_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
          {
            cloud: 'GCP_EU_WEST4_PROD',
            env: 'PROD',
            namespace: 'devtools',
          },
        ],
      },
      health: {
        tier: 'TIER_1_B',
        experts: {
          users: ['dirk'],
          slack_channel: '#system-services',
        },
      },
    },
  ],
}
