load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_test", "pytest_test")

py_library(
    name = "launchdarkly_deps",
    srcs = [
        "config.py",
        "launchdarkly_client.py",
        "launchdarkly_diff_builder.py",
        "launchdarkly_types.py",
    ],
    deps = [
        requirement("gojsonnet"),
        requirement("dataclasses_json"),
        requirement("launchdarkly_api"),
        requirement("structlog"),
        requirement("pyyaml"),
    ],
)

# a script to sync the launchdarkly diff to remote
py_binary(
    name = "feature_flag_sync",
    srcs = ["launchdarkly_sync.py"],
    main = "launchdarkly_sync.py",
    visibility = ["//tools/feature_flags:__subpackages__"],
    deps = [
        ":launchdarkly_deps",
    ],
)

pytest_test(
    name = "feature_flag_diff_test",
    size = "small",
    srcs = ["launchdarkly_diff_builder_test.py"],
    data = [
        "mock_flags.jsonnet",
        "mock_flags.yaml",
        "mock_remote_flags.json",
    ],
    deps = [
        ":launchdarkly_deps",
    ],
)

pytest_test(
    name = "launchdarkly_types_test",
    size = "small",
    srcs = ["launchdarkly_types_test.py"],
    data = [
        "mock_flags.jsonnet",
        "mock_flags.yaml",
        "mock_remote_flags.json",
    ],
    deps = [
        ":launchdarkly_deps",
    ],
)

py_test(
    name = "flags_validation_test",
    srcs = ["flags_validation_test.py"],
    data = [
        "flags.jsonnet",
        "//deploy/tenants:namespaces_json",
    ],
    deps = [
        requirement("gojsonnet"),
        ":launchdarkly_deps",
    ],
)
