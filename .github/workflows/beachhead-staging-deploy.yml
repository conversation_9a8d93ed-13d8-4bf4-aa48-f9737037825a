name: Deploy Beachhead to Staging

on:
  push:
    branches: ['bazel-last-known-good']
    paths:
      - 'clients/beachhead/**'
  workflow_dispatch:
    branches: ['main', 'bazel-last-known-good']
    inputs:
      tag:
        description: 'Custom tag for the image (e.g., STAGING-custom). If not provided, defaults to STAGING-<short-sha>'
        required: false
        type: string
      message:
        description: 'Reason for this deployment'
        required: true
        type: string

permissions:
  contents: read

concurrency:
  group: beachhead-staging-deploy
  cancel-in-progress: false

jobs:
  deploy-beachhead-staging:
    name: Deploy Beachhead to Staging
    if: github.repository_owner == 'augmentcode'
    runs-on: [self-hosted, gcp-us1]
    timeout-minutes: 30
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0

      - name: Get short SHA
        id: short-sha
        run: echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Determine tag
        id: determine-tag
        run: |
          if [ -n "${{ github.event.inputs.tag }}" ]; then
            echo "tag=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
          else
            echo "tag=STAGING-${{ steps.short-sha.outputs.sha_short }}" >> $GITHUB_OUTPUT
          fi

      - name: Determine deployment message
        id: determine-message
        run: |
          if [ -n "${{ github.event.inputs.message }}" ]; then
            echo "message=${{ github.event.inputs.message }}" >> $GITHUB_OUTPUT
          else
            echo "message=push to bazel-last-known-good" >> $GITHUB_OUTPUT
          fi

      - name: Build and Push Beachhead Image to Staging
        run: |
          echo "Building and pushing beachhead image to staging with tag ${{ steps.determine-tag.outputs.tag }}..."
          /usr/bin/gcloud auth configure-docker us-central1-docker.pkg.dev < /dev/null
          bazel run //clients/beachhead/img:deploy-staging -- push --tag="${{ steps.determine-tag.outputs.tag }}"
          bazel run //clients/beachhead/img:deploy-staging -- push --tag=STAGING # Also push the STAGING tag.

      - name: Print Image Details
        run: |
          echo "Deployment complete. Image pushed to:"
          bazel run //clients/beachhead/img:deploy-staging -- outie-name --tag="${{ steps.determine-tag.outputs.tag }}"

      - name: Post success to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: success()
        with:
          payload: |
            {
              "text": ":rocket: Beachhead deployed to staging",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rocket: Beachhead deployed to staging",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Beachhead image successfully deployed to staging environment.\n\nReason: `${{ steps.determine-message.outputs.message }}`\nTag: `${{ steps.determine-tag.outputs.tag }}`\nCommit: `${{ github.sha }}`\nTriggered by: ${{ github.event_name }}\nRun: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Post failure to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: failure()
        with:
          payload: |
            {
              "text": ":rotating_light: Beachhead staging deployment failed",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rotating_light: Beachhead staging deployment failed",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Failed to deploy beachhead to staging.\n\nReason: `${{ steps.determine-message.outputs.message }}`\nSee run for details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
