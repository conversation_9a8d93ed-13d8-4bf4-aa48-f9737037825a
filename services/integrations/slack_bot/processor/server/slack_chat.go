package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/hashicorp/golang-lru/v2/simplelru"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/slack-go/slack"
	"go.opentelemetry.io/otel/attribute"
	statusproto "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/clock"
	"github.com/augmentcode/augment/base/go/secretstring"
	authproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	gleanclient "github.com/augmentcode/augment/services/integrations/glean/client"
	gleanproto "github.com/augmentcode/augment/services/integrations/glean/proto"
	"github.com/augmentcode/augment/services/integrations/slack_bot/common"
	slackboteventproto "github.com/augmentcode/augment/services/integrations/slack_bot/event_proto"
	processorproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	"github.com/augmentcode/augment/services/integrations/webhookmapping"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	settingsservice "github.com/augmentcode/augment/services/settings/client"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

var (
	eventCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_slack_bot_event_count",
		Help: "Counter of slack bot events",
	}, []string{"tenant_name", "event_type", "status"})
	missingCheckpointCount = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "au_slack_bot_missing_checkpoint_count",
		Help: "Counter of slack bot chat responses where there was a missing checkpoint",
	}, []string{"tenant_name"})

	// The maximum time, in seconds, between a Slack message from a user and when the Slackbot will
	// attempt to respond. If more than this amount of time has passed the message will be dropped.
	// Without this flag, the Slackbot could respond to messages up to a week old, which is a strange
	// user experience.
	maxResponseLatencyFlag = featureflags.NewIntFlag("slackbot_max_response_latency_secs", 600)

	selectRepoFlag = featureflags.NewBoolFlag("slackbot_select_repo_context", false)
)

func init() {
	prometheus.MustRegister(
		eventCount,
		missingCheckpointCount,
	)
}

const (
	IN_PROGRESS_EMOJI                = "eyes"
	GLEAN_EMOJI                      = "mag"
	CLOCK_EMOJIS                     = "clock1,clock2,clock3,clock4,clock5,clock6,clock7,clock8,clock9,clock10,clock11,clock12"
	DELETE_MESSAGE_EMOJI             = "x"
	POSTIVE_FEEDBACK_EMOJI           = "+1"
	NEGATIVE_FEEDBACK_EMOJI          = "-1"
	MAX_BATCH_SIZE                   = 20.0
	BATCH_BACKOFF_FACTOR             = 1.3
	GITHUB_INSTALL_APP_PROMOTION     = ":warning: I'd love to help, but I need access to your GitHub repositories to provide the best assistance. Please install the Augment GitHub app to unlock my full potential!"
	GITHUB_INSTALL_REPO_PROMOTION    = `:warning: The Augment GitHub app is installed, but I don't see any repositories connected yet. To get the most out of my capabilities, please visit the <https://github.com/apps/augmentcode/installations/select_target|Augment Github App Settings> and configure the repos you want me to access. You may need your Github org's admin to approve the request. I'll be much more helpful once that's done!`
	GITHUB_CURRENTLY_SYNCING         = `:hourglass: Thanks for connecting your repo! I'm just getting up to speed with your code - give me a few minutes to sync everything, and try again soon!`
	CHANNEL_WELCOME_MESSAGE_TEMPLATE = `Exciting news! <@%s> just invited <@%s> to join the party! :tada:

:wave: I'm Augment, your AI-powered coding buddy. I'm familiar with your codebase and here to help answer your questions. Just tag me in a message or thread!`
	SELECT_REPO_MESSAGE        = ":warning: I see you have multiple repositories connected to Augment. Please select the ones you want to chat with by using the `%s repo-select` command or the button below. Once you've selected the repositories, you're good to go!"
	MISSING_CHECKPOINT_MESSAGE = "Something went wrong. Support has been notified, please try again later."
	WELCOME_MESSAGE_BASE       = `Hi! I'm your AI assistant that understands your codebase and product. :wave:

:bulb: *Quick Start*
 • DM me for 1:1 help
 • Tag <@%s> in channels or threads
 • Ask about the product, code, architecture, debugging, or more
 • React with :+1: or :-1: or use the "Send feedback" slack message shortcut

:book: Learn more about what I can do <https://docs.augmentcode.com/using-augment/slack|here>%s`

	ALLOWLIST_WARNING = "\n\n_*Note*: Augment is currently configured to only respond in the following channels: %s. Contact your admin or support if you'd like to enable Augment in more channels._"

	REPO_SELECT_NOTICE        = ":warning: *One More Step*\nTo unlock my full capabilities, please select the repositories you want me to access using the `%s repo-select` command or the button below. Once you've selected the repositories, you're good to go!\n\n"
	INSTALL_GITHUB_APP_NOTICE = `
:warning: *One More Step*
To unlock my full capabilities, please install the Augment GitHub app. You may need your Github org's admin to approve the request. I'll be much more helpful once that's done!`
	EXTERNAL_CHANNEL_SIGNOFF          = `Note: This channel includes external members, so I've provided a general response. For detailed code-specific help, let's chat in an internal channel!`
	FEEDBACK_ASK_MESSAGE              = "Thanks for reacting! Could you tell us more about this response and your experience with Augment Slackbot?"
	GLEAN_NO_CONTEXT_SIGNOFF_CHANNELS = ":warning: This response doesn't include additional context from your internal documentation. Please sign in to Glean for more comprehensive answers"
	GLEAN_NO_CONTEXT_SIGNOFF_DM       = "Sign in to Augment's Glean integration for more comprehensive answers"
)

var installGithubAppButton = slack.NewActionBlock(
	common.BLOCK_INSTALL_GITHUB_APP_BUTTON,
	slack.NewButtonBlockElement(
		common.ACTION_GITHUB_APP_BUTTON,
		common.ACTION_GITHUB_APP_BUTTON,
		slack.NewTextBlockObject("plain_text", "Install GitHub App", false, false),
	).WithURL("https://github.com/apps/augmentcode/installations/new"),
)

func annotateContext(ctx context.Context, tenantID, tenantName string, requestContext *requestcontext.RequestContext) context.Context {
	ctx = requestContext.AnnotateLogContext(ctx)
	l := zerolog.Ctx(ctx)
	l.UpdateContext(func(c zerolog.Context) zerolog.Context {
		// TODO: automatically get this from the span?
		return c.Str("tenant_id", tenantID).Str("tenant_name", tenantName)
	})
	return ctx
}

// Return the list of channels that the Slackbot is allowed to respond in for
// the given tenant. If the response is empty/nil, then the slackbot is allowed
// to respond in all channels (but still excluding shared channels).
func getChannelAllowlist(ctx context.Context, tenant *tenantproto.Tenant) ([]string, error) {
	if tenant.Config == nil || tenant.Config.Configs == nil {
		return nil, nil
	}
	return getChannelAllowlistFromConfig(ctx, tenant.Config.Configs)
}

func getChannelAllowlistFromConfig(ctx context.Context, config map[string]string) ([]string, error) {
	if config == nil {
		return nil, nil
	}
	allowlistChannels, ok := config["slackbot_allowlist_channels"]
	if !ok {
		return nil, nil
	}
	var channels []string
	err := json.Unmarshal([]byte(allowlistChannels), &channels)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to unmarshal slackbot allowlist channels: %v", allowlistChannels)
		return nil, err
	}
	return channels, nil
}

func postMessage(ctx context.Context, slackClient SlackClient, channel, threadTs, messageTs, slackUserID string, message slack.MsgOption, isEphemeral bool) error {
	options := []slack.MsgOption{message, slack.MsgOptionDisableLinkUnfurl()}
	if threadTs != messageTs && threadTs != "" {
		options = append(options, slack.MsgOptionTS(threadTs))
	}

	var err error
	if isEphemeral {
		_, err = slackClient.PostEphemeral(channel, slackUserID, options...)
	} else {
		_, _, err = slackClient.PostMessage(channel, options...)
	}

	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to post %s message", map[bool]string{true: "ephemeral", false: "regular"}[isEphemeral])
		return err
	}
	return nil
}

type SlackResponseLine struct {
	Text  string
	Error error

	MissingCheckpoint bool
}

type SlackChatHandler struct {
	tenantCache                  tenantwatcherclient.TenantCache
	tokenExchangeClient          tokenexchange.TokenExchangeClient
	tenantLookup                 TenantLookup
	slackClientFactory           SlackClientFactory
	chatClient                   ChatClient
	requestInsightPublisher      ripublisher.RequestInsightPublisher
	settingsServiceClient        settingsservice.SettingsClient
	gleanClient                  gleanclient.GleanClient
	clockEmojis                  []string
	featureFlags                 featureflags.FeatureFlagHandle
	clock                        clock.Clock
	userEmailCache               *simplelru.LRU[string, *secretstring.SecretString]
	webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource
	slackUserMapping             SlackUserMapping
	slashCommand                 string
}

func NewSlackChatHandler(
	tenantCache tenantwatcherclient.TenantCache,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantLookup TenantLookup,
	slackClientFactory SlackClientFactory,
	chatClient ChatClient,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	settingsServiceClient settingsservice.SettingsClient,
	gleanClient gleanclient.GleanClient,
	featureFlags featureflags.FeatureFlagHandle,
	clock clock.Clock,
	webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource,
	slackUserMapping SlackUserMapping,
	slashCommand string,
) (*SlackChatHandler, error) {
	userEmailCache, err := simplelru.NewLRU[string, *secretstring.SecretString](1000, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create user email cache: %w", err)
	}
	return &SlackChatHandler{
		tenantCache:                  tenantCache,
		tokenExchangeClient:          tokenExchangeClient,
		tenantLookup:                 tenantLookup,
		slackClientFactory:           slackClientFactory,
		chatClient:                   chatClient,
		requestInsightPublisher:      requestInsightPublisher,
		settingsServiceClient:        settingsServiceClient,
		gleanClient:                  gleanClient,
		clockEmojis:                  strings.Split(CLOCK_EMOJIS, ","),
		featureFlags:                 featureFlags,
		clock:                        clock,
		userEmailCache:               userEmailCache,
		webhookTenantMappingResource: webhookTenantMappingResource,
		slackUserMapping:             slackUserMapping,
		slashCommand:                 slashCommand,
	}, nil
}

// gets a string version of the conversation history in the thread up until the message mentioning the bot,
// and the last message as a string
func (h *SlackChatHandler) GetConversationHistory(ctx context.Context, replies []slack.Message, messageTs string) ([]string, string) {
	result := []string{}
	for _, reply := range replies {
		messageText := reply.Text
		if reply.Attachments != nil {
			for _, attachment := range reply.Attachments {
				if attachment.Text != "" {
					messageText += "\nQuoted text:\n" + attachment.Text
				}
			}
		}
		// if the message text is still empty, it's possible the message contained only image attachments
		// we'll ignore it for now until chat supports images
		if messageText != "" {
			// append the user ID to the message here so we're not sending empty messages
			messageText = fmt.Sprintf("%s: %s", reply.User, messageText)
			result = append(result, messageText)
		} else {
			log.Ctx(ctx).Warn().Msgf("Ignoring message %s with empty text in channel %s and team %s", reply.Timestamp, reply.Channel, reply.Team)
		}
		if reply.Timestamp == messageTs {
			break
		}
	}

	// handle empty history
	if len(result) == 0 {
		return nil, ""
	}

	if len(result) == 1 {
		return nil, result[0]
	}

	previousMessages, currentMessage := result[:len(result)-1], result[len(result)-1]

	return previousMessages, currentMessage
}

func (h *SlackChatHandler) getReplies(ctx context.Context, slackClient SlackClient, channel string, threadTs string) ([]slack.Message, error) {
	replies := []slack.Message{}
	hasNext := true
	nextCursor := ""
	var newReplies []slack.Message
	var err error
	for hasNext {
		newReplies, hasNext, nextCursor, err = slackClient.GetConversationReplies(&slack.GetConversationRepliesParameters{
			ChannelID: channel,
			Timestamp: threadTs,
			Cursor:    nextCursor,
		})
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to get conversation replies")
			return nil, err
		}
		replies = append(replies, newReplies...)
	}
	return replies, nil
}

func (h *SlackChatHandler) PostChatResponse(
	ctx context.Context,
	slackClient SlackClient,
	tenantName string,
	slackUserID string,
	channel string,
	threadTs string,
	messageTs string,
	lines <-chan SlackResponseLine,
	riResponseEvents chan<- *riproto.RISlackbotResponse_Event,
	disclaimer string,
	ephemeralSignoffBlocks []slack.Block,
) error {
	resultTs := []string{} // list of all generated messages' timestamps
	newMessage := true     // whether the next text should be in a new message
	blockBuilder := NewStreamingTextBlocks(MAX_TEXT_BLOCK_OBJECT_LENGTH)
	var finalText string

	for resp := range lines {
		if resp.MissingCheckpoint {
			// if we're missing a checkpoint, let's post a message to the user
			// and then return
			missingCheckpointCount.WithLabelValues(tenantName).Inc()
			_, err := h.postMessageAndRecordEvent(
				ctx, slackClient, channel, threadTs, riResponseEvents,
				MISSING_CHECKPOINT_MESSAGE,
				[]slack.Block{NewExpandedTextSectionBlock(MISSING_CHECKPOINT_MESSAGE)},
			)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to post message")
				return err
			}
			return nil
		}
		if resp.Error != nil {
			log.Ctx(ctx).Error().Err(resp.Error).Msg("Failed to chat")
			return resp.Error
		}

		// Add the clock emoji at the very end
		clockEmoji := NewExpandedTextSectionBlock(fmt.Sprintf(":%s:", h.clockEmojis[strings.Count(resp.Text, "\n")%len(h.clockEmojis)]))

		// TODO: This is not particularly efficient, we repeat a lot of work here
		blocks := blockBuilder.Update(resp.Text)
		if newMessage {
			ts, err := h.postMessageAndRecordEvent(
				ctx, slackClient, channel, threadTs, riResponseEvents,
				resp.Text, append(blocks, clockEmoji),
			)
			// let's assume that a new message won't overflow the max message length
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to post message")
				return err
			}
			resultTs = append(resultTs, ts)
			newMessage = false
			blockBuilder.Success(resp.Text, blocks)
		} else {
			// let's try posting the message and handle a message too long error
			err := h.updateMessageAndRecordEvent(
				ctx,
				slackClient, channel, resultTs[len(resultTs)-1], riResponseEvents, resp.Text, append(blocks, clockEmoji))
			var slackError slack.SlackErrorResponse
			var slackRateLimitError slack.RateLimitedError
			if errors.As(err, &slackError) && slackError.Err == "msg_too_long" {
				blocks = blockBuilder.Split(resp.Text)
				err := h.updateMessageAndRecordEvent(
					ctx, slackClient, channel, resultTs[len(resultTs)-1],
					riResponseEvents, resp.Text, blocks)
				if err != nil {
					log.Ctx(ctx).Error().Err(err).Msg("Failed to update message")
					return err
				}
				newMessage = true
			} else if errors.As(err, &slackRateLimitError) {
				log.Ctx(ctx).Warn().Msgf("slack rate limited")
				time.Sleep(slackRateLimitError.RetryAfter)
			} else if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to update message")
				return err
			}
		}
		finalText = resp.Text
	}

	// final update to the last message to remove the in-progress emoji and add disclaimer
	blocks := blockBuilder.Update(finalText)
	if len(blocks) > 0 {
		// If disclaimer exists, add divider and context blocks
		var extraBlocks []slack.Block
		if disclaimer != "" {
			extraBlocks = []slack.Block{
				slack.NewDividerBlock(),
				slack.NewContextBlock(
					"disclaimer",
					slack.NewTextBlockObject("mrkdwn", disclaimer, false, false),
				),
			}
		}
		if newMessage {
			ts, err := h.postMessageAndRecordEvent(ctx, slackClient, channel, threadTs, riResponseEvents, finalText, append(blocks, extraBlocks...))
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to post last message")
				return err
			}
			resultTs = append(resultTs, ts)
		} else {
			// we can't overflow here bc we're just removing the in-progress emoji
			// unless we have a disclaimer, in which case we'll try again
			// without the disclaimer
			err := h.updateMessageAndRecordEvent(ctx, slackClient, channel, resultTs[len(resultTs)-1], riResponseEvents, finalText, append(blocks, extraBlocks...))
			if err != nil {
				var slackErr slack.SlackErrorResponse
				if errors.As(err, &slackErr) && slackErr.Err == "msg_too_long" {
					// skip the extra blocks if this message is too long
					err = h.updateMessageAndRecordEvent(ctx, slackClient, channel, threadTs, riResponseEvents, finalText, blocks)
					if err != nil {
						log.Ctx(ctx).Error().Err(err).Msg("Failed to update last message")
						return err
					}
					return nil
				}
				log.Ctx(ctx).Error().Err(err).Msg("Failed to update last message")
				return err
			}

		}
		if len(ephemeralSignoffBlocks) > 0 {
			// Add a small delay to ensure main message appears first
			time.Sleep(500 * time.Millisecond)
			// post an ephemeral message with a button to connect to Glean
			err := postMessage(ctx, slackClient, channel, threadTs, resultTs[len(resultTs)-1], slackUserID, slack.MsgOptionBlocks(ephemeralSignoffBlocks...), true)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to post ephemeral message")
				return err
			}
		}

	}

	return nil
}

func (h *SlackChatHandler) postMessageAndRecordEvent(
	ctx context.Context,
	slackClient SlackClient,
	channel string,
	threadTs string,
	riResponseEvents chan<- *riproto.RISlackbotResponse_Event,
	riText string,
	blocks []slack.Block,
) (string, error) {
	_, ts, err := slackClient.PostMessage(channel, slack.MsgOptionTS(threadTs), slack.MsgOptionDisableLinkUnfurl(), slack.MsgOptionBlocks(blocks...))
	if err != nil {
		return "", err
	}
	riResponseEvents <- &riproto.RISlackbotResponse_Event{
		Time: timestamppb.Now(),
		Event: &riproto.RISlackbotResponse_Event_PostMessage{
			PostMessage: &riproto.RISlackbotResponse_PostMessage{
				ResponseMessageTimestamp: ts,
				Text:                     riText,
			},
		},
	}
	return ts, nil
}

func (h *SlackChatHandler) updateMessageAndRecordEvent(
	ctx context.Context,
	slackClient SlackClient,
	channel string,
	messageTs string,
	riResponseEvents chan<- *riproto.RISlackbotResponse_Event,
	riText string,
	updatedBlocks []slack.Block,
) error {
	_, _, _, err := slackClient.UpdateMessage(channel, messageTs, slack.MsgOptionBlocks(updatedBlocks...), slack.MsgOptionDisableLinkUnfurl())
	if err != nil {
		if err.Error() == "message_not_found" {
			// this is a special case where the message was deleted by the user - stop streaming a response
			log.Ctx(ctx).Info().Msgf("Message not found due to user deletion, stopping response stream for %s", messageTs)
			return nil
		}
		return err
	}
	riResponseEvents <- &riproto.RISlackbotResponse_Event{
		Time: timestamppb.Now(),
		Event: &riproto.RISlackbotResponse_Event_UpdateMessage{
			UpdateMessage: &riproto.RISlackbotResponse_UpdateMessage{
				ResponseMessageTimestamp: messageTs,
				Text:                     riText,
			},
		},
	}
	return nil
}

// takes a stream of chat responses and return partial responses line by line
func (h *SlackChatHandler) getLines(ch <-chan ChatResponse) <-chan SlackResponseLine {
	linesCh := make(chan SlackResponseLine)
	go func() {
		cumulativeAnswer := ""
		partialLine := ""
		missingCheckpoint := false
		for resp := range ch {
			if resp.Resp.CheckpointNotFound {
				missingCheckpoint = true
			}
			if resp.Error != nil {
				linesCh <- SlackResponseLine{Error: resp.Error, MissingCheckpoint: missingCheckpoint}
				close(linesCh)
				return
			}
			partialLine += resp.Resp.Text
			lines := strings.Split(partialLine, "\n")
			for i, line := range lines {
				if i == len(lines)-1 {
					partialLine = line
					break
				}
				// this is incredibly hacky, but claude always starts code blocks with
				// language hints, so something like ```python code ```. slack doesn't support this,
				// so we strip the language hint here. feel free to come up with a better slackbot
				// system prompt and remove this.
				trimmedLine := strings.TrimRight(line, " \t")
				if strings.HasPrefix(line, "```") && strings.HasSuffix(trimmedLine, "```") && len(trimmedLine) > 6 && !strings.Contains(line[3:len(trimmedLine)-3], "```") {
					// For inline code blocks, convert triple backticks to single backtick (```code``` -> `code`)
					line = "`" + strings.TrimSpace(line[3:len(trimmedLine)-3]) + "`"
				} else if strings.HasPrefix(line, "```") {
					// For multi-line code blocks, keep just the opening ```
					line = "```"
				}
				cumulativeAnswer += line + "\n"
				linesCh <- SlackResponseLine{Text: cumulativeAnswer, MissingCheckpoint: missingCheckpoint}
			}
		}
		if partialLine != "" {
			cumulativeAnswer += partialLine
			linesCh <- SlackResponseLine{Text: cumulativeAnswer, MissingCheckpoint: missingCheckpoint}
		}
		close(linesCh)
	}()
	return linesCh
}

// takes a stream of line by line partial responses and batches them up with exponential backoff
func (h *SlackChatHandler) batchLines(lines <-chan SlackResponseLine) <-chan SlackResponseLine {
	batchCh := make(chan SlackResponseLine)
	// note that the sizes here are the number of new lines in the current partial response
	// compared to the previous partial one
	batchSize := 1.0
	go func() {
		defer close(batchCh)
		currentBatchLines := 0
		finalMessage := ""
		for resp := range lines {
			if resp.MissingCheckpoint || resp.Error != nil {
				batchCh <- resp
				return
			}
			currentBatchLines++
			finalMessage = resp.Text
			if currentBatchLines >= int(batchSize) {
				batchCh <- SlackResponseLine{Text: resp.Text}
				finalMessage = ""
				currentBatchLines = 0
				batchSize = min(batchSize*BATCH_BACKOFF_FACTOR, MAX_BATCH_SIZE)
			}
		}
		// send any remaining content
		if len(finalMessage) > 0 {
			batchCh <- SlackResponseLine{Text: finalMessage}
		}
	}()
	return batchCh
}

func (h *SlackChatHandler) HandleReactionAddedEvent(ctx context.Context, event *slackboteventproto.ReactionAddedEvent, slackClient SlackClient) error {
	log.Ctx(ctx).Info().Msgf("Received feedback reaction: %s", event.Reaction)

	if event.Reaction == DELETE_MESSAGE_EMOJI && event.ItemUser == slackClient.BotUserId() {
		_, _, err := slackClient.DeleteMessage(event.ItemChannel, event.ItemTimestamp)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to delete message")
			return err
		}
	} else if strings.HasPrefix(event.Reaction, POSTIVE_FEEDBACK_EMOJI) || strings.HasPrefix(event.Reaction, NEGATIVE_FEEDBACK_EMOJI) {

		// Reactions events don't have a thread timestamp, so we get it from the message we have
		threadTs, err := h.getThreadTsFromReply(event.ItemChannel, event.ItemTimestamp, slackClient)
		if err != nil {
			log.Error().Err(err).Msg("Failed to get thread timestamp")
			return nil
		}

		log.Ctx(ctx).Info().Msgf("Found thread timestamp: %s", threadTs)

		// Create the metadata we need to pass to the modal
		// This is the data we need to reference the original message
		feedbackData := &processorproto.FeedbackModalMetadata{
			Channel:          event.ItemChannel,
			MessageTimestamp: event.ItemTimestamp,
			ThreadTimestamp:  threadTs,
		}

		buttonValue, err := common.EncodeProtoMetadata(feedbackData)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to encode feedback data")
			return err
		}

		provideFeedbackButton := slack.NewActionBlock(
			common.BLOCK_PROVIDE_FEEDBACK_BUTTON,
			slack.NewButtonBlockElement(
				common.ACTION_PROVIDE_FEEDBACK_BUTTON,
				buttonValue,
				slack.NewTextBlockObject("plain_text", "Tell us more", false, false),
			),
		)

		// send a message to the user asking for feedback
		blocks := []slack.Block{
			slack.NewSectionBlock(
				slack.NewTextBlockObject("mrkdwn", FEEDBACK_ASK_MESSAGE, false, false),
				nil,
				nil,
			),
			provideFeedbackButton,
		}
		_, err = slackClient.PostEphemeral(event.ItemChannel, event.User, slack.MsgOptionBlocks(blocks...), slack.MsgOptionTS(threadTs))
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to post ephemeral message")
			return err
		}
	}
	return nil
}

func (h *SlackChatHandler) getThreadTsFromReply(channel string, messageTs string, slackClient SlackClient) (string, error) {
	messages, _, _, err := slackClient.GetConversationReplies(&slack.GetConversationRepliesParameters{
		ChannelID: channel,
		Timestamp: messageTs,
		Limit:     1,
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to get conversation history")
		return "", err
	}
	if len(messages) == 0 {
		return "", fmt.Errorf("No messages found in conversation history")
	}
	threadTs := messages[0].ThreadTimestamp
	if threadTs == "" {
		return "", fmt.Errorf("No thread timestamp found in message")
	}
	return threadTs, nil
}

func (h *SlackChatHandler) HandleEvent(ctx context.Context, data []byte) error {
	ctx, span := tracer.Start(ctx, "HandleEvent")
	defer span.End()

	var event slackboteventproto.SlackEvent
	err := proto.Unmarshal(data, &event)
	if err != nil {
		eventCount.WithLabelValues("", "", "error").Inc()
		log.Error().Err(err).Msg("Failed to unmarshal event")
		return err
	}
	if event.Metadata == nil {
		eventCount.WithLabelValues("", "", "error").Inc()
		log.Error().Msg("Missing metadata")
		return status.Error(codes.InvalidArgument, "Missing metadata")
	}
	requestId := requestcontext.RequestId(event.Metadata.RequestId)
	tenantID := event.Metadata.TenantId

	span.SetAttributes(
		attribute.String("request_id", requestId.String()),
		attribute.String("tenant_id", event.Metadata.TenantId),
		attribute.String("tenant_name", event.Metadata.TenantName),
		attribute.String("event_type", event.EventType),
	)

	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "slack-bot", secretstring.SecretString{})
	ctx = annotateContext(ctx, tenantID, event.Metadata.TenantName, requestContext)

	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForService(ctx, tenantID, []tokenexchangeproto.Scope{
		// To get tenant settings for slack bot repos, and SETTINGS_W to call
		// glean
		// TODO: I think we can remove SETTINGS_W once python servers support
		// per-method scope checks
		tokenexchangeproto.Scope_SETTINGS_RW,
		// To get content from github-state, and then issue the chat request.
		// Some of the content-manager APIs beneath that require CONTENT_W.
		tokenexchangeproto.Scope_CONTENT_RW,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get service token")
		return err
	}
	requestContext = requestContext.WithAuthToken(serviceToken)

	log.Ctx(ctx).Info().Msgf("Received event: requestId=%s, tenantID=%s, eventType=%s", requestId.String(),
		tenantID, event.EventType)

	if tenantID == "" {
		log.Ctx(ctx).Error().Msg("Missing tenantID")
		eventCount.WithLabelValues("", "", "error").Inc()
		return status.Error(codes.InvalidArgument, "Missing tenantID")
	}

	tenant, err := h.tenantCache.GetTenant(tenantID)
	if err != nil {
		if errors.Is(err, tenantwatcherclient.ErrTenantNotFound) {
			eventCount.WithLabelValues("", "", "error").Inc()
			log.Ctx(ctx).Warn().Msg("Tenant not found: Ignoring event")
			return nil
		}
		log.Ctx(ctx).Error().Err(err).Msg("Error getting tenant")
		return err
	}

	err = h.handleEvent(ctx, &event, tenant, requestContext)
	if err != nil {
		status := status.Code(err)
		eventCount.WithLabelValues(event.Metadata.TenantName, event.EventType, status.String()).Inc()
		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle event")
		return err
	}
	eventCount.WithLabelValues(event.Metadata.TenantName, event.EventType, "OK").Inc()
	return nil
}

func (h *SlackChatHandler) handleEvent(ctx context.Context, event *slackboteventproto.SlackEvent,
	tenant *tenantproto.Tenant, requestContext *requestcontext.RequestContext,
) error {
	tenantID := tenant.Id
	tenantName := tenant.Name
	tenantInfo := &riproto.TenantInfo{
		TenantId:   tenantID,
		TenantName: tenantName,
	}
	tenantSettings, err := h.settingsServiceClient.GetTenantSettings(ctx, requestContext)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting tenant settings")
		return err
	}

	slackClient, err := h.slackClientFactory.GetSlackClient(ctx, tenantID, tenantSettings.Settings)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get slack client")
		return err
	}

	var messageTs string
	var threadTs string
	var channel string
	var slackUserID string
	switch event.GetEvent().(type) {
	case *slackboteventproto.SlackEvent_AppMention:
		appMention := event.GetAppMention()
		slackUserID = appMention.User
		// filter out the bot mentioning itself
		if event.GetAppMention().User == slackClient.BotUserId() {
			log.Ctx(ctx).Info().Msgf("Skipping app mention bc from bot: tenantID=%s eventType=%s",
				tenantID, event.EventType)
			return nil
		}
		// slack uses timestamps as unique IDs
		messageTs = appMention.Timestamp
		threadTs = appMention.ThreadTimestamp
		// if it's a top-level message, we'll want to respond to it by
		// starting a thread
		if threadTs == "" {
			threadTs = messageTs
		}
		channel = appMention.Channel
	case *slackboteventproto.SlackEvent_Message:
		message := event.GetMessage()
		slackUserID = message.User
		// filter out events from the bot itself
		if event.GetMessage().User == slackClient.BotUserId() {
			log.Ctx(ctx).Info().Msgf("Skipping event bc from bot: requestId=%s, tenantID=%s eventType=%s", requestContext.RequestId.String(),
				tenantID, event.EventType)
			return nil
		}
		messageTs = message.Timestamp
		threadTs = message.ThreadTimestamp
		if threadTs == "" {
			threadTs = messageTs
		}
		channel = message.Channel
	case *slackboteventproto.SlackEvent_ReactionAdded:
		// We only record reactions from real users in RI. We don't bother with metadata in this case
		// because it's not really a chat request.
		reactionAdded := event.GetReactionAdded()
		botUser := slackClient.BotUserId()

		if reactionAdded.User == botUser || reactionAdded.ItemUser != botUser {
			log.Ctx(ctx).Info().Msgf("Skipping reaction added event from tenantID=%s because from bot or not on bot message", tenantID)
			return nil
		}

		riRequestEvent := getSlackbotRequestEvent(tenantInfo, event)
		err = h.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), tenantInfo, riRequestEvent)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event")
		}

		err = h.HandleReactionAddedEvent(ctx, event.GetReactionAdded(), slackClient)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to handle reaction added event")
			return err
		}
		return nil
	case *slackboteventproto.SlackEvent_MemberJoinedChannel:
		botUser := slackClient.BotUserId()
		memberJoined := event.GetMemberJoinedChannel()
		if memberJoined.User != botUser {
			return nil
		}

		err := h.handleBotJoinedChannel(ctx, slackClient, memberJoined, tenant)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to handle bot joined channel event")
			return err
		}
		return nil
	case *slackboteventproto.SlackEvent_AppUninstalled:
		// Note that this will update tenantSettings
		riErr := h.handleAppUninstalled(ctx, tenantInfo, requestContext)
		if riErr != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to handle app uninstalled event")
		}
		return nil
	case *slackboteventproto.SlackEvent_AppHomeOpened:
		err := h.handleAppHomeOpened(ctx, tenantSettings.Settings, tenant, slackClient, event.GetAppHomeOpened())
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to handle app home opened event")
			return err
		}
		return nil
	default:
		log.Ctx(ctx).Info().Msgf("Unexpected event type: %s", event.EventType)
		return nil
	}

	// Record the request and request metadata. We publish the metadata immediately but defer
	// publishing  the request so that we can record additional info about the request as we learn it.
	// This logic intentionally comes after skipping bot events above, so that we don't have to filter
	// out the bot's own events in every query.
	riMetadataEvent := h.getSlackbotMetadataEvent(ctx, slackClient, tenantInfo, requestContext, slackUserID)
	riRequestEvent := getSlackbotRequestEvent(tenantInfo, event)
	err = h.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), tenantInfo, riMetadataEvent)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event")
	}
	defer func() {
		err := h.requestInsightPublisher.PublishRequestEvent(ctx, requestContext.RequestId.String(), tenantInfo, riRequestEvent)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event")
		}
	}()

	// Don't respond if too much time has passed.
	slackMessageTime, err := parseSlackTimestamp(messageTs)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to parse slack message timestamp. Will continue processing the message.")
	} else {
		maxResponseLatencySecs, err := maxResponseLatencyFlag.Get(h.featureFlags)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed read max response latency flag. Will continue processing the message.")
		} else if h.clock.Since(slackMessageTime) > time.Duration(maxResponseLatencySecs)*time.Second {
			log.Ctx(ctx).Info().Msgf(
				"Skipping event because it's too old: requestId=%s, messageTs=%s, currentTs=%s",
				requestContext.RequestId.String(), slackMessageTime, h.clock.Now().Format(time.RFC3339))
			return nil
		}
	}

	// Set up a channel to stream response events to, and a goroutine that aggregates them and
	// publishes when the channel closes.
	riResponseEvents := make(chan *riproto.RISlackbotResponse_Event, 10)
	go func() {
		requestID := requestContext.RequestId.String()
		events := []*riproto.RISlackbotResponse_Event{}
		for event := range riResponseEvents {
			events = append(events, event)
		}
		err := h.requestInsightPublisher.PublishRequestEvent(
			ctx, requestID, tenantInfo,
			getSlackbotResponseEvent(tenantInfo, channel, threadTs, messageTs, events),
		)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to publish request event for request %s", requestID)
		}
	}()
	defer close(riResponseEvents)

	// make sure we aren't in a shared channel
	// we can add a caching layer for this API call in the future
	channelInfo, err := slackClient.GetConversationInfo(&slack.GetConversationInfoInput{ChannelID: channel})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get conversation info")
		return err
	}

	channelType := riproto.RISlackbotRequest_UNKNOWN_CHANNEL_TYPE
	if channelInfo.IsIM {
		channelType = riproto.RISlackbotRequest_IM
	} else if channelInfo.IsMpIM {
		channelType = riproto.RISlackbotRequest_MPIM
	} else if channelInfo.IsExtShared {
		channelType = riproto.RISlackbotRequest_SHARED_CHANNEL
	} else if channelInfo.IsPrivate {
		channelType = riproto.RISlackbotRequest_PRIVATE_CHANNEL
	} else if channelInfo.IsGeneral {
		channelType = riproto.RISlackbotRequest_GENERAL_CHANNEL
	} else if channelInfo.IsChannel {
		channelType = riproto.RISlackbotRequest_CHANNEL
	}
	riRequestEvent.GetSlackbotRequest().ChannelType = channelType

	// If there is a non-empty channel allowlist, we pop up an error
	// in channels that are not in the allowlist.
	channelAllowlist, err := getChannelAllowlist(ctx, tenant)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get channel allowlist")
		return err
	}
	if len(channelAllowlist) > 0 {
		log.Ctx(ctx).Info().Msgf("Checking channel %s against allowlist %v", channel, channelAllowlist)
		if !slices.Contains(channelAllowlist, channel) && !(channelInfo.IsIM && slices.Contains(channelAllowlist, slackUserID)) {
			message := slack.MsgOptionText("Sorry, I am not enabled in this channel. Contact your admin or support to enable Augment in more channels.", false)
			return postMessage(ctx, slackClient, channel, threadTs, messageTs, slackUserID, message, false)
		}
		// This channel is allowlisted, go ahead and try to post the message
	}

	// add an in-progress indicator if we aren't in the assistant view
	if !h.isAssistantView(ctx, tenantSettings.Settings, channelInfo.IsIM) {
		err = slackClient.AddReaction(IN_PROGRESS_EMOJI, slack.NewRefToMessage(channel, messageTs))
		if err != nil {
			if err.Error() == "already_reacted" {
				log.Ctx(ctx).Info().Msg("Already reacted")
			} else {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to add reaction")
				return err
			}
		}
		defer func() {
			// remove the in-progress indicator
			err := slackClient.RemoveReaction(IN_PROGRESS_EMOJI, slack.NewRefToMessage(channel, messageTs))
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to remove reaction")
			}
		}()
	}

	// Check if any repos have been connected to the app
	repoStatusBlocks, repoStatusRiText := h.getRepoStatusMessage(ctx, requestContext, tenantSettings.Settings, channel, channelInfo.IsExtShared, event)
	if len(repoStatusBlocks) > 0 {
		// Send an error message to the user if no repos are connected
		_, err := h.postMessageAndRecordEvent(ctx, slackClient, channel, threadTs, riResponseEvents, repoStatusRiText, repoStatusBlocks)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to post repo status message")
			return err
		}
		log.Ctx(ctx).Info().Msgf("Posted repo status message instead of chatting: requestId=%s, tenantID=%s, eventType=%s", event.Metadata.RequestId,
			event.Metadata.TenantId, event.EventType)
		return nil
	}

	// get existing replies and dialog from them
	replies, err := h.getReplies(ctx, slackClient, channel, threadTs)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get replies")
		return err
	}
	previousThreadMessages, currentMessageText := h.GetConversationHistory(ctx, replies, messageTs)

	// TODO: this is hack for now - we should clip the conversation history within the glean service if needed
	conversationHistory := strings.Join(previousThreadMessages, "\n\n")
	fullHistory := fmt.Sprintf(
		"Here is the previous slack conversation:\n\n%s\n\n"+
			"Here is the user question for you:\n\n%s",
		conversationHistory,
		currentMessageText,
	)

	gleanOauthURL := ""
	gleanDocuments, err := h.getGleanDocuments(ctx, requestContext, tenantSettings.Settings, fullHistory, slackClient, slackUserID, tenantID, tenantName, channel, messageTs, channelInfo)
	if err != nil {
		var authErr *GleanAuthRequiredError
		if errors.As(err, &authErr) {
			gleanOauthURL = authErr.OAuthURL
		} else {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to get glean documents")
			return err
		}
	}

	// start the chat request
	chat, err := h.chatClient.Chat(ctx, tenantID, tenantSettings.Settings, requestContext, previousThreadMessages, currentMessageText, slackClient.BotUserId(), channel, channelInfo.Name, channelInfo.IsExtShared, gleanDocuments)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to chat")
		return err
	}
	// batch chat responses into line by line partial responses
	lines := h.getLines(chat)
	// batch partial responses exponentially to avoid hitting rate limits
	// on really long responses
	lines = h.batchLines(lines)

	disclaimer := ""
	emphemeralSignoffBlocks := []slack.Block{}
	if channelInfo.IsExtShared {
		// If the channel is shared, we add a disclaimer to the response that we don't use any codebase context
		disclaimer = EXTERNAL_CHANNEL_SIGNOFF
	} else if gleanOauthURL != "" {

		// Add the user id to the oauth url so that we can map augment users to slack users
		// TODO: we may eventually want to make state a json object so that we can pass more data if needed and annotate what the data is used for
		gleanOauthURL += fmt.Sprintf("&state=%s", slackUserID)

		// If the user is not logged into Glean, we add a disclaimer to the response
		if channelInfo.IsIM {
			emphemeralSignoffBlocks = []slack.Block{NewExpandedTextSectionBlock(GLEAN_NO_CONTEXT_SIGNOFF_DM)}
		} else {
			disclaimer = GLEAN_NO_CONTEXT_SIGNOFF_CHANNELS
		}

		// send an ephemeral message with a button to connect to Glean
		emphemeralSignoffBlocks = append(emphemeralSignoffBlocks, slack.NewActionBlock(
			common.BLOCK_SIGN_INTO_GLEAN_BUTTON,
			slack.NewButtonBlockElement(
				common.ACTION_SIGN_INTO_GLEAN_BUTTON,
				common.ACTION_SIGN_INTO_GLEAN_BUTTON,
				slack.NewTextBlockObject("plain_text", "Connect Glean", false, false),
			).WithStyle(slack.StylePrimary).WithURL(gleanOauthURL)))

	}

	// post the chat response
	err = h.PostChatResponse(ctx, slackClient, tenant.Name, slackUserID, channel, threadTs, messageTs, lines, riResponseEvents, disclaimer, emphemeralSignoffBlocks)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to post chat response")
		return err
	}

	log.Ctx(ctx).Info().Msgf("Finished: requestId=%s, tenantID=%s, eventType=%s", requestContext.RequestId.String(),
		tenantID, event.EventType)

	return nil
}

type GleanAuthRequiredError struct {
	OAuthURL string
}

func (e *GleanAuthRequiredError) Error() string {
	return "Glean authentication required"
}

// getGleanDocuments returns documents from glean for the user message if available and the feature flag is enabled
// returns an empty slice if the feature flag is disabled, the channel is shared, or glean is not configured
// if the user is not logged into Glean or needs to re-authenticate, it returns an GleanAuthRequiredError with the oauth url to log in instead of documents
func (h *SlackChatHandler) getGleanDocuments(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	tenantSettings *settingsproto.TenantSettings,
	message string,
	slackClient SlackClient,
	slackUserId string,
	tenantID string,
	tenantName string,
	channel string,
	messageTs string,
	channelInfo *slack.Channel,
) (gleanDocuments []*gleanproto.Document, err error) {
	ctx, span := tracer.Start(ctx, "getGleanDocuments")
	defer span.End()

	useGlean := false
	flag, err := h.featureFlags.BindContext("tenant_name", tenantName)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to bind context to feature flag, defaulting to false")
	} else {
		useGlean, err = flag.GetBool("enable_glean", false)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to get use glean flag, defaulting to false")
		}
	}

	var returnErr error
	if useGlean && !channelInfo.IsExtShared {
		// Check if Glean is configured. User ID is a vestige of when we used OAuth with Glean,
		// since everyone should be on JWT now we can just pass a dummy value.
		augmentUserId := "dummyuser"
		isConfigured, err := h.gleanClient.IsToolConfigured(ctx, requestContext, augmentUserId)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to check if Glean is configured")
			span.RecordError(err)
			return gleanDocuments, nil
		}

		if !isConfigured {
			log.Ctx(ctx).Warn().Msg("Glean is not configured, not using Glean for Slack response")
			return gleanDocuments, nil
		}

		// Add an emoji to show that we are using Glean
		err = slackClient.AddReaction(GLEAN_EMOJI, slack.NewRefToMessage(channel, messageTs))
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to add Glean emoji reaction")
			span.RecordError(err)
			// We don't return here as this is not critical to the main functionality
		}

		// Get the user's email from Slack (needed when tenant uses JWT).
		userEmail, err := h.getUserEmail(ctx, slackClient, slackUserId)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to get user email")
			span.RecordError(err)
			// Continue without email, it can work with OAuth. We'll just use an empty string.
			emptyUserEmail := secretstring.New("")
			userEmail = &emptyUserEmail
		}

		req := &gleanproto.SearchRequest{
			Query: message,
			// only include private docs in IMs with the bot to safeguard against private docs being shared in channels with users who shouldn't have access
			IncludePrivateDocs: channelInfo.IsIM,
			UserId:             augmentUserId,
			UserEmail:          userEmail.Expose(),
		}

		gleanResp, err := h.gleanClient.Search(ctx, requestContext, req)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Msg("Failed to call glean")
			span.RecordError(err)
			// don't return an error here, as this is not critical to the chat process
			// TODO: should we return an error if this fails? Or at least some
			// kind of warning to the user?
		} else if gleanResp.OauthUrl != "" {
			log.Ctx(ctx).Info().Msg("Glean re-authentication required, returning oauth_url")
			gleanOauthURL := gleanResp.OauthUrl
			returnErr = &GleanAuthRequiredError{OAuthURL: gleanOauthURL}
		} else {
			log.Ctx(ctx).Info().Msgf("Found %d documents from Glean", len(gleanResp.Documents))
			gleanDocuments = gleanResp.Documents
		}

		if gleanResp.StatusCode != 0 && (gleanResp.StatusCode < 200 || gleanResp.StatusCode >= 300) {
			// Log non 2xx status codes just for helping to diagnose when Glean server is being wonky.
			// We check StatusCode == 0 to prevent erroring when the server didn't set status code.
			log.Ctx(ctx).Warn().Msgf("Non-2xx status code from Glean search: %d", gleanResp.StatusCode)
			span.RecordError(err)
		}

		// remove glean emoji reaction if no documents were found
		if len(gleanDocuments) == 0 {
			err = slackClient.RemoveReaction(GLEAN_EMOJI, slack.NewRefToMessage(channel, messageTs))
			if err != nil {
				log.Ctx(ctx).Warn().Err(err).Msg("Failed to remove Glean emoji reaction")
				span.RecordError(err)
				// We don't return here as this is not critical to the main functionality
			}
		}
	}
	return gleanDocuments, returnErr
}

func (h *SlackChatHandler) handleAppUninstalled(ctx context.Context, tenantInfo *riproto.TenantInfo, requestContext *requestcontext.RequestContext) error {
	riEvent := ripublisher.NewRequestEvent()
	riEvent.Event = &riproto.RequestEvent_SlackbotInstallationEvent{
		SlackbotInstallationEvent: &riproto.RISlackbotInstallationEvent{
			Status: &statusproto.Status{
				Code:    int32(codes.OK),
				Message: "Success",
			},
			EventType: riproto.InstallEventType_UNINSTALL,
		},
	}

	riErr := h.requestInsightPublisher.PublishRequestEvent(context.Background(), "", tenantInfo, riEvent)
	if riErr != nil {
		log.Ctx(ctx).Error().Err(riErr).Msg("Failed to publish request event")
	}

	tenantID := tenantInfo.TenantId
	err := h.webhookTenantMappingResource.Delete(ctx, fmt.Sprintf("slack-%s", tenantID))
	if err != nil {
		return fmt.Errorf("failed to delete webhook mapping: %w", err)
	}

	h.slackClientFactory.ClearClientForTenant(tenantID)

	// reset slack settings to an empty struct
	// use empty instead of nil to make sure the field gets updated instead of ignored
	settings := &settingsproto.TenantSettings{
		SlackSettings:  &settingsproto.SlackSettings{},
		GithubSettings: nil,
	}

	// We need to get a new token with SETTINGS_W
	serviceToken, err := h.tokenExchangeClient.GetSignedTokenForService(ctx, tenantID, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_SETTINGS_RW,
	})
	if err != nil {
		return fmt.Errorf("failed to get service token: %w", err)
	}
	requestContext = requestContext.WithAuthToken(serviceToken)

	_, err = h.settingsServiceClient.UpdateTenantSettings(ctx, requestContext, settings, "")
	if err != nil {
		return fmt.Errorf("Error updating tenant settings: %s", err)
	}

	log.Ctx(ctx).Info().Msgf("Uninstalled slackbot for tenant %s", tenantID)

	return nil
}

func (h *SlackChatHandler) getUserEmail(
	ctx context.Context, slackClient SlackClient, slackUserID string,
) (*secretstring.SecretString, error) {
	email, ok := h.userEmailCache.Get(slackUserID)
	if ok {
		return email, nil
	}
	userProfile, err := slackClient.GetUserProfile(&slack.GetUserProfileParameters{UserID: slackUserID})
	if err != nil {
		return nil, err
	}
	emailSecret := secretstring.New(userProfile.Email)
	h.userEmailCache.Add(slackUserID, &emailSecret)
	return &emailSecret, nil
}

func (h *SlackChatHandler) getRepoStatusMessage(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	tenantSettings *settingsproto.TenantSettings,
	channel string,
	isShared bool,
	event *slackboteventproto.SlackEvent,
) (blocks []slack.Block, riText string) {
	selectRepo, err := selectRepoFlag.Get(h.featureFlags)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get select repo context flag. Defaulting to false.")
		selectRepo = false
	}

	reposForContext, checkpoints, allRepos, installationId, err := h.tenantLookup.GetRepos(ctx, tenantSettings, requestContext, channel, selectRepo)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg(
			"Failed to get repos. Defaulting to assuming github is installed.",
		)
		return nil, ""
	} else if installationId == 0 {
		log.Ctx(ctx).Info().Msgf("No installation found. Promoting github app")
		return []slack.Block{
			NewExpandedTextSectionBlock(GITHUB_INSTALL_APP_PROMOTION),
			installGithubAppButton,
		}, GITHUB_INSTALL_APP_PROMOTION
	} else if len(allRepos) == 0 {
		// GitHub typically doesn't allow app installations without selecting repos.
		// However, we've encountered edge cases where this occurred, so we're keeping this check as a safeguard.
		log.Ctx(ctx).Info().Msgf("No repos installed. Promoting repo installation directions.")
		return []slack.Block{
			NewExpandedTextSectionBlock(GITHUB_INSTALL_REPO_PROMOTION),
		}, GITHUB_INSTALL_REPO_PROMOTION
	} else if len(reposForContext) == 0 && selectRepo && !isShared {
		// Not checking for shared channels because no repo context is used in shared channels.
		log.Ctx(ctx).Info().Msgf("No repo selected for channel. Sending message to select repo.")

		buttonValue, err := common.EncodeProtoMetadata(event)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to encode regeneration event data")
			return nil, ""
		}

		repoSelectButton := slack.NewActionBlock(
			common.BLOCK_REPO_SELECT_BUTTON,
			slack.NewButtonBlockElement(
				common.ACTION_OPEN_REPO_SELECT_MODAL,
				buttonValue,
				slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
			),
		)

		return []slack.Block{
			NewExpandedTextSectionBlock(fmt.Sprintf(SELECT_REPO_MESSAGE, h.slashCommand)),
			repoSelectButton,
		}, fmt.Sprintf(SELECT_REPO_MESSAGE, h.slashCommand)
	} else if len(checkpoints) == 0 && !isShared {
		// Not checking for shared channels because no repo context is used in shared channels.

		log.Ctx(ctx).Info().Msgf("Repo for channel not synced. Sending message to try again soon.")
		return []slack.Block{
			NewExpandedTextSectionBlock(GITHUB_CURRENTLY_SYNCING),
		}, GITHUB_CURRENTLY_SYNCING
	}

	return nil, ""
}

// Get the Time from a Slack timestamp. Slack timestamps are formatted as "XXX.XXX", and only the
// portion before the "." is actually a timestamp. See https://github.com/slackhq/slack-api-docs/issues/7.
func parseSlackTimestamp(timestamp string) (time.Time, error) {
	parts := strings.Split(timestamp, ".")
	if len(parts) != 2 {
		return time.Time{}, fmt.Errorf("invalid Slack timestamp format: %s", timestamp)
	}

	seconds, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse seconds from Slack timestamp: %v", err)
	}

	return time.Unix(seconds, 0), nil
}

func (h *SlackChatHandler) getSlackbotMetadataEvent(
	ctx context.Context, slackClient SlackClient, tenantInfo *riproto.TenantInfo,
	requestContext *requestcontext.RequestContext, slackUserID string,
) *riproto.RequestEvent {
	// riUserID is the deprecated notion of a user id, where we record an email if possible and
	// otherwise record the slack ID. We're in the process of migrating to explicit opaqueUserId and
	// userEmail fields in the requestMetadata object instead. We always record the Slack ID as the
	// opaqueUserId in metadata, and record the associated email from Slack as userEmail if possible.
	var riUserID string
	var userEmail string
	emailSecret, err := h.getUserEmail(ctx, slackClient, slackUserID)
	if err == nil {
		riUserID = emailSecret.Expose()
		userEmail = emailSecret.Expose()
	} else {
		// If we can't get the user's email, record the slack ID, which is better than nothing.
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get user profile")
		riUserID = fmt.Sprintf("slack:%s", slackUserID)
	}

	riMetadataEvent := ripublisher.NewRequestEvent()
	riMetadataEvent.Event = &riproto.RequestEvent_RequestMetadata{
		RequestMetadata: &riproto.RequestMetadata{
			RequestType: riproto.RequestType_SLACKBOT_CHAT,
			SessionId:   requestContext.RequestSessionId.String(),
			UserId:      riUserID,
			OpaqueUserId: &authproto.UserId{
				UserId:     slackUserID,
				UserIdType: authproto.UserId_SLACK,
			},
			UserEmail: &userEmail,
			UserAgent: "slack-bot-processor",
		},
	}
	return riMetadataEvent
}

func getSlackbotRequestEvent(
	tenantInfo *riproto.TenantInfo, event *slackboteventproto.SlackEvent,
) *riproto.RequestEvent {
	riRequestEvent := ripublisher.NewRequestEvent()
	riRequestEvent.Event = &riproto.RequestEvent_SlackbotRequest{
		SlackbotRequest: &riproto.RISlackbotRequest{
			SlackEvent: event,
		},
	}
	return riRequestEvent
}

func getSlackbotResponseEvent(
	tenantInfo *riproto.TenantInfo, channel string, threadTs string, messageTs string,
	events []*riproto.RISlackbotResponse_Event,
) *riproto.RequestEvent {
	riResponseEvent := ripublisher.NewRequestEvent()
	riResponseEvent.Event = &riproto.RequestEvent_SlackbotResponse{
		SlackbotResponse: &riproto.RISlackbotResponse{
			Channel:                 channel,
			ThreadTimestamp:         threadTs,
			RequestMessageTimestamp: messageTs,
			ResponseEvents:          events,
		},
	}
	return riResponseEvent
}

func (h *SlackChatHandler) handleBotJoinedChannel(ctx context.Context, slackClient SlackClient, memberJoined *slackboteventproto.MemberJoinedChannelEvent, tenant *tenantproto.Tenant) error {
	log.Ctx(ctx).Info().Msgf("Bot joined channel: %s", memberJoined.Channel)

	allowListedChannels, err := getChannelAllowlist(ctx, tenant)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get channel allowlist")
		return err
	}

	if len(allowListedChannels) > 0 && !slices.Contains(allowListedChannels, memberJoined.Channel) {
		log.Ctx(ctx).Info().Msgf("Channel %s is not in allowlist, skipping welcome message", memberJoined.Channel)
		return nil
	}

	welcomeMessage := fmt.Sprintf(CHANNEL_WELCOME_MESSAGE_TEMPLATE, memberJoined.Inviter, slackClient.BotUserId())

	_, _, err = slackClient.PostMessage(
		memberJoined.Channel,
		slack.MsgOptionText(welcomeMessage, false),
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to send welcome message")
		return err
	}

	return nil
}

func (h *SlackChatHandler) handleAppHomeOpened(
	ctx context.Context,
	tenantSettings *settingsproto.TenantSettings,
	tenant *tenantproto.Tenant,
	slackClient SlackClient,
	event *slackboteventproto.AppHomeOpenedEvent,
) error {
	log.Ctx(ctx).Info().Msgf("App home opened: %s", event.User)

	// Find conversation history of channel to see if messages have already been sent
	conversationHistory, err := slackClient.GetConversationHistory(&slack.GetConversationHistoryParameters{
		ChannelID: event.Channel,
		Limit:     1,
	})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get conversation history")
		return err
	}
	if len(conversationHistory.Messages) > 0 {
		log.Ctx(ctx).Info().Msg("Conversation history found, skipping welcome message")
		return nil
	}

	// check to see if channel/user is in allowlist
	allowListedChannels, err := getChannelAllowlist(ctx, tenant)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get channel allowlist")
		return err
	}
	allowlistMsg := ""
	if len(allowListedChannels) > 0 {
		if !slices.Contains(allowListedChannels, event.Channel) && !slices.Contains(allowListedChannels, event.User) {
			log.Ctx(ctx).Info().Msgf("Channel %s is not in allowlist, skipping welcome message", event.Channel)
			return nil
		}
		allowlistMsg = fmt.Sprintf(ALLOWLIST_WARNING, formatAllowlistChannelList(allowListedChannels))
	}

	blocks := []slack.Block{
		slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", fmt.Sprintf(WELCOME_MESSAGE_BASE, slackClient.BotUserId(), allowlistMsg), false, false),
			nil,
			nil,
		),
	}

	installedGithubApp := tenantSettings != nil && tenantSettings.GithubSettings != nil && tenantSettings.GithubSettings.InstallationId != 0
	// check if multiple repos are installed for the orgs
	// doesn't check for individual repo mappings since its assuming this is the first time the bot is being used by the user
	multipleRepos := installedGithubApp && len(tenantSettings.GithubSettings.Repos) > 1

	if !installedGithubApp {
		blocks = append(blocks, slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", INSTALL_GITHUB_APP_NOTICE, false, false),
			nil,
			nil,
		))
		blocks = append(blocks, installGithubAppButton)
	} else if multipleRepos {
		blocks = append(blocks, slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", fmt.Sprintf(REPO_SELECT_NOTICE, h.slashCommand), false, false),
			nil,
			nil,
		))
		repoSelectActionBlock := slack.NewActionBlock(
			common.BLOCK_REPO_SELECT_BUTTON,
			slack.NewButtonBlockElement(
				common.ACTION_OPEN_REPO_SELECT_MODAL,
				common.ACTION_OPEN_REPO_SELECT_MODAL,
				slack.NewTextBlockObject("plain_text", "Select Repositories", false, false),
			),
		)
		blocks = append(blocks, repoSelectActionBlock)
	}

	_, _, err = slackClient.PostMessage(
		event.Channel,
		slack.MsgOptionBlocks(blocks...),
		slack.MsgOptionDisableLinkUnfurl(),
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to send welcome message")
		return err
	}
	return nil
}

func (h *SlackChatHandler) isAssistantView(
	ctx context.Context,
	tenantSettings *settingsproto.TenantSettings,
	isIM bool,
) bool {
	if !isIM {
		return false
	}

	log.Ctx(ctx).Info().Msgf("Assistant flow enabled: %v", tenantSettings.SlackSettings.AssistantFlowEnabled)
	return tenantSettings.SlackSettings.AssistantFlowEnabled
}

// formatAllowlistChannelList formats a list of channel IDs into a string of channel mentions. If any of the channels are DMs, it will add "and some specific DMs" to the end of the string.
// group DMs are formatted as channels, so will likely be seen as *private channel* to most users
// e.g. "<#C123>, <#C456>, <#C789>, and some specific DMs"
func formatAllowlistChannelList(channels []string) string {
	items := []string{}
	hasDMs := false
	for _, channel := range channels {
		// IMs are specified as "D" or we allowlist users as "U"
		if strings.HasPrefix(channel, "D") || strings.HasPrefix(channel, "U") {
			hasDMs = true
		} else {
			items = append(items, fmt.Sprintf("<#%s>", channel))
		}
	}
	if hasDMs {
		items = append(items, "some specific DMs")
	}
	return joinWithCommasAndAnd(items)
}
