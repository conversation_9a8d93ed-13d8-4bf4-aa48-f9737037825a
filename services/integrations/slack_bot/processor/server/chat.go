package main

import (
	"context"
	"errors"
	"io"
	"strings"
	"sync"

	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	modelfinderproto "github.com/augmentcode/augment/services/api_proxy/model_finder_proto"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	modelinstanceproto "github.com/augmentcode/augment/services/deploy/model_instance/proto"
	gleanproto "github.com/augmentcode/augment/services/integrations/glean/proto"
	slackbotproto "github.com/augmentcode/augment/services/integrations/slack_bot/processor/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
)

var defaultModelFlag = featureflags.NewStringFlag("chat_raw_output_model", "")

type ChatResponse struct {
	Resp  chatproto.ChatResponse
	Error error
}

type ChatClient interface {
	Chat(ctx context.Context, tenantID string, tenantSettings *settingsproto.TenantSettings, requestContext *requestcontext.RequestContext, previousThreadMessages []string, currentMessage string, botId string, channelId string, channelName string, isExternal bool, gleanDocuments []*gleanproto.Document) (<-chan ChatResponse, error)
	Close()
}

type ModelChatClient struct {
	modelName  string
	chatClient chatproto.ChatClient
	chatConn   *grpc.ClientConn
}

func (c *ModelChatClient) Close() {
	c.chatConn.Close()
}

func NewModelChatClient(modelName string, endpoint string, clientCerts credentials.TransportCredentials) (*ModelChatClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(clientCerts),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	return &ModelChatClient{
		modelName:  modelName,
		chatClient: chatproto.NewChatClient(conn),
		chatConn:   conn,
	}, nil
}

type ChatClientImpl struct {
	featureFlags      featureflags.FeatureFlagHandle
	tenantLookup      TenantLookup
	modelFinderClient modelfinderproto.ModelFinderClient
	clientCreds       credentials.TransportCredentials
	mutex             sync.Mutex
	modelChatClient   *ModelChatClient
}

func NewChatClient(ctx context.Context,
	featureFlags featureflags.FeatureFlagHandle,
	tenantLookup TenantLookup,
	modelFinderClient modelfinderproto.ModelFinderClient, clientCreds credentials.TransportCredentials,
) ChatClient {
	return &ChatClientImpl{
		featureFlags:      featureFlags,
		tenantLookup:      tenantLookup,
		modelFinderClient: modelFinderClient,
		clientCreds:       clientCreds,
		mutex:             sync.Mutex{},
		modelChatClient:   nil,
	}
}

func (c *ChatClientImpl) Close() {
	if c.modelChatClient != nil {
		c.modelChatClient.Close()
	}
}

func (c *ChatClientImpl) getChatModels(ctx context.Context) ([]*modelinstanceproto.ModelInstanceConfig, error) {
	resp := make([]*modelinstanceproto.ModelInstanceConfig, 0)
	models, err := c.modelFinderClient.GetInferenceModels(ctx, &modelfinderproto.GetModelsRequest{})
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting models")
		return nil, err
	}
	for _, model := range models.Models {
		if *model.ModelType == modelinstanceproto.ModelType_CHAT {
			resp = append(resp, model)
		}
	}
	return resp, nil
}

func (c *ChatClientImpl) getFeatureFlagHandle(ctx context.Context, tenantID string) (featureflags.FeatureFlagHandle, error) {
	tenantName, err := c.tenantLookup.GetTenantName(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	return c.featureFlags.BindContext("tenant_name", tenantName)
}

func getModelPriority(model *modelinstanceproto.ModelInstanceConfig) int {
	if model.GetChat() != nil {
		return int(*model.GetChat().ModelPriority)
	}
	return -1
}

func (c *ChatClientImpl) getCurrentChatModel(ctx context.Context, tenantID string) (*modelinstanceproto.ModelInstanceConfig, error) {
	ff, err := c.getFeatureFlagHandle(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	modelName, err := defaultModelFlag.Get(ff)
	if err != nil {
		return nil, err
	}
	if modelName == "" {
		// get default model
		models, err := c.getChatModels(ctx)
		if err != nil {
			return nil, err
		}
		if len(models) == 0 {
			return nil, status.Error(codes.FailedPrecondition, "No chat models found")
		}
		currentModel := models[0]
		for _, model := range models {
			if getModelPriority(model) > getModelPriority(currentModel) {
				currentModel = model
			}
		}
		return currentModel, nil
	} else {
		models, err := c.getChatModels(ctx)
		if err != nil {
			return nil, err
		}
		for _, model := range models {
			if *model.Name == modelName {
				return model, nil
			}
		}
		return nil, status.Error(codes.NotFound, "Model not found")
	}
}

func (c *ChatClientImpl) getModelChatClient(ctx context.Context, tenantID string) (*ModelChatClient, error) {
	chatModel, err := c.getCurrentChatModel(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	c.mutex.Lock()
	if c.modelChatClient != nil {
		log.Ctx(ctx).Info().Msgf("Using existing chat client for model: %v", chatModel)
		if *chatModel.Name == c.modelChatClient.modelName {
			c.mutex.Unlock()
			return c.modelChatClient, nil
		}
	}
	c.mutex.Unlock()
	log.Ctx(ctx).Info().Msgf("Creating new chat client for model: %v", chatModel)
	client, err := NewModelChatClient(
		*chatModel.Name, *chatModel.GetChat().ChatEndpoint, c.clientCreds)
	if err != nil {
		return nil, err
	}
	c.mutex.Lock()
	if c.modelChatClient != nil {
		c.modelChatClient.Close()
	}
	c.modelChatClient = client
	c.mutex.Unlock()
	return client, nil
}

func (c *ChatClientImpl) Chat(
	ctx context.Context,
	tenantID string,
	tenantSettings *settingsproto.TenantSettings,
	requestContext *requestcontext.RequestContext,
	previousThreadMessages []string,
	currentMessage string,
	botId string,
	channelId string,
	channelName string,
	isExternal bool,
	gleanDocuments []*gleanproto.Document,
) (<-chan ChatResponse, error) {
	ch := make(chan ChatResponse)
	ff, err := c.getFeatureFlagHandle(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	selectRepoForContext, err := selectRepoFlag.Get(ff)
	if err != nil {
		log.Error().Err(err).Msg("Error getting select repo context flag, defaulting to false")
		selectRepoForContext = false
	}
	go func() {
		defer close(ch)
		log.Ctx(ctx).Info().Msgf("Chat request: tenantID=%s, requestId=%s", tenantID, requestContext.RequestId)

		var reposForContext []*slackbotproto.Repo
		var checkpoints []*blobsproto.Blobs
		var allRepos []*settingsproto.RepoInformation

		// Get repo information for the chat request
		// We don't want to add any repo information for external requests
		if isExternal {
			log.Ctx(ctx).Info().Msgf("Not adding repo information for external request")
		} else {
			reposForContext, checkpoints, allRepos, _, err = c.tenantLookup.GetRepos(ctx, tenantSettings, requestContext, channelId, selectRepoForContext)
			if err != nil {
				ch <- ChatResponse{
					Error: err,
				}
				return
			}
		}

		if len(checkpoints) == 0 {
			baseCheckpoint := ""
			checkpoints = append(checkpoints, &blobsproto.Blobs{
				BaselineCheckpointId: &baseCheckpoint,
			})
		}
		log.Ctx(ctx).Info().Msgf("Sending chat request: checkpoints=%s, conversation_history_len=%v", checkpoints, len(strings.Join(previousThreadMessages, "\n\n")))
		chatMetadata := &slackbotproto.SlackbotChatMetadata{
			BotId:                  botId,
			ChannelName:            channelName,
			PreviousThreadMessages: previousThreadMessages,
			Repos:                  reposForContext,
			AllRepos:               allRepos,
			GleanDocuments:         gleanDocuments,
		}
		chatMetadataBytes, err := protojson.Marshal(chatMetadata)
		if err != nil {
			ch <- ChatResponse{
				Error: err,
			}
			return
		}

		chatClient, err := c.getModelChatClient(ctx, tenantID)
		if err != nil {
			ch <- ChatResponse{
				Error: err,
			}
			return
		}

		chatRequest := &chatproto.ChatRequest{
			ModelName: chatClient.modelName,
			ChatHistory: []*chatproto.Exchange{{
				RequestMessage: string(chatMetadataBytes),
			}},
			Message:             currentMessage,
			Blobs:               checkpoints,
			PromptFormatterName: "slackbot",
		}

		ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
		stream, err := chatClient.chatClient.ChatStream(ctx, chatRequest)
		if err != nil {
			ch <- ChatResponse{
				Error: err,
			}
			return
		}
		for {
			resp, err := stream.Recv()
			if err != nil {
				if errors.Is(err, io.EOF) {
					return
				}
				ch <- ChatResponse{
					Error: err,
				}
				return
			}
			ch <- ChatResponse{
				Resp: *resp,
			}
		}
	}()
	return ch, nil
}
