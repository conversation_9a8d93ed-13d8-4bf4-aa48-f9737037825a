package main

import (
	"encoding/json"
	"flag"
	"io"
	"net/http"
	"sync"
	"time"

	"github.com/augmentcode/augment/base/logging"
	"github.com/rs/zerolog/log"
)

func handleNotFound(w http.ResponseWriter, req *http.Request) {
	log.Warn().Msgf("Not found: %s", req.URL.Path)
	w.<PERSON><PERSON><PERSON><PERSON><PERSON>(http.StatusNotFound)
	w.<PERSON>().Set("Content-Type", "application/json")
	response := `{"error": "Endpoint not found"}`
	_, err := w.Write([]byte(response))
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
	}
}

type SlackAddReaction struct {
	Channel   string `json:"channel"`
	Name      string `json:"name"`
	Timestamp string `json:"timestamp"`
}

func ParseSlackAddReaction(req *http.Request) *SlackAddReaction {
	r := &SlackAddReaction{}
	r.Channel = req.FormValue("channel")
	r.Name = req.FormValue("name")
	r.Timestamp = req.FormValue("timestamp")
	return r
}

type Action struct {
	Name     string      `json:"name"`
	Request  interface{} `json:"request"`
	Response interface{} `json:"response"`
}

type Handler struct {
	mutex         sync.Mutex
	actions       []*Action
	nextResponses map[string][]string
}

type Response struct {
	Ok bool `json:"ok"`
}

func (h *Handler) getResponse(key string, response interface{}) error {
	responseText, ok := h.nextResponses[key]
	if ok {
		if len(responseText) == 0 {
			delete(h.nextResponses, key)
		} else {
			delete(h.nextResponses, key)
			t := responseText[0]
			err := json.Unmarshal([]byte(t), &response)
			if err != nil {
				return err
			}
			log.Info().Msgf("Response for key: %s: %s", key, t)
			return nil
		}
	}
	log.Info().Msgf("No response for key: %s", key)
	return nil
}

func (h *Handler) HandleAddReaction(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Add reaction: %s", req.URL.Path)
	if err := req.ParseForm(); err != nil {
		http.Error(w, "Unable to parse form", http.StatusBadRequest)
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	r := ParseSlackAddReaction(req)
	log.Info().Msgf("Add reaction: %v", *r)

	response := &Response{Ok: true}
	err := h.getResponse("reactions.add", response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	h.actions = append(h.actions, &Action{
		Name: "reactions.add", Request: r,
		Response: response,
	})

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

type SlackConversationReplies struct {
	Channel string `json:"channel"`
	Ts      string `json:"ts"`
}

func ParseSlackConversationReplies(req *http.Request) *SlackConversationReplies {
	r := &SlackConversationReplies{}
	r.Channel = req.FormValue("channel")
	r.Ts = req.FormValue("ts")
	return r
}

type Message struct {
	Type string `json:"type"`
	Ts   string `json:"ts"`
	Text string `json:"text"`
	User string `json:"user"`
}

type ConversationRepliesResponse struct {
	Ok       bool      `json:"ok"`
	Messages []Message `json:"messages"`
}

func (h *Handler) HandleConversationReplies(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Conversation replies: %s", req.URL.Path)
	if err := req.ParseForm(); err != nil {
		http.Error(w, "Unable to parse form", http.StatusBadRequest)
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	r := ParseSlackConversationReplies(req)
	log.Info().Msgf("Conversation replies: %v", *r)

	response := &ConversationRepliesResponse{Ok: true}
	err := h.getResponse("conversations.replies", response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	h.actions = append(h.actions, &Action{
		Name: "conversations.replies", Request: r,
		Response: response,
	})

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

type SlackConversationInfo struct {
	Channel string `json:"channel"`
}

func ParseSlackConversationInfo(req *http.Request) *SlackConversationInfo {
	r := &SlackConversationInfo{}
	r.Channel = req.FormValue("channel")
	return r
}

type ConversionInfoChannel struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	IsChannel   bool   `json:"is_channel"`
	Created     int    `json:"created"`
	IsArchived  bool   `json:"is_archived"`
	IsGeneral   bool   `json:"is_general"`
	IsShared    bool   `json:"is_shared"`
	IsExtShared bool   `json:"is_ext_shared"`
}

type ConversationInfoResponse struct {
	Ok      bool                  `json:"ok"`
	Channel ConversionInfoChannel `json:"channel"`
}

func (h *Handler) HandleConversationInfo(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Conversation info: %s", req.URL.Path)
	if err := req.ParseForm(); err != nil {
		http.Error(w, "Unable to parse form", http.StatusBadRequest)
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	r := ParseSlackConversationInfo(req)
	log.Info().Msgf("Conversation info: %v", *r)

	response := &ConversationInfoResponse{
		Ok: true,
		Channel: ConversionInfoChannel{
			Id:   "C12345",
			Name: "test-channel", IsChannel: true, Created: int(time.Now().Unix()), IsArchived: false, IsGeneral: false, IsExtShared: false,
		},
	}
	err := h.getResponse("conversations.info", response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	h.actions = append(h.actions, &Action{
		Name: "conversations.info", Request: r,
		Response: response,
	})

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

type RemoveReaction struct {
	Channel   string `json:"channel"`
	Name      string `json:"name"`
	Timestamp string `json:"timestamp"`
}

func ParseRemoveReaction(req *http.Request) *RemoveReaction {
	r := &RemoveReaction{}
	r.Channel = req.FormValue("channel")
	r.Name = req.FormValue("name")
	r.Timestamp = req.FormValue("timestamp")
	return r
}

func (h *Handler) HandleRemoveReaction(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Remove reaction: %s", req.URL.Path)
	if err := req.ParseForm(); err != nil {
		http.Error(w, "Unable to parse form", http.StatusBadRequest)
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	r := ParseRemoveReaction(req)
	log.Info().Msgf("Remove reaction: %v", *r)

	response := &Response{Ok: true}
	err := h.getResponse("reactions.remove", response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	h.actions = append(h.actions, &Action{
		Name: "reactions.remove", Request: r,
		Response: response,
	})

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

type PostEphemeral struct {
	Channel string `json:"channel"`
	User    string `json:"user"`
	Text    string `json:"text"`
}

func ParsePostEphemeral(req *http.Request) *PostEphemeral {
	r := &PostEphemeral{}
	r.Channel = req.FormValue("channel")
	r.User = req.FormValue("user")
	r.Text = req.FormValue("text")
	return r
}

func (h *Handler) HandleChatPostEphemeral(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Post ephemeral: %s", req.URL.Path)
	if err := req.ParseForm(); err != nil {
		http.Error(w, "Unable to parse form", http.StatusBadRequest)
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	r := ParsePostEphemeral(req)
	log.Info().Msgf("Post ephemeral: %v", *r)
	h.actions = append(h.actions, &Action{Name: "chat.postEphemeral", Request: r})

	response := &Response{Ok: true}
	err := h.getResponse("chat.postEphemeral", response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

type PostMessage struct {
	Channel  string `json:"channel"`
	Text     string `json:"text"`
	ThreadTs string `json:"thread_ts"`
}

func ParsePostMessage(req *http.Request) *PostMessage {
	r := &PostMessage{}
	r.Channel = req.FormValue("channel")
	r.Text = req.FormValue("text")
	r.ThreadTs = req.FormValue("thread_ts")
	return r
}

func (h *Handler) HandleChatPostMessage(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Post message: %s", req.URL.Path)
	if err := req.ParseForm(); err != nil {
		http.Error(w, "Unable to parse form", http.StatusBadRequest)
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	r := ParsePostMessage(req)
	log.Info().Msgf("Post message: %v", *r)
	h.actions = append(h.actions, &Action{Name: "chat.postMessage", Request: r})

	response := &Response{Ok: true}
	err := h.getResponse("chat.postMessage", response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

type UpdateMessage struct {
	Channel  string `json:"channel"`
	Text     string `json:"text"`
	Ts       string `json:"ts"`
	ThreadTs string `json:"thread_ts"`
}

func ParseUpdateMessage(req *http.Request) *UpdateMessage {
	r := &UpdateMessage{}
	r.Channel = req.FormValue("channel")
	r.Text = req.FormValue("text")
	r.Ts = req.FormValue("ts")
	r.ThreadTs = req.FormValue("thread_ts")
	return r
}

func (h *Handler) HandleChatUpdate(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Update message: %s", req.URL.Path)
	if err := req.ParseForm(); err != nil {
		http.Error(w, "Unable to parse form", http.StatusBadRequest)
		return
	}

	h.mutex.Lock()
	defer h.mutex.Unlock()

	r := ParseUpdateMessage(req)
	log.Info().Msgf("Update message: %v", *r)
	h.actions = append(h.actions, &Action{Name: "chat.update", Request: r})

	response := &Response{Ok: true}
	err := h.getResponse("chat.update", response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	jsonData, err := json.Marshal(response)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

func (h *Handler) HandleClear(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Clear: %s", req.URL.Path)
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.actions = []*Action{}
	h.nextResponses = make(map[string][]string)

	w.WriteHeader(http.StatusOK)
}

type NextResponse struct {
	Action   string `json:"action"`
	Response string `json:"response"`
}

func (h *Handler) HandleNextResponse(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Next response: %s", req.URL.Path)
	h.mutex.Lock()
	defer h.mutex.Unlock()

	body, err := io.ReadAll(req.Body)
	if err != nil {
		log.Error().Err(err).Msg("Error reading body")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	var nextResponse NextResponse
	err = json.Unmarshal(body, &nextResponse)
	if err != nil {
		log.Error().Err(err).Msg("Error parsing body")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	log.Info().Msgf("Next response: %v", nextResponse)

	h.nextResponses[nextResponse.Action] = append(h.nextResponses[nextResponse.Action], nextResponse.Response)

	w.WriteHeader(http.StatusOK)
}

func (h *Handler) HandleGet(w http.ResponseWriter, req *http.Request) {
	log.Info().Msgf("Get: %s", req.URL.Path)
	h.mutex.Lock()
	defer h.mutex.Unlock()

	jsonData, err := json.Marshal(h.actions)
	if err != nil {
		log.Error().Err(err).Msg("Error marshalling actions")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
	log.Info().Msgf("Actions: %v: %s", h.actions, string(jsonData))
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(jsonData)
	if err != nil {
		log.Error().Err(err).Msg("Error writing response")
		w.WriteHeader(http.StatusInternalServerError)
		return
	}
}

func main() {
	logging.SetupServerLogging()

	flag.Parse()

	h := &Handler{
		mutex: sync.Mutex{},
	}

	http.HandleFunc("POST /reactions.add", h.HandleAddReaction)
	http.HandleFunc("POST /reactions.remove", h.HandleRemoveReaction)
	http.HandleFunc("POST /conversations.replies", h.HandleConversationReplies)
	http.HandleFunc("POST /conversations.info", h.HandleConversationInfo)
	http.HandleFunc("POST /chat.postEphemeral", h.HandleChatPostEphemeral)
	http.HandleFunc("POST /chat.postMessage", h.HandleChatPostMessage)
	http.HandleFunc("POST /chat.update", h.HandleChatUpdate)

	http.HandleFunc("POST /next_response", h.HandleNextResponse)
	http.HandleFunc("POST /clear", h.HandleClear)
	http.HandleFunc("GET /all", h.HandleGet)
	http.HandleFunc("/", handleNotFound)

	log.Info().Msg("Server starting on :8080")
	err := http.ListenAndServe(":8080", nil)
	if err != nil {
		log.Fatal().Err(err).Msg("Error starting server")
	}
}
