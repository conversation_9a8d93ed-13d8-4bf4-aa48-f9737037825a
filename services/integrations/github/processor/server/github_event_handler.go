package main

import (
	"archive/tar"
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"slices"
	"strings"
	"time"
	"unicode/utf8"

	"github.com/bradleyfalzon/ghinstallation/v2"
	"github.com/google/go-github/v64/github"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/attribute"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	githubstateclient "github.com/augmentcode/augment/services/integrations/github/state/client"
	githubstatepersistproto "github.com/augmentcode/augment/services/integrations/github/state/proto"
	webhookmapping "github.com/augmentcode/augment/services/integrations/webhookmapping"

	statusproto "google.golang.org/genproto/googleapis/rpc/status"

	blobnames "github.com/augmentcode/augment/base/blob_names"
	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	contentmanagerproto "github.com/augmentcode/augment/services/content_manager/proto"
	githubproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	requestinsightproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	settingsservice "github.com/augmentcode/augment/services/settings/client"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangeproto "github.com/augmentcode/augment/services/token_exchange/proto"
)

var (
	registerRepoRequests = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_github_repository_register_repos",
			Help: "Number of repository uploader requests to register repos",
		},
		[]string{"tenant_id", "reason", "status"},
	)

	uploadDiffRequests = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_github_repository_upload_diff",
			Help: "Number of repository uploader requests to update a repo using a diff",
		},
		[]string{"tenant_id", "status"},
	)

	getFileFromGithub = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_github_repository_uploader_get_file_from_github",
			Help: "Number of calls to github api to retreive a file",
		},
		[]string{"tenant_id"},
	)

	applyGitDiff = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_github_repository_git_apply_diff",
			Help: "Number of repository uploader requests to git apply",
		},
		[]string{"tenant_id", "status"},
	)

	pubsubMessage = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_github_processor_pubsub_message",
			Help: "Number of pubsub messages received",
		},
		[]string{"event_type", "status"},
	)

	diffRenameRegex           = regexp.MustCompile(`(^|\n)diff --git a\/(.*) b\/(.*)\n`)
	diffSymlinkSubmoduleRegex = regexp.MustCompile(`(^|\n)diff --git a\/(.*) b\/(.*)\n.*(120000|160000)\n`)

	binaryPatchRegex   = regexp.MustCompile(`error: (?:cannot apply binary patch to|binary patch does not apply to|binary patch to) '(.+)'`)
	symlinkRegex       = regexp.MustCompile(`error: (.+): wrong type`)
	patchNotApplyRegex = regexp.MustCompile(`error: (.+): patch does not apply`)
	fileNotFoundRegex  = regexp.MustCompile(`no file named (.+) found in`)
)

type UploadDiffResult string

const (
	tooManyFiles                   UploadDiffResult = "TOO_MANY_FILES"
	tooManyCommits                 UploadDiffResult = "TOO_MANY_COMMITS"
	ignoreFileChanged              UploadDiffResult = "IGNORE_FILE_CHANGED"
	fileIgnoreStatusChanged        UploadDiffResult = "FILE_IGNORE_STATUS_CHANGED"
	githubCompareCommitsApiTimeout UploadDiffResult = "GITHUB_API_TIMEOUT"
	failedToApplyDiff              UploadDiffResult = "FAILED_TO_APPLY_DIFF"
	noReRegister                   UploadDiffResult = "" // returned when we do not need to re-register the repo
)

const (
	// constants for uploading blobs to content manager
	// matching the values of the constants in services/content_manager/server/deploy.jsonnet

	// max number of blob names that can be added or removed to a single checkpoint
	maxBlobNamesForCheckpoint = 10000

	// max bytes that can be uploaded together in a batch upload
	// mirroring  batch_upload_content_limit
	maxBatchUploadBlobContentSize = 1024 * 1024 * 2

	// max number of blobs that can be uploaded together in a batch upload
	// mirroring batch_upload_blob_limit
	maxBatchUploadBlobCount = 1000

	// GitHub API comparison limits
	// These constants are used to determine when to process changes as a patch
	// or when to re-register the entire repository.
	// Reference: https://docs.github.com/en/rest/commits/commits?apiVersion=2022-11-28#compare-two-commits

	//  max number of files that can be changed in a single diff
	// if more than this, we re-register the repo
	maxFileChangesInOnePush = 300

	//  max number of commits that can be changed in a single diff
	// if more than this, we re-register the repo
	maxCommitsInOnePush = 250
)

const (
	// max characters in a patch error to send to request insights
	maxGitApplyErrorRILength = 1000

	// max retries to register a repo for hitting a rate limit
	maxRepoRegistrationRetries = 3
)

func init() {
	prometheus.MustRegister(
		registerRepoRequests,
		uploadDiffRequests,
		getFileFromGithub,
		applyGitDiff,
		pubsubMessage,
	)
}

func blobsToString(blobs *blobsproto.Blobs) string {
	if blobs == nil {
		return "(nil)"
	}
	return fmt.Sprintf("blobs(baseline_checkpoint_id=%s num_added=%d num_deleted=%d)",
		blobs.GetBaselineCheckpointId(),
		len(blobs.GetAdded()),
		len(blobs.GetDeleted()),
	)
}

// Deduplicates the list of RepoInformation objects in GithubSettings, in place.
func dedupGithubSettingsRepos(settings *settingsproto.TenantSettings) (foundDuplicates bool) {
	if settings == nil || settings.GithubSettings == nil {
		return false
	}

	seen := make(map[string]struct{})
	newRepos := make([]*settingsproto.RepoInformation, 0, len(settings.GithubSettings.Repos))
	for _, repo := range settings.GithubSettings.Repos {
		key := fmt.Sprintf("%s/%s", repo.RepoOwner, repo.RepoName)
		if _, ok := seen[key]; ok {
			foundDuplicates = true
		} else {
			seen[key] = struct{}{}
			newRepos = append(newRepos, repo)
		}
	}
	settings.GithubSettings.Repos = newRepos
	return foundDuplicates
}

func annotateContext(ctx context.Context, tenantID, tenantName string, requestContext *requestcontext.RequestContext) context.Context {
	ctx = requestContext.AnnotateLogContext(ctx)
	l := zerolog.Ctx(ctx)
	l.UpdateContext(func(c zerolog.Context) zerolog.Context {
		// TODO: automatically get this from the span?
		return c.Str("tenant_id", tenantID).Str("tenant_name", tenantName)
	})
	return ctx
}

type GithubEventHandler interface {
	// Handles a github push event and updates the state accordingly
	// For new repos, download all files. For known repos, retrieve previously
	// uploaded content, apply the diff, and upload the changed files.
	// For deleted installations, remove the repos from the state.
	HandleGithubEvent(ctx context.Context, data []byte) error

	// Register a repo into github state, uploading all its contents as of the latest commit.
	// Overwrites any existing state for the repo.
	// If headCommitSHA is not empty, it will upload the repo as of that commit SHA.
	RegisterRepo(
		ctx context.Context,
		requestContext *requestcontext.RequestContext,
		githubClient GithubRepositoriesClient,
		repoInfo *settingsproto.RepoInformation,
		tenantID string,
		tenantName string,
		branch string,
		headCommitSHA string,
	) (*blobsproto.Blobs, *errorWithRateLimit)

	// Used on repo installation events
	// Sleeps until the github rate limit reset time
	handleInstallationRateLimitSleep(ctx context.Context, tenantID string, rateLimit *github.Rate) error

	// Get RequestContext for the given tenant id.
	GetRequestContext(ctx context.Context, tenantID string, requestId requestcontext.RequestId) (*requestcontext.RequestContext, error)
}

type githubEventHandlerImpl struct {
	requestInsightPublisher      ripublisher.RequestInsightPublisher
	contentManagerClient         contentmanagerclient.ContentManagerClient
	tokenExchangeClient          tokenexchange.TokenExchangeClient
	githubStateClient            githubstateclient.GithubStateClient
	featureFlagHandle            featureflags.FeatureFlagHandle
	webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource
	settingsServiceClient        settingsservice.SettingsClient
	githubClients                githubClients
	lagTracker                   lagTracker

	// skip fetching the latest commit for e2e testing, as that is deterministic
	registerRepoWithLatestCommit bool
}

type DiffInfo struct {
	filename    string
	oldBlobName blobnames.BlobName
	newBlobName blobnames.BlobName
	// for removed or renamed files
	oldFileName string
	removed     bool
	renamed     bool

	// This is a possible ignore file, so:
	// 1) it is okay if it is missing
	// 2) if it changes, we should be forced to re-download the repo, so no diff handling
	isIgnoreFile bool
}

func NewGithubEventHandler(
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	contentManagerClient contentmanagerclient.ContentManagerClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	githubClients githubClients,
	featureFlagHandle featureflags.FeatureFlagHandle,
	githubStateClient githubstateclient.GithubStateClient,
	registerRepoWithLatestCommit bool,
	webhookTenantMappingResource webhookmapping.WebhookTenantMappingResource,
	settingsServiceClient settingsservice.SettingsClient,
	lagTracker lagTracker,
) GithubEventHandler {
	return &githubEventHandlerImpl{
		requestInsightPublisher:      requestInsightPublisher,
		contentManagerClient:         contentManagerClient,
		tokenExchangeClient:          tokenExchangeClient,
		githubClients:                githubClients,
		featureFlagHandle:            featureFlagHandle,
		githubStateClient:            githubStateClient,
		registerRepoWithLatestCommit: registerRepoWithLatestCommit,
		webhookTenantMappingResource: webhookTenantMappingResource,
		settingsServiceClient:        settingsServiceClient,
		lagTracker:                   lagTracker,
	}
}

func (geh *githubEventHandlerImpl) HandleGithubEvent(ctx context.Context, data []byte) error {
	ctx, span := tracer.Start(ctx, "HandleGithubEvent")
	defer span.End()

	var event githubproto.GithubEvent
	err := proto.Unmarshal(data, &event)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal event")
		pubsubMessage.WithLabelValues("", status.Code(err).String()).Inc()
		return err
	}
	span.SetAttributes(
		attribute.String("tenant_id", event.Metadata.TenantId),
		attribute.String("tenant_name", event.Metadata.TenantName),
		attribute.String("event_type", event.EventType),
	)

	ctx = log.Ctx(ctx).With().Str("event_type", event.EventType).Logger().WithContext(ctx)

	requestId := requestcontext.RequestId(event.Metadata.RequestId)
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "github-processor", secretstring.SecretString{})
	ctx = annotateContext(ctx, event.Metadata.TenantId, event.Metadata.TenantName, requestContext)

	githubClient := geh.githubClients.installationClient(
		event.Metadata.TenantName,
		event.Metadata.InstallationId)

	riResult := &requestinsightproto.RIGithubProcessingResult{}
	riEvents := []*requestinsightproto.RequestEvent{
		ripublisher.NewRequestEvent(),
		ripublisher.NewRequestEvent(),
	}
	riEvents[0].Event = &requestinsightproto.RequestEvent_GithubEvent{
		GithubEvent: &requestinsightproto.RIGithubEvent{
			GithubEvent: &event,
		},
	}
	riEvents[1].Event = &requestinsightproto.RequestEvent_GithubProcessingResult{
		GithubProcessingResult: riResult,
	}

	switch eventData := event.Event.(type) {
	case *githubproto.GithubEvent_Push:
		log.Ctx(ctx).Info().Msgf("Handling push event for tenantID %s, tenantName %s, requestID %s", event.Metadata.TenantId, event.Metadata.TenantName, requestId)
		err = geh.HandlePush(ctx, eventData, event.Metadata.TenantId,
			event.Metadata.TenantName, requestId, githubClient.Repositories,
			riResult)
	case *githubproto.GithubEvent_Installation:
		log.Ctx(ctx).Info().Msgf("Handling installation event for tenantID %s, tenantName %s, requestID %s", event.Metadata.TenantId, event.Metadata.TenantName, requestId)
		err = geh.HandleInstallation(ctx, eventData, event.Metadata.TenantId, event.Metadata.TenantName, requestId, githubClient.Repositories)
	default:
		err = fmt.Errorf("unknown event type: %s", event.EventType)
	}

	if err != nil {
		// Check if it's a `*ghinstallation.HTTPError`, if so: pull out the status code
		// and set it in the `riResult.Status.Code`
		var httpErr *ghinstallation.HTTPError
		var statusCode int32
		var statusCodeString string

		if errors.As(err, &httpErr) {
			statusCode = int32(httpErr.Response.StatusCode)
			statusCodeString = httpErr.Response.Status
		} else {
			// Default to trying to interpret the error as a gRPC error (and corresponding status codes).
			statusCode = int32(status.Code(err))
			statusCodeString = status.Code(err).String()
		}
		riResult.Status = &statusproto.Status{
			Code:    statusCode,
			Message: err.Error(),
		}
		riErr := geh.requestInsightPublisher.PublishRequestEvents(ctx, event.Metadata.RequestId, &requestinsightproto.TenantInfo{
			TenantId:   event.Metadata.TenantId,
			TenantName: event.Metadata.TenantName,
		}, riEvents)
		if riErr != nil {
			log.Ctx(ctx).Error().Err(riErr).Msg("Failed to publish request event")
			// we ignore this error as there is already a (potentially) more important error
		}

		log.Ctx(ctx).Error().Err(err).Msg("Failed to handle event")
		pubsubMessage.WithLabelValues(event.EventType, statusCodeString).Inc()
		return err
	}
	err = geh.requestInsightPublisher.PublishRequestEvents(ctx, event.Metadata.RequestId, &requestinsightproto.TenantInfo{
		TenantId:   event.Metadata.TenantId,
		TenantName: event.Metadata.TenantName,
	}, riEvents)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to publish request event")
		return err
	}
	pubsubMessage.WithLabelValues(event.EventType, "ok").Inc()
	return nil
}

func (geh *githubEventHandlerImpl) HandleInstallation(ctx context.Context, installEvent *githubproto.GithubEvent_Installation, tenantID string, tenantName string,
	requestId requestcontext.RequestId,
	githubClient GithubRepositoriesClient,
) error {
	ctx, span := tracer.Start(ctx, "HandleInstallation")
	defer span.End()
	span.SetAttributes(
		attribute.String("tenant_id", tenantID),
		attribute.String("tenant_name", tenantName),
	)

	requestContext, err := geh.GetRequestContext(ctx, tenantID, requestId)
	if err != nil {
		return fmt.Errorf("failed to get request context: %w", err)
	}

	ctx = annotateContext(ctx, tenantID, tenantName, requestContext)

	tenantSettingsResponse, err := geh.settingsServiceClient.GetTenantSettings(ctx, requestContext)
	if err != nil {
		return fmt.Errorf("Error getting tenant settings: %s", err)
	}
	currentSettings := tenantSettingsResponse.GetSettings()

	// go proto unmarshalling sets empty fields to nil bc omitempty
	if currentSettings.GithubSettings == nil {
		currentSettings.GithubSettings = &settingsproto.GithubSettings{}
	}

	if currentSettings.GithubSettings.Repos == nil {
		currentSettings.GithubSettings.Repos = []*settingsproto.RepoInformation{}
	}

	switch installEvent.Installation.Action {
	case "added":
		log.Ctx(ctx).Info().Msgf("Handling installation added repos event")

		newRepos := []*settingsproto.RepoInformation{}
		for _, repo := range installEvent.Installation.Repos {
			repoInfo := &settingsproto.RepoInformation{
				RepoOwner: repo.Owner,
				RepoName:  repo.Name,
			}
			newRepos = append(newRepos, repoInfo)
		}

		// Update settings first
		currentSettings.GithubSettings.Repos = append(currentSettings.GithubSettings.Repos, newRepos...)

		// Also dedup repos
		dedupGithubSettingsRepos(currentSettings)

		_, err = geh.settingsServiceClient.UpdateTenantSettings(ctx, requestContext, currentSettings, "")
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to update github settings for tenant %s", tenantID)
			return fmt.Errorf("Error updating tenant settings: %s", err)
		}

		// Then proceed with repository registration
		// Register all repos even if one fails unless it fails due to rate limit
		// If we hit a rate limit, sleep until reset time and conintue registering up to maxRepoRegistrationRetries times
		var registrationErrors []error
		var deletedRepos []*settingsproto.RepoInformation
		for i := 0; i < len(newRepos); i++ {
			repo := newRepos[i]
			for retryCount := 0; retryCount < maxRepoRegistrationRetries; retryCount++ {
				repository, resp, err := githubClient.Get(ctx, repo.RepoOwner, repo.RepoName)
				if err != nil {
					// Check for rate limit in Get response
					// If rate limit is hit, sleep until reset time
					if resp != nil && resp.Rate.Remaining <= 0 {
						if err := geh.handleInstallationRateLimitSleep(ctx, tenantID, &resp.Rate); err != nil {
							return err
						}
						log.Ctx(ctx).Info().Msgf("Github rate limit hit, will retry registering repo %s/%s", githublib.RedactString(repo.RepoOwner), githublib.RedactString(repo.RepoName))
						continue // Retry this same repo
					}
					// This can happen if a repo is deleted
					if resp != nil && resp.StatusCode == http.StatusNotFound {
						log.Ctx(ctx).Info().Msgf("Repository %s/%s is not found and was probably deleted, skipping registration", githublib.RedactString(repo.RepoOwner), githublib.RedactString(repo.RepoName))
						deletedRepos = append(deletedRepos, repo)
						break
					}

					// For all other errors, log and continue, only failing at
					// the end
					registrationErrors = append(registrationErrors, fmt.Errorf("failed to get repository for installation %s/%s: %w",
						githublib.RedactString(repo.RepoOwner),
						githublib.RedactString(repo.RepoName),
						err))
					break
				}
				if repository.DefaultBranch == nil {
					registrationErrors = append(registrationErrors, fmt.Errorf("default branch is nil for %s/%s",
						githublib.RedactString(repo.RepoOwner),
						githublib.RedactString(repo.RepoName)))
					break
				}
				defaultBranch := repository.GetDefaultBranch()

				blobs, errWithRateLimit := geh.RegisterRepo(ctx, requestContext, githubClient, repo, tenantID, tenantName, defaultBranch, "")
				if errWithRateLimit != nil {
					// Check for rate limit in RegisterRepo response
					// If rate limit is hit, sleep until reset time and try registering this repo again
					if errWithRateLimit.RateLimit != nil {
						if err := geh.handleInstallationRateLimitSleep(ctx, tenantID, errWithRateLimit.RateLimit); err != nil {
							return err
						}
						log.Ctx(ctx).Info().Msgf("Github rate limit hit, will retry registering repo %s/%s", githublib.RedactString(repo.RepoOwner), githublib.RedactString(repo.RepoName))
						continue // Retry this same repo
					}

					// for any other error, we don't retry
					log.Ctx(ctx).Error().Err(errWithRateLimit.Err).Msgf("Failed to register new repo %s/%s",
						githublib.RedactString(repo.RepoOwner),
						githublib.RedactString(repo.RepoName))
					registerRepoRequests.WithLabelValues(tenantID, "NEW_REPO", "ERROR").Inc()
					registrationErrors = append(registrationErrors, fmt.Errorf("failed to register repo %s/%s: %w",
						githublib.RedactString(repo.RepoOwner),
						githublib.RedactString(repo.RepoName),
						errWithRateLimit.Err))
					break
				}

				registerRepoRequests.WithLabelValues(tenantID, "NEW_REPO", "OK").Inc()
				log.Ctx(ctx).Info().Msgf("Registered new repo %s/%s with checkpoint blobs %s",
					githublib.RedactString(repo.RepoOwner),
					githublib.RedactString(repo.RepoName),
					blobsToString(blobs))
				break
			}
		}

		if len(deletedRepos) > 0 {
			for _, deleted := range deletedRepos {
				currentSettings.GithubSettings.Repos = slices.DeleteFunc(currentSettings.GithubSettings.Repos, func(repo *settingsproto.RepoInformation) bool {
					return repo.RepoOwner == deleted.RepoOwner && repo.RepoName == deleted.RepoName
				})
			}

			_, err := geh.settingsServiceClient.UpdateTenantSettings(ctx, requestContext, currentSettings, "")
			if err != nil {
				return err
			}
		}

		if len(registrationErrors) > 0 {
			return fmt.Errorf("failed to register some repos: %v", errors.Join(registrationErrors...))
		} else {
			numDeleted := len(deletedRepos)
			log.Ctx(ctx).Info().Msgf("Successfully registered %d repos in installation event, skipped %d deleted repos", len(newRepos)-numDeleted, numDeleted)
		}

		return nil
	case "removed":
		log.Ctx(ctx).Info().Msgf("Handling repository removed event")

		reposToRemove, err := geh.removeRepos(ctx, requestContext, installEvent.Installation.Repos)
		if err != nil {
			return err
		}

		// Just in case the existing list has duplicates, dedup it here
		dedupGithubSettingsRepos(currentSettings)

		// Remove the repos from the settings
		currentSettings.GithubSettings.Repos = slices.DeleteFunc(currentSettings.GithubSettings.Repos, func(repo *settingsproto.RepoInformation) bool {
			key := fmt.Sprintf("%s/%s", repo.RepoOwner, repo.RepoName)
			return reposToRemove[key]
		})

		_, err = geh.settingsServiceClient.UpdateTenantSettings(ctx, requestContext, currentSettings, "")
		if err != nil {
			return fmt.Errorf("Error updating tenant settings: %s", err)
		}

		return nil

	case "deleted":
		log.Ctx(ctx).Info().Msgf("Handling github app uninstallation")

		sender := installEvent.Installation.GetSender()
		if sender != nil {
			log.Ctx(ctx).Info().Msgf("Github app uninstallation event sent by '%s', email: '%s'", sender.GetLogin(), sender.GetEmail())
		} else {
			log.Ctx(ctx).Info().Msgf("No sender found for github app uninstallation event.")
		}

		riEvent := ripublisher.NewRequestEvent()
		riInstallEvent := &requestinsightproto.RIGithubAppInstallationEvent{
			Status:    &statusproto.Status{},
			EventType: requestinsightproto.InstallEventType_UNINSTALL,
		}

		tenantInfo := &requestinsightproto.TenantInfo{
			TenantId:   tenantID,
			TenantName: tenantName,
		}

		err = geh.handleAppUninstalled(ctx, requestContext, tenantID, installEvent.Installation.Repos, currentSettings)
		if err != nil {
			riInstallEvent.Status.Code = int32(status.Code(err))
			riInstallEvent.Status.Message = err.Error()
		} else {
			riInstallEvent.Status.Code = int32(codes.OK)
			riInstallEvent.Status.Message = "Success"
		}

		riEvent.Event = &requestinsightproto.RequestEvent_GithubAppInstallationEvent{
			GithubAppInstallationEvent: riInstallEvent,
		}
		riErr := geh.requestInsightPublisher.PublishRequestEvent(context.Background(), "", tenantInfo, riEvent)
		if riErr != nil {
			// we ignore this error as there is already a (potentially) more important error
			log.Ctx(ctx).Error().Err(riErr).Msg("Failed to publish request event")
		}

		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to handle github app uninstallation for tenant %s", tenantID)
			return err
		}

		log.Ctx(ctx).Info().Msgf("Deleted github installation for tenant %s", tenantID)

		return nil

	default:
		return fmt.Errorf("unknown installation event action: %s", installEvent.Installation.Action)
	}
}

func (geh *githubEventHandlerImpl) handleInstallationRateLimitSleep(ctx context.Context, tenantID string, rate *github.Rate) error {
	sleepDuration := time.Until(rate.Reset.Time) + time.Second*5
	log.Ctx(ctx).Info().Msgf("Github rate limit hit, sleeping for %d seconds", sleepDuration.Minutes())
	select {
	case <-ctx.Done():
		return ctx.Err()
	case <-time.After(sleepDuration):
		return nil
	}
}

func (geh *githubEventHandlerImpl) removeRepos(ctx context.Context, requestContext *requestcontext.RequestContext, repos []*githubproto.Repository) (reposRemoved map[string]bool, err error) {
	githubStateRepos := make([]*githubstatepersistproto.GithubRepo, 0)
	reposRemoved = make(map[string]bool)

	for _, repo := range repos {
		githubStateRepos = append(githubStateRepos, &githubstatepersistproto.GithubRepo{
			RepoOwner: repo.Owner,
			RepoName:  repo.Name,
		})
		key := fmt.Sprintf("%s/%s", repo.Owner, repo.Name)
		reposRemoved[key] = true
	}

	err = geh.githubStateClient.DeleteGithubStateForRepos(ctx, requestContext, githubStateRepos)
	if err != nil {
		return nil, fmt.Errorf("failed to delete github state for repos: %w", err)
	}
	log.Ctx(ctx).Info().Msgf("Deleted github state for %d repos:", len(repos))
	return reposRemoved, nil
}

func (geh *githubEventHandlerImpl) handleAppUninstalled(ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string, repos []*githubproto.Repository, settings *settingsproto.TenantSettings) error {
	_, err := geh.removeRepos(ctx, requestContext, repos)
	if err != nil {
		return err
	}

	err = geh.webhookTenantMappingResource.Delete(ctx, fmt.Sprintf("github-%s", tenantID))
	if err != nil {
		return fmt.Errorf("failed to delete webhook mapping: %w", err)
	}
	log.Ctx(ctx).Info().Msgf("Deleted github webhook mapping for tenant %s", tenantID)

	geh.lagTracker.stopTenantLagTracker(tenantID)

	// reset github settings to an empty struct
	// use empty instead of nil to make sure the field gets updated instead of ignored
	settings.GithubSettings = &settingsproto.GithubSettings{}

	_, err = geh.settingsServiceClient.UpdateTenantSettings(ctx, requestContext, settings, "")
	if err != nil {
		return fmt.Errorf("Error updating tenant settings: %s", err)
	}

	return err
}

func (geh *githubEventHandlerImpl) HandlePush(ctx context.Context, pushEvent *githubproto.GithubEvent_Push,
	tenantID, tenantName string, requestId requestcontext.RequestId, githubClient GithubRepositoriesClient,
	riResult *requestinsightproto.RIGithubProcessingResult,
) error {
	push := pushEvent.Push
	owner := push.Repository.Owner
	repo := push.Repository.Name
	head := push.AfterSha
	branch := push.Ref
	time := push.HeadCommitTimestamp

	if strings.HasPrefix(branch, "refs/heads/") {
		// Extract the branch name
		branch = strings.TrimPrefix(branch, "refs/heads/")
	}

	if head == "" || head == "0000000000000000000000000000000000000000" {
		log.Ctx(ctx).Info().Msgf("Ignoring push with empty head")
		return nil
	}

	// TODO: use settings service to decide which repos to upload
	// currently process the default branch for all repos the app has access to

	// Get default branch
	repository, _, err := githubClient.Get(ctx, owner, repo)
	if err != nil {
		return fmt.Errorf("failed to get repository: %w", err)
	}
	if repository.DefaultBranch == nil {
		return fmt.Errorf("default branch is nil")
	}
	defaultBranch := repository.GetDefaultBranch()

	if branch != defaultBranch {
		// Is the branch name sensitive info? Don't log it for now
		log.Ctx(ctx).Info().Msgf("Ignoring push to non-default (%s) branch", githublib.RedactString(defaultBranch))
		return nil
	}

	requestContext, err := geh.GetRequestContext(ctx, tenantID, requestId)
	if err != nil {
		return fmt.Errorf("failed to get request context: %w", err)
	}

	// get current ref state
	githubRef := geh.getGithubRef(owner, repo, branch)
	lastUploadedCheckpoint, err := geh.githubStateClient.GetCurrentRefState(ctx, requestContext, githubRef)
	if err != nil {
		if status.Code(err) == codes.NotFound {
			log.Ctx(ctx).Info().Msgf("Repo %s/%s not found in state, registering as new repo", githublib.RedactString(owner),
				githublib.RedactString(repo))
			registerRepoParam := ""
			if geh.registerRepoWithLatestCommit {
				registerRepoParam = head
			}
			repoInfo := &settingsproto.RepoInformation{
				RepoOwner: owner,
				RepoName:  repo,
			}
			blobs, errWithRateLimit := geh.RegisterRepo(ctx, requestContext, githubClient, repoInfo, tenantID, tenantName, branch, registerRepoParam)
			if errWithRateLimit != nil && errWithRateLimit.Err != nil {
				log.Ctx(ctx).Error().Err(errWithRateLimit.Err).Msgf("Failed to register new repo %s/%s", githublib.RedactString(owner),
					githublib.RedactString(repo))
				registerRepoRequests.WithLabelValues(tenantID, "NEW_REPO", "ERROR").Inc()
				return errWithRateLimit.Err
			}
			registerRepoRequests.WithLabelValues(tenantID, "NEW_REPO", "OK").Inc()
			log.Ctx(ctx).Info().Msgf("Registered new repo %s/%s with checkpoint blobs %s", githublib.RedactString(owner), githublib.RedactString(repo), blobsToString(blobs))
			return nil
		}
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get current ref state for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		return err
	}

	if push.Forced {
		log.Ctx(ctx).Info().Msgf("Push is forced, registering as new repo")
		repoInfo := &settingsproto.RepoInformation{
			RepoOwner: owner,
			RepoName:  repo,
		}

		blobs, errWithRateLimit := geh.RegisterRepo(ctx, requestContext, githubClient, repoInfo, tenantID, tenantName, branch, head)
		if errWithRateLimit != nil && errWithRateLimit.Err != nil {
			log.Ctx(ctx).Error().Err(errWithRateLimit.Err).Msgf("Failed to register new repo %s/%s", githublib.RedactString(owner),
				githublib.RedactString(repo))
			registerRepoRequests.WithLabelValues(tenantID, "FORCE_PUSH", "ERROR").Inc()
			return errWithRateLimit.Err
		}
		registerRepoRequests.WithLabelValues(tenantID, "FORCE_PUSH", "OK").Inc()
		log.Ctx(ctx).Info().Msgf("Registered new repo %s/%s with checkpoint blobs %s", githublib.RedactString(owner), githublib.RedactString(repo), blobsToString(blobs))
		return nil
	}

	riResult.LastUpdatedCommit = lastUploadedCheckpoint.CommitSha

	if head == lastUploadedCheckpoint.CommitSha {
		log.Ctx(ctx).Info().Msgf("Ignoring push to same commit %s", githublib.RedactString(head))
		return nil
	}

	forceReuploadReason, err := geh.UploadDiff(ctx, requestContext, tenantID, tenantName, githubClient, lastUploadedCheckpoint, head, time, riResult)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to upload diff for repo %s/%s, commit %s", githublib.RedactString(owner),
			githublib.RedactString(repo), githublib.RedactString(head))
		uploadDiffRequests.WithLabelValues(tenantID, "ERROR").Inc()
		return err
	}
	if forceReuploadReason != noReRegister {
		uploadDiffRequests.WithLabelValues(tenantID, "REUPLOAD").Inc()
		log.Ctx(ctx).Info().Msgf("Forcing reupload due to %s for repo %s/%s, commit %s",
			string(forceReuploadReason), githublib.RedactString(owner),
			githublib.RedactString(repo), githublib.RedactString(head))
		blobs, errWithRateLimit := geh.RegisterRepo(ctx, requestContext, githubClient, &settingsproto.RepoInformation{
			RepoOwner: owner,
			RepoName:  repo,
		}, tenantID, tenantName, branch, head)
		if errWithRateLimit != nil && errWithRateLimit.Err != nil {
			log.Ctx(ctx).Error().Err(errWithRateLimit.Err).Msgf("Failed to reupload repo %s/%s, commit %s", githublib.RedactString(owner),
				githublib.RedactString(repo), githublib.RedactString(head))
			registerRepoRequests.WithLabelValues(tenantID, string(forceReuploadReason), "ERROR").Inc()
			return errWithRateLimit.Err
		}
		log.Ctx(ctx).Info().Msgf("Reuploaded repo %s/%s with checkpoint blobs %s", githublib.RedactString(owner),
			githublib.RedactString(repo), blobsToString(blobs))
		registerRepoRequests.WithLabelValues(tenantID, string(forceReuploadReason), "OK").Inc()

		return nil
	}

	uploadDiffRequests.WithLabelValues(tenantID, "OK").Inc()
	log.Ctx(ctx).Info().Msgf("Uploaded diff for repo %s/%s, commit %s", githublib.RedactString(owner),
		githublib.RedactString(repo), githublib.RedactString(head))
	return nil
}

type errorWithRateLimit struct {
	Err       error
	RateLimit *github.Rate
}

func (geh *githubEventHandlerImpl) RegisterRepo(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	githubClient GithubRepositoriesClient,
	repoInfo *settingsproto.RepoInformation,
	tenantID string,
	tenantName string,
	branch string,
	headCommitSHA string, // empty string means use the default branch
) (*blobsproto.Blobs, *errorWithRateLimit) {
	log.Ctx(ctx).Info().Msgf("Registering repo %s/%s", githublib.RedactString(repoInfo.RepoOwner),
		githublib.RedactString(repoInfo.RepoName))

	// in test mode, we use the commit in the push event that is being processed
	// otherwise use the latest commit on the default branch
	var getCommitParam string
	if headCommitSHA == "" {
		getCommitParam = branch
	} else {
		getCommitParam = headCommitSHA
	}

	commit, githubResp, err := githubClient.GetCommit(ctx, repoInfo.RepoOwner, repoInfo.RepoName, getCommitParam, nil)
	if err != nil {
		if githubResp != nil && githubResp.StatusCode == 409 { // 409 Conflict is returned for empty repositories
			log.Ctx(ctx).Info().Msgf("Repository %s/%s is empty. Skipping registration", githublib.RedactString(repoInfo.RepoOwner), githublib.RedactString(repoInfo.RepoName))
			return &blobsproto.Blobs{}, nil
		} else if githubResp != nil && githubResp.Rate.Remaining <= 0 {
			return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to get latest commit due to rate limit: %w", err), RateLimit: &githubResp.Rate}
		}
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to get latest commit: %w", err)}
	}

	latestCommitTime := &timestamppb.Timestamp{
		Seconds: commit.Commit.Author.Date.Time.Unix(),
	}

	latestCommitSHA := commit.GetSHA()
	parentCommits := commit.Parents
	parentCommitSHAs := make([]string, len(parentCommits))
	for i, parentCommit := range parentCommits {
		parentCommitSHAs[i] = parentCommit.GetSHA()
	}

	log.Ctx(ctx).Info().Msgf("Latest commit time: %s", latestCommitTime)
	log.Ctx(ctx).Info().Msgf("Latest commit SHA: %s", githublib.RedactString(latestCommitSHA))

	// Check if we have already registered this commit
	githubRef := geh.getGithubRef(repoInfo.RepoOwner, repoInfo.RepoName, branch)
	latestCheckpoint, err := geh.githubStateClient.GetCurrentRefState(ctx, requestContext, githubRef)
	if err != nil && status.Code(err) != codes.NotFound {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to get current ref state: %w", err)}
	}
	if latestCheckpoint != nil && latestCheckpoint.CommitSha == latestCommitSHA {
		log.Ctx(ctx).Info().Msgf("Already registered commit %s for repo %s/%s", githublib.RedactString(latestCommitSHA), githublib.RedactString(repoInfo.RepoOwner), githublib.RedactString(repoInfo.RepoName))
		return latestCheckpoint.Blobs, nil
	}

	// Get tarball URL based on the head commit
	tarballURL, githubResp, err := githubClient.GetArchiveLink(ctx, repoInfo.RepoOwner, repoInfo.RepoName, github.Tarball, &github.RepositoryContentGetOptions{Ref: latestCommitSHA}, 1)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get tarball URL for repo %s/%s", githublib.RedactString(repoInfo.RepoOwner), githublib.RedactString(repoInfo.RepoName))
		if githubResp != nil && githubResp.Rate.Remaining <= 0 {
			return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to get tarball URL due to rate limit: %w", err), RateLimit: &githubResp.Rate}
		}
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to get tarball URL: %w", err)}
	}

	// Download the tarball - this link only lasts for 5 minutes so download the tarball locally
	resp, err := geh.githubClients.httpClient().Get(tarballURL.String())
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to download tarball: %w", err)}
	}
	defer resp.Body.Close()

	// Create a file to store the tarball
	tarballPath := fmt.Sprintf("%s_%s.tar.gz", repoInfo.RepoOwner, repoInfo.RepoName)
	tarballFile, err := os.Create(tarballPath)
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to create tarball file: %w", err)}
	}
	defer tarballFile.Close()

	// Copy the tarball content to the file
	_, err = io.Copy(tarballFile, resp.Body)
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to save tarball: %w", err)}
	}

	// Create an ignore stack with .augmentignore, .gitignore
	reader, closeReader, err := readTarball(ctx, tarballPath)
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to read ignore files: %w", err)}
	}
	pathFilter, err := func() (PathFilter, error) {
		// Make sure to close this reader as soon as we're done, especially
		// since we create a new one right after this, to try to save memory
		defer closeReader()
		return newPathFilterFromTarball(ctx, reader)
	}()
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to read ignore files: %w", err)}
	}

	// Upload the blobs from the tarball, creating a new reader for this
	reader, closeReader, err = readTarball(ctx, tarballPath)
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to read tarball: %w", err)}
	}
	defer closeReader()
	filenameToBlobName, blobNames, err := geh.uploadBlobsFromTarball(ctx, pathFilter, reader, requestContext, tenantID, tenantName, repoInfo.RepoOwner, repoInfo.RepoName)
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to upload blobs from tarball: %w", err)}
	}

	// Create a checkpoint for the repo with nil baseline checkpoint id and no deleted blobs
	checkpointBlobs, err := geh.createCheckpoint(ctx, blobNames, nil, nil, requestContext)
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to create checkpoint: %w", err)}
	}

	log.Ctx(ctx).Info().Msgf("Created final checkpoint for repo %s with checkpoint %s", githublib.RedactString(repoInfo.RepoName), blobsToString(checkpointBlobs))

	// empty base commit and no deleted files since this is the initial uploaded commit
	// always set forced to true when registering a new repo
	err = geh.updateGithubState(filenameToBlobName, nil, requestContext, ctx, "", latestCommitSHA, parentCommitSHAs, githubRef, latestCommitTime, checkpointBlobs, true)
	if err != nil {
		return nil, &errorWithRateLimit{Err: fmt.Errorf("failed to update github state: %w", err)}
	}

	log.Ctx(ctx).Info().Msgf("Done registering repo %s", githublib.RedactString(repoInfo.RepoName))

	return checkpointBlobs, nil
}

func (geh *githubEventHandlerImpl) createUploadBlobContent(ctx context.Context, tenantID string, path string, contentBytes []byte) *contentmanagerproto.UploadBlobContent {
	validFile, err := geh.isValidFileToUpload(ctx, tenantID, contentBytes)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to validate file requirements")
		return nil
	}
	if !validFile {
		return nil
	}

	return &contentmanagerproto.UploadBlobContent{
		Content: contentBytes,
		Metadata: []*contentmanagerproto.BlobMetadata{
			{
				Key:   "path",
				Value: path,
			},
		},
	}
}

func (geh *githubEventHandlerImpl) updateGithubState(
	filenameToBlobName map[string]blobnames.BlobName,
	deletedFileNames []string,
	requestContext *requestcontext.RequestContext,
	ctx context.Context,
	baseCommitSHA string,
	latestCommitSHA string,
	parentCommitSHAs []string,
	githubRef *githubstatepersistproto.GithubRef,
	commitTime *timestamppb.Timestamp,
	checkpointBlobs *blobsproto.Blobs,
	forced bool,
) error {
	var githubStateDiffInfos []*githubstatepersistproto.DiffInfo
	for filename, blobName := range filenameToBlobName {
		blobNameProto, err := blobnames.NewBlobNameProto(blobName, false)
		if err != nil {
			return fmt.Errorf("failed to create blob name proto: %w", err)
		}
		githubStateDiffInfos = append(githubStateDiffInfos, &githubstatepersistproto.DiffInfo{
			FilePath: filename,
			Change: &githubstatepersistproto.DiffInfo_ContentBlobName{
				ContentBlobName: blobNameProto,
			},
		})
	}
	for _, filename := range deletedFileNames {
		githubStateDiffInfos = append(githubStateDiffInfos, &githubstatepersistproto.DiffInfo{
			FilePath: filename,
			Change: &githubstatepersistproto.DiffInfo_Deleted{
				Deleted: true,
			},
		})
	}

	_, err := geh.githubStateClient.UpdateCurrentRefCheckpoint(ctx, requestContext, githubRef, latestCommitSHA, parentCommitSHAs, baseCommitSHA, commitTime, checkpointBlobs, githubStateDiffInfos, forced)
	if err != nil {
		return fmt.Errorf("failed to update current ref checkpoint: %w", err)
	}
	return nil
}

func (geh *githubEventHandlerImpl) getGithubRef(repoOwner, repoName, branch string) *githubstatepersistproto.GithubRef {
	return &githubstatepersistproto.GithubRef{
		Repo: &githubstatepersistproto.GithubRepo{
			RepoOwner: repoOwner,
			RepoName:  repoName,
		},
		Ref: branch,
	}
}

func (geh *githubEventHandlerImpl) uploadBlobsFromTarball(ctx context.Context, pathFilter PathFilter, reader *tar.Reader, requestContext *requestcontext.RequestContext, tenantID, tenantName, repoOwner, repoName string) (map[string]blobnames.BlobName, []blobnames.BlobName, error) {
	var uploads []*contentmanagerproto.UploadBlobContent
	filenameToBlobName := make(map[string]blobnames.BlobName)
	blobNames := make([]blobnames.BlobName, 0)
	size := 0
	var ignoredFiles, skippedSymlinks, skippedLargeFiles int

	for {
		header, err := reader.Next()
		if err != nil {
			if err == io.EOF {
				break
			}
			return nil, nil, err
		}
		if header.Typeflag == tar.TypeSymlink {
			log.Ctx(ctx).Debug().Msgf("Skipping symlink %s", githublib.RedactString(header.Name))
			skippedSymlinks++
			continue
		}
		valid, err := geh.isValidFileSizeToUpload(ctx, tenantName, header.Size)
		if err != nil {
			return nil, nil, err
		}
		if !valid {
			log.Ctx(ctx).Info().Msgf("Skipping file %s because it is too large", githublib.RedactString(header.Name))
			skippedLargeFiles++
			continue
		}

		// Remove the tarball name file prefix to access files with the path we expect
		tarballPath := strings.SplitN(header.Name, "/", 2)
		var filePath string
		if len(tarballPath) > 1 {
			filePath = tarballPath[1]
		}

		switch header.Typeflag {
		case tar.TypeReg:
			if pathFilter.IgnoreFile(filePath) {
				ignoredFiles++
				continue
			}

			content, err := io.ReadAll(reader)
			if err != nil {
				return nil, nil, fmt.Errorf("failed to read file from tarball %s: %w", githublib.RedactString(filePath), err)
			}
			blob := geh.createUploadBlobContent(ctx, tenantID, filePath, content)

			if blob == nil {
				continue
			}

			err = geh.addBlobToBatch(ctx, blob, &uploads, &size, requestContext)
			if err != nil {
				return nil, nil, fmt.Errorf("failed to add blob to batch: %w", err)
			}

			filenameToBlobName[filePath] = blobnames.GetBlobName(filePath, content)
			blobNames = append(blobNames, filenameToBlobName[filePath])

		default:
			continue
		}
	}
	log.Ctx(ctx).Info().Msgf("Ignored %d files, skipped %d symlinks and %d large files in repo %s/%s",
		ignoredFiles, skippedSymlinks, skippedLargeFiles, githublib.RedactString(repoOwner), githublib.RedactString(repoName))

	if len(uploads) == 0 {
		log.Ctx(ctx).Info().Msg("No files found in repo")
		return nil, nil, nil
	}

	log.Ctx(ctx).Info().Msgf("Uploading %d blobs", len(uploads))
	_, err := geh.contentManagerClient.BatchUploadBlobContent(ctx, uploads, contentmanagerproto.IndexingPriority_DEFAULT, requestContext)
	if err != nil {
		return nil, nil, err
	}
	return filenameToBlobName, blobNames, nil
}

// Add a blob to the batch if the batch is not full. Otherwise, upload the batch and start a new one.
func (geh *githubEventHandlerImpl) addBlobToBatch(
	ctx context.Context,
	blob *contentmanagerproto.UploadBlobContent,
	uploads *[]*contentmanagerproto.UploadBlobContent,
	size *int,
	requestContext *requestcontext.RequestContext,
) error {
	if *size+len(blob.Content) > maxBatchUploadBlobContentSize || len(*uploads)+1 > maxBatchUploadBlobCount {
		log.Ctx(ctx).Info().Msgf("Uploading %d blobs", len(*uploads))
		_, err := geh.contentManagerClient.BatchUploadBlobContent(ctx, *uploads, contentmanagerproto.IndexingPriority_DEFAULT, requestContext)
		if err != nil {
			return fmt.Errorf("failed to upload blobs: %w", err)
		}

		*uploads = nil
		*size = 0
	}

	*uploads = append(*uploads, blob)
	*size += len(blob.Content)

	return nil
}

func (geh *githubEventHandlerImpl) createCheckpoint(ctx context.Context, blobNamesAdded []blobnames.BlobName, blobNamesDeleted []blobnames.BlobName, baselineCheckpoint *blobsproto.Blobs, requestContext *requestcontext.RequestContext) (*blobsproto.Blobs, error) {
	var added [][]byte
	for _, blobName := range blobNamesAdded {
		b, err := blobnames.DecodeHexBlobName(blobName)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to decode blob name")
			return nil, err
		}
		added = append(added, b)
	}

	var deleted [][]byte
	for _, blobName := range blobNamesDeleted {
		b, err := blobnames.DecodeHexBlobName(blobName)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to decode blob name")
			return nil, err
		}
		deleted = append(deleted, b)
	}

	currBaselineCheckpoint := baselineCheckpoint
	addedIndex, deletedIndex := 0, 0

	// Add up to maxBlobNamesForCheckpoint blobs in each checkpoint
	for addedIndex < len(added) || deletedIndex < len(deleted) {
		var addedBlobsForCheckpoint, deletedBlobsForCheckpoint [][]byte
		remainingSlots := maxBlobNamesForCheckpoint

		// Add deleted blobs
		if deletedIndex < len(deleted) {
			count := min(remainingSlots, len(deleted)-deletedIndex)
			deletedBlobsForCheckpoint = deleted[deletedIndex : deletedIndex+count]
			deletedIndex += count
			remainingSlots -= count
		}

		// Add added blobs
		if remainingSlots > 0 && addedIndex < len(added) {
			count := min(remainingSlots, len(added)-addedIndex)
			addedBlobsForCheckpoint = added[addedIndex : addedIndex+count]
			addedIndex += count
		}

		blobs := &blobsproto.Blobs{
			BaselineCheckpointId: nil,
			Added:                addedBlobsForCheckpoint,
			Deleted:              deletedBlobsForCheckpoint,
		}

		if currBaselineCheckpoint != nil {
			blobs.BaselineCheckpointId = currBaselineCheckpoint.BaselineCheckpointId
			if len(currBaselineCheckpoint.Added) > 0 || len(currBaselineCheckpoint.Deleted) > 0 {
				log.Ctx(ctx).Error().Msg("Baseline checkpoint blobs should not have added or deleted blobs")
				return nil, fmt.Errorf("baseline checkpoint blobs should not have added or deleted blobs")
			}
		}
		checkpointId, err := geh.contentManagerClient.CheckpointBlobs(ctx, blobs, requestContext)
		if err != nil {
			return nil, fmt.Errorf("failed to checkpoint blobs: %w", err)
		}
		log.Ctx(ctx).Info().Msgf("Checkpointed blobs with checkpoint id %s", checkpointId)
		currBaselineCheckpoint = &blobsproto.Blobs{
			BaselineCheckpointId: &checkpointId,
			Added:                nil,
			Deleted:              nil,
		}
	}

	return currBaselineCheckpoint, nil
}

// Handle a new diff, updating github state as necessary. If a result besides
// noReRegister is returned, the repo should be re-registered. If an error is
// returned, then the result will be ignored (treated as noReRegister) and we
// will log then return the error instead.
func (geh *githubEventHandlerImpl) UploadDiff(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	tenantID string,
	tenantName string,
	githubClient GithubRepositoriesClient,
	lastUploadedCheckpoint *githubstatepersistproto.RefCheckpoint,
	head string,
	commitTime *timestamppb.Timestamp,
	riResult *requestinsightproto.RIGithubProcessingResult,
) (uploadDiffResult UploadDiffResult, err error) {
	baseCommit := lastUploadedCheckpoint.CommitSha
	githubRef := lastUploadedCheckpoint.Ref
	owner := githubRef.Repo.RepoOwner
	repo := githubRef.Repo.RepoName

	// compare new commit with last uploaded commit
	comparison, _, err := githubClient.CompareCommits(ctx, owner, repo, baseCommit, head, &github.ListOptions{})
	if err != nil {
		return noReRegister, fmt.Errorf("failed to compare commits: %w", err)
	}

	if *comparison.Status == "behind" {
		log.Ctx(ctx).Info().Msg("Ignoring push to behind commit")
		return noReRegister, nil
	}

	log.Ctx(ctx).Info().Msgf("Handling push for repo %s/%s, new commit %s", githublib.RedactString(owner),
		githublib.RedactString(repo), githublib.RedactString(head))

	if len(comparison.Commits) >= maxCommitsInOnePush {
		log.Ctx(ctx).Info().Msgf("Push has %d commits, repo %s/%s should be re-registered instead ", len(comparison.Commits), githublib.RedactString(owner), githublib.RedactString(repo))
		return tooManyCommits, nil
	}

	if len(comparison.Files) >= maxFileChangesInOnePush {
		log.Ctx(ctx).Info().Msgf("Push has %d file changes, repo %s/%s should be re-registered instead ", len(comparison.Files), githublib.RedactString(owner), githublib.RedactString(repo))
		return tooManyFiles, nil
	}

	headCommit := comparison.Commits[len(comparison.Commits)-1]
	parentCommits := headCommit.Parents
	parentCommitSHAs := make([]string, len(parentCommits))

	for i, parentCommit := range parentCommits {
		parentCommitSHAs[i] = parentCommit.GetSHA()
	}

	// get raw diff from github to be able to use for git apply
	// TODO: modify logic so we don't need to make this second API call
	diff, _, err := githubClient.CompareCommitsRaw(ctx, owner, repo, baseCommit, head, github.RawOptions{Type: github.Diff})
	if err != nil {
		if githubErr, ok := err.(*github.ErrorResponse); ok && githubErr.Response.StatusCode == 504 {
			log.Ctx(ctx).Info().Msgf("Received 504 error from GitHub API, repo %s/%s should be re-registered instead ", githublib.RedactString(owner), githublib.RedactString(repo))
			return githubCompareCommitsApiTimeout, nil
		}
		return noReRegister, fmt.Errorf("failed to get raw diff: %w", err)
	}

	log.Ctx(ctx).Info().Msgf("Got diff with %d file changes", len(comparison.Files))

	// parse compareCommits to get information about what needs to be downloaded
	diffInfos, filenamesToDownload, err := geh.parseCompareCommits(comparison)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to parse compare commits for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		return noReRegister, err
	}

	if riResult != nil {
		for key, diffInfo := range diffInfos {
			riResult.DiffInfos = append(riResult.DiffInfos, &requestinsightproto.RIGithubProcessingResult_DiffInfo{
				Filename:    key,
				OldBlobname: string(diffInfo.oldBlobName),
				NewBlobname: string(diffInfo.newBlobName),
				OldFilename: diffInfo.oldFileName,
				Removed:     diffInfo.removed,
				Renamed:     diffInfo.renamed,
			})
		}
		for _, filename := range filenamesToDownload {
			riResult.FilenamesToDownload = append(riResult.FilenamesToDownload, filename)
		}
	}

	if len(diffInfos) == 0 {
		log.Ctx(ctx).Info().Msg("No changes in this commit")
		// update github state with no files changes and the old checkpoint id
		err = geh.updateGithubState(nil, nil, requestContext, ctx, baseCommit, head, parentCommitSHAs, githubRef, commitTime, lastUploadedCheckpoint.Blobs, false)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to update github state for repo %s/%s", githublib.RedactString(owner),
				githublib.RedactString(repo))
			return noReRegister, err
		}
		return noReRegister, nil
	}

	ignoreDiffInfos, ignoreChanged := getIgnoreDiffInfos(diffInfos)
	if ignoreChanged {
		return ignoreFileChanged, nil
	}

	// create a temporary directory to store all files
	tmpDir, err := createTempDir()
	if err != nil {
		return noReRegister, fmt.Errorf("failed to create temp dir: %w", err)
	}

	// First, check ignored files
	var ignoredFiles []string
	{
		// This is a list of all possibly relevant ignore file locations - note
		// that we do not necessarily know if they exist, though.
		ignoreFilenamesToDownload := make([]string, 0, len(ignoreDiffInfos))
		for filename := range ignoreDiffInfos {
			ignoreFilenamesToDownload = append(ignoreFilenamesToDownload, filename)
		}
		// Make this deterministic, particularly for unit tests
		slices.Sort(ignoreFilenamesToDownload)

		if riResult != nil {
			for filename, diffInfo := range ignoreDiffInfos {
				riResult.IgnoreDiffInfos = append(riResult.IgnoreDiffInfos, &requestinsightproto.RIGithubProcessingResult_DiffInfo{
					Filename:    filename,
					OldBlobname: string(diffInfo.oldBlobName),
					NewBlobname: string(diffInfo.newBlobName),
					OldFilename: diffInfo.oldFileName,
					Removed:     diffInfo.removed,
					Renamed:     diffInfo.renamed,
				})
			}
			for _, filename := range ignoreFilenamesToDownload {
				riResult.IgnoreFilenamesToDownload = append(riResult.IgnoreFilenamesToDownload, filename)
			}
		}

		ignoreContentManagerDownloads, ignoreGithubFileDownloads, err := geh.getFileDownloadInfo(ctx, requestContext, githubRef, baseCommit, ignoreFilenamesToDownload, ignoreDiffInfos)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to get ignorefile download info for repo %s/%s", githublib.RedactString(owner),
				githublib.RedactString(repo))
			return noReRegister, err
		}

		// Try to download all necessary ignore files in every relevant
		// directory, whether from content manager or github. Ignore any missing
		// files, since not every directory will have an ignore file.
		err = geh.fetchContentManagerContent(ctx, requestContext, riResult, tenantID, ignoreContentManagerDownloads, tmpDir, owner, repo)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to fetch ignorefile content manager content for repo %s/%s", githublib.RedactString(owner),
				githublib.RedactString(repo))
			return noReRegister, err
		}
		_, err = geh.fetchGithubContent(ctx, tenantName, githubClient, ignoreGithubFileDownloads, owner, repo, baseCommit, tmpDir, true)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to fetch ignorefile github content for repo %s/%s", githublib.RedactString(owner),
				githublib.RedactString(repo))
			return noReRegister, err
		}
		getFileFromGithub.WithLabelValues(tenantID).Add(float64(len(ignoreGithubFileDownloads)))

		// Filter out files as necessary, and update the regular
		// filenamesToDownload list and diffInfos map
		pathFilter, err := newPathFilterFromDiffInfos(ctx, ignoreDiffInfos, tmpDir)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to create path filter for repo %s/%s", githublib.RedactString(owner),
				githublib.RedactString(repo))
			return noReRegister, err
		}

		newFilenamesToDownload := make([]string, 0, len(filenamesToDownload))
		for filename, diffinfo := range diffInfos {
			ignored := pathFilter.IgnoreFile(filename)
			if diffinfo.renamed {
				ignoreOld, ignoreNew := ignored, pathFilter.IgnoreFile(diffinfo.filename)
				if ignoreOld && ignoreNew {
					// Ignore both files in the patch
					ignoredFiles = append(ignoredFiles, diffinfo.oldFileName, diffinfo.filename)
					delete(diffInfos, filename)
					continue
				} else if ignoreOld != ignoreNew {
					// The ignore status of this file has changed, so start from scratch.
					// TODO: handle this more gracefully, without having to
					// re-register the whole repo.
					log.Ctx(ctx).Info().Msgf("File ignore status changed, re-registering repo %s/%s", githublib.RedactString(owner), githublib.RedactString(repo))
					return fileIgnoreStatusChanged, nil
				}
			}
			if ignored {
				ignoredFiles = append(ignoredFiles, filename)
				delete(diffInfos, filename)
			} else {
				if slices.Contains(filenamesToDownload, filename) {
					newFilenamesToDownload = append(newFilenamesToDownload, filename)
				}
			}
		}
		log.Ctx(ctx).Info().Msgf("Ignored %d files in repo %s/%s", len(ignoredFiles), githublib.RedactString(owner), githublib.RedactString(repo))
		filenamesToDownload = newFilenamesToDownload
	}

	// Make this deterministic, particularly for unit tests
	slices.Sort(filenamesToDownload)

	contentManagerDownloads, githubFileDownloads, err := geh.getFileDownloadInfo(ctx, requestContext, githubRef, baseCommit, filenamesToDownload, diffInfos)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to get file download info for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		return noReRegister, err
	}

	// download all necessary files from content manager
	err = geh.fetchContentManagerContent(ctx, requestContext, riResult, tenantID, contentManagerDownloads, tmpDir, owner, repo)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to fetch content manager content for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		return noReRegister, err
	}

	// download all necessary files from github
	filesToIgnoreFromGithubDownloads, err := geh.fetchGithubContent(ctx, tenantName, githubClient, githubFileDownloads, owner, repo, baseCommit, tmpDir, false)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to fetch github content for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		return noReRegister, err
	}
	getFileFromGithub.WithLabelValues(tenantID).Add(float64(len(githubFileDownloads)))

	if len(filesToIgnoreFromGithubDownloads) > 0 {
		log.Info().Msgf("Ignoring %d files in repo %s/%s that cannot be downloaded", len(filesToIgnoreFromGithubDownloads), githublib.RedactString(owner), githublib.RedactString(repo))
		ignoredFiles = append(ignoredFiles, filesToIgnoreFromGithubDownloads...)
		if riResult != nil {
			riResult.IgnoredFiles = ignoredFiles
		}
	}

	ignoredFiles, err = geh.applyDiff(ctx, tenantName, requestContext, diff, tmpDir, ignoredFiles, riResult, githubRef, baseCommit, head)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to apply git diff for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		applyGitDiff.WithLabelValues(tenantID, "ERROR").Inc()
		return geh.failedDiffResult(ctx, tenantName, requestContext, err)
	}
	applyGitDiff.WithLabelValues(tenantID, "OK").Inc()
	if riResult != nil {
		riResult.IgnoredFiles = ignoredFiles
	}

	// process the diff to get the new files and blobs to add and remove
	blobsToAdd, blobsToRemove, err := geh.processPatchedFiles(ctx, tenantName, diffInfos, tmpDir, ignoredFiles)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to process patched files for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		geh.uploadFailedDiff(ctx, tenantName, requestContext, diff, githubRef, baseCommit, head, riResult)
		return geh.failedDiffResult(ctx, tenantName, requestContext, err)
	}
	log.Ctx(ctx).Info().Msgf("Got %d files to add and %d files to remove for repo %s/%s", len(blobsToAdd), len(blobsToRemove),
		githublib.RedactString(owner), githublib.RedactString(repo))

	if riResult != nil {
		// This has probably changed in the meantime, reset it
		riResult.DiffInfos = nil
		riResult.FilenamesToDownload = nil
		for key, diffInfo := range diffInfos {
			riResult.DiffInfos = append(riResult.DiffInfos, &requestinsightproto.RIGithubProcessingResult_DiffInfo{
				Filename:    key,
				OldBlobname: string(diffInfo.oldBlobName),
				NewBlobname: string(diffInfo.newBlobName),
				OldFilename: diffInfo.oldFileName,
				Removed:     diffInfo.removed,
				Renamed:     diffInfo.renamed,
			})
		}
		for _, filename := range filenamesToDownload {
			riResult.FilenamesToDownload = append(riResult.FilenamesToDownload, filename)
		}
	}

	if len(blobsToAdd) == 0 && len(blobsToRemove) == 0 {
		log.Ctx(ctx).Info().Msg("No files to upload")
		// update github state with no files changes and the old checkpoint id
		err = geh.updateGithubState(nil, nil, requestContext, ctx, baseCommit, head, parentCommitSHAs, githubRef, commitTime, lastUploadedCheckpoint.Blobs, false)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msgf("Failed to update github state for repo %s/%s", githublib.RedactString(owner),
				githublib.RedactString(repo))
			return noReRegister, err
		}
		return noReRegister, nil
	}

	checkpointBlobs, err := geh.updateContentManager(blobsToAdd, blobsToRemove, lastUploadedCheckpoint.Blobs, requestContext, ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to update content manager for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		return noReRegister, err
	}

	if riResult != nil {
		riResult.CheckpointBlobs = checkpointBlobs
	}

	// clean up all local files
	err = os.RemoveAll(tmpDir)
	if err != nil {
		return noReRegister, fmt.Errorf("failed to remove temp dir: %w", err)
	}

	// update filename to blob name map
	fileNameToBlobName := make(map[string]blobnames.BlobName)
	deletedFileNames := make([]string, 0)

	for _, diffInfo := range diffInfos {
		if diffInfo.newBlobName != "" {
			fileNameToBlobName[diffInfo.filename] = diffInfo.newBlobName
		}
		// remove filenames from deletes and renames
		if diffInfo.oldFileName != "" {
			deletedFileNames = append(deletedFileNames, diffInfo.oldFileName)
		}
	}

	err = geh.updateGithubState(fileNameToBlobName, deletedFileNames, requestContext, ctx, baseCommit, head, parentCommitSHAs, githubRef, commitTime, checkpointBlobs, false)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to update github state for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))
		return noReRegister, err
	}

	return noReRegister, nil
}

type filesToDownloadFromContentManager struct {
	downloadBlobContentKeys []*contentmanagerproto.BlobContentKey
	filenamesToDownload     []string
}

// parse the github.CommitsComparison to extract all information about which files need to be downloaded, modifed, removed, etc.
func (geh *githubEventHandlerImpl) parseCompareCommits(comparison *github.CommitsComparison) (diffInfos map[string]*DiffInfo, filenamesToDownload []string, err error) {
	diffInfos = make(map[string]*DiffInfo)

	for _, file := range comparison.Files {

		diffInfo := &DiffInfo{
			filename: *file.Filename,
		}

		switch *file.Status {
		case "added":
			// nothing to download here

		case "removed":
			diffInfo.removed = true
			diffInfo.oldFileName = *file.Filename
			filenamesToDownload = append(filenamesToDownload, *file.Filename)

		case "modified":

			filenamesToDownload = append(filenamesToDownload, *file.Filename)

		case "renamed":
			diffInfo.renamed = true
			diffInfo.oldFileName = *file.PreviousFilename
			filenamesToDownload = append(filenamesToDownload, *file.PreviousFilename)

			// set in map with previous filename to be able to access later and update blob values
			diffInfos[*file.PreviousFilename] = diffInfo
			continue

		default:
			return nil, nil, fmt.Errorf("unknown status %s", *file.Status)
		}
		diffInfos[*file.Filename] = diffInfo
	}

	return diffInfos, filenamesToDownload, nil
}

func (geh *githubEventHandlerImpl) getFileDownloadInfo(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	githubRef *githubstatepersistproto.GithubRef,
	commitSha string,
	filePaths []string,
	diffInfos map[string]*DiffInfo,
) (contentManagerDownloads *filesToDownloadFromContentManager, githubFileDownloads []string, err error) {
	responseChan, err := geh.githubStateClient.GetCurrentRefFiles(ctx, requestContext, githubRef, commitSha, filePaths)
	if err != nil {
		return nil, nil, err
	}
	downloadBlobContentKeys := make([]*contentmanagerproto.BlobContentKey, 0)
	contentManagerFileNames := make([]string, 0)

	for result := range responseChan {

		if result.Err != nil {
			return nil, nil, result.Err
		}

		if result.Resp.CommitSha != commitSha {
			return nil, nil, fmt.Errorf("commit sha mismatch")
		}

		for _, fileInfo := range result.Resp.FileInfos {
			s := status.FromProto(fileInfo.Status)
			if s.Code() == codes.NotFound {
				githubFileDownloads = append(githubFileDownloads, fileInfo.FilePath)
			} else {
				blobName, err := blobnames.FromBlobNameProto(fileInfo.BlobName)
				if err != nil {
					return nil, nil, err
				}
				downloadBlobContentKeys = append(downloadBlobContentKeys, &contentmanagerproto.BlobContentKey{
					BlobName: string(blobName),
				})
				diffInfo, ok := diffInfos[fileInfo.FilePath]
				if !ok {
					return nil, nil, fmt.Errorf("diff info not found for file %s",
						githublib.RedactString(fileInfo.FilePath))
				}
				diffInfo.oldBlobName = blobName

				contentManagerFileNames = append(contentManagerFileNames, fileInfo.FilePath)
			}
		}
	}
	contentManagerDownloads = &filesToDownloadFromContentManager{
		downloadBlobContentKeys: downloadBlobContentKeys,
		filenamesToDownload:     contentManagerFileNames,
	}
	return contentManagerDownloads, githubFileDownloads, nil
}

// download files from content manager and create them locally
func (geh *githubEventHandlerImpl) fetchContentManagerContent(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	riResult *requestinsightproto.RIGithubProcessingResult,
	tenantID string,
	contentManagerDownloads *filesToDownloadFromContentManager,
	tmpDir string,
	owner string,
	repo string,
) error {
	downloadBlobContentKeys := contentManagerDownloads.downloadBlobContentKeys
	filenamesToDownload := contentManagerDownloads.filenamesToDownload
	if len(downloadBlobContentKeys) == 0 {
		return nil
	}

	downloadChan, err := geh.contentManagerClient.BatchDownloadContent(ctx, downloadBlobContentKeys, requestContext, tenantID)
	if err != nil {
		if riResult != nil {
			// This error could have filenames, which are sensitive info, so save
			// the error in RI and avoid logging or returning it
			riResult.FileDownloadError = err.Error()
		}

		err = fmt.Errorf("failed to download content from content manager for repo %s/%s", githublib.RedactString(owner),
			githublib.RedactString(repo))

		log.Ctx(ctx).Error().Err(err).Msg("Failed to download content from content manager")
		return err
	}
	log.Ctx(ctx).Info().Msgf("Downloading %d files from content manager", len(downloadBlobContentKeys))

	// download the files
	// responses are in the same order as the requests
	i := 0
	for result := range downloadChan {

		if result.Err != nil {
			return result.Err
		}

		switch r := result.Resp.Response.(type) {

		case *contentmanagerproto.BatchGetContentResponse_NotFoundContent:
			return fmt.Errorf("blob %s not found", r.NotFoundContent.BlobName)
		case *contentmanagerproto.BatchGetContentResponse_FinalContent:

			filePath := filenamesToDownload[i]

			err = createFile(tmpDir, filePath, []byte(r.FinalContent.Content))
			if err != nil {
				if riResult != nil {
					riResult.FileDownloadError = fmt.Sprintf("failed to create file: %v", err)
				}
				return errors.New("failed to create file")
			}
		}
		i++
	}
	return nil
}

// download files from github and create them locally
func (geh *githubEventHandlerImpl) fetchGithubContent(
	ctx context.Context,
	tenantName string,
	githubClient GithubRepositoriesClient,
	githubFileDownloads []string,
	owner string,
	repo string,
	baseCommit string,
	tmpDir string,
	ignoreMissing bool,
) (filesToIgnore []string, err error) {
	log.Ctx(ctx).Info().Msgf("Fetching %d files from github", len(githubFileDownloads))

	for _, filePath := range githubFileDownloads {
		file, _, resp, err := githubClient.GetContents(ctx, owner, repo, filePath, &github.RepositoryContentGetOptions{Ref: baseCommit})
		if err != nil || file == nil {
			// TODO: It's unclear to me what the best way to do this check is
			// from reading github's API docs
			if ignoreMissing && resp != nil && resp.StatusCode == http.StatusNotFound {
				continue
			}
			return nil, fmt.Errorf("failed to get file contents: %w", err)
		}
		if file.DownloadURL == nil {
			log.Ctx(ctx).Debug().Msgf("No download link found for file %s", githublib.RedactString(filePath))
			filesToIgnore = append(filesToIgnore, filePath)
			continue
		}
		if file.Type != nil && (*file.Type == "symlink" || *file.Type == "submodule") {
			log.Ctx(ctx).Debug().Msgf("Skipping symlink or submodule %s", githublib.RedactString(filePath))
			filesToIgnore = append(filesToIgnore, filePath)
			continue
		}
		validSize, err := geh.isValidFileSizeToUpload(ctx, tenantName, int64(file.GetSize()))
		if err != nil {
			return nil, err
		}
		if !validSize {
			log.Ctx(ctx).Info().Msgf("Skipping file %s because it is too large", githublib.RedactString(filePath))
			filesToIgnore = append(filesToIgnore, filePath)
			continue
		}

		// This is copied from the github library's implementation of
		// DownloadContents(). We manually do this so we can see the file
		// metadata from the initial GetContents() call, particularly for
		// symlinks. For symlinks, it seems like DownloadContents() returns the
		// metadata of the symlink target instead of the symlink itself.
		dlReq, err := http.NewRequestWithContext(ctx, http.MethodGet, *file.DownloadURL, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to create request: %w", err)
		}
		dlResp, err := geh.githubClients.httpClient().Do(dlReq)
		if err != nil {
			return nil, fmt.Errorf("failed to download file: %w", err)
		}
		defer dlResp.Body.Close()

		content, err := io.ReadAll(dlResp.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read content: %w", err)
		}

		// This is probably a symlink? The contents when downloading a symlink
		// from github are usually a single line with a path to the symlink
		// target, which we sanity check here
		if len(content) > 0 && file.GetSize() == 0 && !strings.Contains(string(content), "\n") {
			if !strings.Contains(string(content), "\n") {
				log.Ctx(ctx).Info().Msgf("Skipping likely symlink %s", githublib.RedactString(filePath))
				filesToIgnore = append(filesToIgnore, filePath)
				continue
			}
		}

		err = createFile(tmpDir, filePath, content)
		if err != nil {
			return nil, fmt.Errorf("failed to create file: %w", err)
		}
	}
	return filesToIgnore, nil
}

// find any patch errors that deal with files that should be ignored - currently binary files and symlinks
// binary patch errors should come in a pair - one for the specific file and one for the patch not applying
// symlink errors do not come in pairs
func parseGitApplyErrorForFilesToIgnore(output string) []string {
	lines := strings.Split(output, "\n")

	var failedFiles []string
	var lastBinaryFile string

	for _, line := range lines {
		binaryMatches := binaryPatchRegex.FindStringSubmatch(line)
		symlinkMatches := symlinkRegex.FindStringSubmatch(line)
		if binaryMatches != nil {
			lastBinaryFile = binaryMatches[1]
		} else if symlinkMatches != nil {
			lastBinaryFile = ""
			failedFiles = append(failedFiles, symlinkMatches[1])
		} else {
			notApplyMatches := patchNotApplyRegex.FindStringSubmatch(line)
			if notApplyMatches != nil && notApplyMatches[1] == lastBinaryFile {
				failedFiles = append(failedFiles, lastBinaryFile)
				lastBinaryFile = ""
			}
		}
	}

	return failedFiles
}

// Apply the patch to the local files. Returns a new list of ignored files, the
// original ignored files and any newly ignored files, and an error
func (geh *githubEventHandlerImpl) applyDiff(
	ctx context.Context,
	tenantName string,
	requestContext *requestcontext.RequestContext,
	diff string,
	tmpDir string,
	ignoredFiles []string,
	riResult *requestinsightproto.RIGithubProcessingResult,
	githubRef *githubstatepersistproto.GithubRef,
	baseCommit,
	head string,
) ([]string, error) {
	log.Ctx(ctx).Info().Msg("Applying git diff")

	// See if there are any files we should ignore from the diff. Symlinks,
	// submodules, etc.
	diffSymlinkSubmoduleMatches := diffSymlinkSubmoduleRegex.FindAllStringSubmatch(diff, -1)
	log.Ctx(ctx).Info().Msgf("Found %d symlinks or submodules in diff to ignore", len(diffSymlinkSubmoduleMatches))
	for _, match := range diffSymlinkSubmoduleMatches {
		ignoredFiles = append(ignoredFiles, match[2])
		if match[2] != match[3] {
			ignoredFiles = append(ignoredFiles, match[3])
		}
	}

	// Look for any renames, in those cases we want to ignore both files. This
	// will catch any symlinks, submodules, etc that failed to download earlier
	// and were not caught by the diff check earlier (like renames).
	diffRenameMatches := diffRenameRegex.FindAllStringSubmatch(diff, -1)
	renames := make(map[string]string)
	for _, match := range diffRenameMatches {
		if match[2] == match[3] {
			continue
		}
		renames[match[2]] = match[3]
	}
	for _, ignored := range ignoredFiles {
		if newFile, ok := renames[ignored]; ok {
			ignoredFiles = append(ignoredFiles, newFile)
		}
	}

	// Create a temporary file for the patch
	patchFile, err := os.CreateTemp(tmpDir, "patch-*.txt")
	if err != nil {
		return nil, err
	}
	defer os.Remove(patchFile.Name())

	// Write the patch content to the temp file
	if _, err := patchFile.Write([]byte(diff)); err != nil {
		patchFile.Close()
		return nil, err
	}

	if err := patchFile.Close(); err != nil {
		return nil, err
	}

	runGitApply := func() (string, error) {
		// TODO: if this list gets too large, maybe we can use the patterns from
		// .augmentignore/.gitignore as excludes? It's unclear to me if `git apply
		// --exclude` uses the same format as .gitignore, though
		args := []string{"apply", "--unidiff-zero"}
		for _, file := range ignoredFiles {
			args = append(args, "--exclude", file)
		}
		args = append(args, patchFile.Name())
		cmd := exec.Command("git", args...)
		cmd.Dir = tmpDir
		output, err := cmd.CombinedOutput()
		return string(output), err
	}

	output, applyErr := runGitApply()

	var sanitizedErr error
	if applyErr != nil {
		// If the patch failed due to binary files or symlinks, try to apply the
		// patch again without the files to ignore. Note that this will only
		// catch modifications or deletions, not additions.
		failedFilesToIgnore := parseGitApplyErrorForFilesToIgnore(output)
		if len(failedFilesToIgnore) > 0 {
			log.Ctx(ctx).Info().Msgf("Retry git apply without binary files and symlinks")
			ignoredFiles = append(ignoredFiles, failedFilesToIgnore...)

			output, applyErr = runGitApply()
			if applyErr != nil {
				sanitizedErr = errors.New("patch failed even after excluding binary files and symlinks")
			} else {
				log.Ctx(ctx).Info().Msg("Patch applied successfully after excluding binary files and symlinks")
			}
		}
	}

	if applyErr != nil {
		geh.uploadFailedDiff(ctx, tenantName, requestContext, diff, githubRef, baseCommit, head, riResult)

		if riResult != nil {
			errorStr := applyErr.Error() + "\n" + string(output)
			if len(errorStr) > maxGitApplyErrorRILength {
				errorStr = errorStr[:maxGitApplyErrorRILength] + "... (truncated)"
			}
			riResult.GitApplyError = errorStr
		}
		if sanitizedErr != nil {
			return nil, sanitizedErr
		}
		return nil, errors.New("patch failed")
	}

	return ignoredFiles, nil
}

// process the local files to get the new file content and blobs to add and remove
func (geh *githubEventHandlerImpl) processPatchedFiles(
	ctx context.Context,
	tenantID string,
	diffInfos map[string]*DiffInfo,
	tmpDir string,
	ignoredFiles []string,
) (blobsToAdd []*contentmanagerproto.UploadBlobContent, blobsToRemove []blobnames.BlobName, err error) {
	for _, diffInfo := range diffInfos {
		// use this filePath instead of map key because it may have been renamed
		filePath := diffInfo.filename
		fullPath := filepath.Join(tmpDir, diffInfo.filename)
		if !diffInfo.removed && !slices.Contains(ignoredFiles, filePath) {
			stat, err := os.Lstat(fullPath)
			if err != nil {
				log.Info().Err(err).Msgf("Failed to stat file %s", githublib.RedactString(filePath))
				return nil, nil, fmt.Errorf("failed to stat file %s", githublib.RedactString(filePath))
			}
			if (stat.Mode() & os.ModeSymlink) != 0 {
				log.Ctx(ctx).Info().Msgf("Skipping symlink %s", githublib.RedactString(filePath))
				continue
			}

			newContent, err := os.ReadFile(fullPath)
			if err != nil {
				return nil, nil, fmt.Errorf("failed to read file %s", githublib.RedactString(filePath))
			}

			contentBytes := []byte(newContent)

			validFile, err := geh.isValidFileToUpload(ctx, tenantID, contentBytes)
			if err != nil {

				log.Ctx(ctx).Error().Err(err).Msg("Failed to validate file requirements")
				return nil, nil, err
			}
			if !validFile {
				log.Ctx(ctx).Info().Msgf("Skipping file %s because it is too large or not utf-8", githublib.RedactString(filePath))
				continue
			}

			blob := geh.createUploadBlobContent(ctx, tenantID, filePath, contentBytes)
			if blob != nil {
				diffInfo.newBlobName = blobnames.GetBlobName(filePath, contentBytes)
				blobsToAdd = append(blobsToAdd, blob)
			}
		}
		if diffInfo.oldBlobName != "" {
			blobsToRemove = append(blobsToRemove, diffInfo.oldBlobName)
		}
	}
	// Sort both lists to make things easier to test, the actual order doesn't
	// matter as long as it is deterministic
	slices.SortFunc(blobsToAdd, func(a, b *contentmanagerproto.UploadBlobContent) int {
		aKey := a.Metadata[0].Value
		bKey := b.Metadata[0].Value
		return strings.Compare(aKey, bKey)
	})
	slices.SortFunc(blobsToRemove, func(a, b blobnames.BlobName) int {
		return strings.Compare(a.String(), b.String())
	})
	return blobsToAdd, blobsToRemove, nil
}

// update content manager with added and removed blobs
func (geh *githubEventHandlerImpl) updateContentManager(blobsToAdd []*contentmanagerproto.UploadBlobContent, blobsToRemove []blobnames.BlobName, baselineCheckpoint *blobsproto.Blobs, requestContext *requestcontext.RequestContext, ctx context.Context) (checkpointBlobs *blobsproto.Blobs, err error) {
	size := 0
	var uploads []*contentmanagerproto.UploadBlobContent
	var addedBlobNames []blobnames.BlobName

	for _, blob := range blobsToAdd {
		err := geh.addBlobToBatch(ctx, blob, &uploads, &size, requestContext)
		if err != nil {
			return nil, err
		}
	}
	if len(uploads) > 0 {
		log.Ctx(ctx).Info().Msgf("Uploading %d blobs", len(uploads))
		upload_blob_results, err := geh.contentManagerClient.BatchUploadBlobContent(ctx, uploads, contentmanagerproto.IndexingPriority_DEFAULT, requestContext)
		if err != nil {
			return nil, err
		}
		for _, upload_blob_result := range upload_blob_results {
			addedBlobNames = append(addedBlobNames, (blobnames.BlobName)(upload_blob_result.BlobName))
		}
		if len(addedBlobNames) != len(uploads) {
			log.Ctx(ctx).Error().Msgf("Expected %d blob names while batch uploading but got %d", len(uploads), len(addedBlobNames))
			// TODO: should we return an error here?
		}
	}

	checkpointBlobs, err = geh.createCheckpoint(ctx, addedBlobNames, blobsToRemove, baselineCheckpoint, requestContext)
	if err != nil {
		return nil, err
	}
	return checkpointBlobs, nil
}

// Upload the diff as a blob, so we can use it for debugging. We do this rather
// than putting it directly into the RI event because diffs can be large.
func (geh *githubEventHandlerImpl) uploadFailedDiff(
	ctx context.Context,
	tenantName string,
	requestContext *requestcontext.RequestContext,
	diff string,
	githubRef *githubstatepersistproto.GithubRef,
	baseCommit, head string,
	riResult *requestinsightproto.RIGithubProcessingResult,
) {
	if ok, err := geh.isValidFileSizeToUpload(ctx, tenantName, int64(len(diff))); !ok || err != nil {
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to validate diff size for upload")
		} else {
			log.Ctx(ctx).Error().Msg("Diff is too large to upload")
		}
		return
	}
	log.Ctx(ctx).Info().Msg("Uploading diff for failed patch to content manager")
	diffBlobPath := fmt.Sprintf("github/%s/%s/%s/%s..%s", githubRef.Repo.RepoOwner, githubRef.Repo.RepoName, githubRef.Ref, baseCommit, head)
	blobName, _, blobUploadErr := geh.contentManagerClient.UploadBlobContent(ctx, []byte(diff), diffBlobPath, requestContext)
	if blobUploadErr != nil {
		log.Ctx(ctx).Error().Err(blobUploadErr).Msg("Failed to upload failed diff")
	} else {
		log.Ctx(ctx).Info().Msgf("Uploaded failed diff to blob %s", blobName)
		if riResult != nil {
			riResult.DiffBlobName = blobName
		}
	}
}

func (geh *githubEventHandlerImpl) GetRequestContext(ctx context.Context, tenantID string, requestId requestcontext.RequestId) (*requestcontext.RequestContext, error) {
	serviceToken, err := geh.tokenExchangeClient.GetSignedTokenForService(ctx, tenantID, []tokenexchangeproto.Scope{
		tokenexchangeproto.Scope_CONTENT_RW, tokenexchangeproto.Scope_SETTINGS_RW,
	})
	if err != nil {
		return nil, err
	}

	return requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "background", serviceToken), nil
}

func (geh *githubEventHandlerImpl) failedDiffResult(ctx context.Context, tenantName string, requestContext *requestcontext.RequestContext, initialErr error) (UploadDiffResult, error) {
	flag, err := geh.featureFlagHandle.BindContext("tenant_name", tenantName)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error setting feature flag context")
	} else {
		reRegister, err := flag.GetBool("github_process_reregister_on_failed_diff", false)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Error getting github_process_reregister_on_failed_diff")
			return noReRegister, initialErr
		} else if reRegister {
			// Ignore the error and try re-registering the repo
			log.Ctx(ctx).Info().Msgf("Re-registering repo due to failed diff, ignoring error: %s", initialErr)
			return failedToApplyDiff, nil
		}
	}
	return noReRegister, initialErr
}

// checks to see if a file meets our maximum file length and can
// be decoded in utf-8
func (geh *githubEventHandlerImpl) isValidFileToUpload(ctx context.Context, tenantName string, content []byte) (bool, error) {
	if valid, err := geh.isValidFileSizeToUpload(ctx, tenantName, int64(len(content))); !valid || err != nil {
		return valid, err
	}

	return utf8.Valid(content), nil
}

func (geh *githubEventHandlerImpl) isValidFileSizeToUpload(ctx context.Context, tenantName string, sizeBytes int64) (bool, error) {
	flag, err := geh.featureFlagHandle.BindContext("tenant_name", tenantName)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error setting feature flag context")
		return false, err
	}
	maxFileSize, err := flag.GetInt("max_upload_size_bytes", 1024*1024)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Error getting max_upload_size_bytes")
		return false, err
	}

	// max size for one file upload should always be less than the max size we can upload
	if maxFileSize > maxBatchUploadBlobContentSize {
		return false, fmt.Errorf("max_upload_size_bytes (%d) is greater than max_batch_upload_blob_size (%d)", maxFileSize, maxBatchUploadBlobContentSize)
	}

	if sizeBytes > int64(maxFileSize) {
		return false, nil
	}

	return true, nil
}
