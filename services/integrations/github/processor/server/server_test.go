package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"testing"

	"github.com/augmentcode/augment/base/go/secretstring"
	publicapiproto "github.com/augmentcode/augment/services/api_proxy/public_api"
	githubproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
	"github.com/augmentcode/augment/services/integrations/github/processor/processorpb"
	"github.com/augmentcode/augment/services/integrations/webhookmapping"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	grpcservice "github.com/augmentcode/augment/services/lib/grpc/service"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	settingsserviceproto "github.com/augmentcode/augment/services/settings/proto"
	"github.com/google/go-github/v64/github"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

type mockGithubHttpTransport struct {
	t *testing.T

	installations string
}

func (mt *mockGithubHttpTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	switch req.URL.String() {
	case "https://github.com/login/oauth/access_token":
		body := `{"access_token": "test-token", "token_type": "bearer", "refresh_token": "test-refresh-token"}`
		return &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(body)),
		}, nil
	case "https://api.github.com/user/installations?page=1&per_page=30":
		body := fmt.Sprintf(`{"installations": [%s]}`, mt.installations)
		return &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(body)),
		}, nil
	case "https://api.github.com/user":
		body := `{"login": "test-login"}`
		return &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(body)),
		}, nil
	case "https://api.github.com/user/emails":
		body := `[{"email": "<EMAIL>", "primary": false}, {"email": "<EMAIL>", "primary": true}]`
		return &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(body)),
		}, nil
	default:
		mt.t.Errorf("Unexpected request: %s", req.URL.String())
		return nil, nil
	}
}

func TestHydrateGithubSettings(t *testing.T) {
	// Setup
	mockWebhookResource := new(MockWebhookTenantMappingResource)
	mockGithubClients := new(MockGithubClients)
	mockGithubHttpTransport := &mockGithubHttpTransport{t: t, installations: `{"id": 12345}`}
	mockHttpClient := &http.Client{Transport: mockGithubHttpTransport}
	mockLagTracker := new(MockLagTracker)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockSettingsClientInst := new(mockSettingsClient)
	mockPublisher := new(MockPubsubPublishClient)

	server := &githubProcessorServer{
		githubAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id"),
			clientSecret: secretstring.New("test-client-secret"),
		},
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id2"),
			clientSecret: secretstring.New("test-client-secret2"),
		},
		githubClients:                mockGithubClients,
		webhookTenantMappingResource: mockWebhookResource,
		httpClient:                   mockHttpClient,
		lagTracker:                   mockLagTracker,
		requestInsightPublisher:      mockRequestInsightPublisher,
		settingsServiceClient:        mockSettingsClientInst,
		publishClient:                mockPublisher,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	// Setup mock HTTP client
	mockGithubClients.On("httpClient").Return(mockHttpClient)

	// Setup mock webhook resource
	mockWebhookResource.On("List", mock.Anything, "github-webhook").Return([]webhookmapping.WebhookTenantMapping{}, nil).Once()
	mockWebhookResource.On("Update", mock.Anything, mock.Anything, "github-test-tenant", "github-webhook").Return(&webhookmapping.WebhookTenantMapping{}, nil)

	mockGithubRepoClient := new(MockGithubRepositoriesClient)
	mockGithubAppClient := new(MockGithubAppServiceClient)

	mockGithubClient := GithubClient{
		Repositories: mockGithubRepoClient,
		App:          mockGithubAppClient,
	}

	mockGithubClients.On("installationClient", "", int64(12345)).Return(mockGithubClient)

	mockLagTracker.On("startTenantLagTracker", mockGithubClient, "test-tenant")

	// Also use a channel to ensure that we wait for the goroutine to call this.
	listReposCalled := make(chan struct{})
	listReposCall := mockGithubAppClient.On("ListRepos", mock.Anything, mock.Anything).Return(&github.ListRepositories{Repositories: []*github.Repository{{Name: github.String("test-repo"), Owner: &github.User{Login: github.String("test-owner")}, DefaultBranch: github.String("main")}}}, &github.Response{}, nil)
	listReposCall.RunFn = func(args mock.Arguments) {
		close(listReposCalled)
	}

	mockRequestInsightPublisher.On("PublishRequestEvents", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(nil)

	// Create a context with auth claims
	ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	// set up mock settings client
	mockSettingsClientInst.On("GetTenantSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext")).Return(&settingsserviceproto.GetTenantSettingsResponse{
		Settings: &settingsserviceproto.TenantSettings{
			GithubSettings: &settingsserviceproto.GithubSettings{},
		},
	}, nil)
	mockSettingsClientInst.On("UpdateTenantSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), &settingsserviceproto.TenantSettings{
		GithubSettings: &settingsserviceproto.GithubSettings{
			InstallationId: 12345,
			Repos: []*settingsproto.RepoInformation{
				{
					RepoOwner: "test-owner",
					RepoName:  "test-repo",
				},
			},
		},
	}, "").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil)

	expectedInstallation := &githubproto.InstallationEvent{
		Action: "added",
		Repos: []*githubproto.Repository{
			{
				Owner: "test-owner",
				Name:  "test-repo",
			},
		},
	}

	expectedEvent := &githubproto.GithubEvent{
		Metadata: &githubproto.EventMetadata{
			InstallationId: 12345,
			TenantId:       "test-tenant",
		},
		EventType: "installation",
		Event: &githubproto.GithubEvent_Installation{
			Installation: expectedInstallation,
		},
	}

	mockPublisher.On("Publish", mock.Anything, mock.Anything, mock.AnythingOfType("[]uint8")).
		Run(func(args mock.Arguments) {
			actualData := args.Get(2).([]byte)
			actualEvent := &githubproto.GithubEvent{}
			err := proto.Unmarshal(actualData, actualEvent)
			assert.NoError(t, err, "Failed to unmarshal actual event data")

			assert.Equal(t, expectedEvent.Metadata.InstallationId, actualEvent.Metadata.InstallationId)
			assert.Equal(t, expectedEvent.Metadata.TenantId, actualEvent.Metadata.TenantId)
			assert.Equal(t, expectedEvent.EventType, actualEvent.EventType)

			actualInstallation, ok := actualEvent.Event.(*githubproto.GithubEvent_Installation)
			assert.True(t, ok)

			assert.Equal(t, expectedInstallation.Action, actualInstallation.Installation.Action)
			assert.Equal(t, len(expectedInstallation.Repos), len(actualInstallation.Installation.Repos))
			for i, repo := range expectedInstallation.Repos {
				assert.Equal(t, repo.Owner, actualInstallation.Installation.Repos[i].Owner)
				assert.Equal(t, repo.Name, actualInstallation.Installation.Repos[i].Name)
			}
		}).
		Return(nil)

	// Run function
	req := &processorpb.HydrateGithubSettingsRequest{
		Code:           "test-code",
		InstallationId: 12345,
	}
	resp, err := server.HydrateGithubSettings(ctx, req)

	// Check results
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	<-listReposCalled

	// Try a new installation for the same tenant, which should fail
	mockGithubHttpTransport.installations = `{"id": 12345}, {"id": 67890}`
	mockWebhookResource.On("List", mock.Anything, "github-webhook").Return([]webhookmapping.WebhookTenantMapping{
		{
			Spec: webhookmapping.WebhookTenantMappingSpec{
				WebhookType:  "github",
				WebhookValue: `{"installation_id":12345}`,
				TenantID:     "test-tenant",
			},
		},
	}, nil).Once()
	req = &processorpb.HydrateGithubSettingsRequest{
		Code:           "test-code2",
		InstallationId: 67890,
	}
	resp, err = server.HydrateGithubSettings(ctx, req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "tenant already has a github installation")
	assert.Nil(t, resp)

	mockWebhookResource.AssertExpectations(t)
	mockGithubClients.AssertExpectations(t)
	mockGithubAppClient.AssertExpectations(t)
	mockGithubRepoClient.AssertExpectations(t)
	mockLagTracker.AssertExpectations(t)
	mockPublisher.AssertExpectations(t)
}

// test to ensure that we get all repos from the paginated ListRepos response
func TestUpdateGithubSettingsPagination(t *testing.T) {
	// Setup mock clients and server
	mockWebhookResource := new(MockWebhookTenantMappingResource)
	mockGithubClients := new(MockGithubClients)
	mockGithubHttpTransport := &mockGithubHttpTransport{t: t, installations: `{"id": 12345}`}
	mockHttpClient := &http.Client{Transport: mockGithubHttpTransport}
	mockLagTracker := new(MockLagTracker)
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockSettingsClient := new(mockSettingsClient)
	mockPublisher := new(MockPubsubPublishClient)

	server := &githubProcessorServer{
		githubAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id"),
			clientSecret: secretstring.New("test-client-secret"),
		},
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id2"),
			clientSecret: secretstring.New("test-client-secret2"),
		},
		githubClients:                mockGithubClients,
		webhookTenantMappingResource: mockWebhookResource,
		httpClient:                   mockHttpClient,
		lagTracker:                   mockLagTracker,
		requestInsightPublisher:      mockRequestInsightPublisher,
		settingsServiceClient:        mockSettingsClient,
		publishClient:                mockPublisher,
		userTier:                     publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	mockGithubRepoClient := new(MockGithubRepositoriesClient)
	mockGithubAppClient := new(MockGithubAppServiceClient)

	mockGithubClient := GithubClient{
		Repositories: mockGithubRepoClient,
		App:          mockGithubAppClient,
	}

	// Create a context with auth claims
	ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	// Create 150 test repos (more than one page)
	testRepos := make([]*github.Repository, 150)
	for i := 0; i < 150; i++ {
		testRepos[i] = &github.Repository{
			Owner:         &github.User{Login: github.String(fmt.Sprintf("owner-%d", i))},
			Name:          github.String(fmt.Sprintf("repo-%d", i)),
			DefaultBranch: github.String("main"),
		}
	}

	// Setup mock responses for pagination
	mockGithubAppClient.On("ListRepos", mock.Anything, &github.ListOptions{PerPage: 100}).
		Return(&github.ListRepositories{Repositories: testRepos[:100]}, &github.Response{NextPage: 1}, nil)
	mockGithubAppClient.On("ListRepos", mock.Anything, &github.ListOptions{PerPage: 100, Page: 1}).
		Return(&github.ListRepositories{Repositories: testRepos[100:]}, &github.Response{NextPage: 0}, nil)

	mockSettingsClient.On("GetTenantSettings", mock.Anything, mock.Anything).
		Return(&settingsserviceproto.GetTenantSettingsResponse{
			Settings: &settingsserviceproto.TenantSettings{},
			Version:  "1",
		}, nil)

	// Verify all 150 repos are included in the settings update
	mockSettingsClient.On("UpdateTenantSettings", mock.Anything, mock.Anything, mock.MatchedBy(func(settings *settingsserviceproto.TenantSettings) bool {
		return len(settings.GithubSettings.Repos) == 150
	}), "1").Return(&settingsserviceproto.UpdateTenantSettingsResponse{}, nil)

	// Run the test
	repoInfos, err := server.updateGithubTenantSettings(ctx, mockGithubClient, 12345)

	assert.NoError(t, err)
	assert.Equal(t, 150, len(repoInfos))

	// Verify all repos were registered
	for i := 0; i < 150; i++ {
		assert.Equal(t, fmt.Sprintf("owner-%d", i), repoInfos[i].Owner)
		assert.Equal(t, fmt.Sprintf("repo-%d", i), repoInfos[i].Name)
	}

	mockGithubAppClient.AssertExpectations(t)
	mockSettingsClient.AssertExpectations(t)
	mockPublisher.AssertExpectations(t)
}

func TestHydrateGithubUserSettings(t *testing.T) {
	// Setup mock clients and server
	mockGithubClients := new(MockGithubClients)
	mockGithubHttpTransport := &mockGithubHttpTransport{t: t, installations: `{"id": 12345}`}
	mockHttpClient := &http.Client{Transport: mockGithubHttpTransport}
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockSettingsClientInst := new(mockSettingsClient)

	server := &githubProcessorServer{
		githubAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id"),
			clientSecret: secretstring.New("test-client-secret"),
		},
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id2"),
			clientSecret: secretstring.New("test-client-secret2"),
		},
		githubClients:           mockGithubClients,
		httpClient:              mockHttpClient,
		requestInsightPublisher: mockRequestInsightPublisher,
		settingsServiceClient:   mockSettingsClientInst,
		userTier:                publicapiproto.GetModelsResponse_ENTERPRISE_TIER,
	}

	// Setup mock HTTP client
	mockGithubClients.On("httpClient").Return(mockHttpClient)

	// Create a context with auth claims
	ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	// set up mock settings client
	var nilStringPtr *string
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{},
		},
	}, nil)
	mockSettingsClientInst.On("UpdateUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr, mock.MatchedBy(func(settings *settingsserviceproto.UserSettings) bool {
		// Don't check expirations, because timestamps are tricky
		return settings.GithubUserSettings.AccessToken == "test-token" &&
			settings.GithubUserSettings.RefreshToken == "test-refresh-token" &&
			settings.GithubUserSettings.TokenType == "bearer" &&
			settings.GithubUserSettings.GithubLogin == "test-login"
	}), "").Return(&settingsserviceproto.UpdateUserSettingsResponse{}, nil)

	// Run function
	req := &processorpb.HydrateGithubUserSettingsRequest{
		Code: "test-code",
	}
	resp, err := server.HydrateGithubUserSettings(ctx, req)

	// Check results
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	mockGithubClients.AssertExpectations(t)
	mockRequestInsightPublisher.AssertExpectations(t)
	mockSettingsClientInst.AssertExpectations(t)
}

func TestHydrateGithubUserSettings_CommunityTier(t *testing.T) {
	// Setup mock clients and server
	mockGithubClients := new(MockGithubClients)
	mockGithubHttpTransport := &mockGithubHttpTransport{t: t, installations: `{"id": 12345}`}
	mockHttpClient := &http.Client{Transport: mockGithubHttpTransport}
	mockRequestInsightPublisher := new(MockRequestInsightPublisher)
	mockSettingsClientInst := new(mockSettingsClient)

	server := &githubProcessorServer{
		githubAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id"),
			clientSecret: secretstring.New("test-client-secret"),
		},
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id2"),
			clientSecret: secretstring.New("test-client-secret2"),
		},
		githubClients:           mockGithubClients,
		httpClient:              mockHttpClient,
		requestInsightPublisher: mockRequestInsightPublisher,
		settingsServiceClient:   mockSettingsClientInst,
		userTier:                publicapiproto.GetModelsResponse_COMMUNITY_TIER,
	}

	// Call hydrate and check for error
	req := &processorpb.HydrateGithubSettingsRequest{
		Code: "test-code",
	}
	_, err := server.HydrateGithubSettings(context.Background(), req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "github app installation is not supported in this tier: COMMUNITY_TIER")
}

// TestRevokeOAuthGrant tests the RevokeOAuthGrant function
func TestRevokeOAuthGrant(t *testing.T) {
	// Setup mock clients and server
	mockGithubClients := new(MockGithubClients)
	mockSettingsClientInst := new(mockSettingsClient)

	// Create a custom HTTP client that can be controlled for testing
	mockTransport := &mockTransport{}
	mockHttpClient := &http.Client{Transport: mockTransport}

	server := &githubProcessorServer{
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id"),
			clientSecret: secretstring.New("test-client-secret"),
		},
		githubClients:         mockGithubClients,
		httpClient:            mockHttpClient,
		settingsServiceClient: mockSettingsClientInst,
	}

	// Create a context with auth claims
	ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	// Set up mock settings client to return GitHub settings
	var nilStringPtr *string
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
			},
		},
		Version: "test-version",
	}, nil)

	// Set up mock settings client to accept settings update
	mockSettingsClientInst.On("UpdateUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr,
		mock.MatchedBy(func(settings *settingsserviceproto.UserSettings) bool {
			// Verify that we're clearing the settings
			return settings.GithubUserSettings != nil && settings.GithubUserSettings.AccessToken == ""
		}), "").Return(&settingsserviceproto.UpdateUserSettingsResponse{}, nil)

	// Test 1: Successful revocation
	mockTransport.SetResponse(http.StatusNoContent, "")
	resp, err := server.RevokeOAuthGrant(ctx, &processorpb.RevokeOAuthGrantRequest{})
	assert.NoError(t, err)
	assert.Equal(t, int32(0), resp.Status.Code) // codes.OK = 0
	assert.Contains(t, resp.Status.Message, "successfully revoked")

	// Test 2: GitHub API returns error but we still clear settings
	mockTransport.SetResponse(http.StatusNotFound, "Grant not found")

	// Set up expectations for the second test case
	// First call to GetUserSettings in RevokeOAuthGrant
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
			},
		},
		Version: "test-version",
	}, nil).Once()

	// clearGithubUserSettings doesn't call GetUserSettings, so we don't need to set up an expectation for it

	// Set up mock settings client to accept settings update
	mockSettingsClientInst.On("UpdateUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr,
		mock.MatchedBy(func(settings *settingsserviceproto.UserSettings) bool {
			// Verify that we're clearing the settings
			return settings.GithubUserSettings != nil && settings.GithubUserSettings.AccessToken == ""
		}), "test-version").Return(&settingsserviceproto.UpdateUserSettingsResponse{}, nil).Once()

	resp, err = server.RevokeOAuthGrant(ctx, &processorpb.RevokeOAuthGrantRequest{})
	assert.NoError(t, err)
	assert.Equal(t, int32(6), resp.Status.Code) // codes.AlreadyExists = 6
	assert.Contains(t, resp.Status.Message, "settings were still cleared")

	// Test 3: GitHub API returns error and settings update fails
	mockTransport.SetResponse(http.StatusInternalServerError, "Server error")
	// Override the UpdateUserSettings mock to return an error
	mockSettingsClientInst.ExpectedCalls = nil

	// First call to GetUserSettings in RevokeOAuthGrant
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
			},
		},
		Version: "test-version",
	}, nil).Once()

	// clearGithubUserSettings doesn't call GetUserSettings, so we don't need to set up an expectation for it

	mockSettingsClientInst.On("UpdateUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr,
		mock.Anything, "").Return(nil, fmt.Errorf("settings update failed"))

	resp, err = server.RevokeOAuthGrant(ctx, &processorpb.RevokeOAuthGrantRequest{})
	assert.NoError(t, err)
	assert.Equal(t, int32(13), resp.Status.Code) // codes.Internal = 13
	assert.Contains(t, resp.Status.Message, "failed to clear settings")

	mockSettingsClientInst.AssertExpectations(t)
}

func TestListBranchesForRepoPagination(t *testing.T) {
	mockSettingsClientInst := new(mockSettingsClient)
	mockHttpTransport := &mockTransport{}
	mockHttpClient := &http.Client{Transport: mockHttpTransport}

	server := &githubProcessorServer{
		settingsServiceClient: mockSettingsClientInst,
		httpClient:            mockHttpClient,
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id"),
			clientSecret: secretstring.New("test-client-secret"),
		},
	}

	ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	var nilStringPtr *string
	// This mock is called by s.getGithubClientForUser within ListBranchesForRepo.
	// Removing .Once() as it will be called for each sub-test's call to ListBranchesForRepo.
	mockSettingsClientInst.On("GetUserSettings", mock.Anything, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-access-token",
				GithubLogin: "test-user",
			},
		},
		Version: "test-version",
	}, nil)

	repoOwner := "test-owner"
	repoName := "test-repo"
	// Base URL for link generation; the SUT uses a client that constructs this.
	// The exact path beyond host might not be strictly necessary for go-github link parsing,
	// but using something plausible.
	apiBaseURL := "https://api.github.com" // Or whatever the client would use
	branchesURL := fmt.Sprintf("%s/repos/%s/%s/branches", apiBaseURL, repoOwner, repoName)

	allBranches := []*github.Branch{
		{Name: github.String("feat/feature-A"), Commit: &github.RepositoryCommit{SHA: github.String("sha111")}, Protected: github.Bool(false)},
		{Name: github.String("main"), Commit: &github.RepositoryCommit{SHA: github.String("sha222")}, Protected: github.Bool(true)},
		{Name: github.String("dev"), Commit: &github.RepositoryCommit{SHA: github.String("sha333")}, Protected: github.Bool(false)},
		{Name: github.String("feat/feature-B"), Commit: &github.RepositoryCommit{SHA: github.String("sha444")}, Protected: github.Bool(false)},
		{Name: github.String("release/v1.0"), Commit: &github.RepositoryCommit{SHA: github.String("sha555")}, Protected: github.Bool(true)},
	}
	totalBranches := len(allBranches)
	mockResponderPerPage := 2 // For the mock responder's logic

	responder := func(req *http.Request) (*http.Response, error) {
		q := req.URL.Query()
		pageStr := q.Get("page")
		perPageStr := q.Get("per_page") // The actual SUT passes a fixed per_page to its client.

		page := 1
		if pageStr != "" {
			p, err := strconv.Atoi(pageStr)
			if err == nil && p > 0 {
				page = p
			}
		}
		perPageForCalc := mockResponderPerPage
		if perPageStr != "" {
			pp, err := strconv.Atoi(perPageStr)
			if err == nil && pp > 0 {
			}
		}
		start := (page - 1) * perPageForCalc
		end := start + perPageForCalc
		var pageBranches []*github.Branch
		if start < totalBranches {
			if end > totalBranches {
				end = totalBranches
			}
			pageBranches = allBranches[start:end]
		} else {
			pageBranches = []*github.Branch{}
		}

		branchesJSON, err := json.Marshal(pageBranches)
		if err != nil {
			return &http.Response{StatusCode: http.StatusInternalServerError, Body: io.NopCloser(strings.NewReader("json marshal error"))}, fmt.Errorf("failed to marshal branches: %w", err)
		}

		header := http.Header{}
		var links []string
		totalPages := int(math.Ceil(float64(totalBranches) / float64(perPageForCalc)))

		// Construct Link header
		// Example: Link: <https://api.github.com/repositories/1300192/issues?page=2>; rel="next", <https://api.github.com/repositories/1300192/issues?page=5>; rel="last"
		if page < totalPages {
			links = append(links, fmt.Sprintf("<%s?page=%d&per_page=%d>; rel=\"next\"", branchesURL, page+1, perPageForCalc))
			links = append(links, fmt.Sprintf("<%s?page=%d&per_page=%d>; rel=\"last\"", branchesURL, totalPages, perPageForCalc))
		}
		if page > 1 && totalPages > 0 { // Ensure first/prev only if not on page 1 and there are pages
			links = append(links, fmt.Sprintf("<%s?page=%d&per_page=%d>; rel=\"prev\"", branchesURL, page-1, perPageForCalc))
			links = append(links, fmt.Sprintf("<%s?page=%d&per_page=%d>; rel=\"first\"", branchesURL, 1, perPageForCalc))
		}
		if len(links) > 0 {
			header.Set("Link", strings.Join(links, ", "))
		}

		return &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(string(branchesJSON))),
			Header:     header,
		}, nil
	}

	mockHttpTransport.SetCustomResponder(responder)

	// Test Case 1: No page specified (defaults to page 1 as per SUT's client call)
	t.Run("no page specified (should fetch all)", func(t *testing.T) {
		req := &processorpb.ListGithubRepoBranchesRequest{
			Repo: &processorpb.GithubRepo{Owner: repoOwner, Name: repoName},
		}
		resp, err := server.ListBranchesForRepo(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Branches, totalBranches) // All branches
		assert.Equal(t, "feat/feature-A", resp.Branches[0].Name)
		assert.Equal(t, "main", resp.Branches[1].Name)
		assert.False(t, resp.HasNextPage)
	})

	// Test Case 2: Request page 2
	t.Run("request page 2", func(t *testing.T) {
		page2 := int32(2)
		req := &processorpb.ListGithubRepoBranchesRequest{
			Repo: &processorpb.GithubRepo{Owner: repoOwner, Name: repoName},
			Page: &page2,
		}
		resp, err := server.ListBranchesForRepo(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Branches, mockResponderPerPage) // Page 2: dev, feat/feature-B
		if len(resp.Branches) > 0 {
			assert.Equal(t, "dev", resp.Branches[0].Name)
		}
		if len(resp.Branches) > 1 {
			assert.Equal(t, "feat/feature-B", resp.Branches[1].Name)
		}
		assert.True(t, resp.HasNextPage)
		assert.Equal(t, int32(3), resp.NextPage)
	})

	// Test Case 3: Request page 3 (last page, partial)
	t.Run("request page 3 (last page)", func(t *testing.T) {
		page3 := int32(3)
		req := &processorpb.ListGithubRepoBranchesRequest{
			Repo: &processorpb.GithubRepo{Owner: repoOwner, Name: repoName},
			Page: &page3,
		}
		resp, err := server.ListBranchesForRepo(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Branches, 1) // Page 3: release/v1.0
		if len(resp.Branches) > 0 {
			assert.Equal(t, "release/v1.0", resp.Branches[0].Name)
		}
		assert.False(t, resp.HasNextPage)
	})

	// Test Case 4: Request page beyond last page
	t.Run("request page beyond last", func(t *testing.T) {
		page4 := int32(4)
		req := &processorpb.ListGithubRepoBranchesRequest{
			Repo: &processorpb.GithubRepo{Owner: repoOwner, Name: repoName},
			Page: &page4,
		}
		resp, err := server.ListBranchesForRepo(ctx, req)
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Branches, 0) // Expecting 0 branches
		assert.False(t, resp.HasNextPage)
	})
}

func TestGetGithubUserToken(t *testing.T) {
	mockSettingsClientInst := new(mockSettingsClient)
	mockTransport := &mockTransport{}
	mockHttpClient := &http.Client{Transport: mockTransport}

	server := &githubProcessorServer{
		settingsServiceClient:         mockSettingsClientInst,
		allowUserTokenAccessPeerRegex: regexp.MustCompile("^allowed-peer$"),
		httpClient:                    mockHttpClient,
	}

	// Create a context with auth claims
	ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))
	ctx = grpcservice.MockContextWithPeerNames(t, ctx, []string{"allowed-peer.test-namespace"})

	// Set up some fake github API responses
	mockTransport.SetCustomResponder(func(req *http.Request) (*http.Response, error) {
		t.Log("Request: ", req.URL.String())
		t.Log("Header: ", req.Header)
		switch req.URL.String() {
		case "https://api.github.com/repos/test-owner/public-repo":
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(strings.NewReader("")),
			}, nil
		case "https://api.github.com/repos/test-owner/private-repo":
			if req.Header.Get("Authorization") != "" {
				return &http.Response{
					StatusCode: http.StatusOK,
					Body:       io.NopCloser(strings.NewReader("")),
				}, nil
			} else {
				return &http.Response{
					StatusCode: http.StatusNotFound,
					Body:       io.NopCloser(strings.NewReader("")),
				}, nil
			}
		case "https://api.github.com/user":
			body := `{"login": "test-login"}`
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(strings.NewReader(body)),
			}, nil
		case "https://api.github.com/user/emails":
			body := `[{"email": "<EMAIL>", "primary": false}, {"email": "<EMAIL>", "primary": true}]`
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       io.NopCloser(strings.NewReader(body)),
			}, nil
		default:
			t.Fatal("Unexpected request: ", req.URL.String())
			return nil, nil
		}
	})

	var nilStringPtr *string

	// Test 1: Successful token retrieval
	// This is the response order when a repo is not accessible publicly but is accessible with this token
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
				GithubLogin: "test-login",
			},
		},
		Version: "test-version",
	}, nil).Twice()
	mockSettingsClientInst.On("UpdateUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr, mock.Anything, mock.Anything).Return(&settingsserviceproto.UpdateUserSettingsResponse{}, nil).Once()
	resp, err := server.GetGithubUserToken(ctx, &processorpb.GetGithubUserTokenRequest{Repo: &processorpb.GithubRepo{Owner: "test-owner", Name: "private-repo"}})
	assert.NoError(t, err)
	assert.Equal(t, "test-token", resp.Token)
	assert.Equal(t, "test-login", resp.Login)

	// Test 2: Permission denied error when the peer is not allowed to get the token
	mockTransport.SetResponse(http.StatusOK, "")
	badCtx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	badCtx = metadata.NewIncomingContext(badCtx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))
	badCtx = grpcservice.MockContextWithPeerNames(t, badCtx, []string{"disallowed-peer.test-namespace"})
	resp, err = server.GetGithubUserToken(badCtx, &processorpb.GetGithubUserTokenRequest{})
	assert.Error(t, err)
	t.Log(err)
	assert.Equal(t, codes.PermissionDenied, status.Code(err))

	// Test 3: Unauthenticated error when there is no github token
	// This is the response order when a repo is not accessible publicly but is accessible with this token
	mockTransport.SetResponseList([]int{http.StatusNotFound, http.StatusOK}, []string{"", ""})
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "",
			},
		},
		Version: "test-version",
	}, nil).Once()
	resp, err = server.GetGithubUserToken(ctx, &processorpb.GetGithubUserTokenRequest{Repo: &processorpb.GithubRepo{Owner: "test-owner", Name: "private-repo"}})
	assert.Error(t, err)
	assert.Equal(t, codes.Unauthenticated, status.Code(err))

	// Test 4: Success when there is no github token but the repo is public
	mockTransport.SetResponse(http.StatusOK, "")
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "",
			},
		},
		Version: "test-version",
	}, nil).Once()
	resp, err = server.GetGithubUserToken(ctx, &processorpb.GetGithubUserTokenRequest{Repo: &processorpb.GithubRepo{Owner: "test-owner", Name: "public-repo"}})
	assert.NoError(t, err)
	assert.Equal(t, "", resp.Token)
	assert.Equal(t, "", resp.Login)

	// Test 5: Error when the github token is not authorized for this repo
	mockTransport.SetResponse(http.StatusNotFound, "")
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
			},
		},
		Version: "test-version",
	}, nil).Once()
	resp, err = server.GetGithubUserToken(ctx, &processorpb.GetGithubUserTokenRequest{Repo: &processorpb.GithubRepo{Owner: "test-owner", Name: "private-repo"}})
	assert.Error(t, err)
	assert.Equal(t, codes.Unauthenticated, status.Code(err))

	mockSettingsClientInst.AssertExpectations(t)
}

func TestIsUserOAuthConfigured(t *testing.T) {
	// Setup mock clients and server
	mockGithubClients := new(MockGithubClients)
	mockSettingsClientInst := new(mockSettingsClient)
	mockTransport := &mockTransport{}
	mockHttpClient := &http.Client{Transport: mockTransport}
	mockGithubClients.On("httpClient").Return(mockHttpClient).Maybe()

	server := &githubProcessorServer{
		oauthAppSecrets: appSecrets{
			clientID:     secretstring.New("test-client-id"),
			clientSecret: secretstring.New("test-client-secret"),
		},
		githubClients:                mockGithubClients,
		httpClient:                   mockHttpClient,
		settingsServiceClient:        mockSettingsClientInst,
		userAuthorizationRedirectUri: "http://test.com",
	}
	expectedOAuthURL := fmt.Sprintf("https://github.com/login/oauth/authorize?client_id=test-client-id&redirect_uri=%s&scope=%s", url.QueryEscape("http://test.com"), url.QueryEscape("repo user:email"))

	// Create a context with auth claims
	ctx := auth.WithAugmentClaims(context.Background(), &auth.AugmentClaims{TenantID: "test-tenant"})
	ctx = metadata.NewIncomingContext(ctx, metadata.New(map[string]string{
		"authorization": "Bearer test-augment-auth-token",
	}))

	var nilStringPtr *string
	// Test 1: Not configured
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "",
			},
		},
		Version: "test-version",
	}, nil).Once()
	mockTransport.SetResponse(http.StatusUnauthorized, "")
	resp, err := server.IsUserOAuthConfigured(ctx, &processorpb.IsUserOAuthConfiguredRequest{})
	assert.NoError(t, err)
	assert.False(t, resp.IsConfigured)
	assert.Equal(t, expectedOAuthURL, resp.OauthUrl)
	assert.False(t, resp.ConfiguredButNeedsUpdate)
	assert.Equal(t, "", resp.UpdatedScopes)

	// Test 2: Configured but needs update
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
				GithubLogin: "test-login",
			},
		},
		Version: "test-version",
	}, nil).Once()
	mockTransport.SetCustomResponder(func(req *http.Request) (*http.Response, error) {
		assert.Equal(t, "/user", req.URL.Path)
		resp := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"login": "test-login"}`)),
			Header:     make(http.Header),
		}
		resp.Header.Add("X-OAuth-Scopes", "repo")
		return resp, nil
	})
	resp, err = server.IsUserOAuthConfigured(ctx, &processorpb.IsUserOAuthConfiguredRequest{})
	assert.NoError(t, err)
	assert.True(t, resp.IsConfigured)
	assert.Equal(t, "", resp.OauthUrl)
	assert.True(t, resp.ConfiguredButNeedsUpdate)
	assert.Equal(t, "user:email", resp.UpdatedScopes)

	// Test 3: Configured and up to date
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
				GithubLogin: "test-login",
			},
		},
		Version: "test-version",
	}, nil).Once()
	mockTransport.SetCustomResponder(func(req *http.Request) (*http.Response, error) {
		assert.Equal(t, "/user", req.URL.Path)
		resp := &http.Response{
			StatusCode: http.StatusOK,
			Body:       io.NopCloser(strings.NewReader(`{"login": "test-login"}`)),
			Header:     make(http.Header),
		}
		resp.Header.Add("X-OAuth-Scopes", "repo")
		resp.Header.Add("X-OAuth-Scopes", "user:email")
		return resp, nil
	})
	resp, err = server.IsUserOAuthConfigured(ctx, &processorpb.IsUserOAuthConfiguredRequest{})
	assert.NoError(t, err)
	assert.True(t, resp.IsConfigured)
	assert.Equal(t, "", resp.OauthUrl)
	assert.False(t, resp.ConfiguredButNeedsUpdate)
	assert.Equal(t, "", resp.UpdatedScopes)

	// Test 4: Error when getting user scopes
	mockSettingsClientInst.On("UpdateUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr,
		mock.MatchedBy(func(settings *settingsserviceproto.UserSettings) bool {
			// Verify that we're clearing the settings
			return settings.GithubUserSettings != nil && settings.GithubUserSettings.AccessToken == ""
		}), "").Return(&settingsserviceproto.UpdateUserSettingsResponse{}, nil).Once()
	mockSettingsClientInst.On("GetUserSettings", ctx, mock.AnythingOfType("*requestcontext.RequestContext"), nilStringPtr).Return(&settingsserviceproto.GetUserSettingsResponse{
		Settings: &settingsserviceproto.UserSettings{
			GithubUserSettings: &settingsserviceproto.GithubUserSettings{
				AccessToken: "test-token",
				GithubLogin: "test-login",
			},
		},
		Version: "test-version",
	}, nil).Once()
	mockTransport.SetResponse(http.StatusUnauthorized, "Bad credentials")
	resp, err = server.IsUserOAuthConfigured(ctx, &processorpb.IsUserOAuthConfiguredRequest{})
	assert.NoError(t, err)
	assert.False(t, resp.IsConfigured)
	assert.Equal(t, expectedOAuthURL, resp.OauthUrl)

	mockSettingsClientInst.AssertExpectations(t)
	mockGithubClients.AssertExpectations(t)
}

// mockTransport is a mock http.RoundTripper for testing
type mockTransport struct {
	statusCode int
	response   string

	useList     bool
	statusCodes []int
	responses   []string
	index       int

	useCustom      bool
	customResponse *http.Response

	useCustomList   bool
	customResponses []*http.Response
	customIndex     int

	// customResponder is a function that can be used to generate a custom response.
	// If set, it takes precedence over other response mechanisms.
	customResponder func(req *http.Request) (*http.Response, error)
}

func (m *mockTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	if m.customResponder != nil {
		return m.customResponder(req)
	}
	if m.useCustomList {
		if m.customIndex >= len(m.customResponses) {
			return nil, fmt.Errorf("no more custom responses from list")
		}
		resp := m.customResponses[m.customIndex]
		m.customIndex++
		return resp, nil
	} else if m.useCustom {
		if m.customResponse == nil {
			return nil, fmt.Errorf("customResponse is nil")
		}
		return m.customResponse, nil
	} else if m.useList {
		if m.index >= len(m.statusCodes) {
			return nil, fmt.Errorf("no more responses from list")
		}
		statusCode := m.statusCodes[m.index]
		responseBody := m.responses[m.index]
		m.index++
		return &http.Response{
			StatusCode: statusCode,
			Body:       io.NopCloser(strings.NewReader(responseBody)),
			Header:     make(http.Header), // Ensure Header is not nil
		}, nil
	}
	// Default case if no specific mode is set (e.g. after SetResponse)
	return &http.Response{
		StatusCode: m.statusCode,
		Body:       io.NopCloser(strings.NewReader(m.response)),
		Header:     make(http.Header), // Ensure Header is not nil
	}, nil
}

func (m *mockTransport) SetResponse(statusCode int, response string) {
	m.customResponder = nil
	m.useList = false
	m.useCustom = false
	m.useCustomList = false
	m.index = 0
	m.customIndex = 0
	m.customResponse = nil
	m.customResponses = nil
	m.statusCodes = nil
	m.responses = nil

	m.statusCode = statusCode
	m.response = response
}

func (m *mockTransport) SetResponseList(statusCodes []int, responses []string) {
	m.customResponder = nil
	m.useList = true
	m.useCustom = false
	m.useCustomList = false
	m.customIndex = 0
	m.customResponse = nil
	m.customResponses = nil
	m.statusCode = 0
	m.response = ""

	m.statusCodes = statusCodes
	m.responses = responses
	m.index = 0
}

func (m *mockTransport) SetCustomResponse(response *http.Response) {
	m.customResponder = nil
	m.useList = false
	m.useCustom = true
	m.useCustomList = false
	m.index = 0
	m.customIndex = 0
	m.statusCodes = nil
	m.responses = nil
	m.customResponses = nil
	m.statusCode = 0
	m.response = ""

	m.customResponse = response
}

func (m *mockTransport) SetCustomResponseList(responses []*http.Response) {
	m.customResponder = nil
	m.useList = false
	m.useCustom = false
	m.useCustomList = true
	m.index = 0
	m.customResponse = nil
	m.statusCodes = nil
	m.responses = nil
	m.statusCode = 0
	m.response = ""

	m.customResponses = responses
	m.customIndex = 0
}

// SetCustomResponder allows setting a custom function to generate responses.
// This takes precedence over all other response-setting methods.
func (m *mockTransport) SetCustomResponder(responder func(req *http.Request) (*http.Response, error)) {
	m.useList = false
	m.useCustom = false
	m.useCustomList = false
	m.index = 0
	m.customIndex = 0
	m.statusCode = 0
	m.response = ""
	m.statusCodes = nil
	m.responses = nil
	m.customResponse = nil
	m.customResponses = nil

	m.customResponder = responder
}
