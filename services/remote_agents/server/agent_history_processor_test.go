package main

import (
	"testing"

	"github.com/stretchr/testify/assert"

	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
)

// Helper function to create a test exchange
func createTestExchangeForProcessor(sequenceId uint32, status string, responseText string, responseNodes ...*chatproto.ChatResultNode) *remoteagentsproto.ChatHistoryExchange {
	exchange := &remoteagentsproto.ChatHistoryExchange{
		SequenceId: sequenceId,
	}
	exchange.Exchange = &chatproto.Exchange{
		ResponseText:  &responseText,
		ResponseNodes: responseNodes,
	}
	return exchange
}

// Helper function to create a test exchange with changed files
func createTestExchangeWithChangedFiles(sequenceId uint32, status string, responseText string, changedFiles []*remoteagentsproto.ChangedFile, responseNodes ...*chatproto.ChatResultNode) *remoteagentsproto.ChatHistoryExchange {
	exchange := &remoteagentsproto.ChatHistoryExchange{
		SequenceId:   sequenceId,
		ChangedFiles: changedFiles,
	}
	exchange.Exchange = &chatproto.Exchange{
		ResponseText:  &responseText,
		ResponseNodes: responseNodes,
	}
	return exchange
}

// Helper function to create a test agent
func createTestAgentForProcessor(agentId string, status remoteagentsproto.AgentStatus) *remoteagentsproto.Agent {
	return &remoteagentsproto.Agent{
		RemoteAgentId: agentId,
		Status:        status,
	}
}

// Helper function to find a specific update type in a list of updates
func findUpdateOfType(updates []*remoteagentsproto.AgentHistoryUpdate, updateType remoteagentsproto.AgentHistoryUpdateType) *remoteagentsproto.AgentHistoryUpdate {
	for _, update := range updates {
		if update != nil && update.Type == updateType {
			return update
		}
	}
	return nil
}

// Helper function to check if an exchange is in a list of updates
func containsExchange(updates []*remoteagentsproto.AgentHistoryUpdate, sequenceId uint32) bool {
	for _, update := range updates {
		if update != nil &&
			update.Type == remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE &&
			update.Exchange != nil &&
			update.Exchange.SequenceId == sequenceId {
			return true
		}
	}
	return false
}

// Helper function to check if an exchange update is in a list of updates
func containsExchangeUpdate(updates []*remoteagentsproto.AgentHistoryUpdate, sequenceId uint32) bool {
	for _, update := range updates {
		if update != nil &&
			update.Type == remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE_UPDATE &&
			update.ExchangeUpdate != nil &&
			update.ExchangeUpdate.SequenceId == sequenceId {
			return true
		}
	}
	return false
}

// Helper function to extract text update content
func getExchangeUpdateText(updates []*remoteagentsproto.AgentHistoryUpdate, sequenceId uint32) string {
	for _, update := range updates {
		if update != nil &&
			update.Type == remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE_UPDATE &&
			update.ExchangeUpdate != nil &&
			update.ExchangeUpdate.SequenceId == sequenceId {
			return update.ExchangeUpdate.AppendedText
		}
	}
	return ""
}

// Helper function to extract node updates
func getExchangeUpdateNodes(updates []*remoteagentsproto.AgentHistoryUpdate, sequenceId uint32) []*chatproto.ChatResultNode {
	for _, update := range updates {
		if update != nil &&
			update.Type == remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE_UPDATE &&
			update.ExchangeUpdate != nil &&
			update.ExchangeUpdate.SequenceId == sequenceId {
			return update.ExchangeUpdate.AppendedNodes
		}
	}
	return nil
}

// Helper function to extract changed file updates
func getExchangeUpdateChangedFiles(updates []*remoteagentsproto.AgentHistoryUpdate, sequenceId uint32) []*remoteagentsproto.ChangedFile {
	for _, update := range updates {
		if update != nil &&
			update.Type == remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE_UPDATE &&
			update.ExchangeUpdate != nil &&
			update.ExchangeUpdate.SequenceId == sequenceId {
			return update.ExchangeUpdate.AppendedChangedFiles
		}
	}
	return nil
}

// For backward compatibility in tests
func containsTextUpdate(updates []*remoteagentsproto.AgentHistoryUpdate, sequenceId uint32) bool {
	return containsExchangeUpdate(updates, sequenceId)
}

// For backward compatibility in tests
func getTextUpdateContent(updates []*remoteagentsproto.AgentHistoryUpdate, sequenceId uint32) string {
	return getExchangeUpdateText(updates, sequenceId)
}

// TestProcessSingleExchange tests processing a single exchange
func TestProcessSingleExchange(t *testing.T) {
	// Test case 1: Nil exchange (skipped as ProcessAgentHistory doesn't handle nil exchanges directly)

	// Test case 2: Valid exchange
	processor := NewAgentHistoryProcessor(0)
	exchange := &remoteagentsproto.ChatHistoryExchange{
		SequenceId: 1,
	}

	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{exchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.Equal(t, remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE, updates[0].Type)
	assert.Equal(t, exchange, updates[0].Exchange)

	// Test case 3: Exchange with response text
	processor = NewAgentHistoryProcessor(0)
	responseText := "Incomplete response"
	exchange = createTestExchangeForProcessor(2, "in_progress", responseText)

	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{exchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.Equal(t, remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE, updates[0].Type)
	assert.Equal(t, exchange, updates[0].Exchange)
	assert.Equal(t, responseText, *processor.currentExchange.Exchange.ResponseText)
}

// TestProcessAgentHistory tests the ProcessAgentHistory method
func TestProcessAgentHistory(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// Test case 1: No exchanges
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{}, nil, "agent-id")
	assert.Equal(t, 0, len(updates))

	// Test case 2: With exchanges and agent
	exchange1 := createTestExchangeForProcessor(1, "complete", "First response")
	exchange2 := createTestExchangeForProcessor(2, "in_progress", "Second response")
	agent := createTestAgentForProcessor("agent-id", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)

	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{exchange1, exchange2}, agent, "agent-id")
	assert.Equal(t, 3, len(updates)) // 2 exchanges + 1 agent status

	// Verify that we have both exchanges and an agent status update
	assert.True(t, containsExchange(updates, 1))
	assert.True(t, containsExchange(updates, 2))
	assert.NotNil(t, findUpdateOfType(updates, remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_AGENT_STATUS))
}

// TestProcessExchangeWithTextUpdates tests streaming an exchange with text updates
func TestProcessExchangeWithTextUpdates(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// First, process an initial exchange
	initialExchange := createTestExchangeForProcessor(1, "in_progress", "Initial response")
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{initialExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchange(updates, 1))

	// Now process an update to the exchange with more text
	updatedExchange := createTestExchangeForProcessor(1, "in_progress", "Initial response with more text")
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{updatedExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsTextUpdate(updates, 1))
	assert.Equal(t, " with more text", getTextUpdateContent(updates, 1))

	// Process another update with even more text
	finalExchange := createTestExchangeForProcessor(1, "complete", "Initial response with more text and final content")
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{finalExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates)) // Just text update (no status update since we removed Status field)
	assert.True(t, containsTextUpdate(updates, 1))
	assert.Equal(t, " and final content", getTextUpdateContent(updates, 1))

	// Verify we have a text update
	assert.True(t, containsTextUpdate(updates, 1))
}

// TestReconnectWithCompletedExchange tests reconnecting when a previously incomplete exchange is now complete
func TestReconnectWithCompletedExchange(t *testing.T) {
	processor := NewAgentHistoryProcessor(0)

	// Process an exchange that was previously incomplete but is now complete
	completeExchange := createTestExchangeForProcessor(1, "complete", "This exchange is now complete")

	// First, process it initially
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{completeExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.Equal(t, remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE, updates[0].Type)

	// Now process it again to simulate the server sending it again
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{completeExchange}, nil, "agent-id")

	// We should get no updates since we already have this exchange with the same status
	assert.Equal(t, 0, len(updates))
}

// TestReconnectWithStillIncompleteExchange tests reconnecting when a previously incomplete exchange is still incomplete
func TestReconnectWithStillIncompleteExchange(t *testing.T) {
	processor := NewAgentHistoryProcessor(0)

	// Process an exchange that was previously incomplete and is still incomplete but with more text
	initialExchange := createTestExchangeForProcessor(1, "in_progress", "This exchange is still incomplete")

	// First, process it initially
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{initialExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.Equal(t, remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE, updates[0].Type)

	// Now process an updated version with more text
	updatedExchange := createTestExchangeForProcessor(1, "in_progress", "This exchange is still incomplete but has more text")
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{updatedExchange}, nil, "agent-id")

	// We should get a text update
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsTextUpdate(updates, 1))
	assert.Equal(t, " but has more text", getTextUpdateContent(updates, 1))
}

// TestMultipleExchangesWithTextUpdates tests processing multiple exchanges with text updates
func TestMultipleExchangesWithTextUpdates(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// Process two initial exchanges
	exchange1 := createTestExchangeForProcessor(1, "complete", "First complete exchange")
	exchange2 := createTestExchangeForProcessor(2, "in_progress", "Second incomplete exchange")

	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{exchange1, exchange2}, nil, "agent-id")
	assert.Equal(t, 2, len(updates))
	assert.True(t, containsExchange(updates, 1))
	assert.True(t, containsExchange(updates, 2))

	// Update the second exchange with more text
	updatedExchange2 := createTestExchangeForProcessor(2, "in_progress", "Second incomplete exchange with more text")
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{updatedExchange2}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsTextUpdate(updates, 2))
	assert.Equal(t, " with more text", getTextUpdateContent(updates, 2))

	// Add a new exchange while updating the second one
	exchange3 := createTestExchangeForProcessor(3, "in_progress", "Third exchange")
	finalExchange2 := createTestExchangeForProcessor(2, "complete", "Second incomplete exchange with more text and now complete")

	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{finalExchange2, exchange3}, nil, "agent-id")
	assert.Equal(t, 2, len(updates)) // Text update for exchange2 + new exchange3 (no status update since we removed Status field)
	assert.True(t, containsTextUpdate(updates, 2))
	assert.True(t, containsExchange(updates, 3))
}

// TestAgentStatusUpdates tests processing agent status updates
func TestAgentStatusUpdates(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// Process an initial agent status
	agent := createTestAgentForProcessor("agent-id", remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING)
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{}, agent, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.NotNil(t, findUpdateOfType(updates, remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_AGENT_STATUS))

	// Process the same agent status again - should not generate an update
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{}, agent, "agent-id")
	assert.Equal(t, 0, len(updates))

	// Process a changed agent status
	updatedAgent := createTestAgentForProcessor("agent-id", remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING)
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{}, updatedAgent, "agent-id")
	assert.Equal(t, 1, len(updates))

	agentUpdate := findUpdateOfType(updates, remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_AGENT_STATUS)
	assert.NotNil(t, agentUpdate)
	assert.Equal(t, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, agentUpdate.Agent.Status)
}

// TestNonSequentialUpdates tests processing updates that arrive out of order
func TestNonSequentialUpdates(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// Process exchange 2 first (out of order)
	exchange2 := createTestExchangeForProcessor(2, "in_progress", "Second exchange")
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{exchange2}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchange(updates, 2))

	// Now process exchange 1 (which would normally come first)
	exchange1 := createTestExchangeForProcessor(1, "complete", "First exchange")
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{exchange1}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchange(updates, 1))
}

// TestEdgeCaseTextUpdates tests edge cases for text updates
func TestEdgeCaseTextUpdates(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// Process an initial exchange
	initialExchange := createTestExchangeForProcessor(1, "in_progress", "Initial text")
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{initialExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))

	// Test case where the text completely changes (not just appended)
	completelyDifferentExchange := createTestExchangeForProcessor(1, "in_progress", "Completely different text")
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{completelyDifferentExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchangeUpdate(updates, 1))

	// The text update should be the full new text since it doesn't contain the old text
	assert.Equal(t, "Completely different text", getExchangeUpdateText(updates, 1))

	// Test empty text update (should not generate an update)
	sameExchange := createTestExchangeForProcessor(1, "in_progress", "Completely different text")
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{sameExchange}, nil, "agent-id")
	assert.Equal(t, 0, len(updates))
}

// TestProcessExchangeWithNodeUpdates tests streaming an exchange with node updates
func TestProcessExchangeWithNodeUpdates(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// Create a test node
	node1 := &chatproto.ChatResultNode{
		Id:      1,
		Type:    chatproto.ChatResultNodeType_RAW_RESPONSE,
		Content: "Initial node content",
	}

	// First, process an initial exchange with a node
	initialExchange := createTestExchangeForProcessor(1, "in_progress", "Initial response", node1)
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{initialExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchange(updates, 1))

	// Create a second node
	node2 := &chatproto.ChatResultNode{
		Id:      2,
		Type:    chatproto.ChatResultNodeType_TOOL_USE,
		Content: "Tool use content",
	}

	// Now process an update to the exchange with an additional node
	updatedExchange := createTestExchangeForProcessor(1, "in_progress", "Initial response", node1, node2)
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{updatedExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchangeUpdate(updates, 1))

	// Verify the node update
	nodeUpdates := getExchangeUpdateNodes(updates, 1)
	assert.Equal(t, 1, len(nodeUpdates))
	assert.Equal(t, node2, nodeUpdates[0])

	// Create a third node
	node3 := &chatproto.ChatResultNode{
		Id:      3,
		Type:    chatproto.ChatResultNodeType_MAIN_TEXT_FINISHED,
		Content: "",
	}

	// Process another update with both text and node updates
	finalExchange := createTestExchangeForProcessor(1, "complete", "Initial response with more text", node1, node2, node3)
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{finalExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchangeUpdate(updates, 1))

	// Verify both text and node updates
	assert.Equal(t, " with more text", getExchangeUpdateText(updates, 1))
	nodeUpdates = getExchangeUpdateNodes(updates, 1)
	assert.Equal(t, 1, len(nodeUpdates))
	assert.Equal(t, node3, nodeUpdates[0])
}

// TestProcessExchangeWithChangedFileUpdates tests streaming an exchange with changed file updates
func TestProcessExchangeWithChangedFileUpdates(t *testing.T) {
	// Create a processor
	processor := NewAgentHistoryProcessor(0)

	// Create a test changed file
	file1 := &remoteagentsproto.ChangedFile{
		OldPath:     "",
		NewPath:     "new_file.txt",
		ChangeType:  remoteagentsproto.ChangedFile_ADDED,
		OldContents: "",
		NewContents: "Initial file content",
	}

	// First, process an initial exchange with a changed file
	initialExchange := createTestExchangeWithChangedFiles(1, "in_progress", "Initial response", []*remoteagentsproto.ChangedFile{file1})
	updates := processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{initialExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchange(updates, 1))

	// Create a second changed file
	file2 := &remoteagentsproto.ChangedFile{
		OldPath:     "existing_file.txt",
		NewPath:     "existing_file.txt",
		ChangeType:  remoteagentsproto.ChangedFile_MODIFIED,
		OldContents: "Old content",
		NewContents: "Modified content",
	}

	// Now process an update to the exchange with an additional changed file
	updatedExchange := createTestExchangeWithChangedFiles(1, "in_progress", "Initial response", []*remoteagentsproto.ChangedFile{file1, file2})
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{updatedExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchangeUpdate(updates, 1))

	// Verify the changed file update
	changedFileUpdates := getExchangeUpdateChangedFiles(updates, 1)
	assert.Equal(t, 1, len(changedFileUpdates))
	assert.Equal(t, file2, changedFileUpdates[0])

	// Create a third changed file
	file3 := &remoteagentsproto.ChangedFile{
		OldPath:     "deleted_file.txt",
		NewPath:     "",
		ChangeType:  remoteagentsproto.ChangedFile_DELETED,
		OldContents: "Content to be deleted",
		NewContents: "",
	}

	// Process another update with both text and changed file updates
	finalExchange := createTestExchangeWithChangedFiles(1, "complete", "Initial response with more text", []*remoteagentsproto.ChangedFile{file1, file2, file3})
	updates = processor.ProcessAgentHistory([]*remoteagentsproto.ChatHistoryExchange{finalExchange}, nil, "agent-id")
	assert.Equal(t, 1, len(updates))
	assert.True(t, containsExchangeUpdate(updates, 1))

	// Verify both text and changed file updates
	assert.Equal(t, " with more text", getExchangeUpdateText(updates, 1))
	changedFileUpdates = getExchangeUpdateChangedFiles(updates, 1)
	assert.Equal(t, 1, len(changedFileUpdates))
	assert.Equal(t, file3, changedFileUpdates[0])
}
