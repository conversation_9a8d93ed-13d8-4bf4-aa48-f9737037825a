// Package main implements the Remote Agents server functionality.
//
// This file contains the AgentHistoryProcessor, which is responsible for processing
// agent history data and generating appropriate streaming updates for clients.
//
// AgentHistoryProcessor is separated from the AgentHistoryStreamer to facilitate unit
// testing by isolating the logic from BigTable, gRPC streams, and other dependencies.
//
// It handles:
//  1. Processing initial exchanges when clients reconnect to a stream
//  2. Detecting and generating incremental text updates for ongoing exchanges
//  3. Tracking and reporting agent status changes
//  4. Managing sequence IDs to ensure only new updates are sent to clients
package main

import (
	"strings"

	"google.golang.org/protobuf/proto"

	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
)

// AgentHistoryProcessor handles the business logic of processing agent history updates
type AgentHistoryProcessor struct {
	currentExchange            *remoteagentsproto.ChatHistoryExchange
	reqLastProcessedSequenceId uint32
	lastAgent                  *remoteagentsproto.Agent
}

// NewAgentHistoryProcessor creates a new AgentHistoryProcessor
func NewAgentHistoryProcessor(reqLastProcessedSequenceId uint32) *AgentHistoryProcessor {
	return &AgentHistoryProcessor{
		currentExchange:            nil,
		reqLastProcessedSequenceId: reqLastProcessedSequenceId,
		lastAgent:                  nil,
	}
}

// ProcessAgentHistory processes new agent history exchanges
// It takes the exchanges and agent directly
func (p *AgentHistoryProcessor) ProcessAgentHistory(
	exchanges []*remoteagentsproto.ChatHistoryExchange,
	agent *remoteagentsproto.Agent,
	agentId string,
) []*remoteagentsproto.AgentHistoryUpdate {
	// Process the exchanges and collect updates
	var updates []*remoteagentsproto.AgentHistoryUpdate

	// Process each exchange
	for _, exchange := range exchanges {
		exchangeUpdates := p.processExchange(exchange)
		updates = append(updates, exchangeUpdates...)
	}

	// Process agent status if available
	if agent != nil {
		agentUpdate := p.processAgentStatus(agentId, agent)
		if agentUpdate != nil {
			updates = append(updates, agentUpdate)
		}
	}

	return updates
}

// processExchange processes a single exchange and returns updates
func (p *AgentHistoryProcessor) processExchange(exchange *remoteagentsproto.ChatHistoryExchange) []*remoteagentsproto.AgentHistoryUpdate {
	var updates []*remoteagentsproto.AgentHistoryUpdate
	sequenceId := exchange.SequenceId

	// Check if this is a new exchange or an update to an existing one
	isNewExchange := p.currentExchange == nil || p.currentExchange.SequenceId != sequenceId

	if isNewExchange {
		// New exchange - send it as an update
		update := &remoteagentsproto.AgentHistoryUpdate{
			Type:     remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE,
			Exchange: exchange,
		}

		// Store the exchange for future updates
		p.currentExchange = exchange

		updates = append(updates, update)
	} else {
		// Existing exchange - check if there's new content to stream
		textUpdates := p.processExchangeUpdates(exchange, p.currentExchange)
		updates = append(updates, textUpdates...)
	}

	return updates
}

// processExchangeUpdates processes updates for an exchange, including both text and node updates
func (p *AgentHistoryProcessor) processExchangeUpdates(
	exchange *remoteagentsproto.ChatHistoryExchange,
	existingExchange *remoteagentsproto.ChatHistoryExchange,
) []*remoteagentsproto.AgentHistoryUpdate {
	var updates []*remoteagentsproto.AgentHistoryUpdate

	// Get request ID if available
	var requestId *string
	if exchange.Exchange != nil && exchange.Exchange.RequestId != nil {
		requestId = exchange.Exchange.RequestId
	}

	// Initialize the exchange update
	exchangeUpdate := &remoteagentsproto.ExchangeUpdate{
		RequestId:  requestId,
		SequenceId: exchange.SequenceId,
	}

	// Track if we have any updates to send
	hasUpdates := false

	// Check if there's new text content to stream
	if exchange.Exchange != nil && existingExchange.Exchange != nil &&
		exchange.Exchange.ResponseText != nil && existingExchange.Exchange.ResponseText != nil &&
		*exchange.Exchange.ResponseText != *existingExchange.Exchange.ResponseText {
		// Get the incremental update (new content since last update)
		incrementalUpdate := ""

		// If the new response contains the old one, send just the new part
		if strings.HasPrefix(*exchange.Exchange.ResponseText, *p.currentExchange.Exchange.ResponseText) {
			incrementalUpdate = (*exchange.Exchange.ResponseText)[len(*p.currentExchange.Exchange.ResponseText):]
		} else {
			// Otherwise send the full response (this shouldn't happen in practice)
			incrementalUpdate = *exchange.Exchange.ResponseText
		}

		if incrementalUpdate != "" {
			// Add the text update to the exchange update
			exchangeUpdate.AppendedText = incrementalUpdate
			hasUpdates = true
		}
	}

	// Check if there are new nodes to stream
	if exchange.Exchange != nil && existingExchange.Exchange != nil &&
		len(exchange.Exchange.ResponseNodes) > len(existingExchange.Exchange.ResponseNodes) {
		// Get the new nodes (all nodes after the last one we've seen)
		newNodes := exchange.Exchange.ResponseNodes[len(existingExchange.Exchange.ResponseNodes):]

		if len(newNodes) > 0 {
			// Add the new nodes to the exchange update
			exchangeUpdate.AppendedNodes = newNodes
			hasUpdates = true
		}
	}

	// Check if there are new changed files to stream
	if len(exchange.ChangedFiles) > len(existingExchange.ChangedFiles) {
		// Get the new changed files (all files after the last one we've seen)
		newChangedFiles := exchange.ChangedFiles[len(existingExchange.ChangedFiles):]

		if len(newChangedFiles) > 0 {
			// Add the new changed files to the exchange update
			exchangeUpdate.AppendedChangedFiles = newChangedFiles
			hasUpdates = true
		}
	}

	// If we have updates, create and return the update message
	if hasUpdates {
		// Send the incremental update as an AGENT_HISTORY_EXCHANGE_UPDATE update
		update := &remoteagentsproto.AgentHistoryUpdate{
			Type:           remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_EXCHANGE_UPDATE,
			ExchangeUpdate: exchangeUpdate,
		}

		// Update the current exchange
		p.currentExchange = exchange

		updates = append(updates, update)
	}

	return updates
}

// processAgentStatus processes agent status updates
func (p *AgentHistoryProcessor) processAgentStatus(
	agentId string,
	agent *remoteagentsproto.Agent,
) *remoteagentsproto.AgentHistoryUpdate {
	// Only send agent status update if it has changed
	if p.lastAgent == nil || !proto.Equal(agent, p.lastAgent) {
		update := &remoteagentsproto.AgentHistoryUpdate{
			Type:  remoteagentsproto.AgentHistoryUpdateType_AGENT_HISTORY_AGENT_STATUS,
			Agent: agent,
		}

		// Update the last agent
		p.lastAgent = agent

		return update
	}

	return nil
}

// getLastProcessedSequenceId returns the last sequence ID known to be completed
func (p *AgentHistoryProcessor) getLastProcessedSequenceId() uint32 {
	// If we are already streaming an exchange, return the previous sequence ID
	if p.currentExchange != nil && p.currentExchange.SequenceId > 0 {
		return p.currentExchange.SequenceId - 1
	}

	// Otherwise return the last processed sequence ID as specified by the client
	return p.reqLastProcessedSequenceId
}
