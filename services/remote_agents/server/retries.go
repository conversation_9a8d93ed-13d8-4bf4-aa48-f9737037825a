package main

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand/v2"
	"testing"
	"time"
)

const (
	exponentialBackoffBase   = 100
	exponentialBackoffJitter = 0.5
	exponentialBackoffMaxMs  = 10000 // 10 seconds
	maxLogChars              = 1000  // Maximum number of characters for logs
)

// Default value for maxExponentialBackoffAttempts, can be overridden in tests
var (
	maxExponentialBackoffAttempts = 5

	// The workspace tends to take 15-20s to come up, so try to come up with a
	// backoff scheme for that that is better than exponential backoff, while
	// also trying not to overload the k8s apiserver.
	// TODO: Older beachheads (created before 05/30) don't report status
	// immediately, so use a 60s timeout until we are confident that most/all
	// agents are using newer beachheads
	maxResumeLinearBackoffAttempts = 30
	resumeLinearBackoffDuration    = 2 * time.Second
)

// setMaxExponentialBackoffAttempts sets the maximum number of retries for exponential backoff
// This is useful for testing to avoid long test runs
func setMaxExponentialBackoffAttempts(m *testing.M, maxAttempts int, resumeLinearBackoff time.Duration) {
	maxExponentialBackoffAttempts = maxAttempts
	maxResumeLinearBackoffAttempts = maxAttempts
	resumeLinearBackoffDuration = resumeLinearBackoff
}

var retriableErr = fmt.Errorf("retriable error")

// retryWithExponentialBackoff is a generic function that retries the provided function with exponential backoff
// It can be used with functions that return only an error, or with functions that return a value and an error
// T is the type of the value returned by the function (use any for functions that only return an error)
func retryWithExponentialBackoff[T any](ctx context.Context, maxAttempts int, fn func() (T, error)) (T, error) {
	result, err := fn()
	if err == nil {
		return result, nil
	} else if !errors.Is(err, retriableErr) {
		return result, err
	}

	i := 1
	for ; i < maxAttempts; i++ {
		// Apply exponential backoff with jitter
		jitter := 1.0 + (rand.Float64() * exponentialBackoffJitter)
		backoffMs := int(float64(exponentialBackoffBase*(int(1)<<i)) * jitter)
		backoffMs = int(math.Min(float64(backoffMs), exponentialBackoffMaxMs))
		time.Sleep(time.Duration(backoffMs) * time.Millisecond)

		result, err = fn()
		if err == nil {
			return result, nil
		} else if !errors.Is(err, retriableErr) {
			return result, err
		}
	}

	return result, fmt.Errorf("failed after %d retries: %w", i, err)
}

func retryWithExponentialBackoffAnyError(ctx context.Context, maxAttempts int, fn func() error) error {
	err := fn()
	if err == nil {
		return nil
	}

	i := 1
	for ; i < maxAttempts; i++ {
		// Apply exponential backoff with jitter
		jitter := 1.0 + (rand.Float64() * exponentialBackoffJitter)
		backoffMs := int(float64(exponentialBackoffBase*(int(1)<<i)) * jitter)
		backoffMs = int(math.Min(float64(backoffMs), exponentialBackoffMaxMs))
		time.Sleep(time.Duration(backoffMs) * time.Millisecond)

		err = fn()
		if err == nil {
			return nil
		}
	}

	return fmt.Errorf("failed after %d retries: %w", i, err)
}

func retryWithLinearBackoffAnyError(ctx context.Context, maxAttempts int, backoff time.Duration, fn func() error) error {
	err := fn()
	if err == nil {
		return nil
	}

	i := 1
	for ; i < maxAttempts; i++ {
		time.Sleep(backoff)

		err = fn()
		if err == nil {
			return nil
		}
	}

	return fmt.Errorf("failed after %d retries: %w", i, err)
}
