import type { PlanInfoSchema } from "app/schemas/plan";

// Default info icon styling
const defaultInfoIconStyle = {
  color: "var(--blue-9)",
  weight: 300,
  size: "16px",
  textStyle: {
    fontStyle: "italic" as const,
    opacity: 0.85,
  },
};

export const plans: PlanInfoSchema[] = [
  {
    id: "community",
    name: "Community",
    price: "$0",
    period: "/ month",
    description:
      "For individuals who want to get started with AI-powered engineering.",
    features: [
      "50 agent requests/month",
      "$9 per 100 additional requests",
      "Context Engine",
      "MCP & Native Tools",
      "3,000 chat messages",
      "Unlimited completions",
      "Unlimited instructions",
      "Unlimited Next Edit",
      "Community support",
      { text: "AI training permitted", icon: "info", spacing: true },
    ],
    color: "var(--blue-9)",
    colorScheme: {
      radixColor: "blue",
      gradientStart: "#3b82f6",
      gradientEnd: "#1d4ed8",
    },
    infoIconStyle: defaultInfoIconStyle,
  },
  {
    id: "developer",
    name: "<PERSON>elo<PERSON>",
    price: "$30",
    period: "/ user / month",
    description:
      "For individuals or small teams that want to ship to production, fast.",
    features: [
      "Unlimited agent requests (early release)",
      "$11 per 100 additional requests",
      "Context Engine",
      "MCP & Native Tools",
      "Unlimited chats",
      "Unlimited completions",
      "Unlimited instructions",
      "Unlimited Next Edit",
      "Email and community support",
    ],
    color: "var(--purple-9)",
    colorScheme: {
      radixColor: "purple",
      gradientStart: "#8b5cf6",
      gradientEnd: "#6d28d9",
    },
    infoIconStyle: defaultInfoIconStyle,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: "Custom",
    period: "",
    description:
      "For organizations with high volume, security, or support needs.",
    features: [
      "Unlimited agent requests for now",
      "Custom agent pricing",
      "Context Engine",
      "MCP & Native Tools",
      "Unlimited chats",
      "Unlimited completions",
      "Unlimited instructions",
      "Unlimited Next Edit",
      "Slack integration",
      "Team management",
      "Usage analytics",
      "SSO, OIDC, and SCIM support",
      "Annual SOC 2 Type II reports",
      "Custom terms and invoicing",
    ],
    color: "var(--cyan-9)",
    colorScheme: {
      radixColor: "cyan",
      gradientStart: "#06b6d4",
      gradientEnd: "#0891b2",
    },
    infoIconStyle: defaultInfoIconStyle,
  },
];
