import "@radix-ui/themes/styles.css";
import "./styles/main.css";
import tailwindStyles from "./styles/tailwind.css?url";
import type React from "react";
import { useLayoutEffect } from "react";
import {
  json,
  type LinkDescriptor,
  type LoaderFunctionArgs,
} from "@remix-run/node";
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useRouteLoaderData,
} from "@remix-run/react";
import { QueryClientProvider } from "@tanstack/react-query";
import { Container, Theme } from "@radix-ui/themes";
import { logger } from "@augment-internal/logging";
import { withAuth, isAdmin } from "./.server/auth";
import { queryClient } from "./client-cache/queryClient";
import { initAppStore } from "./utils/app-store";
import { getEarliestData } from "./utils/api";
import { Toasts } from "./components/ui/Toast";
import { GeneralErrorBoundary } from "./components/GeneralErrorBoundary";
import { AnalyticsScripts, useTrackPageView } from "./analytics";
import { loadFeatureFlags } from "./feature-flags/feature-flags.server";
import { withTimeout } from "@augment-internal/ts-utils/timer";
import { applyPolyfill } from "./polyfill/style-scoped/style-scoped.polyfill";
import type { YearMonthDay } from "./utils/date";
import { ClientOnly } from "remix-utils/client-only";

export function ErrorBoundary() {
  return <GeneralErrorBoundary />;
}

export function links(): LinkDescriptor[] {
  return [{ rel: "stylesheet", href: tailwindStyles }];
}

export const loader = async (args: LoaderFunctionArgs) => {
  let earliestData: YearMonthDay | null = null;
  let featureFlags = {};
  const url = new URL(args.request.url);
  if (url.pathname.startsWith("/auth/callback")) {
    return json({
      earliestData,
      featureFlags,
      user: null,
    });
  }

  return await withAuth(async ({ user }) => {
    try {
      const { tenantId } = user;
      earliestData = (await isAdmin(user))
        ? await getEarliestData(user, tenantId)
        : null;

      featureFlags = await withTimeout(
        loadFeatureFlags(user),
        10_000,
        () => "Feature flags timed out",
      );
    } catch (e) {
      logger.error(e);
    }
    return json({
      earliestData,
      featureFlags,
      user,
    });
  })(args);
};

export function Layout({ children }: { children: React.ReactNode }) {
  useLayoutEffect(applyPolyfill, []);
  // Use useRouteLoaderData instead of useLoaderData since Layout is used in both
  // success and error flows. The loader data might be undefined in error cases.
  // https://remix.run/docs/en/main/file-conventions/root#layout-export
  const loaderData = useRouteLoaderData<typeof loader>("root");

  const featureFlags = loaderData?.featureFlags ?? {};

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
        <AnalyticsScripts />
        <script
          dangerouslySetInnerHTML={{
            __html: `window.FEATURE_FLAGS = ${JSON.stringify(featureFlags, null, 2)}`,
          }}
        />
      </head>
      <body>
        <Theme>
          <Toasts>{children}</Toasts>
        </Theme>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

function PageLoading() {
  // TODO: make this a proper loading component
  return <div />;
}

export default function App() {
  const { earliestData } = useLoaderData<typeof loader>();
  if (earliestData) {
    initAppStore(earliestData);
  }
  useTrackPageView();

  return (
    <ClientOnly fallback={<PageLoading />}>
      {() => (
        <QueryClientProvider client={queryClient}>
          <Container mx="4">
            <Outlet />
          </Container>
        </QueryClientProvider>
      )}
    </ClientOnly>
  );
}
