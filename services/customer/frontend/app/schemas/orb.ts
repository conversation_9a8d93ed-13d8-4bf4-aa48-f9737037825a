import { AugmentPlanTypeSchema } from "./augment-plan";
import { z } from "zod";

/**
 * Schema for Orb customer information
 * Contains data retrieved from the Orb API about a customer's subscription and billing status
 */
export const OrbCustomerInfoSchema = z.object({
  portalUrl: z.string().url().nullable(), // nullable for non-admin
  // Plan name can be empty string (when no subscription exists)
  // or any string value returned by the Orb API
  planId: z.string(),
  augmentPlanType: AugmentPlanTypeSchema,
  planName: z.string().nullable(),
  billingPeriodEnd: z.string().nullable(),
  trialPeriodEnd: z.string().nullable(),
  creditsRenewingEachBillingCycle: z.number().nonnegative(),
  billingCycleBillingAmount: z.string(),
  pricePerSeat: z.string(),
  maxNumSeats: z.number().nonnegative(),
  numberOfSeatsThisBillingCycle: z.number().nonnegative(),
  numberOfSeatsNextBillingCycle: z.number().nonnegative(),
  subscriptionEndDate: z.string().nullable(),
  planIsExpired: z.boolean(),
  addUsageAvailable: z.boolean(),
  teamsAllowed: z.boolean(),
  additionalUsageUnitCost: z.string(),
});

export type OrbCustomerInfoSchema = z.infer<typeof OrbCustomerInfoSchema>;
