import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { ProgressPage } from "./ProgressPage";
import {
  InfoCircledIcon,
  UpdateIcon,
  RocketIcon,
  CheckCircledIcon,
} from "@radix-ui/react-icons";

const meta = {
  title: "UI/ProgressPage",
  component: ProgressPage,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    initialProgress: {
      control: { type: "range", min: 0, max: 100, step: 1 },
      description: "Initial progress value (0-100)",
    },
    maxProgress: {
      control: { type: "range", min: 0, max: 100, step: 1 },
      description: "Maximum progress value to animate to (0-100)",
    },
    updateInterval: {
      control: { type: "number", min: 100, max: 5000, step: 100 },
      description: "Interval in milliseconds between progress updates",
    },
    progressIncrement: {
      control: { type: "number", min: 1, max: 50, step: 1 },
      description: "Amount to increment progress by each interval",
    },
    title: {
      control: "text",
      description: "Title to display",
    },
    description: {
      control: "text",
      description: "Description to display",
    },
    message: {
      control: "text",
      description: "Message to display below the progress bar",
    },
    progressColor: {
      control: "select",
      options: [
        "ds-color-accent",
        "ds-color-success",
        "ds-color-error",
        "ds-color-warning",
        "ds-color-neutral",
      ],
      description: "Color of the progress bar (CSS variable name)",
    },
    progressHeight: {
      control: "text",
      description: "Height of the progress bar",
    },
    variant: {
      control: "select",
      options: ["default", "minimal", "elevated"],
      description: "Visual variant of the component",
    },
    size: {
      control: "select",
      options: ["sm", "md", "lg"],
      description: "Size of the component",
    },
    pulseAnimation: {
      control: "boolean",
      description: "Whether to show a pulsing animation on the progress bar",
    },
    maxWidth: {
      control: "text",
      description: "Maximum width of the container",
    },
    icon: {
      control: "boolean",
      description: "Whether to show an icon (for demo purposes)",
    },
  },
} satisfies Meta<typeof ProgressPage>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    initialProgress: 0,
    maxProgress: 90,
    updateInterval: 1000,
    progressIncrement: 10,
    title: "Processing Your Request",
    description:
      "Your request is being processed. This may take a few moments.",
    message: "Please wait while we complete this operation.",
    progressColor: "ds-color-accent",
    progressHeight: "12px",
    variant: "default",
    size: "md",
    pulseAnimation: true,
    maxWidth: "800px",
  },
  render: (args) => {
    // Only add icon if the icon arg is true
    const iconComponent = args.icon ? <InfoCircledIcon /> : undefined;
    return <ProgressPage {...args} icon={iconComponent} />;
  },
};

export const Elevated: Story = {
  args: {
    initialProgress: 10,
    maxProgress: 90,
    updateInterval: 1000,
    progressIncrement: 10,
    title: "Processing Your Request",
    description:
      "Your request is being processed. This may take a few moments.",
    message: "Please wait while we complete this operation.",
    progressColor: "ds-color-accent",
    progressHeight: "12px",
    variant: "elevated",
    size: "md",
    pulseAnimation: true,
    maxWidth: "800px",
    icon: true,
  },
  render: (args) => <ProgressPage {...args} icon={<UpdateIcon />} />,
};

export const Minimal: Story = {
  args: {
    initialProgress: 20,
    maxProgress: 90,
    updateInterval: 1000,
    progressIncrement: 10,
    title: "Processing Your Request",
    description:
      "Your request is being processed. This may take a few moments.",
    message: "Please wait while we complete this operation.",
    progressColor: "ds-color-accent",
    progressHeight: "8px",
    variant: "minimal",
    size: "sm",
    pulseAnimation: true,
    maxWidth: "600px",
  },
};

export const Large: Story = {
  args: {
    initialProgress: 30,
    maxProgress: 90,
    updateInterval: 1000,
    progressIncrement: 10,
    title: "Processing Your Request",
    description:
      "Your request is being processed. This may take a few moments.",
    message: "Please wait while we complete this operation.",
    progressColor: "ds-color-accent",
    progressHeight: "16px",
    variant: "elevated",
    size: "lg",
    pulseAnimation: true,
    maxWidth: "900px",
    icon: true,
  },
  render: (args) => <ProgressPage {...args} icon={<RocketIcon />} />,
};

export const Success: Story = {
  args: {
    initialProgress: 40,
    maxProgress: 100,
    updateInterval: 500,
    progressIncrement: 15,
    title: "Deploying Your Changes",
    description:
      "Your changes are being deployed to the production environment.",
    message: "This won't take long.",
    progressColor: "ds-color-success",
    progressHeight: "12px",
    variant: "elevated",
    size: "md",
    pulseAnimation: true,
    maxWidth: "800px",
    icon: true,
  },
  render: (args) => <ProgressPage {...args} icon={<CheckCircledIcon />} />,
};
