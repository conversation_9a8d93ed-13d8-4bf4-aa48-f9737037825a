import Modal from "app/components/ui/Modal";
import { PlanOptionCard } from "./PlanOptionCard";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  cancelSubscription,
  putUserOnPlan,
  plansQueryOptions,
  userQueryOptions,
} from "app/client-cache";
import { Button } from "@radix-ui/themes";
import { isCommunityPlanOption } from "app/schemas/plan";
import { toast } from "app/components/ui/Toast";
import { typography } from "app/utils/style";
import { Enabled } from "app/components/ui/Enabled";
import { useNavigate } from "@remix-run/react";

export interface CancelSubscriptionModalProps {
  /** Optional flag to indicate if the modal is open (for storybook only) */
  isOpen?: boolean;
  /** Optional callback when the open state changes (for storybook only) */
  onOpenChange?: (isOpen: boolean) => void;
}

export function CancelSubscriptionModal({
  isOpen,
  onOpenChange,
}: CancelSubscriptionModalProps) {
  const plansQuery = useQuery(plansQueryOptions);
  const userQuery = useQuery(userQueryOptions);
  const cancelSubscriptionMutation = useMutation(cancelSubscription);
  const planMutation = useMutation(putUserOnPlan);
  const navigate = useNavigate();

  if (!plansQuery.data || !userQuery.data) return null;
  const communityPlan = plansQuery.data.find(isCommunityPlanOption);

  // Check if the user's current plan is the community plan
  const isCommunityCurrentPlan = userQuery.data.plan.name === "community";
  const shouldOfferCommunityPlan = !!(!isCommunityCurrentPlan && communityPlan);

  const handleSwitchToCommunityPlan = () => {
    if (!communityPlan) return;
    planMutation.mutate(
      { planId: communityPlan.id },
      {
        onSuccess: (data) => {
          // Only navigate if the plan was actually changed
          if (!data.alreadyOnPlan) {
            // Redirect to account page which will show the pending state
            navigate("/account", { replace: true });
          }
        },
      },
    );
  };

  function handleCancelSubscription() {
    cancelSubscriptionMutation.mutate(undefined, {
      onSuccess: () => {
        toast.success({
          title: "Subscription canceled",
          description: "Your subscription has been successfully canceled.",
        });
      },
      onError: () => {
        toast.error({
          title: "Failed to cancel subscription",
          description: "Please try again later.",
        });
      },
    });
  }

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Cancel your subscription?"
      description={
        shouldOfferCommunityPlan
          ? "Before you cancel, consider switching to our free Community plan to keep access to basic features."
          : ""
      }
      maxWidth="550px"
      trigger={(open) => (
        <Button onClick={open} color="red" variant="ghost">
          Cancel subscription
        </Button>
      )}
      footer={(close) => (
        <>
          <Button variant="soft" onClick={close}>
            Cancel
          </Button>
          <Button
            onClick={close(handleCancelSubscription)}
            disabled={cancelSubscriptionMutation.isPending}
            variant="solid"
            color="red"
          >
            Cancel subscription
          </Button>
        </>
      )}
    >
      {(close) => (
        <>
          <Enabled enabled={shouldOfferCommunityPlan}>
            {communityPlan && (
              <PlanOptionCard
                plan={communityPlan}
                isCurrent={false}
                allowCardClick={false}
                onClick={close(handleSwitchToCommunityPlan)}
                className="community-plan-card"
              />
            )}
          </Enabled>
          <div className="soft-notice">
            If you cancel your subscription:
            <ul>
              <li>You will lose access to premium features</li>
              <li>
                Your subscription will remain active until the end of your
                billing period
              </li>
              <li>
                You won&apos;t be charged again after your current billing
                period
              </li>
            </ul>
          </div>
          <style scoped>{`
            :scope.modal-body {
              display: flex;
              flex-direction: column;
              margin-top: var(--ds-spacing-3);
              gap: var(--ds-spacing-3);
              ${typography.text2.regular}
              line-height: 1.75;
            }

            .soft-notice {
              margin-top: var(--ds-spacing-4);
              color: var(--ds-color-text-subtle);
              ul {
                list-style: disc;
                padding-left: var(--ds-spacing-5);
              }
            }
          `}</style>
        </>
      )}
    </Modal>
  );
}
