import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Checkbox, Flex, Text } from "@radix-ui/themes";
import Modal from "../../ui/Modal";
import { PlanPicker } from "./PlanPicker";
import type { PlanOptionSchema } from "app/schemas/plan";
import { getPlanDescriptionWithPricingLink } from "./utils";
import { toast } from "app/components/ui/Toast";
import { useMutation } from "@tanstack/react-query";
import { createCheckoutSession } from "app/client-cache";
import { USAGE_UNITS } from "app/data/constants";

export type PlanPickerDialogProps = {
  plans: PlanOptionSchema[];
  currentPlanId?: string;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSelectPlan: (planId: string) => void;
  title?: string;
  description?: React.ReactNode;
  hasPaymentMethod?: boolean;
  scheduledPlanId?: string;
};

export function PlanPickerDialog({
  plans,
  currentPlanId,
  isOpen,
  onOpenChange,
  onSelectPlan,
  title = "Choose a Plan",
  description = getPlanDescriptionWithPricingLink(),
  hasPaymentMethod = false,
  scheduledPlanId,
}: PlanPickerDialogProps) {
  // Track the selected plan separately from the current plan
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [confirmationChecked, setConfirmationChecked] = useState<
    boolean | "indeterminate"
  >(false);

  const clearState = () => {
    setSelectedPlanId(null);
    setConfirmationChecked(false);
  };

  function isEnterprise() {
    return selectedPlanId
      ? plans.find((plan) => plan.id === selectedPlanId)?.augmentPlanType ===
          "enterprise"
      : false;
  }

  function getButtonText() {
    if (isProcessing) {
      return "Processing...";
    } else if (selectedPlanId) {
      // Check if the selected plan is a Community plan
      const selectedPlan = plans.find((plan) => plan.id === selectedPlanId);
      const isCommunityPlan = selectedPlan?.augmentPlanType === "community";
      return hasPaymentMethod || isCommunityPlan
        ? "Select Plan"
        : "Proceed to Payment";
    } else {
      return "Select a Plan";
    }
  }

  function isPlanUpgrade(currentPlanId: string | undefined, targetPlanId: string): boolean {
    if (!currentPlanId) return true; // No current plan, treat as upgrade (immediate)

    const currentPlan = plans.find((plan) => plan.id === currentPlanId);
    const targetPlan = plans.find((plan) => plan.id === targetPlanId);

    if (!currentPlan || !targetPlan) return true; // Default to upgrade if plans not found

    // Community plan changes are always immediate
    if (targetPlan.augmentPlanType === "community") return true;

    // Changes from community or trial to paid plans are always immediate (upgrades)
    if (currentPlan.augmentPlanType === "community" || currentPlan.augmentPlanType === "trial") {
      return true;
    }

    // For paid-to-paid changes, compare prices to determine upgrade vs downgrade
    if (currentPlan.augmentPlanType === "paid" && targetPlan.augmentPlanType === "paid") {
      const currentPrice = parseFloat(currentPlan.price);
      const targetPrice = parseFloat(targetPlan.price);

      // If we can't parse prices, throw an error to indicate invalid pricing data
      if (isNaN(currentPrice) || isNaN(targetPrice)) {
        throw new Error(`Invalid pricing data: current plan price "${currentPlan.price}", target plan price "${targetPlan.price}"`);
      }

      // Higher price = upgrade (immediate), lower/same price = downgrade (end of billing period)
      return targetPrice > currentPrice;
    }

    // Default to upgrade for any other cases
    return true;
  }

  function hasValidPlanPricing(currentPlanId: string | undefined, targetPlanId: string): boolean {
    try {
      isPlanUpgrade(currentPlanId, targetPlanId);
      return true;
    } catch (error) {
      return false;
    }
  }

  function getCheckboxText() {
    if (!selectedPlanId) return "";
    const selectedPlan = plans.find((plan) => plan.id === selectedPlanId);
    if (!selectedPlan)
      throw new Error("Error with plan selection, please contact support");

    let aiTrainingText = "";
    if (selectedPlan.hasTraining) {
      aiTrainingText = `By switching to the ${selectedPlan.name}, I agree to permit Augment to train AI models on my code and usage data.`;
    }

    // Determine if this is an upgrade (immediate) or downgrade (end of billing period)
    let isUpgrade: boolean;
    let hasValidPricing = true;
    try {
      isUpgrade = isPlanUpgrade(currentPlanId, selectedPlanId);
    } catch (error) {
      // If we can't determine upgrade status due to invalid pricing data,
      // mark pricing as invalid and disable the plan change
      hasValidPricing = false;
      isUpgrade = false; // This value won't be used since hasValidPricing is false
    }

    return (
      <>
        <p>{aiTrainingText}</p>
        <p>
          {!hasValidPricing ? (
            <>
              Unable to determine plan change timing due to invalid pricing data.
              Please contact support for assistance.
            </>
          ) : isUpgrade ? (
            <>
              I understand this change will take effect immediately, and I will lose
              any unused {USAGE_UNITS} from my monthly subscription, but any
              additional purchased {USAGE_UNITS} will remain.
            </>
          ) : (
            <>
              I understand this downgrade will take effect at the end of my billing
              period. My existing {USAGE_UNITS} and invoice amount will remain unchanged
              for the current billing period.
            </>
          )}
        </p>
      </>
    );
  }

  // Use the createCheckoutSession mutation
  const createCheckoutSessionMutation = useMutation(createCheckoutSession);

  const handleSelectPlan = async () => {
    // Don't allow selecting the current plan
    if (selectedPlanId && !isEnterprise() && selectedPlanId !== currentPlanId) {
      setIsProcessing(true);

      try {
        // Check if the user has a payment method or if it's a Community plan
        const selectedPlan = plans.find((plan) => plan.id === selectedPlanId);
        const isCommunityPlan = selectedPlan?.augmentPlanType === "community";

        if (!selectedPlan) {
          throw new Error("Selected plan not found");
        }

        // For Community plan or if user has payment method, proceed directly
        if (hasPaymentMethod || isCommunityPlan) {
          onSelectPlan(selectedPlanId);
          onOpenChange(false);
          return;
        }

        // For non-Community plans without payment method, create checkout session
        createCheckoutSessionMutation.mutate(
          { planId: selectedPlanId },
          {
            onSuccess: () => {
              // Keep isProcessing true during redirection
              // The redirect is handled in the mutation
            },
            onError: () => {
              // Only reset processing state on error
              setIsProcessing(false);
            },
          },
        );
      } catch (error) {
        console.error("Error handling plan selection:", error);
        toast.error({
          title: "Error",
          description: "Failed to process your request. Please try again.",
        });
        setIsProcessing(false);
      }
    }
  };

  function buildCloseHandler(closeFn: () => void) {
    return () => {
      clearState();
      closeFn();
    };
  }

  return (
    <Modal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={title}
      description={description}
      footer={(close) => (
        <>
          <Button
            size="2"
            variant="soft"
            color="gray"
            onClick={buildCloseHandler(close)}
            className="cancel-button"
          >
            Cancel
          </Button>
          <Button
            size="2"
            variant="solid"
            color="blue"
            onClick={() => handleSelectPlan()}
            disabled={
              !selectedPlanId ||
              isEnterprise() ||
              selectedPlanId === currentPlanId ||
              selectedPlanId === scheduledPlanId ||
              isProcessing ||
              !confirmationChecked ||
              (selectedPlanId ? !hasValidPlanPricing(currentPlanId, selectedPlanId) : false)
            }
            className="plan-select-button"
          >
            {getButtonText()}
          </Button>
        </>
      )}
    >
      <PlanPicker
        plans={plans}
        currentPlanId={currentPlanId}
        scheduledPlanId={scheduledPlanId}
        onSelectPlan={(planId) => {
          setSelectedPlanId(planId);
        }}
        title=""
        description=""
        showMainButton={false} // Hide the main button since we have one in the dialog footer
      />
      {selectedPlanId && (
        <Card className="plan-picker-confirmation-card">
          <Text as="label">
            <Flex gap="2">
              <Checkbox
                checked={confirmationChecked}
                onCheckedChange={setConfirmationChecked}
                className="plan-picker-confirmation-checkbox"
                aria-labelledby="plan-picker-confirmation-text"
              />
              <Text size="2" color="gray" id="plan-picker-confirmation-text">
                {getCheckboxText()}
              </Text>
            </Flex>
          </Text>
        </Card>
      )}
      <style scoped>{`
        :scope {
          /* Dialog footer button styles */
          .cancel-button {
            height: 36px;
            padding: 0 16px;
          }

          .plan-select-button {
            font-weight: bold;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25);
            padding: 0 16px;
            height: 36px;
            opacity: 1;
          }

          .plan-select-button[disabled] {
            opacity: 0.6;
          }

          .plan-picker-credits-warning {
            margin-top: var(--ds-spacing-4);
          }

          .plan-picker-confirmation-card {
            margin-top: var(--ds-spacing-4);
            background: var(--ds-color-warning-4);
            border: 1px solid var(--ds-color-warning-9);

            p {
              margin-bottom: var(--ds-spacing-1);
            }

            p:last-child {
              margin-bottom: 0;
            }
          }
        }
      `}</style>
    </Modal>
  );
}

export default PlanPickerDialog;
