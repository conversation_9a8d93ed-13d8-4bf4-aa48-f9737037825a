import { Container } from "@radix-ui/themes";
import { useQuery } from "@tanstack/react-query";
import { userQueryOptions } from "app/client-cache";
import { DangerZone } from "app/components/account/TeamPage/DangerZone";
import { Callout } from "app/components/ui/Callout";

export default function TeamPlanExpired() {
  const userQuery = useQuery(userQueryOptions);

  if (
    userQuery.data &&
    !userQuery.data.isAdmin &&
    userQuery.data.isSelfServeTeamMember
  ) {
    // non admin users on self-serve teams with expired plans should be given the option
    // to exit the team or contact their admin.
    return (
      <Container
        size="3"
        style={{ maxWidth: "800px", margin: "0 auto", padding: "40px 0" }}
      >
        <Callout type="info">
          Team plan expired: Your team&apos;s plan has expired, please contact
          your team administrator or you may exit this team and start your own
          plan.
        </Callout>
        <DangerZone
          title=""
          description="Remove yourself from this team. You will be logged out of Augment and the next time you login you can choose a plan."
        />
      </Container>
    );
  }
}
