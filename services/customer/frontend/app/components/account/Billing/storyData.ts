import type { PlanOptionSchema } from "app/schemas/plan";

/**
 * Sample plan data for stories
 * This is shared between PlanPickerDialog.stories.tsx and SubscriptionPage.stories.tsx
 */
export const samplePlans: PlanOptionSchema[] = [
  {
    id: "orb_community_plan",
    augmentPlanType: "community",
    name: "Community Plan",
    description:
      "For individuals who want to get started with AI-powered engineering.",
    agentRequests: 50,
    hasTraining: true,
    hasTeams: false,
    price: "0",
    priceLabel: "$0/mo",
    color: "var(--blue-9)",
    colorScheme: {
      radixColor: "blue",
      gradientStart: "#3b82f6",
      gradientEnd: "#1d4ed8",
    },
  },
  {
    id: "orb_developer_plan",
    augmentPlanType: "paid",
    name: "Developer Plan",
    description:
      "For professional developers who need more power and flexibility.",
    agentRequests: 500,
    hasTraining: false,
    hasTeams: true,
    price: "19",
    priceLabel: "$19/mo",
    color: "var(--purple-9)",
    colorScheme: {
      radixColor: "purple",
      gradientStart: "#8b5cf6",
      gradientEnd: "#6d28d9",
    },
  },
  {
    id: "orb_enterprise_plan",
    augmentPlanType: "enterprise",
    name: "Enterprise Plan",
    description: "For teams and organizations with advanced needs.",
    agentRequests: 5000,
    hasTraining: false,
    hasTeams: true,
    price: "99",
    priceLabel: "$99/mo",
    color: "var(--cyan-9)",
    colorScheme: {
      radixColor: "cyan",
      gradientStart: "#06b6d4",
      gradientEnd: "#0891b2",
    },
  },
];

/**
 * Alternative custom plans for stories
 */
export const customPlans: PlanOptionSchema[] = [
  {
    id: "basic",
    augmentPlanType: "unknown",
    name: "Basic",
    description: "Essential features for small teams",
    agentRequests: 100,
    hasTraining: false,
    hasTeams: true,
    price: "9",
    priceLabel: "$9/mo",
    color: "var(--green-9)",
    colorScheme: {
      radixColor: "green",
      gradientStart: "#22c55e",
      gradientEnd: "#16a34a",
    },
  },
  {
    id: "pro",
    augmentPlanType: "unknown",
    name: "Pro",
    description: "Advanced features for growing teams",
    agentRequests: 1000,
    hasTraining: false,
    hasTeams: true,
    price: "29",
    priceLabel: "$29/mo",
    color: "var(--orange-9)",
    colorScheme: {
      radixColor: "orange",
      gradientStart: "#f97316",
      gradientEnd: "#ea580c",
    },
  },
];
