import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { useState } from "react";
import { fn } from "@storybook/test";
import { PlanPicker } from "./PlanPicker";
import { samplePlans, customPlans } from "./storyData";

// Wrapper component to show selected plan
const PlanPickerWithState = (props: any) => {
  const [selectedPlanId, setSelectedPlanId] = useState<string | undefined>(
    undefined,
  );

  return (
    <div style={{ maxWidth: "800px", margin: "0 auto" }}>
      <PlanPicker
        {...props}
        onSelectPlan={(planId) => {
          setSelectedPlanId(planId);
          if (props.onSelectPlan) {
            props.onSelectPlan(planId);
          }
        }}
      />
      <div
        style={{
          marginTop: "20px",
          padding: "10px",
          background: selectedPlanId ? "#f0f9ff" : "#fff5f5",
          borderRadius: "8px",
          border: selectedPlanId ? "1px solid #cce5ff" : "1px solid #ffcccc",
        }}
      >
        {selectedPlanId ? (
          <div style={{ fontWeight: "bold", color: "var(--blue-9)" }}>
            Selected Plan:{" "}
            <span style={{ color: "var(--purple-9)" }}>{selectedPlanId}</span>
          </div>
        ) : (
          <div style={{ fontWeight: "bold", color: "var(--red-9)" }}>
            No plan selected
          </div>
        )}
      </div>
    </div>
  );
};

const meta = {
  title: "Components/Account/PlanPicker",
  component: PlanPicker,
  parameters: {
    layout: "centered",
  },
  args: {
    plans: samplePlans,
    title: "Choose Your Plan",
    description:
      "Select a plan that fits your needs. All plans include our core features.",
    onSelectPlan: fn(),
  },
} satisfies Meta<typeof PlanPicker>;

export default meta;
type Story = StoryObj<typeof PlanPicker>;

// Default story with all features
export const Default: Story = {
  render: (args) => <PlanPickerWithState {...args} />,
};

// With a current plan
export const WithCurrentPlan: Story = {
  args: {
    currentPlanId: "orb_developer_plan",
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

// With custom plans
export const CustomPlans: Story = {
  args: {
    plans: customPlans,
    currentPlanId: "basic",
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

// Without card buttons
export const WithoutCardButtons: Story = {
  args: {
    showCardButtons: false,
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

// Without main button
export const WithoutMainButton: Story = {
  args: {
    showMainButton: false,
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

// With message
export const WithMessage: Story = {
  args: {
    showMessage: true,
    message: "You need to select a plan to continue using Augment",
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

// Compact version (no buttons, no title, no description)
export const CompactVersion: Story = {
  args: {
    showCardButtons: false,
    showMainButton: false,
    title: "",
    description: "",
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

export const EnterprisePlan: Story = {
  args: {
    plans: [
      {
        id: "orb_enterprise_plan",
        augmentPlanType: "enterprise",
        name: "Enterprise",
        description: "For teams and organizations with advanced needs.",
        agentRequests: 5000,
        hasTraining: false,
        hasTeams: true,
        price: "99",
        priceLabel: "$99/mo",
        color: "var(--cyan-9)",
        colorScheme: {
          radixColor: "cyan",
          gradientStart: "#06b6d4",
          gradientEnd: "#0891b2",
        },
      },
    ],
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

// All plans including Pro and Max
export const AllPlans: Story = {
  args: {
    plans: [
      // Community Plan
      {
        id: "orb_community_plan",
        augmentPlanType: "community",
        name: "Community",
        description: "For ballers ya know!?",
        agentRequests: 50,
        hasTraining: true,
        hasTeams: false,
        price: "0",
        priceLabel: "$0/mo",
        color: "var(--blue-9)",
        colorScheme: {
          radixColor: "blue",
          gradientStart: "#3b82f6",
          gradientEnd: "#1d4ed8",
        },
      },
      // Dev Plan
      {
        id: "orb_dev_plan",
        augmentPlanType: "paid",
        name: "Dev",
        description: "For developers who need more power and flexibility.",
        agentRequests: 600,
        hasTraining: false,
        hasTeams: true,
        price: "50",
        priceLabel: "$50/mo",
        color: "var(--purple-9)",
        colorScheme: {
          radixColor: "purple",
          gradientStart: "#8b5cf6",
          gradientEnd: "#6d28d9",
        },
      },
      // Pro Plan
      {
        id: "orb_pro_plan",
        augmentPlanType: "paid",
        name: "Pro",
        description: "For professional teams with advanced needs.",
        agentRequests: 1500,
        hasTraining: false,
        hasTeams: true,
        price: "100",
        priceLabel: "$100/mo",
        color: "var(--orange-8)",
        colorScheme: {
          radixColor: "orange",
          gradientStart: "#4ade80",
          gradientEnd: "#22c55e",
        },
      },
      // Max Plan
      {
        id: "orb_max_plan",
        augmentPlanType: "paid",
        name: "Max",
        description: "For teams requiring maximum performance and flexibility.",
        agentRequests: 4500,
        hasTraining: false,
        hasTeams: true,
        price: "250",
        priceLabel: "$250/mo",
        color: "var(--gray-12)",
        colorScheme: {
          radixColor: "gray",
          gradientStart: "#18181b",
          gradientEnd: "#27272a",
        },
      },
      // Enterprise Plan
      {
        id: "orb_enterprise_plan",
        augmentPlanType: "enterprise",
        name: "Enterprise",
        description:
          "Custom solutions for organizations with specific requirements.",
        agentRequests: 10000,
        hasTraining: false,
        hasTeams: true,
        price: "Custom",
        priceLabel: "Talk to us",
        color: "var(--cyan-9)",
        colorScheme: {
          radixColor: "cyan",
          gradientStart: "#06b6d4",
          gradientEnd: "#0891b2",
        },
      },
    ],
    title: "Choose Your Augment Plan",
    description:
      "Select a plan that fits your team's needs. All plans include our core features.",
  },
  render: (args) => <PlanPickerWithState {...args} />,
};

// Upgrade path from Dev to Pro or Max
export const UpgradePath: Story = {
  args: {
    plans: [
      // Dev Plan (current)
      {
        id: "orb_dev_plan",
        augmentPlanType: "paid",
        name: "Dev",
        description: "For developers who need more power and flexibility.",
        agentRequests: 600,
        hasTraining: false,
        hasTeams: true,
        price: "50",
        priceLabel: "$50/mo",
        color: "var(--purple-9)",
        colorScheme: {
          radixColor: "purple",
          gradientStart: "#8b5cf6",
          gradientEnd: "#6d28d9",
        },
      },
      // Pro Plan
      {
        id: "orb_pro_plan",
        augmentPlanType: "paid",
        name: "Pro",
        description: "For professional teams with advanced needs.",
        agentRequests: 1500,
        hasTraining: false,
        hasTeams: true,
        price: "100",
        priceLabel: "$100/mo",
        color: "var(--green-8)",
        colorScheme: {
          radixColor: "green",
          gradientStart: "#4ade80",
          gradientEnd: "#22c55e",
        },
      },
      // Max Plan
      {
        id: "orb_max_plan",
        augmentPlanType: "paid",
        name: "Max",
        description: "For teams requiring maximum performance and flexibility.",
        agentRequests: 4500,
        hasTraining: false,
        hasTeams: true,
        price: "250",
        priceLabel: "$250/mo",
        color: "var(--gray-12)",
        colorScheme: {
          radixColor: "gray",
          gradientStart: "#18181b",
          gradientEnd: "#27272a",
        },
      },
    ],
    currentPlanId: "orb_dev_plan",
    title: "Upgrade Your Plan",
    description:
      "Upgrade to a higher tier to get more agent requests and advanced features.",
    showMessage: true,
    message:
      "Your team is growing! Consider upgrading to a higher tier for more capacity.",
  },
  render: (args) => <PlanPickerWithState {...args} />,
};
