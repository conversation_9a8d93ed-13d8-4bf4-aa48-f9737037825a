import { Card } from "app/components/ui/Card/Card";
import { typography } from "app/utils/style";
import { LeaveTeamModal } from "./LeaveTeamModal";

type DangerZoneProps = {
  title?: string;
  description?: string;
};

export function DangerZone({ title, description }: DangerZoneProps) {
  return (
    <div className="danger-zone">
      <h2 className="danger-zone-title">{title ?? "Danger Zone"}</h2>
      <Card className="danger-zone-card">
        <div className="card-header">
          <h2 className="card-title">Leave Team</h2>
          <p className="card-description">
            {description ??
              "Remove yourself from the team. You will lose access to Augment."}
          </p>
        </div>
        <LeaveTeamModal />
      </Card>
      <style scoped>{`
        :scope.danger-zone {
          padding-top: var(--ds-spacing-6);
          border-top: 1px solid var(--ds-color-neutral-6);
          display: flex;
          flex-direction: column;
          gap: var(--ds-spacing-4);
        }

        .danger-zone-title {
          ${typography.text5.regular}
        }

        .danger-zone-card {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          gap: var(--ds-spacing-3);
          border: 1px solid var(--ds-color-error-9);
          padding: var(--ds-spacing-4);

          .card-header {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: start;

            .card-title {
              ${typography.text2.bold}
              color: var(--ds-color-error-9);
            }

            .card-description {
              color: var(--ds-color-text-subtle);
            }
          }
        }
      `}</style>
    </div>
  );
}
