import type { <PERSON>a, StoryObj } from "@storybook/react";
import TopNav from "../TopNav";

const meta = {
  title: "Navigation/TopNav",
  component: TopNav,
  parameters: {
    layout: "fullscreen",
    hasLocalStub: true,
    featureFlags: {
      auth_central_user_tier_change: true,
    },
  },
  tags: ["autodocs"],
  argTypes: {
    email: {
      description: "User's email address",
      control: { type: "text" },
    },
    tenantTier: {
      description: "User's subscription plan",
      control: {
        type: "select",
        options: ["community", "developer", "enterprise"],
      },
    },
    isPending: {
      description: "Whether the user's plan change is pending",
      control: { type: "boolean" },
    },
    isAdmin: {
      description: "Whether the user has admin privileges",
      control: { type: "boolean" },
    },
    containerWidth: {
      description: "Width of the container",
      control: { type: "text" },
    },
  },
} satisfies Meta<typeof TopNav>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Enterprise: Story = {
  args: {
    email: "<EMAIL>",
    tenantTier: "enterprise",
    isPending: false,
    isAdmin: false,
    containerWidth: "1200px",
    showTeamManagementLink: false,
  },
  parameters: {
    path: "/dashboard/overview",
    featureFlags: {
      team_management: true,
    },
  },
};

export const PendingUpgrade: Story = {
  args: {
    email: "<EMAIL>",
    tenantTier: "developer",
    isPending: true,
    isAdmin: false,
    containerWidth: "1200px",
    showTeamManagementLink: true,
  },
  parameters: {
    path: "/account/subscription",
    featureFlags: {
      team_management: true,
    },
  },
};

export const EnterpriseAdmin: Story = {
  args: {
    email: "<EMAIL>",
    tenantTier: "enterprise",
    isPending: false,
    isAdmin: true,
    containerWidth: "1200px",
    showTeamManagementLink: false,
  },
  parameters: {
    path: "/dashboard/code-changes",
    featureFlags: {
      team_management: true,
    },
  },
};

export const OrbBillingDeveloper: Story = {
  args: {
    email: "<EMAIL>",
    tenantTier: "developer",
    isPending: false,
    isAdmin: true,
    containerWidth: "1200px",
    showTeamManagementLink: true,
  },
  parameters: {
    path: "/account/subscription",
    featureFlags: {
      team_management: true,
    },
  },
};

export const SelectPlanPage: Story = {
  args: {
    email: "<EMAIL>",
    tenantTier: "developer",
    isPending: false,
    isAdmin: true,
    containerWidth: "1200px",
    showTeamManagementLink: true,
  },
  parameters: {
    path: "/account/select-plan",
    featureFlags: {
      team_management: true,
    },
  },
};

export const TeamManagement: Story = {
  args: {
    email: "<EMAIL>",
    tenantTier: "developer",
    isPending: false,
    isAdmin: true,
    containerWidth: "1200px",
    showTeamManagementLink: true,
  },
  parameters: {
    path: "/account/team",
    featureFlags: {
      team_management: true,
    },
  },
};
