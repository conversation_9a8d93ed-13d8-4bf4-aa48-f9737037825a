import {
  LockClosedIcon,
  RocketIcon,
  PersonIcon,
  ClockIcon,
  LightningBoltIcon,
} from "@radix-ui/react-icons";
import { Badge, Box, Button, Flex, TabNav, Text } from "@radix-ui/themes";
import { Form, NavLink, useLocation } from "@remix-run/react";
import type { PlanOptionSchema, UserPlanSchema } from "app/schemas/plan";
import { useQuery } from "@tanstack/react-query";
import { userQueryOptions, subscriptionQueryOptions } from "app/client-cache";
import { CSSTransition } from "app/components/ui/CSSTransitions/CSSTransition";
import {
  fadeAnimation,
  translateYAnimation,
} from "app/components/ui/CSSTransitions/animations";
import { capitalize } from "app/utils/string";
import { cn } from "app/utils/style";

const getPlanBadgeConfig = (
  tier: UserPlanSchema["name"] | undefined,
  plan: PlanOptionSchema["id"] | undefined,
) => {
  if (!tier) {
    return null;
  }

  if (tier == "enterprise") {
    return {
      color: "gray",
      name: "Enterprise",
      icon: LockClosedIcon,
      cssColor: "var(--gray-12)",
    };
  }

  if (!plan) {
    return null;
  }

  // TODO(cam): use the color field on the plan instead of the id
  const config = {
    orb_trial_plan: {
      color: "orange",
      name: "Trial",
      icon: ClockIcon,
      cssColor: "var(--orange-8)",
    },
    orb_community_plan: {
      color: "sky",
      name: "Community",
      icon: PersonIcon,
      cssColor: "var(--sky-8)",
    },
    orb_developer_plan: {
      color: "indigo",
      name: "Developer",
      icon: RocketIcon,
      cssColor: "var(--indigo-9)",
    },
    orb_pro_plan: {
      color: "purple",
      name: "Pro",
      icon: RocketIcon,
      cssColor: "var(--purple-9)",
    },
    orb_max_plan: {
      color: "amber",
      name: "Max",
      icon: LightningBoltIcon,
      cssColor: "var(--amber-10)",
    },
  } as const;

  return config[plan as keyof typeof config];
};

export default function TopNav() {
  const containerWidth = "1200px";
  const { data: userData, isLoading: userDataIsLoading } =
    useQuery(userQueryOptions);
  const { data: subscriptionData } = useQuery({
    ...subscriptionQueryOptions,
    enabled: !userDataIsLoading && userData?.tenantTier !== "enterprise",
  });

  // Extract data from query with defaults for loading states
  const email = userData?.email ?? "";
  const tenantTier = userData?.tenantTier;
  const isPending = userData?.plan?.pending ?? false;
  const isAdmin = userData?.isAdmin ?? false;
  const showTeamManagementLink = userData?.showTeamManagementLink ?? false;

  const location = useLocation();
  const planConfig = getPlanBadgeConfig(tenantTier, subscriptionData?.planId);
  const PlanName = planConfig?.name;
  const PlanIcon = planConfig?.icon;

  let tabLinks: Array<{
    label: string;
    link: string;
  }> = [];
  // can't do anything else on the select plan page or when plan change is pending.
  if (
    location.pathname !== "/account/select-plan" &&
    tenantTier &&
    !isPending
  ) {
    if (tenantTier == "enterprise" && isAdmin) {
      tabLinks.push(
        { label: "Overview", link: "/dashboard/overview" },
        { label: "Code Changes", link: "/dashboard/code-changes" },
        { label: "Adoption", link: "/dashboard/adoption" },
      );
    }
    if (tenantTier !== "enterprise") {
      tabLinks.push({ label: "Subscription", link: "/account/subscription" });
    }
    if (showTeamManagementLink) {
      tabLinks.push({ label: "Team", link: "/account/team" });
    }
  }

  if (tabLinks.length <= 1) {
    tabLinks = [];
  }

  return (
    <Box className="topnav-container">
      <style scoped>
        {`
          /* Scoped styles for TopNav component */
          .tab-link {
            font-weight: 600 !important;
            font-size: 15px !important;
            padding: 8px 12px !important;
            letter-spacing: 0 !important;
            text-rendering: optimizeLegibility !important;
            -webkit-font-smoothing: antialiased !important;
            font-feature-settings: "kern" !important;
          }

          .tab-link-active {
            color: var(--slate-11) !important;
            opacity: 1 !important;
          }

          .tab-link-inactive {
            opacity: 0.7 !important;
          }

          /* Target inner elements as well */
          .tab-link a,
          .tab-link span,
          .tab-link div {
            font-weight: 600 !important;
            font-size: 15px !important;
            letter-spacing: 0 !important;
          }

          /* Skeleton loading animation using design system timing */
          @keyframes pulse {
            0%, 100% {
              opacity: 1;
            }
            50% {
              opacity: 0.5;
            }
          }

          /* Tab navigation animation with CSSTransition */
          :scope .tab-nav-container {
            overflow: hidden;
            margin-top: 12px;
          }

          /* Use CSSTransition animation functions */
          ${fadeAnimation("tab-nav", 0, 1)}
          ${translateYAnimation("-10px", "0px")}

          /* Custom height animation for tab navigation */
          :scope .tab-nav-enter {
            max-height: 0;
          }

          :scope .tab-nav-enter-active {
            max-height: 60px;
          }

          :scope .tab-nav-exit {
            max-height: 60px;
          }

          :scope .tab-nav-exit-active {
            max-height: 0;
          }

          /* Main container styles */
          :scope.topnav-container {
            border-radius: 0;
            overflow: hidden;
            padding: 18px 0;
            margin-bottom: 24px;
            width: 100%;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            /* Prevent layout shift during hydration */
            contain: layout style;
          }

          :scope .topnav-content {
            margin: 0 auto;
            width: 100%;
            padding: 0 32px;
            /* Ensure consistent layout during loading */
            min-height: 38px;
          }

          :scope .topnav-logo-section {
            position: relative;
          }

          :scope .topnav-logo {
            width: 140px;
            height: 38px;
          }

          :scope .topnav-plan-section {
            position: relative;
            z-index: 1;
          }

          :scope .topnav-plan-section.hidden {
            opacity: 0;
          }

          /* Plan badge styles using design system */
          :scope .topnav-plan-icon {
            width: 16px;
            height: 16px;
            background-color: var(--ds-color-neutral-3);
            border-radius: var(--ds-radius-1);
            animation: pulse 1.5s infinite;
          }

          :scope .topnav-plan-text {
            width: 80px;
            height: 15px;
            background-color: var(--ds-color-neutral-3);
            border-radius: var(--ds-radius-1);
            animation: pulse 1.5s infinite;
          }

          :scope .topnav-plan-text-real {
            font-size: 15px;
            letter-spacing: -0.02em;
          }

          /* Plan text gradient styles */
          :scope .topnav-plan-text-gradient {
            background: linear-gradient(90deg, var(--plan-color), color-mix(in srgb, var(--plan-color) 70%, #000));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            color: inherit;
          }

          :scope .topnav-plan-text-pending {
            background: none;
            -webkit-background-clip: unset;
            -webkit-text-fill-color: var(--plan-color);
            color: var(--plan-color);
          }

          /* Email styles */
          :scope .topnav-email {
            opacity: 0.9;
            font-weight: 500;
            font-size: 15px;
          }

          :scope .topnav-email-skeleton {
            width: 120px;
            height: 15px;
            background-color: var(--ds-color-neutral-3);
            border-radius: var(--ds-radius-1);
            animation: pulse 1.5s infinite;
          }

          /* Logout button styles */
          :scope .topnav-logout-button {
            transition: all 0.2s ease;
            font-weight: 600;
            font-size: 15px;
            opacity: 0.9;
            padding: 8px 16px;
          }

          /* Right section container to prevent layout shift */
          :scope .topnav-right-section {
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 200px; /* Prevent collapse during loading */
            justify-content: flex-end;
          }

          /* Tab navigation styles */
          :scope .topnav-tabs {
            border-bottom: none;
            position: relative;
            z-index: 2;
            margin-top: 6px;
          }
        `}
      </style>
      {/* Container to align content with page width */}
      <Box className="topnav-content" style={{ maxWidth: containerWidth }}>
        <Flex
          direction={{ initial: "column", sm: "row" }}
          justify="between"
          align={{ initial: "start", sm: "center" }}
          gap={{ initial: "3", sm: "0" }}
        >
          <Flex align="center" gap="4" py="1" className="topnav-logo-section">
            <img
              src="/augment-logo.svg"
              alt="Augment Logo"
              className="topnav-logo"
            />
            <Flex align="center" gap="3">
              <Flex
                align="center"
                gap="1"
                className={`topnav-plan-section ${location.pathname.includes("/select-plan") ? "hidden" : ""}`}
              >
                {tenantTier && planConfig && PlanIcon ? (
                  <>
                    <PlanIcon style={{ color: planConfig.cssColor }} />
                    <Text
                      size="2"
                      weight="bold"
                      className={`topnav-plan-text-real ${
                        isPending
                          ? "topnav-plan-text-pending"
                          : "topnav-plan-text-gradient"
                      }`}
                      style={
                        {
                          "--plan-color": planConfig.cssColor,
                        } as React.CSSProperties
                      }
                    >
                      {capitalize(PlanName || "")}
                    </Text>
                  </>
                ) : (
                  <>
                    <Box className="topnav-plan-icon" />
                    <Box className="topnav-plan-text" />
                  </>
                )}
              </Flex>
              {isPending && !location.pathname.includes("/select-plan") && (
                <Badge size="1" variant="soft" color="orange" radius="full">
                  <Flex align="center" gap="1">
                    <ClockIcon />
                    <Text size="1" weight="medium">
                      Pending
                    </Text>
                  </Flex>
                </Badge>
              )}
            </Flex>
          </Flex>
          <Flex align="center" gap="3" className="topnav-right-section">
            {email ? (
              <Text size="2" color="gray" className="topnav-email">
                {email}
              </Text>
            ) : (
              <Box className="topnav-email-skeleton" />
            )}
            <Box>
              <Form action="/logout">
                <Button
                  size="2"
                  color="gray"
                  variant="soft"
                  type="submit"
                  className="topnav-logout-button"
                >
                  Logout
                </Button>
              </Form>
            </Box>
          </Flex>
        </Flex>
        <Flex direction="column" gap="0" mt="3">
          <CSSTransition
            in={tabLinks.length > 0}
            timeout={300}
            baseClassName="tab-nav"
            unmountOnExit
          >
            <Box className="tab-nav-container">
              <TabNav.Root size="2" className="topnav-tabs">
                {tabLinks.map((tabLink) => (
                  <TabNav.Link
                    key={tabLink.link}
                    asChild
                    active={location.pathname === tabLink.link}
                    className={cn("tab-link", {
                      "tab-link-active": location.pathname === tabLink.link,
                      "tab-link-inactive": location.pathname !== tabLink.link,
                    })}
                  >
                    <NavLink to={tabLink.link} prefetch="intent">
                      {tabLink.label}
                    </NavLink>
                  </TabNav.Link>
                ))}
              </TabNav.Root>
            </Box>
          </CSSTransition>
        </Flex>
      </Box>
    </Box>
  );
}
