import { json, redirect } from "@remix-run/node";
import { useLoaderD<PERSON>, useNavigate } from "@remix-run/react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Box, Container, Spinner } from "@radix-ui/themes";
import { useState } from "react";
import {
  userQueryOptions,
  putUserOnPlan,
  createCheckoutSession,
} from "../client-cache";
import { PlanPicker } from "../components/account/Billing/PlanPicker";
import type { PlanOptionSchema } from "app/schemas/plan";
import { getPlanDescriptionWithPricingLink } from "../components/account/Billing/utils";
import { withAuth } from "../.server/auth";
import { AuthCentralClient } from "../.server/grpc/auth-central";
import { getAllPlans } from "../utils/plans.server";
import { getUserPendingStatus } from "app/.server/subscription-logic";
import { OrbSubscriptionInfo_SubscriptionStatus } from "~services/auth/central/server/auth_pb";
import TeamPlanExpired from "app/components/account/Billing/TeamPlanExpired";
import { ProgressPage } from "app/components/ui/ProgressPage";
import { Callout } from "app/components/ui/Callout";

export const loader = withAuth(
  async ({ user }) => {
    // Get all available plans using our common utility function
    const [plans, userOrbSubscriptionInfo, paymentInfo] = await Promise.all([
      getAllPlans(user),
      AuthCentralClient.getInstance().getUserOrbSubscriptionInfo(user),
      AuthCentralClient.getInstance().getUserOrbPaymentInfo(user),
    ]);

    const userPendingStatus = await getUserPendingStatus(
      user,
      userOrbSubscriptionInfo.orbSubscriptionInfo,
    );

    // active subscription
    if (userPendingStatus === null) {
      return redirect("/account/subscription");
    }
    // Pending cases should have been handled by parent route.

    const subscription = userOrbSubscriptionInfo.orbSubscriptionInfo;
    const hasExpiredPlan =
      subscription?.case === "subscription" &&
      subscription.value.subscriptionStatus ===
        OrbSubscriptionInfo_SubscriptionStatus.ENDED;

    // Otherwise, show the plan picker with a message
    const message = hasExpiredPlan
      ? "Your subscription has expired. Please select a plan below to continue using Augment."
      : "Please select a plan below to continue using Augment.";

    return json({
      message,
      hasExpiredPlan,
      plans,
      hasPaymentMethod: paymentInfo.hasPaymentMethod,
    });
  },
  {
    adminOnly: false,
  },
);

export default function SelectPlanPage() {
  const { message, plans, hasPaymentMethod } = useLoaderData<typeof loader>();
  const navigate = useNavigate();

  // We still need the user data for the mutation, but we don't use it for displaying the current plan
  const {
    data: userData,
    isLoading,
    isError,
    error,
  } = useQuery(userQueryOptions);

  // State to track the selected plan
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);

  // Use the shared mutation from client-cache
  const putUserOnPlanMutation = useMutation(putUserOnPlan);

  // This function only updates the local state when a plan is selected
  const handleSelectPlan = (planId: string) => {
    const selectedPlan = plans.find(
      (plan: PlanOptionSchema) => plan.id === planId,
    );
    if (!selectedPlan) return;

    // Only allow community or paid plans
    if (
      selectedPlan.augmentPlanType === "community" ||
      selectedPlan.augmentPlanType === "paid"
    ) {
      setSelectedPlanId(planId);
    }
  };

  // Use the createCheckoutSession mutation
  const createCheckoutSessionMutation = useMutation(createCheckoutSession);

  // This function triggers the mutation when the user clicks "Proceed to Checkout"
  const handleProceedToCheckout = async () => {
    if (selectedPlanId) {
      // Check if it's a Community plan
      const selectedPlan = plans.find(
        (plan: PlanOptionSchema) => plan.id === selectedPlanId,
      );
      const isCommunityPlan = selectedPlan?.augmentPlanType === "community";

      // For Community plan or if user has payment method, proceed directly
      if (hasPaymentMethod || isCommunityPlan) {
        handlePlanChange();
        return;
      }

      // For non-Community plans without payment method, create checkout session
      createCheckoutSessionMutation.mutate(
        { planId: selectedPlanId },
        {
          onSuccess: (data) => {
            // If it's a Community plan (should never happen here) or there was an error
            // with the checkout session, fall back to the normal flow
            if (data.isCommunityPlan) {
              handlePlanChange();
            }
            // Otherwise, the redirect is handled in the mutation
          },
          onError: () => {
            // Fall back to the normal flow if there's an error
            handlePlanChange();
          },
        },
      );
    }
  };

  // Helper function to handle the plan change
  const handlePlanChange = () => {
    putUserOnPlanMutation.mutate(
      { planId: selectedPlanId! },
      {
        onSuccess: (data) => {
          // Only navigate if the plan change was actually triggered
          if (!data.alreadyOnPlan) {
            // Use proper Remix navigation to account page which will show the pending state
            navigate("/account", { replace: true });
          }
        },
      },
    );
  };

  if (isLoading) {
    return (
      <ProgressPage
        title="Loading Your Account"
        description=""
        message=""
        progressColor="ds-color-accent"
        icon={<Spinner />}
      />
    );
  }
  if (isError) {
    return (
      <Callout type="error">
        Error Loading Your Account
        <p>{error.message}</p>
      </Callout>
    );
  }

  if (
    !isLoading &&
    userData &&
    !userData.isAdmin &&
    userData.isSelfServeTeamMember
  ) {
    // non admin users on self-serve teams with expired plans should be given the option
    // to exit the team or contact their admin.
    // Note we are doing this on the select-plan page to avoid dealing with routing
    // as a temporary stopgap while we sort out loading and routing issues.
    return <TeamPlanExpired />;
  }

  return (
    <Container
      size="3"
      style={{ maxWidth: "800px", margin: "0 auto", padding: "40px 0" }}
    >
      <Box style={{ padding: "24px" }}>
        <PlanPicker
          plans={plans || []}
          currentPlanId="" // Don't show any plan as current on this page
          onSelectPlan={handleSelectPlan}
          onProceedToCheckout={handleProceedToCheckout}
          selectedPlanId={selectedPlanId}
          title="Select a Plan"
          description={getPlanDescriptionWithPricingLink()}
          showMessage={true}
          message={message}
          isLoading={
            putUserOnPlanMutation.isPending ||
            createCheckoutSessionMutation.isPending
          }
        />
      </Box>
    </Container>
  );
}
