import { Box } from "@radix-ui/themes";
import { Outlet } from "@remix-run/react";
import TopNav from "../components/navigation/TopNav";
import { userQueryOptions } from "../client-cache";
import { useQuery } from "@tanstack/react-query";

export default function Layout() {
  const { data: userData, isPending } = useQuery(userQueryOptions);

  return (
    <>
      {/* Show placeholder during loading, otherwise show the actual components */}
      {isPending || (userData && userData.isSubscriptionPending) ? (
        <>
          {/* Nav placeholder with padding to match the actual navbar */}
          <Box width="100%" height="62px" style={{ padding: "12px 32px" }} />
        </>
      ) : userData ? (
        <TopNav
          email={userData.email}
          tenantTier={userData.tenantTier}
          isPending={userData.plan?.pending || false}
          isAdmin={userData.isAdmin}
          containerWidth="1200px"
          showTeamManagementLink={userData.showTeamManagementLink}
        />
      ) : null}
      {/* Always render the outlet regardless of query status */}
      <Box
        style={{
          maxWidth: "1200px",
          margin: "0 auto",
          width: "100%",
          padding: "0 32px",
        }}
      >
        <Outlet />
      </Box>
    </>
  );
}
