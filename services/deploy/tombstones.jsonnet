local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
local tenantNamespaces = import 'deploy/tenants/namespaces.jsonnet';

// Tombstones for deployments that should be deleted.
//
// To ensure you have the right tombstone name, do these steps:
// 1. Find the app name of the deployment you want to delete.
//    kubectl -n central-staging describe deployments <deployment-name> | grep app=
//    Make sure to spell the name of the name of the deployment correctly.
//    use `kubectl get deployments`, and double check for sublte capitalization errors,
//    such as '33B' vs '33b'.
// 2. Add the tombstone to this file - see the list at the end of this file.
// 3. Run `bazel run //tools/deploy_runner:check_tombstone` to print the list of all tombstones and their app names - check that
//    the app name of the tombstone you added is correct.
// 4. Run `bazel run //tools/deploy_runner:check_tombstone -- --target-name=<your-tombstone-name>` to check where it will have effect.
//    If you're ADDING a tombstone, run it AFTER adding the entry, and confirm it ends by printing `cannot be removed` or `has objects to delete`. If either of these are mentioned, it is okay to add the tombstone.
//    If you're REMOVING a tombstone, run it BEFORE removing the entry, and confirm it ends by printing `can be removed`.

// This list includes central namespaces for clouds that may have tenants
local centralTasks = [
  {
    namespace: 'central-staging',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    namespace: 'central',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  },
  {
    namespace: 'central-staging',
    env: 'STAGING',
    cloud: 'GCP_EU_WEST4_PROD',
  },
  {
    namespace: 'central',
    env: 'PROD',
    cloud: 'GCP_EU_WEST4_PROD',
  },
];

// This list includes central namespaces for GSC (without tenants)
local gscCentralTasksByEnv = {
  STAGING: {
    namespace: 'central-staging',
    env: 'STAGING',
    cloud: 'GCP_US_CENTRAL1_GSC_PROD',
  },
  PROD: {
    namespace: 'central',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_GSC_PROD',
  },
};

local nonAiTutorProdNamespaces =
  [
    ns
    for ns in tenantNamespaces.namespaces
    if !lib.contains(tenantNamespaces.aiTutorNamespaces, ns)
  ];

local lookupTenantNamespaces(names) = [ns for ns in tenantNamespaces.namespaces if lib.contains(names, ns.namespace)];

// Tombstone tenant stuff before central stuff. It's generally safer/nicer for
// things to be cleaned up in the opposite order that they were deployed in, and
// hopefully triggers fewer alerts.
local tombstonePriority = 8;
local tombstoneCentralPriority = 7;

// Helper functions for tombstone definition

// allows to tombstone any app, even if it doesn't fit any pattern we support
local tombstoneApp(envs, appName, priority, tasks, cloud=null) =
  assert std.length(envs) > 0;
  assert std.length(std.filter(function(env) env == 'STAGING' || env == 'PROD', envs)) == std.length(envs) : 'env must be STAGING or PROD';
  assert std.member([tombstoneCentralPriority, tombstonePriority], priority) : 'priority must be one of [tombstoneCentralPriority, tombstonePriority]';
  assert std.length([t for t in tasks if std.member(envs, t.env) && (cloud == null || t.cloud == cloud)]) > 0 : 'must have at least one task';
  lib.flatten([
    [
      {
        name: '%s-%s-tombstone' % [std.asciiLower(env), std.asciiLower(appName)],
        priority: priority,
        kubecfg_tombstone: {
          object: [
            {
              kind: 'app',
              name: '%s' % std.asciiLower(appName),
            },
          ],
          task: [t for t in tasks if t.env == env && (cloud == null || t.cloud == cloud)],
        },
      },

    ]
    for env in envs
  ]);

local allowedModelTypes = ['chat', 'completion', 'edit', 'next-edit'];

// Generation tombstones for models that have inference
local generationTombstones(envs, modelType, name, cloud=null, namespaces=tenantNamespaces.namespaces) =
  // only pick the central namespaces that is relevant for the tenant namespaces. Also include GSC central namespaces (regardless of whether they have tenants)
  local centralNamespaces = [t for t in centralTasks if std.member(std.map(function(c) [c.cloud, c.env], namespaces), [t.cloud, t.env])] + std.map(function(e) gscCentralTasksByEnv[e], envs);


  assert std.member(allowedModelTypes, modelType) : 'modelType must be one of %s' % std.join(', ', allowedModelTypes);
  lib.flatten([
    tombstoneApp(envs, 'infer-%s' % name, tombstoneCentralPriority, centralNamespaces, cloud),
    tombstoneApp(envs, '%s-%s' % [modelType, name], tombstonePriority, namespaces, cloud),
    if modelType == 'next-edit' then tombstoneApp(envs, 'infer-%s-chat' % name, tombstoneCentralPriority, centralNamespaces, cloud) else [],
    if modelType == 'next-edit' then tombstoneApp(envs, 'infer-%s-reranker' % name, tombstoneCentralPriority, centralNamespaces, cloud) else [],
  ]);

// Generation tombstones for third party models that have no inference
local generationTombstonesThirdParty(envs, modelType, name, cloud=null, namespaces=tenantNamespaces.namespaces) =
  assert std.member(allowedModelTypes, modelType) : 'modelType must be one of %s' % std.join(', ', allowedModelTypes);
  tombstoneApp(envs, '%s-%s' % [modelType, name], tombstonePriority, namespaces, cloud);

// Gets rid of the embeddings indexer and transformation key in the namespaces listed. The namespaces
// can be further filtered down by specifying the cloud parameter.
//
// The embedders are not deleted by this call.
local embeddingsIndexerTombstones(envs, name, transformationKeyName, namespaces, cloud=null) =
  assert std.length(envs) > 0;
  assert std.length(std.filter(function(env) env == 'STAGING' || env == 'PROD', envs)) == std.length(envs) : 'env must be STAGING or PROD';
  lib.flatten([
    tombstoneApp(envs, 'embedding-indexer-%s' % name, tombstonePriority, namespaces, cloud),
    [
      {
        name: '%s-%s-transformation-key-tombstone' % [std.asciiLower(env), std.asciiLower(name)],
        priority: tombstonePriority,
        kubecfg_tombstone: {
          object: [
            {
              apiVersion: 'eng.augmentcode.com/v1',
              kind: 'TransformationKey',
              name: transformationKeyName,
            },
          ],
          task: [t for t in namespaces if t.env == env && (cloud == null || t.cloud == cloud)],
        },
      }
      for env in envs
    ],
  ]);


// Embedding tombstones
//
// Wipes out all embedding resources related to an embedding model. There is no per-namespace, per-cloud control
// because we wipe out shared embedders. It is possible to only wipe STAGING or only wipe PROD.
//
// If you need finer grained control, check out embeddingsIndexerTombstones and tombstoneApp.
local embeddingTombstones(envs, name, transformationKeyName, indexerNamespaces=tenantNamespaces.namespaces) =
  assert std.length(envs) > 0;
  assert std.length(std.filter(function(env) env == 'STAGING' || env == 'PROD', envs)) == std.length(envs) : 'env must be STAGING or PROD';

  local embedderNamespaces = [t for t in centralTasks if std.member(envs, t.env)] + std.map(function(e) gscCentralTasksByEnv[e], envs);

  lib.flatten([
    tombstoneApp(envs, 'embedder-%s' % name, tombstoneCentralPriority, embedderNamespaces),
    embeddingsIndexerTombstones(envs, name, transformationKeyName, indexerNamespaces),
  ]);

local engAccessMembershipTombstones(names) = [
  {
    name: 'eng-access-membership-tombstones',
    priority: tombstonePriority,
    kubecfg_tombstone: {
      object: [
        {
          apiVersion: 'cloudidentity.cnrm.cloud.google.com/v1beta1',
          kind: 'CloudIdentityMembership',
          name: 'eng-access-' + name + '-membership',
        }
        for name in names
      ],
      task: [
        {
          cloud: 'GCP_US_CENTRAL1_PROD',
          env: 'PROD',
          namespace: 'devtools',
        },
      ],
    },
  },
];

// ---

lib.flatten([
  // embedder example
  //   embeddingTombstones(['STAGING', 'PROD'], 'ethanol6-04-1-v3', 'dr-ethanol6-04-1-v3-30line-1024char'),

  // generation examples
  //   generationTombstones(['STAGING'], 'completion', 'ender-16b-meth0416-4-seth6-16-1'),
  //   generationTombstonesThirdParty(['PROD'], 'chat', 'claude-sonnet-3-5-16k-v5-chat', null, nonAiTutorProdNamespaces),

  // engineering access example
  //   engAccessMembershipTombstones(['mlm', 'dion']),

  // Specific appName
  //   tombstoneApp(['STAGING'], appName='completion-eldenv3-15b', priority=tombstonePriority, tasks=lookupTenantNamespaces(['pre-prod'])),
  //   tombstoneApp(['STAGING'], 'infer-pleasehold-7b-v1', tombstoneCentralPriority, centralTasks, cloud='GCP_US_CENTRAL1_PROD'),
  generationTombstones(['STAGING'], 'completion', 'qweldenv3-1-14b'),
  embeddingTombstones(['STAGING'], 'wolf-location-v17-doc', 'dr-wolf-location-v17-doc-2048char'),
  embeddingTombstones(['STAGING'], 'wolf-location-v17-query', 'dr-wolf-location-v17-doc-2048char'),
  monitoringLib.alertTombstone([
    'gpu-inference-high-utilization',
    'gpu-inference-utilization-spikes',
    'api-proxy-completion-error-rate-error-staging',
    'api-proxy-completion-error-rate-warning-staging',
    'api-proxy-completion-error-rate-error-prod',
    'api-proxy-completion-error-rate-warning-prod',
    'api-proxy-chat-stream-error-rate-error-staging',
    'api-proxy-chat-stream-error-rate-warning-staging',
    'api-proxy-chat-stream-error-rate-error-prod',
    'api-proxy-chat-stream-error-rate-warning-prod',
    'customer-ui-errors',
    'request-insight-full-export-pubsub-unacked',
    'auth-active-no-subscription-high',
    'auth-central-billing-event-processor-dead-letter',
    'request-insight-duplicate-emails-warning',
    'request-insight-multiple-orb-subscriptions-warning',
    'request-insight-user-subscription-inconsistencies-warning',
    'request-insight-orphaned-active-orb-subscriptions-warning',
    'auth-central-team-management-error-rate',
  ], -1),  // Alert tombstoning priority is -1 ("after everything else") to avoid losing alert coverage
  tombstoneApp(['PROD'], appName='alert-policy-fixup-job', priority=tombstonePriority, tasks=[{
    namespace: 'devtools',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_PROD',
  }, {
    namespace: 'devtools',
    env: 'PROD',
    cloud: 'GCP_US_CENTRAL1_GSC_PROD',
  }]),
])
