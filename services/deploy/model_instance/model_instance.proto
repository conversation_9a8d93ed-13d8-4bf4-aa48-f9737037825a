syntax = "proto2";
package augment.model_instance_config;

// generated models types
enum ModelType {
  // the model is used for completions
  // this is a misnomer. It should be called COMPLETION
  INFERENCE = 0;

  reserved 1;

  // the model is used for code edit/instruction
  EDIT = 2;

  // the model is used for chat
  CHAT = 3;

  // the model is used for next edit
  NEXT_EDIT = 4;

  // the model is used for autofix
  AUTOFIX = 5;
}

message Language {
  required string name = 1;
  required string vscode_name = 2;
  repeated string extensions = 3;
}

// this is misnamed. it should have been CompletionModelConfig
message InferenceModelConfig {
  required uint32 suggested_prefix_char_count = 5;

  required uint32 suggested_suffix_char_count = 6;

  required int64 model_priority = 7;

  // should be set for every INFERENCE model
  required string completion_endpoint = 8;

  optional string inference_endpoint = 9 [deprecated = true];

  reserved 12;

  repeated Language languages = 14;
}

message EditModelConfig {
  required uint32 suggested_prefix_char_count = 5;

  required uint32 suggested_suffix_char_count = 6;

  required int64 model_priority = 7;

  // should be set for every EDIT model
  required string edit_endpoint = 8;
}

message ChatModelConfig {
  required uint32 suggested_prefix_char_count = 5;

  required uint32 suggested_suffix_char_count = 6;

  required int64 model_priority = 7;

  // should be set for every CHAT model
  required string chat_endpoint = 8;
}

message NextEditGenModelConfig {
  required uint32 suggested_prefix_char_count = 5;

  required uint32 suggested_suffix_char_count = 6;

  required int64 model_priority = 7;

  // should be set for every NEXT_EDIT model
  required string next_edit_endpoint = 8;
}

message AutofixModelConfig {
  required uint32 suggested_prefix_char_count = 5;

  required uint32 suggested_suffix_char_count = 6;

  required int64 model_priority = 7;

  // should be set for every AUTOFIX model
  required string autofix_endpoint = 8;
}

// the model configuration controlls the mapping of model names in api proxy/model finder
// to the generation hosts (completion host, chat host, ...).
message ModelInstanceConfig {
  reserved 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 27, 29;

  // name of the model
  required string name = 22;

  // the type of the model.
  required ModelType model_type = 24;

  oneof model_config {
    // needs to be set if the model_type is INFERENCE
    InferenceModelConfig inference = 28;

    // needs to be set if the model_type is EDIT
    EditModelConfig edit = 30;

    // needs to be set if the model_type is CHAT
    ChatModelConfig chat = 31;

    // needs to be set if the model_type is NEXT_EDIT
    NextEditGenModelConfig next_edit = 32;

    // needs to be set if the model_type is AUTOFIX
    AutofixModelConfig autofix = 33;
  }

  // Optional - list of handlers defined inside a single deployment model
  repeated string handler_names = 34;
}
