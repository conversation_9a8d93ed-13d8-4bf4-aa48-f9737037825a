// jsonnet library for configuration
// shared between the different pods that rely on model instance and model
// configurations.
local languages = std.parseYaml(importstr 'base/languages/languages.yaml');
local AVG_CHAR_PER_TOKEN = 3;
local PROMPT_FRACTION = 0.5;
local PREFIX_FRACTION = 0.75;

// TODO: we should fix the name of completionService to be compitable with edit
function(
  modelConfig,
  generationService,
  modelPriority,
  overrideModelName=null,
)
  local hasInference = std.objectHas(modelConfig, 'inference');
  // Only the inference (completion, edit, chat, etc.) can use retrieval
  if modelConfig.model_type == 'INFERENCE' then
    {
      name: if overrideModelName != null then overrideModelName else modelConfig.name,
      model_type: modelConfig.model_type,
      inference: {
        // a rough estimate how many characters of prefix and suffix we might fit into the context assuming is able to fill the rest
        suggested_prefix_char_count: AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION,
        suggested_suffix_char_count: AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION,
        model_priority: modelPriority,
        completion_endpoint: if generationService != null && generationService != '' then '%s:50051' % generationService else null,
        languages: std.map(function(l) {
          name: languages[l].name,
          vscode_name: languages[l].vscode_name,
          extensions: languages[l].extensions,
        }, modelConfig.inference.languages,),
      },
      handler_names: if std.objectHas(modelConfig, 'handler_names') then modelConfig.handler_names else [],
    }
  else if modelConfig.model_type == 'EDIT' then
    {
      name: if overrideModelName != null then overrideModelName else modelConfig.name,
      model_type: modelConfig.model_type,
      edit: {
        // a rough estimate how many characters of prefix and suffix we might fit into the context assuming is able to fill the rest
        // TODO: we should make this configurable
        suggested_prefix_char_count: if hasInference then AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION else 0,
        suggested_suffix_char_count: if hasInference then AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION else 0,
        model_priority: modelPriority,
        edit_endpoint: if generationService != null && generationService != '' then '%s:50051' % generationService else null,
      },
      handler_names: if std.objectHas(modelConfig, 'handler_names') then modelConfig.handler_names else [],
    }
  else if modelConfig.model_type == 'CHAT' then
    {
      name: if overrideModelName != null then overrideModelName else modelConfig.name,
      model_type: modelConfig.model_type,
      chat: {
        // a rough estimate how many characters of prefix and suffix we might fit into the context assuming is able to fill the rest
        // TODO: we should make this configurable
        suggested_prefix_char_count: if hasInference then AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION else 0,
        suggested_suffix_char_count: if hasInference then AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION else 0,
        model_priority: modelPriority,
        chat_endpoint: if generationService != null && generationService != '' then '%s:50051' % generationService else null,
      },
      handler_names: if std.objectHas(modelConfig, 'handler_names') then modelConfig.handler_names else [],
    }
  else if modelConfig.model_type == 'NEXT_EDIT' then
    {
      name: if overrideModelName != null then overrideModelName else modelConfig.name,
      model_type: modelConfig.model_type,
      next_edit: {
        // a rough estimate how many characters of prefix and suffix we might fit into the context assuming is able to fill the rest
        // TODO: we should make this configurable
        suggested_prefix_char_count: AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION,
        suggested_suffix_char_count: AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION,
        model_priority: modelPriority,
        next_edit_endpoint: if generationService != null && generationService != '' then '%s:50051' % generationService else null,
      },
      handler_names: if std.objectHas(modelConfig, 'handler_names') then modelConfig.handler_names else [],
    }
  else if modelConfig.model_type == 'AUTOFIX' then
    {
      name: if overrideModelName != null then overrideModelName else modelConfig.name,
      model_type: modelConfig.model_type,
      autofix: {
        suggested_prefix_char_count: AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION,
        suggested_suffix_char_count: AVG_CHAR_PER_TOKEN * modelConfig.inference.max_context_length * PROMPT_FRACTION * PREFIX_FRACTION,
        model_priority: modelPriority,
        autofix_endpoint: if generationService != null && generationService != '' then '%s:50051' % generationService else null,
      },
      handler_names: if std.objectHas(modelConfig, 'handler_names') then modelConfig.handler_names else [],
    }
  else
    assert false : 'unknown model type: %s' % modelConfig.model_type;
    {}
