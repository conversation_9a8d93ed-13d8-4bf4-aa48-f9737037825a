// Configuration for Orb billing plans, price IDs, and plan logic
function(env='PROD')
  local allPlans = [
    {
      id: 'orb_community_plan',
      color: 'blue',
      sort_order: 1,
      features: {
        training_allowed: true,
        teams_allowed: false,
        max_seats: 1,
        add_credits_available: true,
        plan_type: 'community',
      },
    },
    {
      id: 'orb_trial_plan',
      color: 'gray',
      sort_order: 2,
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 5,
        add_credits_available: false,
        plan_type: 'trial',
      },
    },
    {
      id: 'orb_developer_plan',
      color: 'indigo',
      sort_order: 3,
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 100,
        add_credits_available: true,
        plan_type: 'paid',
      },
    },
    {
      id: 'orb_pro_plan',
      color: 'purple',
      sort_order: 4,
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 100,
        add_credits_available: true,
        plan_type: 'paid',
      },
    },
    {
      id: 'orb_max_plan',
      color: 'gold',
      sort_order: 5,
      features: {
        training_allowed: false,
        teams_allowed: true,
        max_seats: 100,
        add_credits_available: true,
        plan_type: 'paid',
      },
    },
  ];

  // Filter plans based on environment
  local filteredPlans =
    if env == 'DEV' || env == 'STAGING' then
      // Include all plans for dev and staging (including pro and max)
      allPlans
    else
      // Only include community, trial, and developer plans for prod
      [plan for plan in allPlans if plan.id == 'orb_community_plan' || plan.id == 'orb_trial_plan' || plan.id == 'orb_developer_plan'];

  {
    community_plan_id: 'orb_community_plan',
    trial_plan_id: 'orb_trial_plan',
    professional_plan_id: 'orb_developer_plan',
    pro_plan_id: 'orb_pro_plan',
    max_plan_id: 'orb_max_plan',
    // Item IDs stay constant across plans and as plans change
    seats_item_id: {
      DEV: 'fPTPFSjvP7fjF3PZ',
      STAGING: 'fPTPFSjvP7fjF3PZ',
      PROD: 'i6nDyZKNgFu4d4rd',
    },
    included_messages_item_id: {
      DEV: 'kcwmTLYJpnTb33Nq',
      STAGING: 'kcwmTLYJpnTb33Nq',
      PROD: 'DunFsa2DZzHpVq45',
    },
    pricing_unit: 'usermessages',
    cost_per_message: 0.1,  // Cost in USD per message sent. Can be moved to be plan-specific if necessary.

    // The version of the developer plan to use for existing Stripe users migrated to Orb.
    developer_plan_for_stripe_users: {
      DEV: {
        version_number: 8,
        seats_price_id: 'dBG5XVEops2rwFzR',
        included_messages_price_id: 'QwzWagYAVGL2ZjfH',
        messages_per_seat: 600.0,
      },
      STAGING: {
        version_number: 8,
        seats_price_id: 'dBG5XVEops2rwFzR',
        included_messages_price_id: 'QwzWagYAVGL2ZjfH',
        messages_per_seat: 600.0,
      },
      PROD: {
        version_number: 4,
        seats_price_id: 'bE9vSbszbyc4pB78',
        included_messages_price_id: 'Xvtjg8F6DmpuXk9j',
        messages_per_seat: 600.0,
      },
    },
    // The price of the original professional plan in Stripe, used only for existing Stripe users migrated to Orb.
    stripe_professional_plan_price_per_seat: 30.0,

    // Configs related to the logic of plans
    // ID refers to the plan's external_plan_id in Orb
    plans: filteredPlans,
    // These max purchase amounts are in USD
    min_addon_purchase: 10.0,
    max_addon_purchase: 100.0,
  }
