# Misuse Monitor

This directory contains the misuse-monitor service.

This service runs jobs to periodically detect usage patterns that indicate misuse of the system. Users are banned or suspended from the system appropriately.

## Jobs

### API Misuse

The API misuse job detects users who are making a large number of API requests in a short period of time. This is often an indication of a misbehaving bot or a user who has lost control of their API key.

### Free Trial Abuse

The free trial abuse job detects users who are abusing their free trial by using the same session ID across multiple user accounts. This can happen when users are sharing their API key with others, or when trying to create additional free trial accounts after trial has expired.

## Adding new jobs

To add a new job, implement the `Job` interface in `misuse_monitor.go`. Then, add a new entry to the `Jobs` array in `deploy.jsonnet` to configure the job's execution interval. Job execution intervals should be balanced between the cost of running the job and the cost of allowing misuse to continue unchecked.

## GRPC

The service exposes a GRPC API for querying job status and triggering jobs on demand. Use GRPC debug to introspect the API.
