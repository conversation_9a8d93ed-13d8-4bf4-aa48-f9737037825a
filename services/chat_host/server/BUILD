load("@python_pip//:requirements.bzl", "requirement")
load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("//tools/bzl:metadata.bzl", "metadata_test")

py_library(
    name = "handler",
    srcs = ["handler.py"],
    visibility = ["//base/prompt_format:__subpackages__"],
    deps = [
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format:common",
        "//services/chat_host:chat_proto_py_proto",
        "//services/lib/grpc/auth:service_auth",
        "//services/lib/request_context:request_context_py",
        "//services/lib/retrieval:retriever",
        requirement("grpcio"),
    ],
)

py_library(
    name = "chat_handler_metrics",
    srcs = ["chat_handler_metrics.py"],
    deps = [
        requirement("prometheus-client"),
    ],
)

py_library(
    name = "postprocess",
    srcs = ["postprocess.py"],
    deps = [
        "//base/prompt_format_postprocess",
        "//base/tokenizers",
        "//services/inference_host/client",
        "//services/inference_host/client:multiplex",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
    ],
)

py_library(
    name = "request_filter",
    srcs = ["request_filter.py"],
    deps = [
        "//base/feature_flags:feature_flags_py",
        "//base/logging:struct_logging",
        "//services/chat_host:chat_proto_py_proto",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight:request_insight_py_proto",
        "//services/request_insight/publisher:publisher_py",
        requirement("prometheus-client"),
        requirement("requests"),
        requirement("structlog"),
    ],
)

py_library(
    name = "turn_limit",
    srcs = ["turn_limit.py"],
    deps = [
        ":handler",
        "//base/feature_flags:feature_flags_py",
        "//services/chat_host:chat_proto_py_proto",
    ],
)

pytest_test(
    name = "request_filter_test",
    srcs = ["request_filter_test.py"],
    deps = [
        ":request_filter",
    ],
)

pytest_test(
    name = "turn_limit_test",
    srcs = ["turn_limit_test.py"],
    deps = [
        ":turn_limit",
    ],
)

pytest_test(
    name = "postprocess_test",
    srcs = ["postprocess_test.py"],
    deps = [
        ":postprocess",
    ],
)

DEPS = [
    ":handler",
    ":chat_handler",
    ":chat_third_party_handler",
    ":postprocess",
    ":request_filter",
    ":turn_limit",
    ":suggested_questions",
    "//base/python/opentelemetry_utils:traced_threadpool",
    "//base/feature_flags:feature_flags_py",
    "//base/tokenizers",
    "//base/prompt_format_completion",
    "//base/python/grpc:client_options",
    "//base/logging:struct_logging",
    "//base/python/signal_handler",
    "//base/tracing:tracing_py",
    "//services/chat_host:chat_proto_py_proto",
    "//services/inference_host/client",
    "//services/inference_host/client:multiplex",
    "//services/lib/retrieval:retriever_factory",
    "//services/lib/grpc/auth:service_auth",
    "//services/lib/grpc/auth:service_token_auth",
    "//services/lib/grpc/auth:service_auth_interceptor",
    "//services/lib/grpc/metrics:metrics",
    "//services/lib/grpc/tls_config:grpc_tls_config_py",
    "//services/token_exchange/client:client_py",
    "//services/working_set/client:client_py",
    requirement("dataclasses_json"),
    requirement("grpcio-reflection"),
    requirement("grpcio"),
    requirement("grpcio-health-checking"),
    requirement("opentelemetry-instrumentation-grpc"),
    requirement("protobuf"),
    requirement("prometheus-client"),
]

py_binary(
    name = "chat_server",
    srcs = [
        "chat_server.py",
    ],
    deps = DEPS,
)

pytest_test(
    name = "chat_server_test",
    size = "small",
    srcs = ["chat_server_test.py"],
    deps = [
        ":chat_server",
        requirement("pytest-grpc"),
        requirement("requests"),
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":chat_server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
    visibility = [
        "//services/deploy:__subpackages__",
    ],
)

py_library(
    name = "chat_handler",
    srcs = [
        "chat_handler.py",
    ],
    deps = [
        ":chat_handler_metrics",
        ":chat_request_insight_builder",
        ":handler",
        ":utils",
        "//base/logging:struct_logging",
        "//base/prompt_format_chat",
        "//base/prompt_format_retrieve",
        "//base/tokenizers",
        "//base/tokenizers:stream_utils",
        "//services/inference_host:infer_py_proto",
        "//services/inference_host/client",
        "//services/inference_host/client:multiplex",
        "//services/lib/request_context:request_context_py",
        requirement("opentelemetry-api"),
        "//services/content_manager/client",
    ],
)

pytest_test(
    name = "chat_handler_test",
    srcs = ["chat_handler_test.py"],
    deps = [
        ":chat_handler",
        ":postprocess",
        "//base/test_utils:synchronous_executor",
    ],
)

py_library(
    name = "chat_third_party_handler",
    srcs = [
        "chat_third_party_handler.py",
    ],
    # NOTE(arun): We use the third party handler for agent integration tests.
    # NOTE(zheren): We use the third party handler for codebase retrieval agent for LB.
    visibility = ["//services:__subpackages__"],
    deps = [
        ":chat_handler_metrics",
        ":chat_request_insight_builder",
        ":handler",
        ":postprocess",
        ":suggested_questions",
        ":utils",
        "//base/logging:struct_logging",
        "//base/prompt_format_chat",
        "//base/prompt_format_retrieve",
        "//base/stream_processor:basic_stream_processor",
        "//base/stream_processor:claude_stream_processor_v2",
        "//base/stream_processor:claude_stream_processor_v3",
        "//base/stream_processor:claude_with_citations_stream_processor",
        "//base/third_party_clients:clients",
        "//services/chat_host:chat_proto_util",
        "//services/chat_host/server/prompt_format:slackbot_prompt_formatter",
        "//services/chat_host/server/prompt_format:slackbot_prompt_formatter_v2",
        "//services/lib/balanced_third_party_clients",
        "//services/lib/request_context:request_context_py",
        "//services/third_party_arbiter:third_party_arbiter_py_proto",
        "//services/third_party_arbiter/client:client_py",
        requirement("opentelemetry-api"),
        "//services/content_manager/client",
    ],
)

pytest_test(
    name = "chat_third_party_handler_test",
    srcs = ["chat_third_party_handler_test.py"],
    deps = [
        ":chat_third_party_handler",
        "//base/test_utils:synchronous_executor",
    ],
)

pytest_test(
    name = "reprompting_client_test",
    srcs = ["reprompting_client_test.py"],
    deps = [
        ":chat_third_party_handler",
    ],
)

py_library(
    name = "chat_request_insight_builder",
    srcs = [
        "chat_request_insight_builder.py",
    ],
    deps = [
        ":handler",
        "//base/prompt_format_chat",
        "//base/third_party_clients:third_party_model_client",
        "//base/tokenizers",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
    ],
)

pytest_test(
    name = "chat_request_insight_builder_test",
    srcs = ["chat_request_insight_builder_test.py"],
    deps = [
        ":chat_request_insight_builder",
    ],
)

py_library(
    name = "utils",
    srcs = ["utils.py"],
    deps = [
        "//base/prompt_format_chat",
        "//services/chat_host:chat_proto_py_proto",
    ],
)

py_library(
    name = "suggested_questions",
    srcs = ["suggested_questions.py"],
    deps = [
        requirement("dataclasses_json"),
        "//base/prompt_format:common",
        "//base/third_party_clients:clients",
    ],
)

pytest_test(
    name = "suggested_questions_test",
    size = "small",
    srcs = ["suggested_questions_test.py"],
    deps = [
        ":chat_server",
        requirement("pytest-grpc"),
    ],
)

# This is shared by chat, edit, autofix; move out
jsonnet_library(
    name = "anthropic-lib",
    srcs = ["anthropic-lib.jsonnet"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:lib",
    ],
)

# This is shared by chat, edit, autofix; move out
jsonnet_library(
    name = "openai-lib",
    srcs = ["openai-lib.jsonnet"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:lib",
    ],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        ":anthropic-lib",
        ":openai-lib",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":monitoring_kubecfg",
    ],
)
