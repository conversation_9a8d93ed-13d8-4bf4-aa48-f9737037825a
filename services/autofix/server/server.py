"""Server for the Autofix service."""

import argparse
import logging
import os
import pathlib
import threading
import time
import typing
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass

import grpc
import opentelemetry
import opentelemetry.instrumentation.grpc
import prometheus_client
import structlog
from dataclasses_json import dataclass_json
from grpc_health.v1 import health, health_pb2_grpc
from grpc_reflection.v1alpha import reflection

import base.feature_flags
import base.tracing
from base.python.opentelemetry_utils.traced_threadpool import TracedThreadPoolExecutor
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.third_party_model_client import ThirdPartyModelClient
import services.lib.grpc.tls_config.tls_config as tls_config
from base.logging.struct_logging import setup_struct_logging
from base.python.grpc import client_options
from base.python.signal_handler.signal_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services.autofix import autofix_pb2, autofix_pb2_grpc
from services.autofix.server.handler import AutofixHandler
from services.autofix.server.autofix_metrics import (
    _autofix_find_missing_blob_name_count_summary,
    _autofix_find_missing_nonindexed_blob_name_count_summary,
    _autofix_find_missing_latency,
)
from services.lib.grpc.auth.service_auth_interceptor import (
    ServiceAuthInterceptor,
    get_auth_info_from_grpc_context,
)
from services.lib.grpc.auth.service_token_auth import (
    GrpcPublicKeySource,
    ServiceTokenAuth,
)
from services.lib.grpc.metrics.metrics import MetricsServerInterceptor
from services.lib.request_context.request_context import RequestContext
from services.token_exchange.client import client as token_exchange_client
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.edit_host.client import EditClient
from services.lib.retrieval import retriever_factory
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)

log = structlog.get_logger()
tracer = base.tracing.setup_opentelemetry()


@dataclass_json
@dataclass(frozen=True)
class ThirdPartyModelConfig:
    """Config for each chat model."""

    gcp_project_id: str

    gcp_region: str

    anthropic_api_key_path: str

    client_type: str
    """ Type of third party client, e.g. 'vertexai' """

    model_name: str

    temperature: float

    max_output_tokens: int


@dataclass_json
@dataclass
class AuthConfig:
    """Configuration for the token authentication."""

    # the endpoint for the token exchange service
    token_exchange_endpoint: str


@dataclass_json
@dataclass
class Config:
    """Configuration for the example server."""

    # name of the model hosted by this server
    model_name: str

    # the port the grpc server will listen on
    port: int

    # the path to the feature flag sdk key
    feature_flags_sdk_key_path: typing.Optional[str]

    # the endpoint for the dynamic feature flags service or None if not used
    dynamic_feature_flags_endpoint: typing.Optional[str]

    # the configuration for the token authentication
    auth_config: AuthConfig

    # Config for the chat model used for root-cause and fix plan
    # No need to support tool use
    chat_model_config: ThirdPartyModelConfig

    # Config for the chat model used for command checking
    # Must support tool use (i.e. be an Anthropic model)
    chat_lite_model_config: ThirdPartyModelConfig

    # Config for the edit service
    edit_endpoint: str
    edit_model_name: str

    # Config for retriever(s)
    context_retrieval: retriever_factory.RetrievalConfig
    location_retrieval: retriever_factory.RetrievalConfig

    # Config for the content manager client
    content_manager_endpoint: str

    # TLS configuration for the central clients
    central_client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the client to talk to GRPC services in the same namespace
    client_mtls: typing.Optional[tls_config.ClientConfig] = None

    # TLS configuration for the server
    server_mtls: typing.Optional[tls_config.ServerConfig] = None

    # Grace period for the server to shutdown
    shutdown_grace_period_s: int = 25

    @classmethod
    def load_config(cls, config_file: pathlib.Path):
        """Loads the configuration from a file."""
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )


class AutofixServicer(autofix_pb2_grpc.AutofixServicer):
    """AutofixServicer RPC server."""

    def __init__(
        self,
        config: Config,
        handler: AutofixHandler,
    ):
        self.config = config
        self.handler = handler

    def Check(
        self,
        request: autofix_pb2.CheckRequest,
        context: grpc.ServicerContext,
    ) -> autofix_pb2.CheckResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.check_command(request, request_context, auth_info)

    def Plan(
        self,
        request: autofix_pb2.PlanRequest,
        context: grpc.ServicerContext,
    ) -> autofix_pb2.PlanResponse:
        auth_info = get_auth_info_from_grpc_context(context)
        request_context = RequestContext.from_grpc_context(context)
        with request_context.with_context_logging(), auth_info.with_context_logging():
            return self.handler.plan(request, request_context, auth_info)

    def FindMissing(
        self,
        request: autofix_pb2.FindMissingRequest,
        context: grpc.ServicerContext,
    ) -> autofix_pb2.FindMissingResponse:
        request_start_time = time.time()
        status_code = grpc.StatusCode.UNKNOWN
        request_context = RequestContext.from_grpc_context(context)
        auth_info = get_auth_info_from_grpc_context(context)
        try:
            request_context.bind_context_logging()
            auth_info.bind_context_logging()

            _autofix_find_missing_blob_name_count_summary.labels(
                self.config.model_name,
                request_context.request_source,
                auth_info.tenant_name,
            ).observe(len(request.blob_names))

            log.info(
                "find_missing content: model_name=%s, blob_count=%d",
                self.config.model_name,
                len(request.blob_names),
            )

            # We create an exclusive thread pool per request to avoid deadlocking.
            with TracedThreadPoolExecutor(
                max_workers=4,
                thread_name_prefix=f"find-missing-{request_context.request_id[-8:]}-",
            ) as executor:
                response = self.handler.find_missing(
                    request=request,
                    request_context=request_context,
                    executor=executor,
                )

            _autofix_find_missing_nonindexed_blob_name_count_summary.labels(
                self.config.model_name,
                request_context.request_source,
                auth_info.tenant_name,
            ).observe(len(response.nonindexed_blob_names))

            status_code = grpc.StatusCode.OK
            return response
        except grpc.RpcError as ex:
            status_code = ex.code()  # pylint: disable=no-member # type: ignore
            if status_code == grpc.StatusCode.CANCELLED:
                log.error("FindMissing cancelled")
            else:
                log.error("FindMissing failed: %s", ex)
            log.exception(ex)
            context.abort(
                code=ex.code(),  # pylint: disable=no-member # type: ignore
                details=ex.details(),  # pylint: disable=no-member # type: ignore
            )
            raise  # Should not get here, context.abort should have raised
        except Exception as ex:  # pylint: disable=broad-exception-caught
            log.error("FindMissing failed: %s", ex)
            log.exception(ex)
            raise
        finally:
            latency = time.time() - request_start_time
            _autofix_find_missing_latency.labels(
                self.config.model_name,
                str(status_code),
                request_context.request_source,
                auth_info.tenant_name,
            ).observe(latency)


# Code duplication of the same in chat host; not sure these ever need to differ,
# so move to base/third_party_clients
def _make_chat_client(config: ThirdPartyModelConfig) -> ThirdPartyModelClient:
    if config.client_type == "anthropic_vertexai":
        client = AnthropicVertexAiClient(
            project_id=config.gcp_project_id,
            region=config.gcp_region,
            model_name=config.model_name,
            temperature=config.temperature,
            max_output_tokens=config.max_output_tokens,
        )
    elif config.client_type == "anthropic_direct":
        anthropic_api_key = (
            pathlib.Path(config.anthropic_api_key_path)
            .read_text(encoding="utf-8")
            .strip()
        )
        client = AnthropicDirectClient(
            api_key=anthropic_api_key,
            model_name=config.model_name,
            temperature=config.temperature,
            max_output_tokens=config.max_output_tokens,
        )
    else:
        raise ValueError(f"Unknown model type: {config.client_type}")
    return client


def _make_content_manager_client(config: Config) -> ContentManagerClient:
    """Returns a content manager client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.content_manager_endpoint
        )
    )
    return ContentManagerClient.create_for_endpoint(
        config.content_manager_endpoint,
        credentials=tls_config.get_client_tls_creds(config.client_mtls),
        options=options,
    )


def _make_edit_client(config: Config) -> EditClient:
    """Returns an edit client."""
    options = client_options.get_grpc_client_options(
        client_options.GrpcClientOptions(
            load_balancing="headless" in config.edit_endpoint
        )
    )
    return EditClient(
        config.edit_endpoint,
        tls_config.get_client_tls_creds(config.client_mtls),
        options,
    )


def _make_handler(
    config: Config, ri_publisher: RequestInsightPublisher
) -> AutofixHandler:
    content_manager_client = _make_content_manager_client(config)
    context_retriever = retriever_factory.create_retriever(
        config.context_retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        # TODO: revisit when adding request insights
        ri_builder=None,
        search_timeout_ms=5000,
    )
    location_retriever = retriever_factory.create_retriever(
        config.location_retrieval,
        content_manager_client,
        tls_config.get_client_tls_creds(config.client_mtls),
        # TODO: revisit when adding request insights
        ri_builder=None,
        search_timeout_ms=5000,
    )
    edit_client = _make_edit_client(config)
    return AutofixHandler(
        _make_chat_client(config.chat_lite_model_config),
        _make_chat_client(config.chat_model_config),
        edit_client,
        config.edit_model_name,
        content_manager_client,
        context_retriever,
        location_retriever,
        ri_publisher,
    )


def _serve(
    config: Config,
    ri_publisher: RequestInsightPublisher,
    shutdown_event: threading.Event,
):
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)
    custom_endpoint = None
    if config.dynamic_feature_flags_endpoint is not None:
        custom_endpoint = config.dynamic_feature_flags_endpoint

    context = base.feature_flags.Context.setup(path, custom_endpoint)
    base.feature_flags.set_global_context(context)

    namespace = os.environ["POD_NAMESPACE"]
    token_client = token_exchange_client.GrpcTokenExchangeClient.create(
        config.auth_config.token_exchange_endpoint,
        namespace,
        tls_config.get_client_tls_creds(config.central_client_mtls),
    )
    service_auth = ServiceTokenAuth(
        GrpcPublicKeySource(token_client),
        required_scopes=["CONTENT_R"],
    )
    auth_interceptor = ServiceAuthInterceptor(service_auth)
    server = grpc.server(
        ThreadPoolExecutor(max_workers=10),
        interceptors=[
            opentelemetry.instrumentation.grpc.server_interceptor(),
            MetricsServerInterceptor(),
            auth_interceptor,
        ],
    )

    # Reply to health check RPCs
    health_pb2_grpc.add_HealthServicer_to_server(health.HealthServicer(), server)

    autofix_pb2_grpc.add_AutofixServicer_to_server(
        AutofixServicer(config, _make_handler(config, ri_publisher)),
        server,
    )
    service_names = (
        autofix_pb2.DESCRIPTOR.services_by_name["Autofix"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(service_names, server)

    server_credentials = tls_config.get_server_tls_creds(config.server_mtls)
    if server_credentials:
        actual_port = server.add_secure_port(f"[::]:{config.port}", server_credentials)
    else:
        actual_port = server.add_insecure_port(f"[::]:{config.port}")
    server.start()
    logging.info("Listening on %s", actual_port)
    shutdown_event.wait()
    logging.info("Shutting down server")
    server.stop(grace=config.shutdown_grace_period_s).wait()
    logging.info("Server shutdown complete")


def main():
    # Set up the signal handler
    # This will catch SIGTERM and SIGINT and exit gracefully
    standard_handler = GracefulSignalHandler()

    # Set up the logging
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    RequestInsightPublisher.add_publisher_arguments(parser)
    args = parser.parse_args()
    logging.info("Args %s", args)

    config = Config.load_config(args.config_file)
    logging.info("Config %s", config)

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()
    ri_publisher = RequestInsightPublisher.create_from_args(args)

    prometheus_client.start_http_server(9090)

    _serve(config, ri_publisher, standard_handler.get_shutdown_event())


if __name__ == "__main__":
    main()
