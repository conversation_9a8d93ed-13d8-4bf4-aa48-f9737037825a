from prometheus_client import Histogram

INF = float("inf")
# Latency Buckets from 1ms to ~30s
latency_buckets = [(1.2**i - 1) / 100.0 for i in range(45)] + [INF]
# Blob name Buckets up to 2^15 = 32,768
_blob_name_buckets = [0] + [2**i for i in range(16)]

_autofix_find_missing_blob_name_count_summary = Histogram(
    "au_autofix_host_find_missing_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=_blob_name_buckets,
    documentation="Number of blob names in a find-missing request",
)
_autofix_find_missing_nonindexed_blob_name_count_summary = Histogram(
    "au_autofix_host_find_missing_nonindexed_blob_name_count",
    labelnames=["model", "request_source", "tenant_name"],
    buckets=_blob_name_buckets,
    documentation="Number of nonindexed blob names in a find-missing request",
)
_autofix_find_missing_latency = Histogram(
    "au_autofix_host_find_missing_latency_seconds",
    labelnames=["model", "status_code", "request_source", "tenant_name"],
    documentation="Latency of a find-missing request (in the autofix host)",
    buckets=latency_buckets,
)
