import itertools
import pytest
from unittest.mock import Mock, patch

from services.autofix.server.handler import <PERSON>fixHand<PERSON>
from base.prompt_format_chat.lib.token_counter import RoughTokenCounter
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
)
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolUseResponse,
)
from services.lib.retrieval.retriever import (
    Retriever,
    RetrievalResult,
)
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.edit_host.client import EditClient
from services.request_insight.publisher.request_insight_publisher import (
    RequestInsightPublisher,
)
from services.lib.request_context.request_context import RequestContext
from services.lib.grpc.auth.service_auth import AuthInfo
from services.autofix.autofix_pb2 import CheckRequest, Command
from services.autofix import autofix_pb2


def mock_client():
    client = Mock(spec=ThirdPartyModelClient)
    client.token_counter.return_value = RoughTokenCounter()
    return client


def mock_edit_client():
    client = Mock(spec=EditClient)
    return client


def mock_content_manager():
    client = Mock(spec=ContentManagerClient)
    client.batch_download_all.return_value = itertools.repeat(None)
    return client


def mock_retriever():
    ret = Mock(spec=Retriever)
    ret.retrieve.return_value = RetrievalResult(
        retrieved_chunks=[],
        missing_blob_names=[],
        checkpoint_not_found=False,
    )
    return ret


def handler_under_test(
    chat_client_lite: ThirdPartyModelClient | None = None,
    chat_client: ThirdPartyModelClient | None = None,
    edit_client: EditClient | None = None,
    content_manager_client: ContentManagerClient | None = None,
    context_retriever: Retriever[ChatRetrieverPromptInput] | None = None,
    location_retriever: Retriever[LocalizationNextEditPromptInput] | None = None,
    request_insight_publisher: RequestInsightPublisher | None = None,
):
    """Instantiate a handler with mocked dependencies.

    Tests can replace any number of mocks with a real implementation or
    mock with different behavior as needed.
    """
    return AutofixHandler(
        chat_client_lite or mock_client(),
        chat_client or mock_client(),
        edit_client or mock_edit_client(),
        "edit_model",
        content_manager_client or mock_content_manager(),
        context_retriever or mock_retriever(),
        location_retriever or mock_retriever(),
        request_insight_publisher or Mock(spec=RequestInsightPublisher),
    )


def test_check_command_not_code():
    request = CheckRequest(
        command=Command(
            input="echo hello",
            output="hello",
            exit_code=0,
        )
    )
    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="1234567890",
        tenant_name="test-tenant",
        shard_namespace="test-shard",
        cloud="test-cloud",
    )
    client = mock_client()
    autofix_handler = handler_under_test(chat_client_lite=client)

    client.generate_response_stream.return_value = [
        ThirdPartyModelResponse("some prose"),
        ThirdPartyModelResponse(
            "",
            tool_use=ToolUseResponse(
                "check_command_code_related", {"result": False, "desc": "some desc"}, ""
            ),
        ),
    ]

    result = autofix_handler.check_command(request, request_context, auth_info)

    assert client.generate_response_stream.call_count == 1
    call_kwargs = client.generate_response_stream.call_args.kwargs
    assert (
        "echo hello" in call_kwargs["cur_message"]
    )  # see prompt format test for more...
    assert call_kwargs["tools"] == ["check_command_code_related"]
    assert not result.is_code_related
    assert not result.contains_failure


@pytest.mark.parametrize("contains_error", [True, False])
def test_check_command_contains_error(contains_error):
    request = CheckRequest(
        command=Command(
            input="pytest foo.py",
            output="============================== 2 passed in 1.49s ===============================",
            exit_code=0,
        )
    )
    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="1234567890",
        tenant_name="test-tenant",
        shard_namespace="test-shard",
        cloud="test-cloud",
    )

    client = mock_client()
    autofix_handler = handler_under_test(chat_client_lite=client)

    # Return values for two different calls
    client.generate_response_stream.side_effect = [
        [
            ThirdPartyModelResponse("some prose"),
            ThirdPartyModelResponse(
                "",
                tool_use=ToolUseResponse(
                    "check_command_code_related",
                    {"result": True, "desc": "testing python code"},
                    "",
                ),
            ),
        ],
        [
            ThirdPartyModelResponse("more prose"),
            ThirdPartyModelResponse(
                "",
                tool_use=ToolUseResponse(
                    "command_output_contain_errors",
                    {"result": contains_error, "desc": "explanatio"},
                    "",
                ),
            ),
        ],
    ]

    result = autofix_handler.check_command(request, request_context, auth_info)

    assert client.generate_response_stream.call_count == 2

    call_kwargs = client.generate_response_stream.call_args_list[0].kwargs
    assert "pytest foo.py" in call_kwargs["cur_message"]
    assert call_kwargs["tools"] == ["check_command_code_related"]
    call_kwargs = client.generate_response_stream.call_args_list[1].kwargs
    assert "pytest foo.py" in call_kwargs["cur_message"]
    assert call_kwargs["tools"] == ["command_output_contain_errors"]
    assert result.is_code_related
    assert result.contains_failure == contains_error


def test_check_command_tool_use_failure():
    request = CheckRequest(
        command=Command(
            input="pytest foo.py",
            output="============================== 2 passed in 1.49s ===============================",
            exit_code=0,
        )
    )
    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="1234567890",
        tenant_name="test-tenant",
        shard_namespace="test-shard",
        cloud="test-cloud",
    )

    client = mock_client()
    autofix_handler = handler_under_test(chat_client_lite=client)

    text = ThirdPartyModelResponse("some prose")
    check_correct_tool = ThirdPartyModelResponse(
        "",
        tool_use=ToolUseResponse(
            "check_command_code_related",
            {"result": True, "desc": "testing python code"},
            "",
        ),
    )
    # missing result
    check_incorrect_tool = ThirdPartyModelResponse(
        "",
        tool_use=ToolUseResponse(
            "check_command_code_related", {"desc": "testing python code"}, ""
        ),
    )
    # missing desc
    contain_incorrect_tool = ThirdPartyModelResponse(
        "",
        tool_use=ToolUseResponse("command_output_contain_errors", {"result": True}, ""),
    )

    cases = []
    results = []

    # Model doesn't use any tool
    cases.append([[text]])
    results.append("No tool use in model response")
    cases.append([[text, check_correct_tool], [text]])
    results.append("No tool use in model response")

    # Tool use doesn't match our expectation
    cases.append([[text, check_incorrect_tool]])
    results.append("Tool use check_command_code_related unexpected format")

    cases.append([[text, check_correct_tool], [text, contain_incorrect_tool]])
    results.append("Tool use command_output_contain_errors unexpected format")

    for model_responses, expected_error in zip(cases, results):
        client.generate_response_stream.side_effect = model_responses
        with pytest.raises(RuntimeError) as err:
            _ = autofix_handler.check_command(request, request_context, auth_info)
        assert expected_error in str(err.value)


def test_convert_steering():
    """Test behavior of converting TextReplacement to Modified[File] for user steering
    in the Plan RPC

    Tests an internal method of the handler to avoid having to pass the required data
    through a mock content manager/mock out other unrelated steps of the RPC
    """
    mock_content = mock_content_manager()
    handler = handler_under_test(content_manager_client=mock_content)

    request_context = RequestContext.create()
    auth_info = AuthInfo(
        tenant_id="1234567890",
        tenant_name="test-tenant",
        shard_namespace="test-shard",
        cloud="test-cloud",
    )

    blob_content = {}
    blob_content["unknwown1"] = None
    blob_content["unknwown2"] = None
    blob_content["blob1"] = "blob1\n" * 10
    blob_content["blob2"] = "".join(["line{}\n".format(i) for i in range(10)])

    # Replacements to blob names that the model host can't download the contents
    # for will be converted without the diff, even if the diff is requested.
    # In this request, the third replacement can't be resolved as the blob is unknown
    steering_request = autofix_pb2.UserSteeringExchange(
        request_message="user request",
        summary="change summary",
        replacements=[
            autofix_pb2.TextReplacement(
                description="replacement1",
                path="path1",
                text="text1\n",
                start_line=1,
                end_line=9,
                old_text="".join(blob_content["blob1"].splitlines(keepends=True)[0:8]),
                sequence_id=0,
                old_blob_name="blob1",
            ),
            autofix_pb2.TextReplacement(
                description="replacement2",
                path="path2",
                text="text2\n",
                start_line=4,
                end_line=5,
                old_text="line3\n",
                sequence_id=1,
                old_blob_name="blob2",
            ),
            autofix_pb2.TextReplacement(
                description="replacement3",
                path="path3",
                text="text3\n",
                start_line=2,
                end_line=4,
                old_text="old_text1",
                sequence_id=2,
                old_blob_name="unknwown1",
            ),
        ],
    )
    result = handler._convert_steering_exchange(
        steering_request, blob_content, request_context, auth_info, include_diff=True
    )
    result_without_diff = handler._convert_steering_exchange(
        steering_request, blob_content, request_context, auth_info, include_diff=False
    )
    assert result == result_without_diff
    assert result.message == steering_request.request_message
    assert result.relevant_fix_plan.fix_desc == steering_request.summary
    assert len(result.relevant_fix_plan.changes) == 3
    assert result.relevant_fix_plan.changes[1].path == "path2"
    assert result.relevant_fix_plan.changes[1].change_desc == "replacement2"
    assert result.relevant_fix_plan.changes[1].code_block_start_line == 4
    assert result.relevant_fix_plan.changes[1].code_block == "text2\n"
    # Empty "diff"
    assert len(result.fix) == 0

    # Pop the replacement with unknown blob name
    steering_request.replacements.pop()
    result = handler._convert_steering_exchange(
        steering_request, blob_content, request_context, auth_info, include_diff=True
    )
    assert result.message == steering_request.request_message
    assert result.relevant_fix_plan.fix_desc == steering_request.summary
    assert len(result.relevant_fix_plan.changes) == 2
    assert result.relevant_fix_plan.changes[0].path == "path1"
    assert result.relevant_fix_plan.changes[0].change_desc == "replacement1"
    assert result.relevant_fix_plan.changes[0].code_block_start_line == 1
    assert result.relevant_fix_plan.changes[0].code_block == "text1\n"
    # On this call, blob content was known, so we can include the diff
    assert len(result.fix) == 2
    assert result.fix[0].before.path == result.fix[0].after.path == "path1"
    assert result.fix[0].before.contents == "blob1\n" * 10
    assert result.fix[0].after.contents == "text1\n" + "blob1\n" * 2
    assert result.fix[1].before.path == result.fix[1].after.path == "path2"
    assert result.fix[1].before.contents == "".join(
        ["line{}\n".format(i) for i in range(10)]
    )
    assert result.fix[1].after.contents == "".join(
        ["line{}\n".format(i) for i in range(3)]
    ) + "text2\n" + "".join(["line{}\n".format(i) for i in range(4, 10)])

    # As every blob name in the requests was present in the cache, no calls to download should have occurred
    assert mock_content.batch_download_all.call_count == 0

    # Sanity check that download request is made when cache is empty; if we don't include this, the assertion above
    # could be vacuously true; if for instance the method name was wrong
    handler._convert_steering_exchange(
        steering_request, {}, request_context, auth_info, include_diff=True
    )
    assert mock_content.batch_download_all.call_count == 1


def test_apply_replacement_to_contents():
    """Test some TextReplacement cases not included in test_convert_steering"""
    handler = handler_under_test()
    orig = "line1\nline2\nline3\n"

    # Delete line
    mod = handler._apply_replacement_to_contents(
        autofix_pb2.TextReplacement(
            text="",
            start_line=1,
            end_line=2,
        ),
        orig,
    )
    assert mod.before.contents == orig
    assert mod.after.contents == "line2\nline3\n"

    # Can insert between lines by setting start_line == end_line
    mod = handler._apply_replacement_to_contents(
        autofix_pb2.TextReplacement(
            text="add1\nadd2\nadd3\n",
            start_line=2,
            end_line=2,
        ),
        orig,
    )
    assert mod.before.contents == orig
    assert mod.after.contents == "line1\nadd1\nadd2\nadd3\nline2\nline3\n"

    # Or by repeating the contents of an existing line
    mod = handler._apply_replacement_to_contents(
        autofix_pb2.TextReplacement(
            text="add1\nadd2\nadd3\nline2\n",
            start_line=2,
            end_line=3,
        ),
        orig,
    )
    assert mod.before.contents == orig
    assert mod.after.contents == "line1\nadd1\nadd2\nadd3\nline2\nline3\n"

    # Line range starts within the file, then extends beyond the end of the file
    mod = handler._apply_replacement_to_contents(
        autofix_pb2.TextReplacement(
            text="add1\nadd2\nadd3\n",
            start_line=3,
            end_line=100,
        ),
        orig,
    )
    assert mod.before.contents == orig
    assert mod.after.contents == "line1\nline2\nadd1\nadd2\nadd3\n"

    # Replacing lines off the end of the file results in concatenation, regardless
    # of the distance between replacement.start_line and the number of lines in the
    # file.
    # (we don't expect to use this behavior, but demonstrate what the behavior is)
    mod = handler._apply_replacement_to_contents(
        autofix_pb2.TextReplacement(
            text="add1\nadd2\nadd3\n",
            start_line=100,
            end_line=103,
        ),
        orig,
    )
    assert mod.before.contents == orig
    assert mod.after.contents == orig + "add1\nadd2\nadd3\n"
