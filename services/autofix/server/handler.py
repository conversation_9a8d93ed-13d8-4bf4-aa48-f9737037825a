import concurrent.futures
import re
import opentelemetry.trace
import structlog
from collections import defaultdict
from textwrap import dedent
from typing import Iterable, Callable, TypeVar, Sequence, Tuple, Mapping, MutableMapping

from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext
from services.request_insight.publisher import request_insight_publisher
from services.request_insight import request_insight_pb2

from services.autofix import autofix_pb2
from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from services.lib.retrieval.retriever import (
    RetrievalInput,
    RetrievalResult,
    RetrievalChunk,
    Retriever,
)
from services.lib.retrieval.multi_retriever import (
    multi_retriever_find_missing,
)
from services.content_manager.client.content_manager_client import (
    ContentManagerClient,
    ContentKey,
)
from services.edit_host import edit_pb2
from services.edit_host.client import EditClient
from base.third_party_clients.third_party_model_client import (
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
)
from base.blob_names.python.blob_names import Blobs
from base.diff_utils.changes import (
    Changed,
    Modified,
    Added,
    Deleted,
)
from base.diff_utils.diff_utils import File
from base.ranges.range_types import CharRange
from base.ranges.line_map import LineMap
from base.prompt_format.common import (
    PromptChunk,
    get_request_message_as_text,
    get_response_message_as_text,
)
from base.prompt_format_autofix.common import (
    PromptChunkWithLines,
    format_command_output,
    format_chunks,
    AutofixFileFix,
    AutofixFixPlan,
)
from base.prompt_format_autofix.check_command_prompt_formatter import (
    AutofixCheckCommandInput,
    AutofixCheckCommandOutput,
    AutofixCheckCommandTokenApportionment,
    AutofixCheckCommandV1PromptFormatter,
)
from base.prompt_format_autofix.contains_errors_prompt_formatter import (
    AutofixContainErrorsInput,
    AutofixContainErrorsOutput,
    AutofixContainErrorsTokenApportionment,
    AutofixContainErrorsV1PromptFormatter,
)
from base.prompt_format_autofix.create_fix_plan_prompt_formatter import (
    AutofixCreateFixPlanInput,
    AutofixCreateFixPlanOutput,
    AutofixCreateFixPlanTokenApportionment,
    AutofixCreateFixPlanV1PromptFormatter,
    AutofixSteeringMessage,
    parse_fix_plan_xml,
)
from base.prompt_format_autofix.generate_chatanol_query import (
    GenerateBasicChatanolQueryPromptFormatter,
    GenerateSteeringChatanolQueryPromptFormatter,
    GenerateChatanolQueryTokenApportionment,
    GenerateChatanolQueryInput,
)
from base.prompt_format_next_edit.location_prompt_formatter import (
    LocalizationNextEditPromptInput,
)

tracer = opentelemetry.trace.get_tracer(__name__)
log = structlog.get_logger()


T = TypeVar("T")

# Mapping from blob name to content for a single request
# None means the blob was not found
BlobContentMap = MutableMapping[str, str | None]
PathContentMap = MutableMapping[str, str | None]


def _sanitize_output(output: str) -> str:
    # Remove ANSI escape sequences as they can cause issues with the LLM
    ansi_escape_regex = r"\x1B[@-_][0-?]*[ -/]*[@-~]"
    output = re.sub(ansi_escape_regex, "", output)
    return output


# TODO: push this into ThirdPartyModelClient interface: make request with
# required tool, expect response to unmarshal into specific dataclass?
# Not sure we're going to stick with tool (ab)use long-term to constrain output
# format.
def _parse_tool_use(
    responses: Iterable[ThirdPartyModelResponse], unmarshal: Callable[[object], T]
) -> T:
    for resp in responses:
        if resp.tool_use is not None:
            try:
                return unmarshal(resp.tool_use.input)
            except Exception:
                raise RuntimeError(
                    f"Tool use {resp.tool_use.tool_name} unexpected format: {resp.tool_use.input.keys()}"
                )
    raise RuntimeError("No tool use in model response")


class AutofixHandler:
    def __init__(
        self,
        chat_client_lite: ThirdPartyModelClient,
        chat_client: ThirdPartyModelClient,
        edit_client: EditClient,
        edit_model: str,
        content_manager_client: ContentManagerClient,
        context_retriever: Retriever[ChatRetrieverPromptInput],
        location_retriever: Retriever[LocalizationNextEditPromptInput],
        request_insight_publisher: request_insight_publisher.RequestInsightPublisher,
    ):
        self._chat_client_lite = chat_client_lite
        self._chat_client = chat_client
        self._edit_client = edit_client
        self._edit_model = edit_model
        self._content_manager_client = content_manager_client
        self._context_retriever = context_retriever
        self._location_retriever = location_retriever

        token_counter = self._chat_client_lite.token_counter()
        self._check_formatter = AutofixCheckCommandV1PromptFormatter(
            AutofixCheckCommandTokenApportionment(),
            token_counter,
        )
        self._error_formatter = AutofixContainErrorsV1PromptFormatter(
            AutofixContainErrorsTokenApportionment(),
            token_counter,
        )

        token_counter = self._chat_client.token_counter()
        self._plan_formatter = AutofixCreateFixPlanV1PromptFormatter(
            AutofixCreateFixPlanTokenApportionment(),
            token_counter,
        )
        self._general_context_formatter = GenerateBasicChatanolQueryPromptFormatter(
            GenerateChatanolQueryTokenApportionment(),
            token_counter,
        )
        self._steering_context_formatter = GenerateSteeringChatanolQueryPromptFormatter(
            GenerateChatanolQueryTokenApportionment(),
            token_counter,
        )

        self._request_insight_publisher = request_insight_publisher

    def find_missing(
        self,
        request: autofix_pb2.FindMissingRequest,
        request_context: RequestContext,
        executor: concurrent.futures.Executor,
    ) -> autofix_pb2.FindMissingResponse:
        # We don't currently pass any blobs to the edit host for additional retrieval,
        # so just probe the location retriever
        combined_result = multi_retriever_find_missing(
            [self._context_retriever, self._location_retriever],
            request.blob_names,
            request_context,
            executor,
        )
        return autofix_pb2.FindMissingResponse(
            nonindexed_blob_names=combined_result.missing_blob_names,
        )

    def check_command(
        self,
        request: autofix_pb2.CheckRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> autofix_pb2.CheckResponse:
        ri_check_request_event = request_insight_publisher.new_event()
        ri_check_request_event.autofix_check_request.MergeFrom(
            request_insight_pb2.RIAutofixCheckRequest(request=request)
        )
        self._request_insight_publisher.publish_request_insight(
            self._request_insight_publisher.update_request_info_request(
                request_context.request_id, [ri_check_request_event], auth_info
            )
        )

        command = request.command
        output = _sanitize_output(command.output)
        with tracer.start_as_current_span("check_command_format"):
            prompt = self._check_formatter.format_prompt(
                AutofixCheckCommandInput(command=command.input, command_output=output)
            )

        with tracer.start_as_current_span("check_command_generate"):
            tools: list[str] = list(prompt.tools or [])
            responses = self._chat_client_lite.generate_response_stream(
                model_caller="autofix-check-command",
                messages=[
                    (
                        get_request_message_as_text(item.request_message),
                        get_response_message_as_text(item.response_text),
                    )
                    for item in prompt.chat_history
                ],
                system_prompt=prompt.system_prompt,
                cur_message=prompt.message,
                tools=tools,
            )
            check_output = _parse_tool_use(
                responses,
                AutofixCheckCommandOutput.from_dict,  # type: ignore
            )
        response = autofix_pb2.CheckResponse(
            is_code_related=check_output.result, contains_failure=False
        )

        if response.is_code_related:
            with tracer.start_as_current_span("contain_errors_format"):
                prompt = self._error_formatter.format_prompt(
                    AutofixContainErrorsInput(
                        command=command.input, command_output=output
                    )
                )
            with tracer.start_as_current_span("contain_errors_generate"):
                tools = list(prompt.tools or [])
                responses = self._chat_client_lite.generate_response_stream(
                    model_caller="autofix-contain-errors",
                    messages=[
                        (
                            get_request_message_as_text(item.request_message),
                            get_response_message_as_text(item.response_text),
                        )
                        for item in prompt.chat_history
                    ],
                    system_prompt=prompt.system_prompt,
                    cur_message=prompt.message,
                    tools=tools,
                )
                errors_output = _parse_tool_use(
                    responses,
                    AutofixContainErrorsOutput.from_dict,  # type: ignore
                )
                response.contains_failure = errors_output.result

        # TODO: put this in a wrapper or helper class
        ri_check_response_event = request_insight_publisher.new_event()
        ri_check_response_event.autofix_check_response.MergeFrom(
            request_insight_pb2.RIAutofixCheckResponse(
                response=response,
            )
        )
        self._request_insight_publisher.publish_request_insight(
            self._request_insight_publisher.update_request_info_request(
                request_context.request_id, [ri_check_response_event], auth_info
            )
        )

        return response

    def plan(
        self,
        request: autofix_pb2.PlanRequest,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> autofix_pb2.PlanResponse:
        ri_plan_request_event = request_insight_publisher.new_event()
        ri_plan_request_event.autofix_plan_request.MergeFrom(
            request_insight_pb2.RIAutofixPlanRequest(
                request=request,
            )
        )
        self._request_insight_publisher.publish_request_insight(
            self._request_insight_publisher.update_request_info_request(
                request_context.request_id, [ri_plan_request_event], auth_info
            )
        )
        request_blobs = Blobs.from_proto(request.blobs)
        with tracer.start_as_current_span("convert_vcs_changes"):
            vcs_blobs = self._blobs_in_vcs_changes(request.vcs_change)
            blob_content = self._download_blobs(vcs_blobs, request_context, auth_info)
            changes = self._convert_vcs_changes(request.vcs_change, blob_content)
            missing_blobs: set[str] = {
                k for (k, v) in blob_content.items() if v is None
            }

        history: Sequence[AutofixSteeringMessage] = []
        if len(request.steering_history) > 0:
            with tracer.start_as_current_span("convert_steering_history"):
                # Include diffs for the most recent steering exchange, but not for older ones;
                # this is assuming the behavior of the prompt formatter to save some effort
                # downloading unneeded blobs
                history = [
                    self._convert_steering_exchange(
                        exchange,
                        blob_content,
                        request_context,
                        auth_info,
                        include_diff=i == 0,
                    )
                    for (i, exchange) in enumerate(request.steering_history)
                ]

        with tracer.start_as_current_span("retrieve_edit_locations"):
            locations_result = self._retrieve_edit_locations(
                request.command.output,
                changes,
                request_blobs,
                request_context,
                auth_info,
            )
            # We don't benefit from streaming here, so for the sake of timing accurately,
            # wait for the chunks here
            retrieved_chunks = list(locations_result.get_retrieved_chunks())
            missing_blobs.update(locations_result.get_missing_blob_names())
            checkpoint_not_found = locations_result.get_checkpoint_not_found()

            with tracer.start_as_current_span("add_lines_to_chunks"):
                location_chunks = self._add_lines_to_chunks(
                    retrieved_chunks, request_context, auth_info, cache=blob_content
                )
            del retrieved_chunks

        with tracer.start_as_current_span("retrieve_context"):
            context_result = self._retrieve_context(
                request.command,
                location_chunks,
                history[0] if len(history) else None,
                request_blobs,
                request_context,
                auth_info,
            )
            retrieved_chunks = list(context_result.get_retrieved_chunks())
            missing_blobs.update(context_result.get_missing_blob_names())
            checkpoint_not_found |= context_result.get_checkpoint_not_found()
            with tracer.start_as_current_span("add_lines_to_chunks"):
                context_chunks = self._add_lines_to_chunks(
                    retrieved_chunks, request_context, auth_info, cache=blob_content
                )
            del retrieved_chunks

        edit_locations = [*location_chunks, *context_chunks]
        # Plan may suggest changes to any of these paths
        edit_loc_path_to_blob: dict[str, str] = {
            chunk.path: chunk.blob_name for chunk in edit_locations
        }

        with tracer.start_as_current_span("generate_plan"):
            plan_input = AutofixCreateFixPlanInput(
                edit_locations=edit_locations,
                command=request.command.input,
                command_output=request.command.output,
                # Prompt only handles Modified today; not Deleted/Added
                breaking_change=[c for c in changes if isinstance(c, Modified)],
                steering_history=history,
            )
            plan_output = self._generate_plan(plan_input)

        if not plan_output.fix_plan:
            response = autofix_pb2.PlanResponse(
                unknown_blob_names=list(missing_blobs),
                checkpoint_not_found=checkpoint_not_found,
            )
            # TODO: share this with the main return statement by adding a helper and/or moving it outside the handler
            ri_plan_response_event = request_insight_publisher.new_event()
            ri_plan_response_event.autofix_plan_response.MergeFrom(
                request_insight_pb2.RIAutofixPlanResponse(
                    response=response,
                )
            )
            self._request_insight_publisher.publish_request_insight(
                self._request_insight_publisher.update_request_info_request(
                    request_context.request_id, [ri_plan_response_event], auth_info
                )
            )
            return response

        # Ensure we have the starting file content for all files being changed
        # in the plan; we only actually need to download if we retrieved edit
        # locations which were not in VCSChange
        with tracer.start_as_current_span("download_file_starting_content"):
            path_content = self._download_file_starting_content(
                plan_output.fix_plan.changes,
                edit_loc_path_to_blob,
                blob_content,
                request_context,
                auth_info,
            )
        with tracer.start_as_current_span("generate_replacements"):
            replacements = []
            for change in plan_output.fix_plan.changes:
                contents = path_content.get(change.path)
                if contents is None:
                    log.error(
                        "Cannot generate replacement for path with unknown content"
                    )
                    # Allow per-replacement indication of error in the RPC
                    # response? Unclear what the UI can/should do with partial
                    # plans, or plans with errors for some proposed change locations.
                    continue
                blob_name = edit_loc_path_to_blob[change.path]
                replacements.append(
                    self._generate_replacement(
                        change, blob_name, contents, request_context
                    )
                )
            for i, repl in enumerate(replacements):
                # Give each replacement a unique sequence id; not very useful until/unless
                # we implement streaming individual replacements over multiple messages
                repl.sequence_id = i

        response = autofix_pb2.PlanResponse(
            unknown_blob_names=list(missing_blobs),
            checkpoint_not_found=checkpoint_not_found,
            summary=plan_output.fix_plan.fix_desc,
            replacements=replacements,
        )
        ri_plan_response_event = request_insight_publisher.new_event()
        ri_plan_response_event.autofix_plan_response.MergeFrom(
            request_insight_pb2.RIAutofixPlanResponse(
                response=response,
            )
        )
        self._request_insight_publisher.publish_request_insight(
            self._request_insight_publisher.update_request_info_request(
                request_context.request_id, [ri_plan_response_event], auth_info
            )
        )
        return response

    def _retrieve_edit_locations(
        self,
        command_output: str,
        changes: Sequence[Changed[File]],
        blobs: Blobs,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> RetrievalResult:
        prompt = LocalizationNextEditPromptInput(
            current_file=File("", ""),
            edit_region=CharRange(0, 0),
            instruction="",
            command_output=command_output,
            recent_changes=changes,
        )
        return self._location_retriever.retrieve(
            RetrievalInput(
                prompt_input=prompt,
                blobs=[blobs],
            ),
            request_context,
            auth_info,
        )

    def _retrieve_context(
        self,
        command: autofix_pb2.Command,
        edit_locations: Sequence[PromptChunkWithLines],
        steering_message: AutofixSteeringMessage | None,
        blobs: Blobs,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> RetrievalResult:
        with tracer.start_as_current_span("format_prompt"):
            if steering_message is not None:
                prompt = self._steering_context_formatter.format_prompt(
                    GenerateChatanolQueryInput(
                        command=command.input,
                        command_output=command.output,
                        edit_locations=None,
                        steering_message=steering_message,
                    )
                )
            else:
                prompt = self._general_context_formatter.format_prompt(
                    GenerateChatanolQueryInput(
                        command=command.input,
                        command_output=command.output,
                        edit_locations=edit_locations,
                        steering_message=None,
                    )
                )

        with tracer.start_as_current_span("generate_query"):
            response = self._chat_client.generate_response_stream(
                model_caller="autofix-retrieve-context",
                messages=[],
                system_prompt=None,
                cur_message=prompt,
            )
            # non-streaming model call would be better for this
            generated_query = "".join([resp.text for resp in response if resp.text])

        with tracer.start_as_current_span("retrieve"):
            input = RetrievalInput(
                prompt_input=ChatRetrieverPromptInput(
                    prefix="",
                    suffix="",
                    path="",
                    message=generated_query,
                    selected_code="",
                    chat_history=[],
                ),
                blobs=[blobs],
            )
            return self._context_retriever.retrieve(input, request_context, auth_info)

    def _add_lines_to_chunks(
        self,
        chunks: Iterable[RetrievalChunk],
        request_context: RequestContext,
        auth_info: AuthInfo,
        cache: BlobContentMap | None = None,
    ) -> Sequence[PromptChunkWithLines]:
        # We need to download blobs for chunks which don't have line_start field
        # from retrieval
        blobs = set(
            chunk.blob_name
            for chunk in chunks
            if (chunk.line_start is None and chunk.blob_name)
        )
        blob_content = self._download_blobs(
            blobs, request_context, auth_info, cache=cache
        )

        blobs_indexed = {k: LineMap(v) for k, v in blob_content.items() if v}

        result: list[PromptChunkWithLines] = []
        for chunk in chunks:
            # Prompt formatter is insistent that length_in_lines be equal to this value.
            # chunk.length_in_lines matches this value for wolf-location-v17, but is always
            # off by one for chatanol1-18-hybrid-v2.
            # To avoid splitting the chunk here, we'd need to configure the retrieval lib
            # to correct for bad indexing results, and maybe deploy a corrected indexer
            # under a new transformation key.
            manual_line_count = len(chunk.text.splitlines(keepends=True))
            line_start = chunk.line_start
            if line_start is None:
                if chunk.blob_name is None:
                    log.error("Skipping chunk with no blob name")
                    continue
                lmap = blobs_indexed.get(chunk.blob_name)
                if lmap is None:
                    log.error(
                        "Missing blob content for chunk %s:%s",
                        chunk.blob_name,
                        chunk.chunk_index,
                    )
                    continue
                try:
                    line_start = lmap.get_line_number(chunk.char_start)
                except IndexError:
                    log.error(
                        "Chunk char_start out of range of blob content for chunk %s:%s",
                        chunk.blob_name,
                        chunk.chunk_index,
                    )
                    continue
            result.append(
                PromptChunkWithLines(
                    text=chunk.text,
                    path=chunk.path,
                    char_start=chunk.char_start,
                    char_end=chunk.char_end,
                    line_offset=line_start,
                    length_in_lines=manual_line_count,
                    blob_name=chunk.blob_name or "",
                )
            )
        return result

    @staticmethod
    def _blobs_in_vcs_changes(
        vcs_change: autofix_pb2.VCSChange,
    ) -> set[str]:
        blobs = set()
        for change in vcs_change.working_directory_changes:
            if change.change_type in (
                autofix_pb2.ChangeType.ADDED,
                autofix_pb2.ChangeType.MODIFIED,
            ):
                # Preference is current_blob_name; if we find that this is commonly missing,
                # may add (un)conditional download of indexed_blob_name as well to use as
                # fallback.
                blobs.add(change.current_blob_name)
            if change.change_type in (
                autofix_pb2.ChangeType.DELETED,
                autofix_pb2.ChangeType.MODIFIED,
            ):
                blobs.add(change.head_blob_name)
        return blobs

    @staticmethod
    def _convert_vcs_changes(
        vcs_change: autofix_pb2.VCSChange,
        fetched_content: BlobContentMap,
    ) -> list[Changed[File]]:
        result: list[Changed[File]] = []
        skipped_changes = []
        for change in vcs_change.working_directory_changes:
            if change.change_type == autofix_pb2.ChangeType.ADDED:
                content = fetched_content.get(change.current_blob_name)
                if content is None:
                    skipped_changes.append(change)
                    continue
                result.append(
                    Added(after=File(path=change.after_path, contents=content))
                )
            elif change.change_type == autofix_pb2.ChangeType.DELETED:
                content = fetched_content.get(change.head_blob_name)
                if content is None:
                    skipped_changes.append(change)
                    continue
                result.append(
                    Deleted(before=File(path=change.before_path, contents=content))
                )
            elif change.change_type == autofix_pb2.ChangeType.MODIFIED:
                after_content = fetched_content.get(change.current_blob_name)
                before_content = (
                    fetched_content.get(change.head_blob_name)
                    if after_content is not None
                    else None
                )
                if after_content is None or before_content is None:
                    skipped_changes.append(change)
                    continue
                result.append(
                    Modified(
                        before=File(path=change.before_path, contents=before_content),
                        after=File(path=change.after_path, contents=after_content),
                    )
                )
        if skipped_changes:
            log.warn(
                "Skipped %d changes due to missing blobs",
                len(skipped_changes),
            )
        return result

    def _download_blobs(
        self,
        keys: Iterable[str],
        request_context: RequestContext,
        auth_info: AuthInfo,
        cache: BlobContentMap | None = None,
    ) -> BlobContentMap:
        result = {}
        ckey_list: list[ContentKey] = []
        if cache:
            sentinel = object()
            for key in keys:
                cached = cache.get(key, sentinel)
                if cached is not sentinel:
                    result[key] = cached
                else:
                    ckey_list.append(ContentKey(blob_name=key))
            log.debug(
                "Found %d/%d blobs in cache", len(result), len(result) + len(ckey_list)
            )
        else:
            ckey_list = [ContentKey(blob_name=key) for key in keys]
        if not ckey_list:
            return result

        # TODO: limit on this download? We know that the prompt can only handle so much. Today, we present the
        # prompt formatter with all changes and it ranks them and computes diffs until the prompt budget is
        # exhausted. Ideally we would not download a bunch of content we won't use.
        # TODO: no way currently to request download of multiple blobs' content
        # without having to fetch info row, as this call returns 'metadata' from that row
        content_it: Iterable[tuple[bytes, dict[str, str]] | None] = (
            self._content_manager_client.batch_download_all(
                keys=ckey_list,
                request_context=request_context,
                tenant_id=auth_info.tenant_id,
            )
        )
        for key, content in zip(ckey_list, content_it):
            value = None
            if content:
                value = content[0].decode("utf-8")
            result[key.blob_name] = value
        return result

    def _download_file_starting_content(
        self,
        changes: Sequence[AutofixFileFix],
        edit_loc_path_to_blob: Mapping[str, str],
        blob_content: BlobContentMap,
        request_context: RequestContext,
        auth_info: AuthInfo,
    ) -> PathContentMap:
        """Produce a map from path to file content for paths in `changes`

        Args:
            changes: the changes which we're fetching starting content for
            edit_loc_path_to_blob: map from path to blob name; source of truth, we have no
                other way of turning path into blob name
            blob_content: in-out parameter; map from blob name to blob contents loaded from content manager

        Returns:
            A map from path to file content for paths whose blobs we know and which content manager
            has content for.
        Side-effect: Updates blob_content with any newly downloaded content.
        """
        paths_needed = set(change.path for change in changes)
        blobs_needed: set[str] = set(
            edit_loc_path_to_blob.get(p) for p in paths_needed
        ) - {None}  # type: ignore
        blob_content.update(
            self._download_blobs(
                blobs_needed, request_context, auth_info, cache=blob_content
            )
        )

        path_content: PathContentMap = {}
        hallucinated_paths = []
        for path in paths_needed:
            blob_name = edit_loc_path_to_blob.get(path)
            if isinstance(blob_name, str):
                # Still may be 'None' if there was no content for the blob name
                # in content manager
                path_content[path] = blob_content.get(blob_name)
            else:
                path_content[path] = None
                hallucinated_paths.append(path)
        if hallucinated_paths:
            # METRIC / RI (we shouldn't log the path, but RI would be OK)
            log.error(
                "Plan includes changes to %d paths not retrieved",
                len(hallucinated_paths),
            )
        return path_content

    def _convert_steering_exchange(
        self,
        exchange: autofix_pb2.UserSteeringExchange,
        blob_content: BlobContentMap,
        request_context: RequestContext,
        auth_info: AuthInfo,
        include_diff: bool,
    ) -> AutofixSteeringMessage:
        descs: list[AutofixFileFix] = [
            AutofixFileFix(
                path=repl.path,
                change_desc=repl.description,
                code_block_start_line=repl.start_line,
                code_block=repl.text,
            )
            for repl in exchange.replacements
        ]

        diffs: list[Modified[File]] = []
        if include_diff:
            blobs_needed = set(change.old_blob_name for change in exchange.replacements)
            blob_content.update(
                self._download_blobs(
                    blobs_needed, request_context, auth_info, cache=blob_content
                )
            )
            for repl in exchange.replacements:
                content = blob_content.get(repl.old_blob_name)
                if content is None:
                    # If client only includes blob names that were returned by a
                    # previous call to plan, this should be extremely rare, and probably
                    # only possible after adding content_manager GC
                    log.error(
                        "Missing content for one or more blobs in steering history; not including diff in prompt"
                    )
                    diffs = []
                    break
                diffs.append(self._apply_replacement_to_contents(repl, content))

        return AutofixSteeringMessage(
            message=exchange.request_message,
            relevant_fix_plan=AutofixFixPlan(
                fix_desc=exchange.summary,
                changes=descs,
            ),
            fix=diffs,
        )

    @staticmethod
    def _apply_replacement_to_contents(
        repl: autofix_pb2.TextReplacement, contents: str
    ) -> Modified[File]:
        """Applies a TextReplacement to the contents of a file, yielding a new file"""
        # We're currently not verifying that the replaced lines match repl.old_text or that the replacement
        # is within the bounds of the file
        #
        # How should we handle failure to apply? Fail entire RPC? Throw
        # away the diff from the steering exchange?
        # N.B. if start_line > len(split), this just appends repl.text
        # to the current file contents
        split = contents.splitlines(keepends=True)
        split[repl.start_line - 1 : repl.end_line - 1] = [repl.text]
        after_contents = "".join(split)
        return Modified(
            before=File(path=repl.path, contents=contents),
            after=File(path=repl.path, contents=after_contents),
        )

    @staticmethod
    def _merge_changes_by_path(
        changes: Sequence[AutofixFileFix],
    ) -> Sequence[AutofixFileFix]:
        changes_dict = defaultdict(list)
        for change in changes:
            changes_dict[change.path].append(change)
        # Sort by code_block_start_line?
        return [
            AutofixFileFix(
                path=path,
                change_desc="\n\n".join(
                    map(lambda change: change.change_desc, file_changes)
                ),
                code_block_start_line=min(
                    map(lambda change: change.code_block_start_line, file_changes)
                ),
                code_block="\n...\n".join(
                    map(lambda change: change.code_block, file_changes)
                ),
            )
            for path, file_changes in changes_dict.items()
        ]

    def _generate_plan(
        self,
        input: AutofixCreateFixPlanInput,
    ) -> AutofixCreateFixPlanOutput:
        prompt = self._plan_formatter.format_prompt(input)
        # We don't benefit from this being a stream today; would need
        # to enable parse_fix_plan_xml to consume a stream of chars
        # instead
        model_response = self._chat_client.generate_response_stream(
            model_caller="autofix-create-fix-plan",
            messages=[
                (
                    get_request_message_as_text(item.request_message),
                    get_response_message_as_text(item.response_text),
                )
                for item in prompt.chat_history
            ],
            system_prompt=prompt.system_prompt,
            cur_message=prompt.message,
        )
        xml_text: str = "".join([resp.text for resp in model_response if resp.text])
        xml = parse_fix_plan_xml(xml_text)
        changes: Sequence[AutofixFileFix] = []
        fix_desc = ""
        files: set[str] = {file.path for file in input.edit_locations}
        if xml.fix_plan and xml.fix_plan.fix_desc:
            fix_desc = xml.fix_plan.fix_desc
            if xml.fix_plan.changes:
                changes = list(filter(lambda x: x.path in files, xml.fix_plan.changes))
                changes = self._merge_changes_by_path(changes)
        return AutofixCreateFixPlanOutput(
            thinking=xml.thinking,
            critical_thinking=xml.critical_thinking,
            fix_plan=AutofixFixPlan(fix_desc=fix_desc, changes=changes),
            # diagnostic/RI purposes
            system_prompt=prompt.system_prompt,
            prompt=prompt.message,
        )

    def _generate_replacement(
        self,
        change: AutofixFileFix,
        starting_blob: str,
        starting_content: str,
        request_context: RequestContext,
    ) -> autofix_pb2.TextReplacement:
        req = edit_pb2.InstructionRequest(
            model_name=self._edit_model,
            path="",
            prefix="",
            selected_text="",
            suffix="",
            instruction="",
            # TODO: This edit can be cancelled by sequence_id if any other
            # autofix request or Instruction/SmartPaste are hitting the same
            # Forger inference host with the same session id concurrently.
            sequence_id=0,
            chat_history=[
                edit_pb2.Exchange(
                    request_message=change.change_desc, response_text="not used"
                )
            ],
            code_block=change.code_block or "not used",
            target_file_path=change.path,
            target_file_content=starting_content,
        )

        response: edit_pb2.InstructionResponse
        lines = []
        start_line = None
        end_line = None
        sequence_id = None
        for response in self._edit_client.instruction_stream(req, request_context):
            if not response.HasField("replace_text"):
                continue
            replace = response.replace_text
            if replace.HasField("start_line"):
                start_line = replace.start_line - 1
            if replace.HasField("end_line"):
                end_line = replace.end_line - 1
            if replace.HasField("text"):
                lines.append(replace.text)
            # Note: edit.proto interface permits there to be multiple replacements
            # in the response stream
            # However, in practice, Forger will never generate multiple replacements,
            # even for sparse changes in a large file, and the Edit host doesn't
            # correct for this either. So we get away with assuming a single replacement.
            # If we move to a different Edit host configuration, this may break.
            # The change to generate multiple TextReplacements is not significant
            # effort, but splitting the change_desc across those replacements
            # may be.
            assert sequence_id in (None, replace.sequence_id)
            sequence_id = replace.sequence_id

        if start_line is None or end_line is None:
            log.error("No start/end line for replacement")
            # This is probably not an appropriate fallback response
            start_line = end_line = -1
            lines = []
            replaced_lines = []
        else:
            # Method gives no way to stop after end_line lines...
            replaced_lines = starting_content.splitlines(keepends=True)[
                start_line:end_line
            ]

        return autofix_pb2.TextReplacement(
            description=change.change_desc,
            old_blob_name=starting_blob,
            path=change.path,
            text="".join(lines),
            start_line=start_line + 1,
            end_line=end_line + 1,
            old_text="".join(replaced_lines),
            sequence_id=sequence_id or 0,
        )
