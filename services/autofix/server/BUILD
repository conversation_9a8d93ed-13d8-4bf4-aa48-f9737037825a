load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:kubecfg.bzl", "kubecfg_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library", "py_oci_image", "pytest_test")

py_binary(
    name = "server",
    srcs = [
        "server.py",
    ],
    deps = [
        ":autofix_metrics",
        ":handler",
        "//base/logging:struct_logging",
        "//base/python/opentelemetry_utils:traced_threadpool",
        "//base/python/signal_handler",
        "//base/third_party_clients:clients",
        "//base/tracing:tracing_py",
        "//services/autofix:autofix_py_proto",
        "//services/content_manager/client",
        "//services/edit_host:client",
        "//services/lib/grpc/auth:service_auth_interceptor",
        "//services/lib/grpc/auth:service_token_auth",
        "//services/lib/grpc/metrics",
        "//services/lib/grpc/tls_config:grpc_tls_config_py",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/publisher:publisher_py",
        requirement("grpcio-health-checking"),
        requirement("dataclasses_json"),
        requirement("grpcio"),
        requirement("grpcio-reflection"),
        requirement("opentelemetry-instrumentation-grpc"),
        requirement("prometheus-client"),
        requirement("protobuf"),
    ],
)

py_library(
    name = "handler",
    srcs = [
        "handler.py",
    ],
    deps = [
        "//base/prompt_format_autofix:check_command_prompt_formatter",
        "//base/prompt_format_autofix:contains_errors_prompt_formatter",
        "//base/prompt_format_autofix:create_fix_plan_prompt_formatter",
        "//base/prompt_format_autofix:generate_chatanol_query",
        "//base/prompt_format_retrieve:prompt_formatter",
        "//base/third_party_clients:clients",
        "//services/autofix:autofix_py_proto",
        "//services/content_manager/client",
        "//services/edit_host:client",
        "//services/lib/retrieval:retriever",
        "//services/lib/retrieval:retriever_factory",
        "//services/request_insight/publisher:publisher_py",
        requirement("dataclasses_json"),
        requirement("opentelemetry-api"),
        requirement("prometheus-client"),
    ],
)

py_library(
    name = "autofix_metrics",
    srcs = [
        "autofix_metrics.py",
    ],
    deps = [
        requirement("prometheus-client"),
    ],
)

pytest_test(
    name = "handler_test",
    srcs = [
        "handler_test.py",
    ],
    deps = [
        ":handler",
    ],
)

py_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":server",
    tars = ["//tools/docker:grpc_health_probe_tar"],
)

kubecfg_library(
    name = "kubecfg_lib",
    srcs = ["deploy_lib.jsonnet"],
    data = [
        ":image",
    ],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/chat_host/server:anthropic-lib",
        "//services/deploy:endpoints",
        "//services/request_insight/publisher:publisher_lib",
    ],
)
