syntax = "proto3";

package autofix;

import "base/blob_names/blob_names.proto";

service Autofix {
  // Experimental APIs, subject to change.
  // Based on https://www.notion.so/RFC-Autofix-Productionizing-Backend-135bba10175a809da7bef76066b3e7f0
  rpc Check(CheckRequest) returns (CheckResponse);
  // NOTE: this may be converted to streaming eventually
  rpc Plan(PlanRequest) returns (PlanResponse);
  rpc FindMissing(FindMissingRequest) returns (FindMissingResponse);
}

// Copied from next_edit.proto. TODO: share!
enum ChangeType {
  ADDED = 0;
  DELETED = 1;
  MODIFIED = 2;
  RENAMED = 3;
}

message WorkingDirectoryChange {
  optional string before_path = 1 [debug_redact = true];

  optional string after_path = 2 [debug_redact = true];

  ChangeType change_type = 3;

  // The before version in HEAD.
  optional string head_blob_name = 4;

  // The indexed version in the current workspace.
  optional string indexed_blob_name = 5;

  // The current version the IDE sees but might not be indexed yet.
  optional string current_blob_name = 6;
}

message VCSChange {
  repeated WorkingDirectoryChange working_directory_changes = 1;
}

message Command {
  string input = 1;
  string output = 2;
  optional uint32 exit_code = 3;
}

// Autofix step 1: given a command check in the background whether to enter the fix workflow
message CheckRequest {
  Command command = 1;
}

message CheckResponse {
  bool is_code_related = 1;
  bool contains_failure = 2;
}

// A single exchange between the user and the model. Right now, it just contains a user message
// and information about the solution corresponding to that message, as well as a request ID for each steering exchange.
message UserSteeringExchange {
  string request_message = 1 [debug_redact = true];

  // text summary of the whole plan
  string summary = 2 [debug_redact = true];

  // list of replacements to apply
  repeated TextReplacement replacements = 3;

  string request_id = 4;
}

// Autofix step 2: given a command failure produce a set of suggested fixes
message PlanRequest {
  Command command = 1;
  VCSChange vcs_change = 2;
  base.blob_names.Blobs blobs = 3;
  repeated UserSteeringExchange steering_history = 4;
}

message TextReplacement {
  // human readable description
  string description = 1;

  // path to the file with the replacement
  string path = 2 [debug_redact = true];

  // the text to be pasted
  string text = 3 [debug_redact = true];

  // code line in which to start the replacement, relative to sent code (incl. prefix)
  uint32 start_line = 4;

  // code line in which to end the replacement, relative to sent code (incl. suffix)
  uint32 end_line = 5;

  // Old text to replace
  string old_text = 6 [debug_redact = true];

  // Sequence ID of the replacment
  uint32 sequence_id = 7;

  // If provided, the exact blob the replacement is expected to apply to
  optional string old_blob_name = 8;
}

// NOTE: based on InstructionResponse. Consider abstracting a Replacement message.
message PlanResponse {
  // standard retrieval response fields
  repeated string unknown_blob_names = 1;
  bool checkpoint_not_found = 2;

  // text summary of the whole plan
  optional string summary = 3;

  // list of replacements to apply
  repeated TextReplacement replacements = 4;
}

message FindMissingRequest {
  // The collection of blob names.
  repeated string blob_names = 1;
}

message FindMissingResponse {
  // The subset of blob names that are not indexed for the given model.
  repeated string nonindexed_blob_names = 1;
}
