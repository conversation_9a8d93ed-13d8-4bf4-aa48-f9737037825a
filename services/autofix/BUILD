load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:rust.bzl", "rust_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "autofix_proto",
    srcs = ["autofix.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "@protobuf//:empty_proto",
    ],
)

py_grpc_library(
    name = "autofix_py_proto",
    protos = [":autofix_proto"],
    visibility = [
        "//services:__subpackages__",
    ],
)

ts_proto_library(
    name = "autofix_ts_proto",
    data = [
        "//base/blob_names:blob_names_ts_proto",
    ],
    node_modules = "//:node_modules",
    proto = ":autofix_proto",
    visibility = ["//services:__subpackages__"],
)

go_grpc_library(
    name = "autofix_go_grpc",
    importpath = "github.com/augmentcode/augment/services/autofix",
    proto = ":autofix_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_go_proto",
    ],
)
