"""Service availability health checker."""

import argparse
from collections import defaultdict
import sys
import time
import uuid
from random import randint
from pathlib import Path
from google.protobuf.json_format import MessageToDict

import grpc
import structlog
from prometheus_client import Counter, start_http_server

import services.api_proxy.client.grpc_client as api_proxy_grpc_client
import services.api_proxy.model_finder_pb2 as model_finder_pb2
import services.api_proxy.model_finder_pb2_grpc as model_finder_pb2_grpc
import services.api_proxy.public_api_pb2 as public_api_pb2
from base.augment_client.client import (
    AugmentClient,
    AutofixCommand,
    BlobsJson,
    UploadContent,
    VCSChange,
)
from base.logging.struct_logging import setup_struct_logging
from base.python.signal_handler.signal_handler import StandardSignalHandler
from base.blob_names.python.blob_names import get_blob_name

log = structlog.get_logger()

# New health check metrics
_health_check_status_counter = Counter(
    "api_proxy_health_check_status",
    "API Proxy health checks with success/failure",
    ["status"],
)

_health_check_model_status_counter = Counter(
    "api_proxy_health_check_model_status",
    "API Proxy health checks for a given model with success/failure",
    ["model", "model_priority", "status"],
)

_health_check_agent_status_counter = Counter(
    "api_proxy_health_check_agent_status",
    "API Proxy health checks for agents with success/failure",
    ["endpoint", "status"],
)

_health_check_remote_agent_status_counter = Counter(
    "api_proxy_health_check_remote_agent_status",
    "API Proxy health checks for remote agents with success/failure",
    ["endpoint", "status"],
)

# Status label values
SUCCESS = "success"
FAILURE = "failure"


next_edit_indexed_attempts = 5


def is_third_party(model_name: str):
    return (
        model_name.endswith("-chat")  # Currently all chat models are third party
        or model_name.startswith("o1-")
        or model_name.startswith("o3-")
        or model_name.startswith("gpt4o-")
        or model_name.startswith("gpt4-")
        or model_name.startswith("claude-")
        or model_name.startswith("gemini-")
        or model_name.startswith("gemini2-")
        or model_name.startswith("llama3-1-405b-vertex")  # Llama 3 can be self-hosted
        or model_name.startswith("wolf")  # autofix relies on claude models currently
        or model_name.startswith("deepseek-")  # currently using fireworks API
        or model_name.startswith("grok")
    )


def perform_agents_check(client: AugmentClient):
    try:
        log.info("Checking agents list remote tools")
        response, _ = client.post_proto(
            "agents/list-remote-tools",
            public_api_pb2.ListRemoteToolsRequest(),
            public_api_pb2.ListRemoteToolsResponse(),
        )
        assert any(
            tool.remote_tool_id == public_api_pb2.RemoteToolId.WEB_SEARCH
            for tool in response.tools
        )
        _health_check_agent_status_counter.labels(
            endpoint="list-remote-tools", status=SUCCESS
        ).inc()
    except Exception as ex:  # pylint: disable=broad-exception-caught
        log.error("Exception on agents: %s", ex)
        log.exception(ex)
        _health_check_agent_status_counter.labels(
            endpoint="list-remote-tools", status=FAILURE
        ).inc()


def perform_remote_agents_check(client: AugmentClient):
    try:
        log.info("Checking remote-agents/list")
        response, _ = client.post_proto(
            "remote-agents/list",
            public_api_pb2.ListRemoteAgentsRequest(),
            public_api_pb2.ListRemoteAgentsResponse(),
        )
        assert response.max_remote_agents > 0
        _health_check_remote_agent_status_counter.labels(
            endpoint="remote-agents/list", status=SUCCESS
        ).inc()
    except Exception as ex:  # pylint: disable=broad-exception-caught
        log.error("Exception on remote-agents/list: %s", ex)
        log.exception(ex)
        _health_check_remote_agent_status_counter.labels(
            endpoint="remote-agents/list", status=FAILURE
        ).inc()


def _perform_checks(
    client: AugmentClient,
    model_finder_client: model_finder_pb2_grpc.ModelFinderStub,
    last_time_for_model: dict[str, float],
    third_party_rate_limit_seconds: int,
    third_party_default_model_rate_limit_seconds: int,
    interval_seconds: int,
):
    global next_edit_indexed_attempts  # TODO: split healthchecks to classes and keep internal state intead
    # we call the get models endpoint to ensure that the API proxy is up and running
    # but we do not actually use the response
    models = client.get_models()
    log.info("Get Models response %s", models)

    default_model_names = []
    for model in models.models:
        if model.is_default:
            default_model_names.append(model.internal_name)

    model_finder_response: model_finder_pb2.GetModelsResponse = (
        model_finder_client.GetGenerationModels(model_finder_pb2.GetModelsRequest())
    )
    log.info("Model finder response %s", model_finder_response)

    upload = UploadContent(
        content="def test_health_check():\n    return True",
        path_name="augment/health_check/test.py",
    )

    perform_agents_check(client)
    perform_remote_agents_check(client)

    blob_names = client.batch_upload([upload])

    response = client.find_missing(model_name="", memory_object_names=blob_names)
    if len(response.unknown_memory_names) != 0:
        raise ValueError("Invalid find missing response")

    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=blob_names,
        deleted_blobs=[],
    )

    # Randomize an offset to avoid all namespaces hitting Third Party APIs together at deployment
    current_third_party_delay = (
        randint(0, third_party_default_model_rate_limit_seconds) - interval_seconds
    )

    for model in model_finder_response.models:
        success = True
        model_priority = 0
        is_default = model.name in default_model_names

        relevant_rate_limit_seconds = (
            third_party_default_model_rate_limit_seconds
            if is_default
            else third_party_rate_limit_seconds
        )

        if is_third_party(model.name):
            if relevant_rate_limit_seconds == 0:
                log.info(
                    "Disabled third party model %s is_default=%s",
                    model.name,
                    is_default,
                )
                continue
            # If this is the first time this specific model runs, set a fake time in which it ran last
            # to delay its first run
            if last_time_for_model[model.name] == 0:
                current_third_party_delay %= relevant_rate_limit_seconds
                last_time_for_model[model.name] = (
                    time.time()
                    - relevant_rate_limit_seconds
                    + current_third_party_delay
                )
                # Stagger third-party models - next model we see we will push to the next interval run
                # In total we'll have (relevant_rate_limit_seconds / interval_seconds) "slots"
                current_third_party_delay += interval_seconds
            if (
                time.time() - last_time_for_model[model.name]
                < relevant_rate_limit_seconds
            ):
                log.info(
                    "Skipping third party model %s is_default=%s next run in %s minutes",
                    model.name,
                    is_default,
                    int(
                        (
                            relevant_rate_limit_seconds
                            - (time.time() - last_time_for_model[model.name])
                        )
                        / 60
                    ),
                )
                continue

        if model.HasField("inference"):  # inference==completion
            model_priority = model.inference.model_priority
            try:
                log.info("Checking completion model %s", model.name)
                model_client = client.client_for_model(model.name)
                completion = model_client.complete(
                    prompt="def test_health_check():\n    ",
                    path="augment/health_check/test.py",
                    blobs=blobs,
                    max_tokens=4,
                )
                log.info("Completion %s: %s", model.name, completion)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                success = False
                # an operation on a model failed. We report a problem on that model, but move
                # on to other models
                log.error("Exception on %s for %s: %s", client, model.name, ex)
                log.exception(ex)
        elif model.HasField("edit"):
            model_priority = model.edit.model_priority
            # We are (temporarily?) overloading edit host with two types of handlers
            try:
                model_client = client.client_for_model(model.name)
                if "droid" in model.name:
                    # Backward compatability with droid-style requests
                    log.info("Checking edit model %s", model.name)
                    edit = model_client.edit(
                        selected_text="def quicksort(nums):\n",
                        instruction="Implement the function",
                        prefix="",
                        suffix="",
                        path="augment/health_check/test.py",
                        blobs=blobs,
                    )
                    log.info("Edit %s: %s", model.name, edit)
                elif "forger" in model.name:
                    log.info("Checking edit model %s", model.name)
                    smart_paste_stream = model_client.smart_paste_stream(
                        selected_text="def quicksort(nums):\n",
                        prefix="",
                        suffix="",
                        path="augment/health_check/test.py",
                        blobs=blobs,
                        code_block="def quicksort(nums):\n    return nums",
                        target_file_path="augment/test/quicksort.py",
                        target_file_content="def quicksort(nums):\n    pass",
                    )
                    replace_text = ""
                    for response in smart_paste_stream:
                        if (
                            hasattr(response, "replacement_text")
                            and response.replacement_text is not None
                        ):
                            replace_text += response.replacement_text
                    log.info("Smart Paste %s: %s", model.name, replace_text)
                else:
                    log.info("Checking instruction model %s", model.name)
                    instruction_stream = model_client.instruction_stream(
                        selected_text="def quicksort(nums):\n",
                        instruction="Implement the function",
                        prefix="",
                        suffix="",
                        path="augment/health_check/test.py",
                        blobs=blobs,
                    )
                    replace_text = ""
                    for response in instruction_stream:
                        if (
                            hasattr(response, "replacement_text")
                            and response.replacement_text is not None
                        ):
                            replace_text += response.replacement_text
                    log.info("Instruction %s: %s", model.name, replace_text)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                success = False
                # an operation on a model failed. We report a problem on that model, but move
                # on to other models
                log.error("Exception on %s for %s: %s", client, model.name, ex)
                log.exception(ex)
        elif model.HasField("chat"):
            model_priority = model.chat.model_priority
            try:
                log.info("Checking chat model %s", model.name)
                model_client = client.client_for_model(model.name)
                chat_stream = model_client.chat_stream(
                    selected_code="def test_health_check():\n",
                    message="What does test_health_check return? Answer as succintly as possible",
                    prefix="",
                    suffix="",
                    path="augment/health_check/test.py",
                    blobs=blobs,
                    timeout=30,  # Chat requests can take longer, esp. for third party APIs
                )
                response_text = ""
                for chat_response in chat_stream:
                    response_text += chat_response.text
                log.info("Chat %s: %s", model.name, response_text)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                success = False
                # an operation on a model failed. We report a problem on that model, but move
                # on to other models
                log.error("Exception on %s for %s: %s", client, model.name, ex)
                log.exception(ex)
        elif model.HasField("next_edit"):
            try:
                file_name = "example_03_21_2025.py"  # updating to recover from data corruption (PR-21347)
                old_code = """\
def quicksort(nums):
    if len(nums) <= 1:
        return nums
    pivot = nums[0]
    left = []
    right = []
    for i in range(1, len(nums)):
        if nums[i] < pivot:
            left.append(nums[i])
        else:
            right.append(nums[i])
    return quicksort(left) + [pivot] + quicksort(right)
"""
                new_code = """\
def qsort(nums):
    if len(nums) <= 1:
        return nums
    pivot = nums[0]
    left = []
    right = []
    for i in range(1, len(nums)):
        if nums[i] < pivot:
            left.append(nums[i])
        else:
            right.append(nums[i])
    return quicksort(left) + [pivot] + quicksort(right)
"""

                upload_new_code = UploadContent(
                    content=new_code,
                    path_name=file_name,
                )

                next_edit_blob_names = client.batch_upload([upload_new_code])
                response = client.find_missing(
                    model_name="", memory_object_names=next_edit_blob_names
                )

                if len(response.unknown_memory_names) != 0:
                    log.info(
                        "Next Edit Healthcheck (attempt %d) find_missing returned an unknown blob names: %s",
                        next_edit_indexed_attempts,
                        response.unknown_memory_names,
                    )
                    if next_edit_indexed_attempts > 0:
                        next_edit_indexed_attempts -= 1
                        continue
                    else:
                        raise ValueError("Next Edit: Invalid find missing response")

                old_blob_name = get_blob_name(file_name, old_code.encode("utf-8"))

                edit_events = [
                    {
                        "path": file_name,
                        "before_blob_name": old_blob_name,
                        "after_blob_name": next_edit_blob_names[0],
                        "edits": [
                            {
                                "before_start": 4,
                                "after_start": 4,
                                "before_text": "quick",
                                "after_text": "q",
                            }
                        ],
                    }
                ]

                blobs = BlobsJson(
                    checkpoint_id=None,
                    added_blobs=[next_edit_blob_names[0]],
                    deleted_blobs=[],
                )

                log.info("Checking next edit model %s", model.name)
                model_client = client.client_for_model(model.name)
                stream_response = list(
                    model_client.next_edit_stream(
                        mode="BACKGROUND",
                        scope="WORKSPACE",
                        instruction="",
                        prefix=new_code[3:],
                        suffix=new_code[:3],
                        selected_text="",
                        edit_events=edit_events,
                        blobs=blobs,
                        timeout=30,  # Next edit requests can take longer
                    )
                )
                log.info("next-edit %s: %s", model.name, stream_response)
                # There must be at least one suggestion.
                if not stream_response or not stream_response[0].next_edit:
                    raise ValueError("Invalid next edit response")
            except Exception as ex:  # pylint: disable=broad-exception-caught
                success = False
                # an operation on a model failed. We report a problem on that model, but move
                # on to other models
                log.error("Exception on %s for %s: %s", client, model.name, ex)
                log.exception(ex)
        elif model.HasField("autofix"):
            model_priority = model.autofix.model_priority
            try:
                log.info("Checking autofix model %s", model.name)
                model_client = client.client_for_model(model.name)

                command = AutofixCommand(
                    input="cargo test",
                    output="test result: FAILED\nerror: test failed",
                )

                check_response = model_client.autofix_check(command=command)
                log.info("Autofix %s check: %s", model.name, check_response)
                if (
                    not check_response.is_code_related
                    or not check_response.contains_failure
                ):
                    raise ValueError("Invalid autofix check response")

                plan_response = model_client.autofix_plan(
                    command=command,
                    vcs_change=VCSChange(working_directory_changes=[], commits=[]),
                    blobs=blobs,
                    timeout=30,  # Autofix requests can take longer
                )
                log.info("Autofix %s plan: %s", model.name, plan_response)
            except Exception as ex:  # pylint: disable=broad-exception-caught
                success = False
                # an operation on a model failed. We report a problem on that model, but move
                # on to other models
                log.error("Exception on %s for %s: %s", client, model.name, ex)
                log.exception(ex)

        else:
            success = True  # We should consider to fail. Opting for success for backward compatibility.
            # an operation on a model failed. We report a problem on that model, but move
            # on to other models
            log.warn(f"Unknown model {MessageToDict(model)=}")

        log.info("Health Check Result model=[%s]: success=[%s]", model.name, success)
        # Report model health. Poking both SUCCESS and FAILURE helps with PromQL aggregation.
        last_time_for_model[model.name] = time.time()
        _health_check_model_status_counter.labels(
            model.name, str(model_priority), SUCCESS
        ).inc(1 if success else 0)
        _health_check_model_status_counter.labels(
            model.name, str(model_priority), FAILURE
        ).inc(0 if success else 1)


def run(
    client: AugmentClient,
    model_finder_client: model_finder_pb2_grpc.ModelFinderStub,
    last_success_time_for_model: dict[str, float],
    third_party_rate_limit_seconds: int,
    third_party_default_model_rate_limit_seconds: int,
    interval_seconds: int,
):
    # Report overall health. Poking both SUCCESS and FAILURE helps with PromQL aggregation.
    try:
        _perform_checks(
            client,
            model_finder_client=model_finder_client,
            last_time_for_model=last_success_time_for_model,
            third_party_rate_limit_seconds=third_party_rate_limit_seconds,
            third_party_default_model_rate_limit_seconds=third_party_default_model_rate_limit_seconds,
            interval_seconds=interval_seconds,
        )
        _health_check_status_counter.labels(SUCCESS).inc(1)
        _health_check_status_counter.labels(FAILURE).inc(0)
    except Exception as ex:  # pylint: disable=broad-exception-caught
        # either get models or upload failed (likely).
        # this is usually a very bad situation and we can't report on the models itself
        log.error("Exception on %s: %s", client, ex)
        log.exception(ex)
        _health_check_status_counter.labels(SUCCESS).inc(0)
        _health_check_status_counter.labels(FAILURE).inc(1)


def main():
    """Main entrypoint."""
    _ = StandardSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument("--endpoint", required=True)
    parser.add_argument("--model-finder-endpoint", required=True)
    parser.add_argument("--token-file", type=Path, help="Path to file containing token")
    parser.add_argument("--session-id")
    parser.add_argument("--interval", default=30, type=int)
    parser.add_argument("--third-party-rate-limit-seconds", default=1800, type=int)
    parser.add_argument(
        "--third-party-default-model-rate-limit-seconds", default=1800, type=int
    )
    parser.add_argument("--client-mtls", action="store_true")
    parser.add_argument("--client-mtls-ca-cert", type=Path)
    parser.add_argument("--client-mtls-client-key", type=Path)
    parser.add_argument("--client-mtls-client-cert", type=Path)

    args = parser.parse_args()
    log.info("Args: %s", args)

    if args.token_file is None:
        token_file = Path("~/.config/augment/api_token").expanduser()
        if not token_file.exists():
            log.error("Must specify a token with --token-file or at %s", token_file)
            sys.exit(1)
    else:
        token_file = args.token_file
    token = token_file.read_text(encoding="utf-8").splitlines()[0].rstrip()

    # begin listening for Prometheus requests
    start_http_server(9090)

    if args.client_mtls:
        credentials = grpc.ssl_channel_credentials(
            root_certificates=args.client_mtls_ca_cert.read_bytes(),
            private_key=args.client_mtls_client_key.read_bytes(),
            certificate_chain=args.client_mtls_client_cert.read_bytes(),
        )
    else:
        credentials = None

    model_finder_stub = api_proxy_grpc_client.setup_stub(
        endpoint=args.model_finder_endpoint, credentials=credentials
    )

    client = AugmentClient(
        url=args.endpoint,
        token=token,
        timeout=10,
        session_id=uuid.UUID(args.session_id) if args.session_id else None,
        user_agent="AugmentHealthCheck/0",
    )
    log.info("Client %s", client)
    last_success_time_for_model = defaultdict(float)
    while True:
        run(
            client,
            model_finder_client=model_finder_stub,
            last_success_time_for_model=last_success_time_for_model,
            third_party_rate_limit_seconds=args.third_party_rate_limit_seconds,
            third_party_default_model_rate_limit_seconds=args.third_party_default_model_rate_limit_seconds,
            interval_seconds=args.interval,
        )
        time.sleep(args.interval)


if __name__ == "__main__":
    main()
