use std::collections::HashMap;
use std::sync::Arc;

use actix_web::HttpRequest;
use async_trait::async_trait;

use request_insight_publisher::request_insight::{
    self, full_export_user_event, session_event, CompletionRequestIdIssuedEvent,
    EditRequestIdIssuedEvent, InferRequest, NextEditRequestIdIssuedEvent,
    RecordFullExportUserEventsRequest, RecordSessionEventsRequest, RequestEvent, RequestMetadata,
    RequestType, SessionEvent, TextEditEvent, UpdateRequestInfoRequest,
};
use request_insight_publisher::to_tenant_info_proto;
use request_insight_publisher::RequestInsightPublisherConfig;

use crate::api_auth::User;
use crate::metrics::{
    COMPLETION_RESOLUTION_COLLECTOR, EDIT_RESOLUTION_COLLECTOR, NEXT_EDIT_RESOLUTION_COLLECTOR,
};
use request_context::{RequestContext, RequestId, RequestSessionId, TenantInfo};
use uuid::Uuid;

/// Extracts request metadata from the request.
///
/// Args:
///     request_context: The request context
///     request_type: The request type
///     user: The user
///     req: The request
///
/// Returns:
///     The request metadata
pub fn extract_request_metadata(
    request_context: &RequestContext,
    request_type: RequestType,
    user: &User,
    req: &HttpRequest,
) -> RequestMetadata {
    let user_agent = req
        .headers()
        .get("user-agent")
        .map(|h| h.to_str().unwrap_or("not-utf8"))
        .unwrap_or("")
        .to_string();

    // see https://stackoverflow.com/questions/66989780/how-to-retrieve-the-ip-address-of-the-client-from-httprequest-in-actix-web
    let source_ip = req
        .connection_info()
        .realip_remote_addr()
        .unwrap_or("")
        .to_string();

    RequestMetadata {
        request_type: request_type.into(),
        session_id: request_context.request_session_id().to_string(),
        user_id: user.user_id.to_string(),
        opaque_user_id: Some(request_insight_publisher::auth_entities::UserId {
            user_id: user.opaque_user_id.user_id.clone(),
            user_id_type: user.opaque_user_id.user_id_type,
        }),
        user_email: user.user_email.clone(),
        user_agent,
        source_ip,
    }
}

#[async_trait]
#[allow(clippy::too_many_arguments)]
pub trait RequestInsightPublisher {
    async fn record_infer_request(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        infer_request: InferRequest,
    );

    async fn forward_completion_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId, // TODO(luke): NOT the ID of the ongoing request
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        accepted_idx: i32,
    );

    async fn record_completion_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    );

    async fn record_chat_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    );

    async fn record_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    );

    async fn record_remote_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    );

    async fn record_next_edit_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId, // TODO(luke): NOT the ID of the ongoing request
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
        annotated_text: Option<String>,
        annotated_instruction: Option<String>,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_instruction_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId, // ID of the original request
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_smart_paste_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        initial_request_time: prost_wkt_types::Timestamp,
        stream_finish_time: prost_wkt_types::Timestamp,
        apply_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_client_metric(
        &self,
        tenant_info: &TenantInfo,
        session_id: &RequestSessionId,
        event_time: prost_wkt_types::Timestamp,
        event_name: String,
        user_agent: String,
        client_metric: String,
        value: u64,
    );

    async fn record_next_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId, // TODO(luke): NOT the ID of the ongoing request
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_preference_sample(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        request_ids: &[RequestId],
        scores: &HashMap<String, i32>,
        feedback: String,
    );

    async fn record_http_response(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        code: u16,
        error_message: Option<&str>,
    );

    async fn record_request_events(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        events: Vec<RequestEvent>,
    );

    async fn record_session_events(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        user: &User,
        events: Vec<SessionEvent>,
    );

    async fn record_full_export_user_events(&self, request: RecordFullExportUserEventsRequest);

    async fn record_request_metadata(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        metadata: RequestMetadata,
    );

    async fn record_extension_error(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        original_request_id: Option<String>,
        message: String,
        stack_trace: String,
        diagnostics: HashMap<String, String>,
    );

    async fn record_find_missing(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        model_name: String,
        blob_count: i32,
        missing_count: i32,
        nonindexed_count: i32,
    );

    async fn record_batch_upload(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        blob_count: i32,
        total_size: i64,
    );

    async fn record_client_completion_timeline(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        initial_request_time: prost_wkt_types::Timestamp,
        api_start_time: prost_wkt_types::Timestamp,
        api_end_time: prost_wkt_types::Timestamp,
        emit_time: prost_wkt_types::Timestamp,
    );
}

#[derive(Clone)]
pub struct RequestInsightPublisherImpl {
    publisher: Arc<dyn request_insight_publisher::RequestInsightPublisher + Send + Sync + 'static>,
}

#[async_trait]
impl RequestInsightPublisher for RequestInsightPublisherImpl {
    async fn record_infer_request(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        infer_request: request_insight::InferRequest,
    ) {
        self.publisher
            .record_infer_request(request_context, tenant_info, infer_request)
            .await
    }

    async fn forward_completion_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        accepted_idx: i32,
    ) {
        COMPLETION_RESOLUTION_COLLECTOR
            .with_label_values(&[if accepted_idx == -1 {
                "rejected"
            } else {
                "accepted"
            }])
            .inc();

        self.publisher
            .forward_completion_resolution(
                tenant_info,
                request_id,
                emit_time,
                resolve_time,
                accepted_idx,
            )
            .await
    }

    async fn record_completion_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    ) {
        self.publisher
            .record_completion_feedback(tenant_info, request_id, rating, note)
            .await
    }

    async fn record_client_completion_timeline(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        initial_request_time: prost_wkt_types::Timestamp,
        api_start_time: prost_wkt_types::Timestamp,
        api_end_time: prost_wkt_types::Timestamp,
        emit_time: prost_wkt_types::Timestamp,
    ) {
        self.publisher
            .record_client_completion_timeline(
                tenant_info,
                original_request_id,
                initial_request_time,
                api_start_time,
                api_end_time,
                emit_time,
            )
            .await
    }

    async fn record_chat_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    ) {
        self.publisher
            .record_chat_feedback(tenant_info, request_id, rating, note, user_agent)
            .await
    }

    async fn record_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    ) {
        self.publisher
            .record_agent_feedback(tenant_info, request_id, rating, note, user_agent)
            .await
    }

    async fn record_remote_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    ) {
        self.publisher
            .record_remote_agent_feedback(tenant_info, request_id, rating, note, user_agent)
            .await
    }

    async fn record_next_edit_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    ) {
        self.publisher
            .record_next_edit_feedback(tenant_info, request_id, rating, note)
            .await
    }

    async fn record_instruction_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    ) {
        self.publisher
            .record_instruction_resolution(
                tenant_info,
                request_id,
                is_accepted_chunks,
                is_accept_all,
                is_reject_all,
                emit_time,
                resolve_time,
            )
            .await
    }

    async fn record_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
        annotated_text: Option<String>,
        annotated_instruction: Option<String>,
    ) {
        EDIT_RESOLUTION_COLLECTOR
            .with_label_values(&[if is_accepted { "accepted" } else { "rejected" }])
            .inc();

        self.publisher
            .record_edit_resolution(
                tenant_info,
                request_id,
                emit_time,
                resolve_time,
                is_accepted,
                annotated_text,
                annotated_instruction,
            )
            .await
    }

    async fn record_smart_paste_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId, // ID of the original request
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        initial_request_time: prost_wkt_types::Timestamp,
        stream_finish_time: prost_wkt_types::Timestamp,
        apply_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    ) {
        self.publisher
            .record_smart_paste_resolution(
                tenant_info,
                original_request_id,
                is_accepted_chunks,
                is_accept_all,
                is_reject_all,
                initial_request_time,
                stream_finish_time,
                apply_time,
                resolve_time,
            )
            .await
    }

    async fn record_client_metric(
        &self,
        tenant_info: &TenantInfo,
        session_id: &RequestSessionId,
        event_time: prost_wkt_types::Timestamp,
        event_name: String,
        user_agent: String,
        client_metric: String,
        value: u64,
    ) {
        self.publisher
            .record_client_metric(
                tenant_info,
                session_id,
                event_time,
                event_name,
                user_agent,
                client_metric,
                value,
            )
            .await
    }

    async fn record_next_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
    ) {
        NEXT_EDIT_RESOLUTION_COLLECTOR
            .with_label_values(&[if is_accepted { "accepted" } else { "rejected" }])
            .inc();

        self.publisher
            .record_next_edit_resolution(
                tenant_info,
                request_id,
                emit_time,
                resolve_time,
                is_accepted,
            )
            .await
    }

    async fn record_preference_sample(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        request_ids: &[RequestId],
        scores: &HashMap<String, i32>,
        feedback: String,
    ) {
        self.publisher
            .record_preference_sample(request_context, tenant_info, request_ids, scores, feedback)
            .await
    }

    async fn record_http_response(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        code: u16,
        error_message: Option<&str>,
    ) {
        self.publisher
            .record_http_response(request_context, tenant_info, code, error_message)
            .await
    }

    async fn record_request_events(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        events: Vec<RequestEvent>,
    ) {
        let record_request_events_request = UpdateRequestInfoRequest {
            request_id: request_context.request_id().to_string(),
            session_id: request_context.request_session_id().option_to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events,
        };
        self.publisher
            .record_request_events(record_request_events_request)
            .await
    }

    async fn record_session_events(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        user: &User,
        events: Vec<SessionEvent>,
    ) {
        let record_session_events_request = RecordSessionEventsRequest {
            session_id: request_context.request_session_id().to_string(),
            opaque_user_id: Some(request_insight_publisher::auth_entities::UserId {
                user_id: user.opaque_user_id.user_id.clone(),
                user_id_type: user.opaque_user_id.user_id_type,
            }),
            tenant_info: to_tenant_info_proto(tenant_info),
            events,
        };

        self.publisher
            .record_session_events(record_session_events_request)
            .await
    }

    async fn record_full_export_user_events(&self, request: RecordFullExportUserEventsRequest) {
        let req_clone = request.clone();
        self.publisher.record_full_export_user_events(request).await;

        // User events are being deprecated in favor of session events. During this migration,
        // duplicate all user events as session events.
        let mut session_events_request = RecordSessionEventsRequest {
            session_id: req_clone
                .session_id
                .expect("session_id should be filled in by api-proxy handler"),
            opaque_user_id: None,
            tenant_info: req_clone.tenant_info,
            events: Vec::new(),
        };
        for ue in req_clone.extension_data.unwrap().user_events {
            let mut se = SessionEvent {
                event_id: Some(Uuid::new_v4().to_string()),
                time: ue.time,
                event: None,
            };
            match ue.event {
                Some(full_export_user_event::Event::TextEdit(e)) => {
                    se.event = Some(session_event::Event::TextEdit(TextEditEvent {
                        reason: e.reason,
                        content_changes: e.content_changes,
                        file_path: ue.file_path,
                        after_changes_hash: e.after_changes_hash,
                        source_folder_root: e.source_folder_root,
                        hash_char_ranges: e.hash_char_ranges,
                        after_doc_length: e.after_doc_length,
                    }));
                }
                Some(full_export_user_event::Event::CompletionRequestIdIssued(e)) => {
                    se.event = Some(session_event::Event::CompletionRequestIdIssued(
                        CompletionRequestIdIssuedEvent {
                            request_id: e.request_id,
                            file_path: ue.file_path,
                        },
                    ));
                }
                Some(full_export_user_event::Event::EditRequestIdIssued(e)) => {
                    se.event = Some(session_event::Event::EditRequestIdIssued(
                        EditRequestIdIssuedEvent {
                            request_id: e.request_id,
                            file_path: ue.file_path,
                        },
                    ));
                }
                Some(full_export_user_event::Event::NextEditRequestIdIssued(e)) => {
                    se.event = Some(session_event::Event::NextEditRequestIdIssued(
                        NextEditRequestIdIssuedEvent {
                            request_id: e.request_id,
                            file_path: ue.file_path,
                        },
                    ));
                }
                _ => {
                    tracing::warn!("Skipping unknown event type in user/session event conversion");
                    continue;
                }
            };

            session_events_request.events.push(se);
        }

        self.publisher
            .record_session_events(session_events_request)
            .await;
    }

    async fn record_request_metadata(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        metadata: RequestMetadata,
    ) {
        self.publisher
            .record_request_metadata(request_context, tenant_info, metadata)
            .await
    }

    async fn record_extension_error(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        original_request_id: Option<String>,
        message: String,
        stack_trace: String,
        diagnostics: HashMap<String, String>,
    ) {
        self.publisher
            .record_extension_error(
                request_context,
                tenant_info,
                original_request_id,
                message,
                stack_trace,
                diagnostics,
            )
            .await
    }

    async fn record_find_missing(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        model_name: String,
        blob_count: i32,
        missing_count: i32,
        nonindexed_count: i32,
    ) {
        let event = request_insight::RequestEvent {
            time: Some(prost_wkt_types::Timestamp::from(
                std::time::SystemTime::now(),
            )),
            event: Some(request_insight::request_event::Event::FindMissing(
                request_insight::RiFindMissing {
                    model_name,
                    blob_count,
                    missing_count,
                    nonindexed_count,
                },
            )),
            ..Default::default()
        };

        self.record_request_events(request_context, tenant_info, vec![event])
            .await;
    }

    async fn record_batch_upload(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        blob_count: i32,
        total_size: i64,
    ) {
        let event = request_insight::RequestEvent {
            time: Some(prost_wkt_types::Timestamp::from(
                std::time::SystemTime::now(),
            )),
            event: Some(request_insight::request_event::Event::BatchUpload(
                request_insight::RiBatchUpload {
                    blob_count,
                    total_size,
                },
            )),
            ..Default::default()
        };

        self.record_request_events(request_context, tenant_info, vec![event])
            .await;
    }
}

impl RequestInsightPublisherImpl {
    pub async fn new(config: RequestInsightPublisherConfig) -> Self {
        let publisher =
            Arc::new(request_insight_publisher::RequestInsightPublisherImpl::new(config).await);
        Self { publisher }
    }
}

#[cfg(test)]
pub mod tests {
    use super::*;

    use async_trait::async_trait;
    use request_insight_publisher::request_insight::{self};
    use std::{collections::HashMap, sync::Arc};

    #[derive(Clone)]
    pub struct CompletionData {
        pub emit_time: prost_wkt_types::Timestamp,
        pub resolve_time: prost_wkt_types::Timestamp,
        pub accepted_idx: i32,
    }

    #[derive(Clone)]
    pub struct EditResolutionData {
        pub emit_time: prost_wkt_types::Timestamp,
        pub resolve_time: prost_wkt_types::Timestamp,
        pub is_accepted: bool,
        pub annotated_text: Option<String>,
        pub annotated_instruction: Option<String>,
    }

    #[derive(Clone)]
    pub struct InstructionResolutionData {
        pub is_accepted_chunks: Vec<bool>,
        pub is_accept_all: bool,
        pub is_reject_all: bool,
        pub emit_time: prost_wkt_types::Timestamp,
        pub resolve_time: prost_wkt_types::Timestamp,
    }

    #[derive(Clone)]
    pub struct SmartPasteResolutionData {
        pub initial_request_time: prost_wkt_types::Timestamp,
        pub stream_finish_time: prost_wkt_types::Timestamp,
        pub apply_time: prost_wkt_types::Timestamp,
        pub resolve_time: prost_wkt_types::Timestamp,
        pub is_accepted_chunks: Vec<bool>,
        pub is_accept_all: bool,
        pub is_reject_all: bool,
    }

    #[derive(Clone)]
    pub struct ClientMetricData {
        pub session_id: String,
        pub event_time: prost_wkt_types::Timestamp,
        pub event_name: String,
        pub user_agent: String,
        pub client_metric: String,
        pub value: u64,
    }

    #[derive(Clone)]
    pub struct NextEditResolutionData {
        pub emit_time: prost_wkt_types::Timestamp,
        pub resolve_time: prost_wkt_types::Timestamp,
        pub is_accepted: bool,
    }

    #[derive(Clone)]
    pub struct FeedbackData {
        pub rating: i32,
        pub note: String,
    }

    #[derive(Clone)]
    pub struct PreferenceData {
        pub request_ids: Vec<RequestId>,
        pub scores: HashMap<String, i32>,
        pub feedback: String,
    }

    #[derive(Clone)]
    pub struct ClientTimelineData {
        pub initial_request_time: prost_wkt_types::Timestamp,
        pub api_start_time: prost_wkt_types::Timestamp,
        pub api_end_time: prost_wkt_types::Timestamp,
        pub emit_time: prost_wkt_types::Timestamp,
    }

    type ClientMetricMap = HashMap<RequestSessionId, ClientMetricData>;
    type ResolutionMap = HashMap<RequestId, CompletionData>;
    type EditResolutionMap = HashMap<RequestId, EditResolutionData>;
    type InstructionResolutionMap = HashMap<RequestId, InstructionResolutionData>;
    type SmartPasteResolutionMap = HashMap<RequestId, SmartPasteResolutionData>;
    type NextEditResolutionMap = HashMap<RequestId, NextEditResolutionData>;
    type FeedbackMap = HashMap<RequestId, FeedbackData>;
    type UserEventsMap =
        HashMap<RequestSessionId, request_insight::RecordFullExportUserEventsRequest>;
    type RequestEventsMap = HashMap<RequestId, request_insight::UpdateRequestInfoRequest>;
    type SessionEventsMap = HashMap<RequestSessionId, request_insight::RecordSessionEventsRequest>;
    type RequestMetadataMap = HashMap<RequestId, request_insight::RequestMetadata>;
    type PreferenceMap = HashMap<RequestId, PreferenceData>;
    type ClientTimelineMap = HashMap<RequestId, ClientTimelineData>;

    pub struct FakeRequestInsightPublisher {
        pub resolutions: Arc<std::sync::Mutex<ResolutionMap>>,
        pub chat_feedback: Arc<std::sync::Mutex<FeedbackMap>>,
        pub agent_feedback: Arc<std::sync::Mutex<FeedbackMap>>,
        pub remote_agent_feedback: Arc<std::sync::Mutex<FeedbackMap>>,
        pub next_edit_feedback: Arc<std::sync::Mutex<FeedbackMap>>,
        pub client_metrics: Arc<std::sync::Mutex<ClientMetricMap>>,
        pub edit_resolutions: Arc<std::sync::Mutex<EditResolutionMap>>,
        pub instruction_resolutions: Arc<std::sync::Mutex<InstructionResolutionMap>>,
        pub smart_paste_resolutions: Arc<std::sync::Mutex<SmartPasteResolutionMap>>,
        pub feedback: Arc<std::sync::Mutex<FeedbackMap>>,
        pub next_edit_resolutions: Arc<std::sync::Mutex<NextEditResolutionMap>>,
        pub request_events: Arc<std::sync::Mutex<RequestEventsMap>>,
        pub session_events: Arc<std::sync::Mutex<SessionEventsMap>>,
        // Map from session id to the last RecordFullExportUserEventsRequest for that session.
        pub user_events: Arc<std::sync::Mutex<UserEventsMap>>,
        pub request_metadata: Arc<std::sync::Mutex<RequestMetadataMap>>,
        pub preferences: Arc<std::sync::Mutex<PreferenceMap>>,
        pub client_timelines: Arc<std::sync::Mutex<ClientTimelineMap>>,
    }

    impl FakeRequestInsightPublisher {
        pub fn new() -> FakeRequestInsightPublisher {
            FakeRequestInsightPublisher {
                resolutions: Arc::new(std::sync::Mutex::new(ResolutionMap::new())),
                chat_feedback: Arc::new(std::sync::Mutex::new(FeedbackMap::new())),
                agent_feedback: Arc::new(std::sync::Mutex::new(FeedbackMap::new())),
                remote_agent_feedback: Arc::new(std::sync::Mutex::new(FeedbackMap::new())),
                next_edit_feedback: Arc::new(std::sync::Mutex::new(FeedbackMap::new())),
                client_metrics: Arc::new(std::sync::Mutex::new(ClientMetricMap::new())),
                edit_resolutions: Arc::new(std::sync::Mutex::new(EditResolutionMap::new())),
                instruction_resolutions: Arc::new(std::sync::Mutex::new(
                    InstructionResolutionMap::new(),
                )),
                smart_paste_resolutions: Arc::new(std::sync::Mutex::new(
                    SmartPasteResolutionMap::new(),
                )),
                feedback: Arc::new(std::sync::Mutex::new(FeedbackMap::new())),
                next_edit_resolutions: Arc::new(
                    std::sync::Mutex::new(NextEditResolutionMap::new()),
                ),
                user_events: Arc::new(std::sync::Mutex::new(UserEventsMap::new())),
                request_events: Arc::new(std::sync::Mutex::new(RequestEventsMap::new())),
                session_events: Arc::new(std::sync::Mutex::new(SessionEventsMap::new())),
                request_metadata: Arc::new(std::sync::Mutex::new(RequestMetadataMap::new())),
                preferences: Arc::new(std::sync::Mutex::new(PreferenceMap::new())),
                client_timelines: Arc::new(std::sync::Mutex::new(ClientTimelineMap::new())),
            }
        }

        pub fn _get_completion_data(&self, request_id: &RequestId) -> Option<CompletionData> {
            self.resolutions.lock().unwrap().get(request_id).cloned()
        }

        pub fn _get_next_edit_resolution_data(
            &self,
            request_id: &RequestId,
        ) -> Option<NextEditResolutionData> {
            self.next_edit_resolutions
                .lock()
                .unwrap()
                .get(request_id)
                .cloned()
        }

        pub fn _get_edit_resolution_data(
            &self,
            request_id: &RequestId,
        ) -> Option<EditResolutionData> {
            self.edit_resolutions
                .lock()
                .unwrap()
                .get(request_id)
                .cloned()
        }

        pub fn _get_instruction_resolution_data(
            &self,
            request_id: &RequestId,
        ) -> Option<InstructionResolutionData> {
            self.instruction_resolutions
                .lock()
                .unwrap()
                .get(request_id)
                .cloned()
        }

        pub fn _get_client_metric_data(
            &self,
            session_id: &RequestSessionId,
        ) -> Option<ClientMetricData> {
            self.client_metrics.lock().unwrap().get(session_id).cloned()
        }

        pub fn _get_feedback(&self, request_id: &RequestId) -> Option<FeedbackData> {
            self.feedback.lock().unwrap().get(request_id).cloned()
        }

        pub fn _get_chat_feedback(&self, request_id: &RequestId) -> Option<FeedbackData> {
            self.chat_feedback.lock().unwrap().get(request_id).cloned()
        }

        pub fn _get_agent_feedback(&self, request_id: &RequestId) -> Option<FeedbackData> {
            self.agent_feedback.lock().unwrap().get(request_id).cloned()
        }

        async fn _get_next_edit_feedback(&self, request_id: &RequestId) -> Option<FeedbackData> {
            self.next_edit_feedback
                .lock()
                .unwrap()
                .get(request_id)
                .cloned()
        }

        pub fn _get_request_events(
            &self,
            request_id: &RequestId,
        ) -> Option<request_insight::UpdateRequestInfoRequest> {
            self.request_events.lock().unwrap().get(request_id).cloned()
        }

        pub fn _get_user_events(
            &self,
            session_id: &RequestSessionId,
        ) -> Option<request_insight::RecordFullExportUserEventsRequest> {
            self.user_events.lock().unwrap().get(session_id).cloned()
        }

        pub fn _get_session_events(
            &self,
            session_id: &RequestSessionId,
        ) -> Option<request_insight::RecordSessionEventsRequest> {
            self.session_events.lock().unwrap().get(session_id).cloned()
        }

        pub fn _get_request_metadata(
            &self,
            request_id: &RequestId,
        ) -> Option<request_insight::RequestMetadata> {
            self.request_metadata
                .lock()
                .unwrap()
                .get(request_id)
                .cloned()
        }
        pub fn _get_preference_data(&self, request_id: &RequestId) -> Option<PreferenceData> {
            self.preferences.lock().unwrap().get(request_id).cloned()
        }

        pub fn _get_client_completion_timelines(
            &self,
            request_id: &RequestId,
        ) -> Option<ClientTimelineData> {
            self.client_timelines
                .lock()
                .unwrap()
                .get(request_id)
                .cloned()
        }
    }

    #[async_trait]
    impl RequestInsightPublisher for FakeRequestInsightPublisher {
        async fn record_infer_request(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            _infer_request: request_insight::InferRequest,
        ) {
        }

        async fn forward_completion_resolution(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            emit_time: prost_wkt_types::Timestamp,
            resolve_time: prost_wkt_types::Timestamp,
            accepted_idx: i32,
        ) {
            self.resolutions.lock().unwrap().insert(
                *request_id,
                CompletionData {
                    emit_time,
                    resolve_time,
                    accepted_idx,
                },
            );
        }

        async fn record_next_edit_resolution(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            emit_time: prost_wkt_types::Timestamp,
            resolve_time: prost_wkt_types::Timestamp,
            is_accepted: bool,
        ) {
            self.next_edit_resolutions.lock().unwrap().insert(
                *request_id,
                NextEditResolutionData {
                    emit_time,
                    resolve_time,
                    is_accepted,
                },
            );
        }

        async fn record_client_metric(
            &self,
            _tenant_info: &TenantInfo,
            session_id: &RequestSessionId,
            event_time: prost_wkt_types::Timestamp,
            event_name: String,
            user_agent: String,
            client_metric: String,
            value: u64,
        ) {
            self.client_metrics.lock().unwrap().insert(
                *session_id,
                ClientMetricData {
                    session_id: session_id.to_string(),
                    event_time,
                    event_name,
                    user_agent,
                    client_metric,
                    value,
                },
            );
        }

        async fn record_completion_feedback(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            _rating: i32,
            _note: &str,
        ) {
            self.feedback.lock().unwrap().insert(
                *request_id,
                FeedbackData {
                    rating: _rating,
                    note: _note.to_string(),
                },
            );
        }

        async fn record_chat_feedback(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            rating: i32,
            note: &str,
            _user_agent: &str,
        ) {
            self.chat_feedback.lock().unwrap().insert(
                *request_id,
                FeedbackData {
                    rating,
                    note: note.to_string(),
                },
            );
        }

        async fn record_find_missing(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            _model_name: String,
            _blob_count: i32,
            _missing_count: i32,
            _nonindexed_count: i32,
        ) {
            // No-op for the fake implementation
        }

        async fn record_batch_upload(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            _blob_count: i32,
            _total_size: i64,
        ) {
            // No-op for the fake implementation
        }

        async fn record_agent_feedback(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            rating: i32,
            note: &str,
            _user_agent: &str,
        ) {
            self.agent_feedback.lock().unwrap().insert(
                *request_id,
                FeedbackData {
                    rating,
                    note: note.to_string(),
                },
            );
        }

        async fn record_remote_agent_feedback(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            rating: i32,
            note: &str,
            _user_agent: &str,
        ) {
            self.remote_agent_feedback.lock().unwrap().insert(
                *request_id,
                FeedbackData {
                    rating,
                    note: note.to_string(),
                },
            );
        }

        async fn record_next_edit_feedback(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            _rating: i32,
            _note: &str,
        ) {
            self.next_edit_feedback.lock().unwrap().insert(
                *request_id,
                FeedbackData {
                    rating: _rating,
                    note: _note.to_string(),
                },
            );
        }

        async fn record_instruction_resolution(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            is_accepted_chunks: Vec<bool>,
            is_accept_all: bool,
            is_reject_all: bool,
            emit_time: prost_wkt_types::Timestamp,
            resolve_time: prost_wkt_types::Timestamp,
        ) {
            self.instruction_resolutions.lock().unwrap().insert(
                *request_id,
                InstructionResolutionData {
                    is_accepted_chunks,
                    is_accept_all,
                    is_reject_all,
                    emit_time,
                    resolve_time,
                },
            );
        }

        async fn record_edit_resolution(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            emit_time: prost_wkt_types::Timestamp,
            resolve_time: prost_wkt_types::Timestamp,
            is_accepted: bool,
            annotated_text: Option<String>,
            annotated_instruction: Option<String>,
        ) {
            self.edit_resolutions.lock().unwrap().insert(
                *request_id,
                EditResolutionData {
                    emit_time,
                    resolve_time,
                    is_accepted,
                    annotated_text,
                    annotated_instruction,
                },
            );
        }

        async fn record_smart_paste_resolution(
            &self,
            _tenant_info: &TenantInfo,
            request_id: &RequestId,
            is_accepted_chunks: Vec<bool>,
            is_accept_all: bool,
            is_reject_all: bool,
            initial_request_time: prost_wkt_types::Timestamp,
            stream_finish_time: prost_wkt_types::Timestamp,
            apply_time: prost_wkt_types::Timestamp,
            resolve_time: prost_wkt_types::Timestamp,
        ) {
            self.smart_paste_resolutions.lock().unwrap().insert(
                *request_id,
                SmartPasteResolutionData {
                    initial_request_time,
                    stream_finish_time,
                    apply_time,
                    resolve_time,
                    is_accepted_chunks,
                    is_accept_all,
                    is_reject_all,
                },
            );
        }

        async fn record_preference_sample(
            &self,
            request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            request_ids: &[RequestId],
            scores: &HashMap<String, i32>,
            feedback: String,
        ) {
            self.preferences.lock().unwrap().insert(
                request_context.request_id(),
                PreferenceData {
                    request_ids: request_ids.to_vec(),
                    scores: scores.clone(),
                    feedback,
                },
            );
        }

        async fn record_request_events(
            &self,
            request_context: &RequestContext,
            tenant_info: &TenantInfo,
            events: Vec<request_insight::RequestEvent>,
        ) {
            self.request_events.lock().unwrap().insert(
                request_context.request_id(),
                request_insight::UpdateRequestInfoRequest {
                    request_id: request_context.request_id().to_string(),
                    session_id: request_context.request_session_id().option_to_string(),
                    tenant_info: request_insight_publisher::to_tenant_info_proto(tenant_info),
                    events,
                },
            );
        }

        async fn record_session_events(
            &self,
            request_context: &RequestContext,
            tenant_info: &TenantInfo,
            user: &User,
            events: Vec<request_insight::SessionEvent>,
        ) {
            self.session_events.lock().unwrap().insert(
                request_context.request_session_id(),
                request_insight::RecordSessionEventsRequest {
                    session_id: request_context.request_session_id().to_string(),
                    opaque_user_id: Some(request_insight_publisher::auth_entities::UserId {
                        user_id: user.opaque_user_id.user_id.clone(),
                        user_id_type: user.opaque_user_id.user_id_type,
                    }),
                    tenant_info: request_insight_publisher::to_tenant_info_proto(tenant_info),
                    events,
                },
            );
        }

        async fn record_full_export_user_events(
            &self,
            request: request_insight::RecordFullExportUserEventsRequest,
        ) {
            let session_id_str = request.session_id.clone().unwrap();
            let session_id = RequestSessionId::try_from(&session_id_str).unwrap();
            self.user_events.lock().unwrap().insert(session_id, request);
        }

        async fn record_http_response(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            _code: u16,
            _error_message: Option<&str>,
        ) {
        }

        async fn record_request_metadata(
            &self,
            request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            metadata: request_insight::RequestMetadata,
        ) {
            self.request_metadata
                .lock()
                .unwrap()
                .insert(request_context.request_id(), metadata);
        }

        async fn record_extension_error(
            &self,
            _request_context: &RequestContext,
            _tenant_info: &TenantInfo,
            _original_request_id: Option<String>,
            _message: String,
            _stack_trace: String,
            _diagnostics: HashMap<String, String>,
        ) {
        }

        async fn record_client_completion_timeline(
            &self,
            _tenant_info: &TenantInfo,
            original_request_id: &RequestId,
            initial_request_time: prost_wkt_types::Timestamp,
            api_start_time: prost_wkt_types::Timestamp,
            api_end_time: prost_wkt_types::Timestamp,
            emit_time: prost_wkt_types::Timestamp,
        ) {
            self.client_timelines.lock().unwrap().insert(
                *original_request_id,
                ClientTimelineData {
                    initial_request_time,
                    api_start_time,
                    api_end_time,
                    emit_time,
                },
            );
        }
    }
}
