use std::convert::TryFrom;

use actix_web::{HttpRequest, HttpResponse};
use request_context::TenantInfo;
use tonic::{Code, Status};
use tracing_actix_web::RootSpan;

use crate::agents;
use crate::chat;
use crate::handler_utils::gate_on_circuit_breaker;
use crate::handler_utils::{
    convert_blobs_and_names, request_context_from_req, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,
};
use crate::model_registry::ModelRegistry;
use crate::public_api_proto;
use crate::request_insight_util::extract_request_metadata;
use content_manager_client::ContentManagerClient;
use request_insight_publisher::request_insight;

// Endpoints for features not yet released to the user/tenant/namespace return 404
// ENABLE_AGENTS was the original flag for this. Now that feature flags exist for the
// features themselves, consult them so if a feature is released we honor that.
pub const ENABLE_AGENTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("enable_agents", false);
pub const VSCODE_AGENT_MODE_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("vscode_agent_mode_min_version", "");
pub const VSCODE_AGENT_MODE_STABLE_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("vscode_agent_mode_min_stable_version", "");
pub const INTELLIJ_AGENT_MODE_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("intellij_agent_mode_min_version", "");

// The circuit breaker flag, in contrast, cuts off access to an already public endpoint, making
// it respond with "resource exhausted"
pub const CB_AGENTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_agents", false);

fn gate_agents(
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
    req: &HttpRequest,
    tenant_info: &TenantInfo,
) -> Result<(), Status> {
    gate_on_circuit_breaker(&CB_AGENTS, feature_flags, req, tenant_info)?;
    if ENABLE_AGENTS.get_from(feature_flags)
        || VSCODE_AGENT_MODE_FLAG.get_from(feature_flags) != ""
        || INTELLIJ_AGENT_MODE_FLAG.get_from(feature_flags) != ""
        || VSCODE_AGENT_MODE_STABLE_FLAG.get_from(feature_flags) != ""
    {
        // Any of these flags being set means the feature IS available to the
        // user if using the correct client
        return Ok(());
    }
    Err(Status::not_found("Not found"))
}

impl TryFrom<public_api_proto::ChatRequestNode> for chat::ChatRequestNode {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::ChatRequestNode) -> Result<Self, Self::Error> {
        Ok(chat::ChatRequestNode {
            id: from.id,
            r#type: from.r#type,
            text_node: from.text_node.map(|text_node| chat::ChatRequestText {
                content: text_node.content,
            }),
            tool_result_node: from.tool_result_node.map(|tool_result_node| {
                chat::ChatRequestToolResult {
                    tool_use_id: tool_result_node.tool_use_id,
                    content: tool_result_node.content,
                    is_error: tool_result_node.is_error,
                    request_id: tool_result_node.request_id,
                    content_nodes: tool_result_node
                        .content_nodes
                        .into_iter()
                        .map(|content_node| chat::ToolResultContentNode {
                            r#type: content_node.r#type,
                            text_content: content_node.text_content,
                            image_content: content_node.image_content.map(|image_content| {
                                chat::ChatRequestImage {
                                    image_data: image_content.image_data,
                                    format: image_content.format,
                                }
                            }),
                        })
                        .collect(),
                }
            }),
            image_node: from.image_node.map(|image_node| chat::ChatRequestImage {
                image_data: image_node.image_data,
                format: image_node.format,
            }),
            ide_state_node: from.ide_state_node.map(|ide_state_node| {
                let mut chat_ide_state = chat::ChatRequestIdeState::default();

                // Copy workspace folders from the request
                for folder_info in &ide_state_node.workspace_folders {
                    chat_ide_state
                        .workspace_folders
                        .push(chat::WorkspaceFolderInfo {
                            repository_root: folder_info.repository_root.clone(),
                            folder_root: folder_info.folder_root.clone(),
                        });
                }

                // Set workspace_folders_unchanged flag
                chat_ide_state.workspace_folders_unchanged =
                    ide_state_node.workspace_folders_unchanged;

                if let Some(current_terminal) = &ide_state_node.current_terminal {
                    chat_ide_state.current_terminal = Some(chat::TerminalInfo {
                        terminal_id: current_terminal.terminal_id,
                        current_working_directory: current_terminal
                            .current_working_directory
                            .clone(),
                    });
                }

                chat_ide_state
            }),
            edit_events_node: from.edit_events_node.map(|edit_events_node| {
                chat::ChatRequestEditEvents {
                    edit_events: edit_events_node
                        .edit_events
                        .into_iter()
                        .map(|event| chat::ChatRequestFileEdit {
                            path: event.path,
                            before_blob_name: event.before_blob_name,
                            after_blob_name: event.after_blob_name,
                            edits: event
                                .edits
                                .into_iter()
                                .map(|edit| chat::ChatRequestSingleEdit {
                                    before_line_start: edit.before_line_start,
                                    before_text: edit.before_text,
                                    after_line_start: edit.after_line_start,
                                    after_text: edit.after_text,
                                })
                                .collect(),
                        })
                        .collect(),
                    source: match edit_events_node.source {
                        Some(1) => Some(chat::EditEventSource::UserEdit as i32),
                        Some(2) => Some(chat::EditEventSource::CheckpointRevert as i32),
                        _ => Some(chat::EditEventSource::Unspecified as i32),
                    },
                }
            }),
        })
    }
}

impl TryFrom<public_api_proto::ChatResultNode> for chat::ChatResultNode {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::ChatResultNode) -> Result<Self, Self::Error> {
        Ok(chat::ChatResultNode {
            id: from.id,
            r#type: from.r#type,
            content: from.content,
            tool_use: from.tool_use.map(|tool_use| chat::ChatResultToolUse {
                tool_use_id: tool_use.tool_use_id,
                tool_name: tool_use.tool_name,
                input_json: tool_use.input_json,
                is_partial: tool_use.is_partial,
            }),
        })
    }
}

impl From<chat::ChatResultNode> for public_api_proto::ChatResultNode {
    fn from(from: chat::ChatResultNode) -> Self {
        public_api_proto::ChatResultNode {
            id: from.id,
            r#type: from.r#type,
            content: from.content,
            tool_use: from
                .tool_use
                .map(|tool_use| public_api_proto::ChatResultToolUse {
                    tool_use_id: tool_use.tool_use_id,
                    tool_name: tool_use.tool_name,
                    input_json: tool_use.input_json,
                    is_partial: tool_use.is_partial,
                }),
        }
    }
}

impl TryFrom<public_api_proto::ToolDefinition> for chat::ToolDefinition {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::ToolDefinition) -> Result<Self, Self::Error> {
        Ok(chat::ToolDefinition {
            name: from.name,
            description: from.description,
            input_schema_json: from.input_schema_json,
        })
    }
}

impl TryFrom<public_api_proto::ToolChoice> for chat::ToolChoice {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::ToolChoice) -> Result<Self, Self::Error> {
        Ok(chat::ToolChoice {
            r#type: from.r#type,
            name: from.name,
        })
    }
}

impl TryFrom<public_api_proto::Exchange> for chat::Exchange {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::Exchange) -> Result<Self, Self::Error> {
        Ok(chat::Exchange {
            request_message: from.request_message,
            response_text: from.response_text,
            request_id: from.request_id,
            request_nodes: from
                .request_nodes
                .into_iter()
                .filter(|node| {
                    node.r#type != public_api_proto::ChatRequestNodeType::CheckpointRef as i32
                })
                .map(TryInto::try_into)
                .collect::<Result<Vec<_>, _>>()?,
            response_nodes: from
                .response_nodes
                .into_iter()
                .map(TryInto::try_into)
                .collect::<Result<Vec<_>, _>>()?,
        })
    }
}

impl TryFrom<public_api_proto::LlmGenerateRequest> for agents::LlmGenerateRequest {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::LlmGenerateRequest) -> Result<Self, Self::Error> {
        Ok(agents::LlmGenerateRequest {
            model_name: from.model_name,
            user_message: from
                .user_message
                .into_iter()
                .map(TryInto::try_into)
                .collect::<Result<Vec<_>, _>>()?,
            dialog: from
                .dialog
                .into_iter()
                .map(TryInto::try_into)
                .collect::<Result<Vec<_>, _>>()?,
            max_tokens: from.max_tokens,
            system_prompt: from.system_prompt,
            temperature: from.temperature,
            tool_definitions: from
                .tool_definitions
                .into_iter()
                .map(TryInto::try_into)
                .collect::<Result<Vec<_>, _>>()?,
            tool_choice: from.tool_choice.map(TryInto::try_into).transpose()?,
        })
    }
}

impl TryFrom<public_api_proto::CodebaseRetrievalRequest> for agents::CodebaseRetrievalRequest {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::CodebaseRetrievalRequest) -> Result<Self, Self::Error> {
        Ok(agents::CodebaseRetrievalRequest {
            information_request: from.information_request,
            dialog: from
                .dialog
                .into_iter()
                .map(TryInto::try_into)
                .collect::<Result<Vec<_>, _>>()?,
            blobs: vec![convert_blobs_and_names(&from.blobs, None)?],
            max_output_length: from.max_output_length,
            disable_codebase_retrieval: from.disable_codebase_retrieval,
            enable_commit_retrieval: from.enable_commit_retrieval,
        })
    }
}

impl TryFrom<public_api_proto::EditFileRequest> for agents::EditFileRequest {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::EditFileRequest) -> Result<Self, Self::Error> {
        Ok(agents::EditFileRequest {
            file_path: from.file_path,
            edit_summary: from.edit_summary,
            detailed_edit_description: from.detailed_edit_description,
            file_contents: from.file_contents,
        })
    }
}

impl From<agents::LlmGenerateResponse> for public_api_proto::LlmGenerateResponse {
    fn from(from: agents::LlmGenerateResponse) -> Self {
        public_api_proto::LlmGenerateResponse {
            response_nodes: from.response_nodes.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<agents::CodebaseRetrievalResponse> for public_api_proto::CodebaseRetrievalResponse {
    fn from(from: agents::CodebaseRetrievalResponse) -> Self {
        public_api_proto::CodebaseRetrievalResponse {
            formatted_retrieval: from.formatted_retrieval,
        }
    }
}

impl From<agents::EditFileResponse> for public_api_proto::EditFileResponse {
    fn from(from: agents::EditFileResponse) -> Self {
        public_api_proto::EditFileResponse {
            modified_file_contents: from.modified_file_contents,
            is_error: from.is_error,
        }
    }
}

impl From<agents::ListRemoteToolsResponse> for public_api_proto::ListRemoteToolsResponse {
    fn from(from: agents::ListRemoteToolsResponse) -> Self {
        public_api_proto::ListRemoteToolsResponse {
            tools: from.tools.into_iter().map(Into::into).collect(),
        }
    }
}

impl From<chat::ToolDefinition> for public_api_proto::ToolDefinition {
    fn from(from: chat::ToolDefinition) -> Self {
        public_api_proto::ToolDefinition {
            name: from.name,
            description: from.description,
            input_schema_json: from.input_schema_json,
        }
    }
}

impl TryFrom<public_api_proto::RemoteToolId> for agents::RemoteToolId {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::RemoteToolId) -> Result<Self, Self::Error> {
        match from {
            public_api_proto::RemoteToolId::Unknown => Ok(agents::RemoteToolId::Unknown),
            public_api_proto::RemoteToolId::WebSearch => Ok(agents::RemoteToolId::WebSearch),
            public_api_proto::RemoteToolId::JiraSearch => Ok(agents::RemoteToolId::JiraSearch),
            public_api_proto::RemoteToolId::JiraIssue => Ok(agents::RemoteToolId::JiraIssue),
            public_api_proto::RemoteToolId::JiraProject => Ok(agents::RemoteToolId::JiraProject),
            public_api_proto::RemoteToolId::Jira => Ok(agents::RemoteToolId::Jira),
            public_api_proto::RemoteToolId::LinearSearchIssues => {
                Ok(agents::RemoteToolId::LinearSearchIssues)
            }
            public_api_proto::RemoteToolId::Linear => Ok(agents::RemoteToolId::Linear),
            public_api_proto::RemoteToolId::GithubApi => Ok(agents::RemoteToolId::GithubApi),
            public_api_proto::RemoteToolId::NotionSearch => Ok(agents::RemoteToolId::NotionSearch),
            public_api_proto::RemoteToolId::NotionPage => Ok(agents::RemoteToolId::NotionPage),
            public_api_proto::RemoteToolId::Notion => Ok(agents::RemoteToolId::Notion),
            public_api_proto::RemoteToolId::ConfluenceSearch => {
                Ok(agents::RemoteToolId::ConfluenceSearch)
            }
            public_api_proto::RemoteToolId::ConfluenceContent => {
                Ok(agents::RemoteToolId::ConfluenceContent)
            }
            public_api_proto::RemoteToolId::ConfluenceSpace => {
                Ok(agents::RemoteToolId::ConfluenceSpace)
            }
            public_api_proto::RemoteToolId::Confluence => Ok(agents::RemoteToolId::Confluence),
            public_api_proto::RemoteToolId::Supabase => Ok(agents::RemoteToolId::Supabase),
            public_api_proto::RemoteToolId::Glean => Ok(agents::RemoteToolId::Glean),
        }
    }
}

impl TryFrom<agents::RemoteToolId> for public_api_proto::RemoteToolId {
    type Error = tonic::Status;

    fn try_from(from: agents::RemoteToolId) -> Result<Self, Self::Error> {
        match from {
            agents::RemoteToolId::Unknown => Ok(public_api_proto::RemoteToolId::Unknown),
            agents::RemoteToolId::WebSearch => Ok(public_api_proto::RemoteToolId::WebSearch),
            agents::RemoteToolId::JiraSearch => Ok(public_api_proto::RemoteToolId::JiraSearch),
            agents::RemoteToolId::JiraIssue => Ok(public_api_proto::RemoteToolId::JiraIssue),
            agents::RemoteToolId::JiraProject => Ok(public_api_proto::RemoteToolId::JiraProject),
            agents::RemoteToolId::Jira => Ok(public_api_proto::RemoteToolId::Jira),
            agents::RemoteToolId::LinearSearchIssues => {
                Ok(public_api_proto::RemoteToolId::LinearSearchIssues)
            }
            agents::RemoteToolId::Linear => Ok(public_api_proto::RemoteToolId::Linear),
            agents::RemoteToolId::GithubApi => Ok(public_api_proto::RemoteToolId::GithubApi),
            agents::RemoteToolId::NotionSearch => Ok(public_api_proto::RemoteToolId::NotionSearch),
            agents::RemoteToolId::NotionPage => Ok(public_api_proto::RemoteToolId::NotionPage),
            agents::RemoteToolId::Notion => Ok(public_api_proto::RemoteToolId::Notion),
            agents::RemoteToolId::ConfluenceSearch => {
                Ok(public_api_proto::RemoteToolId::ConfluenceSearch)
            }
            agents::RemoteToolId::ConfluenceContent => {
                Ok(public_api_proto::RemoteToolId::ConfluenceContent)
            }
            agents::RemoteToolId::ConfluenceSpace => {
                Ok(public_api_proto::RemoteToolId::ConfluenceSpace)
            }
            agents::RemoteToolId::Confluence => Ok(public_api_proto::RemoteToolId::Confluence),
            agents::RemoteToolId::Supabase => Ok(public_api_proto::RemoteToolId::Supabase),
            agents::RemoteToolId::Glean => Ok(public_api_proto::RemoteToolId::Glean),
        }
    }
}

impl From<agents::ListRemoteToolsResponseMessage>
    for public_api_proto::ListRemoteToolsResponseMessage
{
    fn from(from: agents::ListRemoteToolsResponseMessage) -> Self {
        public_api_proto::ListRemoteToolsResponseMessage {
            tool_definition: from.tool_definition.map(Into::into),
            remote_tool_id: from.remote_tool_id,
            availability_status: from.availability_status,
            tool_safety: from.tool_safety,
            oauth_url: from.oauth_url,
        }
    }
}

impl From<agents::RemoteToolResponseStatus> for public_api_proto::RemoteToolResponseStatus {
    fn from(from: agents::RemoteToolResponseStatus) -> Self {
        match from {
            agents::RemoteToolResponseStatus::ToolExecutionUnknownStatus => {
                public_api_proto::RemoteToolResponseStatus::ToolExecutionUnknownStatus
            }
            agents::RemoteToolResponseStatus::ToolExecutionSuccess => {
                public_api_proto::RemoteToolResponseStatus::ToolExecutionSuccess
            }
            agents::RemoteToolResponseStatus::ToolNotFound => {
                public_api_proto::RemoteToolResponseStatus::ToolNotFound
            }
            agents::RemoteToolResponseStatus::InvalidToolInput => {
                public_api_proto::RemoteToolResponseStatus::InvalidToolInput
            }
            agents::RemoteToolResponseStatus::ToolExecutionError => {
                public_api_proto::RemoteToolResponseStatus::ToolExecutionError
            }
            agents::RemoteToolResponseStatus::ToolNotAvailable => {
                public_api_proto::RemoteToolResponseStatus::ToolNotAvailable
            }
            agents::RemoteToolResponseStatus::ToolAuthenticationError => {
                public_api_proto::RemoteToolResponseStatus::ToolAuthenticationError
            }
        }
    }
}

#[allow(deprecated)]
impl From<agents::RunRemoteToolResponse> for public_api_proto::RunRemoteToolResponse {
    fn from(from: agents::RunRemoteToolResponse) -> Self {
        public_api_proto::RunRemoteToolResponse {
            tool_output: from.tool_output,
            tool_result_message: from.tool_result_message,
            is_error: from.is_error,
            status: from.status,
        }
    }
}

impl TryFrom<public_api_proto::ListRemoteToolsRequest> for agents::ListRemoteToolsRequest {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::ListRemoteToolsRequest) -> Result<Self, Self::Error> {
        Ok(agents::ListRemoteToolsRequest {
            tool_ids: from
                .tool_id_list
                .map(|list| list.tool_ids)
                .unwrap_or_default()
                .into_iter()
                .map(|t| {
                    public_api_proto::RemoteToolId::try_from(t)
                        .map(|tool| tool as i32)
                        .unwrap_or_else(|e| {
                            tracing::warn!("Failed to convert tool name: {}, error: {}", t, e);
                            public_api_proto::RemoteToolId::Unknown as i32
                        })
                })
                .collect(),
        })
    }
}

impl TryFrom<public_api_proto::RunRemoteToolRequest> for agents::RunRemoteToolRequest {
    type Error = tonic::Status;

    #[allow(deprecated)]
    fn try_from(from: public_api_proto::RunRemoteToolRequest) -> Result<Self, Self::Error> {
        Ok(agents::RunRemoteToolRequest {
            tool_name: from.tool_name,
            tool_id: from
                .tool_id
                .map(|tool_id| {
                    agents::RemoteToolId::try_from(tool_id)
                        .map(|tool| tool as i32)
                        .unwrap_or_else(|e| {
                            tracing::warn!(
                                "Failed to convert tool name: {}, error: {}",
                                tool_id,
                                e
                            );
                            agents::RemoteToolId::Unknown as i32
                        })
                })
                .unwrap_or(agents::RemoteToolId::Unknown as i32),
            tool_input_json: from.tool_input_json,
            extra_tool_input: from.extra_tool_input.map(|input| {
                match input {
                public_api_proto::run_remote_tool_request::ExtraToolInput::AtlassianToolExtraInput(
                    atlassian_input,
                ) => agents::run_remote_tool_request::ExtraToolInput::AtlassianToolExtraInput(
                    agents::AtlassianToolExtraInput {
                        server_url: atlassian_input.server_url,
                        personal_api_token: atlassian_input.personal_api_token,
                        username: atlassian_input.username,
                    },
                ),
                public_api_proto::run_remote_tool_request::ExtraToolInput::NotionToolExtraInput(
                    notion_input,
                ) => agents::run_remote_tool_request::ExtraToolInput::NotionToolExtraInput(
                    agents::NotionToolExtraInput {
                        api_token: notion_input.api_token,
                    },
                ),                public_api_proto::run_remote_tool_request::ExtraToolInput::LinearToolExtraInput(
                    linear_input,
                ) => agents::run_remote_tool_request::ExtraToolInput::LinearToolExtraInput(
                    agents::LinearToolExtraInput {
                        api_token: linear_input.api_token,
                    },
                ),
                public_api_proto::run_remote_tool_request::ExtraToolInput::GithubToolExtraInput(
                    github_input,
                ) => agents::run_remote_tool_request::ExtraToolInput::GithubToolExtraInput(
                    agents::GitHubToolExtraInput {
                        api_token: github_input.api_token,
                    },
                ),
            }
            }),
        })
    }
}

impl TryFrom<agents::ToolSafety> for public_api_proto::ToolSafety {
    type Error = Status;

    fn try_from(value: agents::ToolSafety) -> Result<Self, Self::Error> {
        match value {
            agents::ToolSafety::ToolUnsafe => Ok(public_api_proto::ToolSafety::ToolUnsafe),
            agents::ToolSafety::ToolSafe => Ok(public_api_proto::ToolSafety::ToolSafe),
            agents::ToolSafety::ToolCheck => Ok(public_api_proto::ToolSafety::ToolCheck),
        }
    }
}

impl TryFrom<public_api_proto::ToolSafety> for agents::ToolSafety {
    type Error = Status;

    fn try_from(value: public_api_proto::ToolSafety) -> Result<Self, Self::Error> {
        match value {
            public_api_proto::ToolSafety::ToolUnsafe => Ok(agents::ToolSafety::ToolUnsafe),
            public_api_proto::ToolSafety::ToolSafe => Ok(agents::ToolSafety::ToolSafe),
            public_api_proto::ToolSafety::ToolCheck => Ok(agents::ToolSafety::ToolCheck),
        }
    }
}

impl TryFrom<public_api_proto::CheckToolSafetyRequest> for agents::CheckToolSafetyRequest {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::CheckToolSafetyRequest) -> Result<Self, Self::Error> {
        Ok(agents::CheckToolSafetyRequest {
            tool_id: agents::RemoteToolId::try_from(from.tool_id)
                .map(|tool| tool as i32)
                .unwrap_or_else(|e| {
                    tracing::warn!("Failed to convert tool id: {}, error: {}", from.tool_id, e);
                    agents::RemoteToolId::Unknown as i32
                }),
            tool_input_json: from.tool_input_json,
        })
    }
}

impl From<agents::CheckToolSafetyResponse> for public_api_proto::CheckToolSafetyResponse {
    fn from(from: agents::CheckToolSafetyResponse) -> Self {
        public_api_proto::CheckToolSafetyResponse {
            is_safe: from.is_safe,
        }
    }
}

impl TryFrom<public_api_proto::RevokeToolAccessRequest> for agents::RevokeToolAccessRequest {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::RevokeToolAccessRequest) -> Result<Self, Self::Error> {
        Ok(agents::RevokeToolAccessRequest {
            tool_id: agents::RemoteToolId::try_from(from.tool_id)
                .map(|tool| tool as i32)
                .unwrap_or_else(|e| {
                    tracing::warn!("Failed to convert tool id: {}, error: {}", from.tool_id, e);
                    agents::RemoteToolId::Unknown as i32
                }),
        })
    }
}

impl From<agents::RevokeToolAccessResponse> for public_api_proto::RevokeToolAccessResponse {
    fn from(from: agents::RevokeToolAccessResponse) -> Self {
        // Map gRPC status codes to public API status enum
        let public_status = if let Some(status) = &from.status {
            match Code::from_i32(status.code) {
                Code::Ok => public_api_proto::RevokeToolAccessStatus::Success,
                Code::NotFound => public_api_proto::RevokeToolAccessStatus::NotFound,
                Code::AlreadyExists => public_api_proto::RevokeToolAccessStatus::NotActive,
                Code::Unimplemented => public_api_proto::RevokeToolAccessStatus::Unimplemented,
                Code::Internal => public_api_proto::RevokeToolAccessStatus::Failed,
                Code::Unknown => public_api_proto::RevokeToolAccessStatus::Unknown,
                // For any other status code, map to Unknown
                _ => public_api_proto::RevokeToolAccessStatus::Unknown,
            }
        } else {
            // If status is None, default to Unknown
            public_api_proto::RevokeToolAccessStatus::Unknown
        };

        public_api_proto::RevokeToolAccessResponse {
            status: public_status as i32,
        }
    }
}

impl TryFrom<public_api_proto::TestToolConnectionRequest> for agents::TestToolConnectionRequest {
    type Error = tonic::Status;

    fn try_from(from: public_api_proto::TestToolConnectionRequest) -> Result<Self, Self::Error> {
        Ok(agents::TestToolConnectionRequest {
            tool_id: agents::RemoteToolId::try_from(from.tool_id)
                .map(|tool| tool as i32)
                .unwrap_or_else(|e| {
                    tracing::warn!("Failed to convert tool id: {}, error: {}", from.tool_id, e);
                    agents::RemoteToolId::Unknown as i32
                }),
        })
    }
}

impl From<agents::TestToolConnectionResponse> for public_api_proto::TestToolConnectionResponse {
    fn from(from: agents::TestToolConnectionResponse) -> Self {
        // Map gRPC status codes to public API status enum
        let public_status = if let Some(status) = &from.status {
            match Code::from_i32(status.code) {
                Code::Ok => public_api_proto::TestToolConnectionStatus::Ok,
                Code::NotFound => public_api_proto::TestToolConnectionStatus::NotFound,
                Code::Unimplemented => public_api_proto::TestToolConnectionStatus::Unimplemented,
                Code::Unavailable => public_api_proto::TestToolConnectionStatus::ServiceUnavailable,
                Code::Internal => public_api_proto::TestToolConnectionStatus::Error,
                Code::Unauthenticated => {
                    public_api_proto::TestToolConnectionStatus::AuthenticationError
                }
                Code::PermissionDenied => {
                    public_api_proto::TestToolConnectionStatus::AuthenticationError
                }
                Code::Unknown => public_api_proto::TestToolConnectionStatus::Unknown,
                // For any other status code, map to Unknown
                _ => public_api_proto::TestToolConnectionStatus::Unknown,
            }
        } else {
            // If status is None, default to Unknown
            public_api_proto::TestToolConnectionStatus::Unknown
        };

        public_api_proto::TestToolConnectionResponse {
            status: public_status as i32,
        }
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::LlmGenerateRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::LlmGenerateRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::LlmGenerate,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let inner_request = agents::LlmGenerateRequest::try_from(request)?;
            let inner_result = self
                .agents_client
                .llm_generate(&request_context, inner_request)
                .await?;
            let result = public_api_proto::LlmGenerateResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::CodebaseRetrievalRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::CodebaseRetrievalRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_AGENTS, &feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteToolCall,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let inner_request = agents::CodebaseRetrievalRequest::try_from(request)?;
            let inner_result = self
                .agents_client
                .codebase_retrieval(&request_context, inner_request)
                .await?;
            let result = public_api_proto::CodebaseRetrievalResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::EditFileRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::EditFileRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_agents(&feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteToolCall,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let inner_request = agents::EditFileRequest::try_from(request)?;
            let inner_result = self
                .agents_client
                .edit_file(&request_context, inner_request)
                .await?;
            let result = public_api_proto::EditFileResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::ListRemoteToolsRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::ListRemoteToolsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;
        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_AGENTS, &feature_flags, req, &tenant_info)?;

        let inner_request = agents::ListRemoteToolsRequest::try_from(request)?;
        let inner_result = self
            .agents_client
            .list_remote_tools(&request_context, inner_request)
            .await?;
        let result = public_api_proto::ListRemoteToolsResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::RunRemoteToolRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::RunRemoteToolRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;
        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_AGENTS, &feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::RemoteToolCall,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let inner_request = agents::RunRemoteToolRequest::try_from(request)?;
            let inner_result = self
                .agents_client
                .run_remote_tool(&request_context, inner_request)
                .await?;
            let result = public_api_proto::RunRemoteToolResponse::from(inner_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::CheckToolSafetyRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::CheckToolSafetyRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;
        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_AGENTS, &feature_flags, req, &tenant_info)?;

        let inner_request = agents::CheckToolSafetyRequest::try_from(request)?;
        let inner_result = self
            .agents_client
            .check_tool_safety(&request_context, inner_request)
            .await?;
        let result = public_api_proto::CheckToolSafetyResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::RevokeToolAccessRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::RevokeToolAccessRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;
        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_AGENTS, &feature_flags, req, &tenant_info)?;

        let inner_request = agents::RevokeToolAccessRequest::try_from(request)?;
        let inner_result = self
            .agents_client
            .revoke_tool_access(&request_context, inner_request)
            .await?;
        let result = public_api_proto::RevokeToolAccessResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::TestToolConnectionRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::TestToolConnectionRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;
        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_AGENTS, &feature_flags, req, &tenant_info)?;

        let inner_request = agents::TestToolConnectionRequest::try_from(request)?;
        let inner_result = self
            .agents_client
            .test_tool_connection(&request_context, inner_request)
            .await?;
        let result = public_api_proto::TestToolConnectionResponse::from(inner_result);
        Ok(HttpResponse::Ok().json(result))
    }
}

pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {
    VSCODE_AGENT_MODE_FLAG
        .register(registry)
        .expect("Registering VSCODE_AGENT_MODE_FLAG");
    VSCODE_AGENT_MODE_STABLE_FLAG
        .register(registry)
        .expect("Registering VSCODE_AGENT_MODE_STABLE_FLAG");
    INTELLIJ_AGENT_MODE_FLAG
        .register(registry)
        .expect("Registering INTELLIJ_AGENT_MODE_FLAG");
    ENABLE_AGENTS
        .register(registry)
        .expect("Registering ENABLE_AGENTS");
    CB_AGENTS.register(registry).expect("Registering CB_AGENTS");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_llm_generate_request_conversion() {
        let tool_def = public_api_proto::ToolDefinition {
            name: "test_tool".to_string(),
            description: "A test tool".to_string(),
            input_schema_json: "{}".to_string(),
        };
        let user_msg = public_api_proto::ChatRequestNode {
            id: 1,
            r#type: 0,
            text_node: Some(public_api_proto::ChatRequestText {
                content: "hello".to_string(),
            }),
            tool_result_node: None,
            image_node: None,
            ide_state_node: None,
            edit_events_node: None,
        };
        let dialog = vec![public_api_proto::Exchange {
            request_message: "".to_string(),
            response_text: None,
            request_id: None,
            request_nodes: vec![public_api_proto::ChatRequestNode {
                id: 2,
                r#type: 0,
                text_node: Some(public_api_proto::ChatRequestText {
                    content: "previous request".to_string(),
                }),
                tool_result_node: None,
                image_node: None,
                ide_state_node: None,
                edit_events_node: None,
            }],
            response_nodes: vec![public_api_proto::ChatResultNode {
                id: 3,
                r#type: 0,
                content: "previous response".to_string(),
                tool_use: None,
            }],
        }];
        let request = public_api_proto::LlmGenerateRequest {
            model_name: "test_model".to_string(),
            user_message: vec![user_msg],
            dialog,
            max_tokens: 100,
            system_prompt: "test prompt".to_string(),
            temperature: 0.5,
            tool_definitions: vec![tool_def],
            tool_choice: Some(public_api_proto::ToolChoice {
                r#type: public_api_proto::ToolChoiceType::Tool as i32,
                name: Some("test_tool".to_string()),
            }),
        };

        let inner_request = agents::LlmGenerateRequest::try_from(request).unwrap();
        assert_eq!(inner_request.model_name, "test_model");
        assert_eq!(inner_request.user_message.len(), 1);
        assert_eq!(
            inner_request.user_message[0]
                .text_node
                .as_ref()
                .unwrap()
                .content,
            "hello"
        );
        assert_eq!(inner_request.dialog.len(), 1);
        assert_eq!(inner_request.max_tokens, 100);
        assert_eq!(inner_request.system_prompt, "test prompt");
        assert_eq!(inner_request.temperature, 0.5);
        assert_eq!(inner_request.tool_definitions.len(), 1);
        assert_eq!(inner_request.tool_definitions[0].name, "test_tool");
        assert!(inner_request.tool_choice.is_some());
    }

    #[test]
    fn test_llm_generate_response_conversion() {
        // Test without tool_use
        let response = agents::LlmGenerateResponse {
            response_nodes: vec![chat::ChatResultNode {
                id: 1,
                r#type: 0,
                content: "test response".to_string(),
                tool_use: None,
            }],
        };

        let public_response = public_api_proto::LlmGenerateResponse::from(response);
        assert_eq!(public_response.response_nodes.len(), 1);
        assert_eq!(public_response.response_nodes[0].content, "test response");

        // Test with tool_use
        let response_with_tool = agents::LlmGenerateResponse {
            response_nodes: vec![chat::ChatResultNode {
                id: 2,
                r#type: 0,
                content: "test response with tool".to_string(),
                tool_use: Some(chat::ChatResultToolUse {
                    tool_use_id: "test_id".to_string(),
                    tool_name: "test_tool".to_string(),
                    input_json: "{\"arg\": \"value\"}".to_string(),
                    is_partial: false,
                }),
            }],
        };

        let public_response = public_api_proto::LlmGenerateResponse::from(response_with_tool);
        assert_eq!(public_response.response_nodes.len(), 1);
        let tool_use = public_response.response_nodes[0].tool_use.as_ref().unwrap();
        assert_eq!(tool_use.tool_use_id, "test_id");
        assert_eq!(tool_use.tool_name, "test_tool");
        assert_eq!(tool_use.input_json, "{\"arg\": \"value\"}");
        assert!(!tool_use.is_partial);
    }

    #[test]
    fn test_codebase_retrieval_request_conversion() {
        let dialog = vec![public_api_proto::Exchange {
            request_message: "".to_string(),
            response_text: None,
            request_id: None,
            request_nodes: vec![public_api_proto::ChatRequestNode {
                id: 1,
                r#type: 0,
                text_node: Some(public_api_proto::ChatRequestText {
                    content: "previous request".to_string(),
                }),
                tool_result_node: None,
                image_node: None,
                ide_state_node: None,
                edit_events_node: None,
            }],
            response_nodes: vec![public_api_proto::ChatResultNode {
                id: 2,
                r#type: 0,
                content: "previous response".to_string(),
                tool_use: None,
            }],
        }];
        let request = public_api_proto::CodebaseRetrievalRequest {
            information_request: "find something".to_string(),
            dialog,
            blobs: Some(public_api_proto::Blobs {
                checkpoint_id: None,
                added_blobs: vec!["cafe01".to_string()],
                deleted_blobs: vec!["99face".to_string()],
            }),
            max_output_length: 123,
            disable_codebase_retrieval: true,
            enable_commit_retrieval: false,
        };

        let inner_request = agents::CodebaseRetrievalRequest::try_from(request).unwrap();
        assert_eq!(inner_request.information_request, "find something");
        assert_eq!(inner_request.dialog.len(), 1);
        assert_eq!(inner_request.blobs.len(), 1);
        let blobs = &inner_request.blobs[0];
        assert!(blobs.baseline_checkpoint_id.is_none());
        assert_eq!(blobs.added.len(), 1);
        assert_eq!(blobs.deleted.len(), 1);
        assert_eq!(inner_request.max_output_length, 123);
        assert!(inner_request.disable_codebase_retrieval);
        assert!(!inner_request.enable_commit_retrieval);
    }

    #[test]
    fn test_codebase_retrieval_response_conversion() {
        let response = agents::CodebaseRetrievalResponse {
            formatted_retrieval: "test retrieval".to_string(),
        };

        let public_response = public_api_proto::CodebaseRetrievalResponse::from(response);
        assert_eq!(public_response.formatted_retrieval, "test retrieval");
    }

    #[test]
    fn test_edit_file_request_conversion() {
        let request = public_api_proto::EditFileRequest {
            file_path: "test.txt".to_string(),
            edit_summary: "test edit".to_string(),
            detailed_edit_description: "detailed test edit".to_string(),
            file_contents: "test content".to_string(),
        };

        let inner_request = agents::EditFileRequest::try_from(request).unwrap();
        assert_eq!(inner_request.file_path, "test.txt");
        assert_eq!(inner_request.edit_summary, "test edit");
        assert_eq!(
            inner_request.detailed_edit_description,
            "detailed test edit"
        );
        assert_eq!(inner_request.file_contents, "test content");
    }

    #[test]
    fn test_edit_file_response_conversion() {
        let response = agents::EditFileResponse {
            modified_file_contents: "modified content".to_string(),
            is_error: true,
        };

        let public_response = public_api_proto::EditFileResponse::from(response);
        assert_eq!(public_response.modified_file_contents, "modified content");
        assert!(public_response.is_error);
    }
}
