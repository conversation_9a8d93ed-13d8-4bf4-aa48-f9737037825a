use std::sync::Arc;

use futures::TryStreamExt;
use itertools::Itertools;
use k8s_openapi::api::core::v1::ConfigMap;
use kube::runtime::watcher;

use crate::{
    augment::model_instance_config::{
        model_instance_config::ModelConfig, AutofixModelConfig, ChatModelConfig, EditModelConfig,
        InferenceModelConfig, Language, ModelInstanceConfig, ModelType, NextEditGenModelConfig,
    },
    model_registry::ModelRegistry,
};

#[derive(Clone, Debug, serde::Deserialize)]
pub struct JsonLanguage {
    pub name: String,
    pub vscode_name: String,
    pub extensions: Vec<String>,
}

#[derive(Clone, Debug, serde::Deserialize)]
pub struct JsonInferenceConfig {
    pub suggested_prefix_char_count: u32,
    pub suggested_suffix_char_count: u32,
    pub model_priority: i64,
    pub completion_endpoint: String,
    pub languages: Vec<JsonLanguage>,
}

#[derive(Clone, Debug, serde::Deserialize)]
pub struct JsonEditConfig {
    pub suggested_prefix_char_count: u32,
    pub suggested_suffix_char_count: u32,
    pub model_priority: i64,
    pub edit_endpoint: String,
}

#[derive(Clone, Debug, serde::Deserialize)]
pub struct JsonChatConfig {
    pub suggested_prefix_char_count: u32,
    pub suggested_suffix_char_count: u32,
    pub model_priority: i64,
    pub chat_endpoint: String,
}

#[derive(Clone, Debug, serde::Deserialize)]
pub struct JsonNextEditConfig {
    pub suggested_prefix_char_count: u32,
    pub suggested_suffix_char_count: u32,
    pub model_priority: i64,
    pub next_edit_endpoint: String,
}

#[derive(Clone, Debug, serde::Deserialize)]
pub struct JsonAutofixConfig {
    pub suggested_prefix_char_count: u32,
    pub suggested_suffix_char_count: u32,
    pub model_priority: i64,
    pub autofix_endpoint: String,
}

#[derive(Clone, Debug, serde::Deserialize)]
pub struct JsonModelInstanceConfig {
    pub name: String,
    pub model_type: String,
    pub inference: Option<JsonInferenceConfig>,
    pub edit: Option<JsonEditConfig>,
    pub chat: Option<JsonChatConfig>,
    pub next_edit: Option<JsonNextEditConfig>,
    pub autofix: Option<JsonAutofixConfig>,
    pub handler_names: Option<Vec<String>>,
}

impl TryFrom<JsonModelInstanceConfig> for ModelInstanceConfig {
    type Error = tonic::Status;

    fn try_from(value: JsonModelInstanceConfig) -> Result<ModelInstanceConfig, tonic::Status> {
        if let Some(inference) = value.inference {
            let inference_config = InferenceModelConfig {
                suggested_prefix_char_count: inference.suggested_prefix_char_count,
                suggested_suffix_char_count: inference.suggested_suffix_char_count,
                model_priority: inference.model_priority,
                completion_endpoint: inference.completion_endpoint,
                languages: inference
                    .languages
                    .into_iter()
                    .map(|l| Language {
                        name: l.name,
                        vscode_name: l.vscode_name,
                        extensions: l.extensions,
                    })
                    .collect(),
                ..Default::default()
            };

            Ok(Self {
                name: value.name,
                model_type: ModelType::from_str_name(&value.model_type)
                    .unwrap_or_default()
                    .into(),
                model_config: Some(ModelConfig::Inference(inference_config)),
                handler_names: value.handler_names.unwrap_or_default(),
            })
        } else if let Some(edit) = value.edit {
            let edit_config = EditModelConfig {
                suggested_prefix_char_count: edit.suggested_prefix_char_count,
                suggested_suffix_char_count: edit.suggested_suffix_char_count,
                model_priority: edit.model_priority,
                edit_endpoint: edit.edit_endpoint,
            };

            Ok(Self {
                name: value.name,
                model_type: ModelType::from_str_name(&value.model_type)
                    .unwrap_or_default()
                    .into(),
                model_config: Some(ModelConfig::Edit(edit_config)),
                handler_names: value.handler_names.unwrap_or_default(),
            })
        } else if let Some(chat) = value.chat {
            let chat_config = ChatModelConfig {
                suggested_prefix_char_count: chat.suggested_prefix_char_count,
                suggested_suffix_char_count: chat.suggested_suffix_char_count,
                model_priority: chat.model_priority,
                chat_endpoint: chat.chat_endpoint,
            };

            Ok(Self {
                name: value.name,
                model_type: ModelType::from_str_name(&value.model_type)
                    .unwrap_or_default()
                    .into(),
                model_config: Some(ModelConfig::Chat(chat_config)),
                handler_names: value.handler_names.unwrap_or_default(),
            })
        } else if let Some(next_edit) = value.next_edit {
            let next_edit_config = NextEditGenModelConfig {
                suggested_prefix_char_count: next_edit.suggested_prefix_char_count,
                suggested_suffix_char_count: next_edit.suggested_suffix_char_count,
                model_priority: next_edit.model_priority,
                next_edit_endpoint: next_edit.next_edit_endpoint,
            };

            Ok(Self {
                name: value.name,
                model_type: ModelType::from_str_name(&value.model_type)
                    .unwrap_or_default()
                    .into(),
                model_config: Some(ModelConfig::NextEdit(next_edit_config)),
                handler_names: value.handler_names.unwrap_or_default(),
            })
        } else if let Some(autofix) = value.autofix {
            let autofix_config = AutofixModelConfig {
                suggested_prefix_char_count: autofix.suggested_prefix_char_count,
                suggested_suffix_char_count: autofix.suggested_suffix_char_count,
                model_priority: autofix.model_priority,
                autofix_endpoint: autofix.autofix_endpoint,
            };

            Ok(Self {
                name: value.name,
                model_type: ModelType::from_str_name(&value.model_type)
                    .unwrap_or_default()
                    .into(),
                model_config: Some(ModelConfig::Autofix(autofix_config)),
                handler_names: value.handler_names.unwrap_or_default(),
            })
        } else {
            Err(tonic::Status::invalid_argument("invalid inference config"))
        }
    }
}

// the model finder detects (by listening to model config objects) for all generation models (completion, chat, edit, ..) that
// the api proxy exposes.
//
// the models are stored in the model registry.
pub struct ModelFinder<MR: ModelRegistry> {
    client: kube::Client,
    model_registry: Arc<MR>,
    namespace: String,
}

fn check_label(map: &ConfigMap) -> bool {
    if let Some(labels) = &map.metadata.labels {
        if let Some(x) = labels.get("app.kubernetes.io/type") {
            x == "model-instance-config"
        } else {
            false
        }
    } else {
        false
    }
}

fn get_instance_config(map: ConfigMap) -> Option<ModelInstanceConfig> {
    if let Some(map) = map.data {
        tracing::info!("map {map:?}");
        if let Some(config) = map.get("model_instance.json") {
            let s: Result<JsonModelInstanceConfig, serde_json::Error> =
                serde_json::from_str(config);
            match s {
                Err(e) => {
                    tracing::error!("Failed to deserialize config map: {e}");
                    None
                }
                Ok(c) => {
                    tracing::info!("Found config {c:?}");
                    if c.model_type == "INFERENCE"
                        || c.model_type == "EDIT"
                        || c.model_type == "CHAT"
                        || c.model_type == "NEXT_EDIT"
                        || c.model_type == "AUTOFIX"
                    {
                        // the api client only cares about inference and routing models
                        match c.try_into() {
                            Ok(c) => Some(c),
                            Err(e) => {
                                tracing::error!("Failed to deserialize config map: {e}");
                                None
                            }
                        }
                    } else {
                        None
                    }
                }
            }
        } else {
            None
        }
    } else {
        None
    }
}

impl<MR: ModelRegistry> ModelFinder<MR> {
    pub fn new(client: kube::Client, namespace: &str, model_registry: Arc<MR>) -> Self {
        ModelFinder {
            client,
            model_registry,
            namespace: namespace.to_string(),
        }
    }

    pub async fn run(self) -> Result<(), tonic::Status> {
        let api = kube::api::Api::<ConfigMap>::namespaced(self.client, &self.namespace);
        let mut stream = Box::pin(kube::runtime::utils::StreamBackoff::new(
            watcher(api, watcher::Config::default()),
            watcher::DefaultBackoff::default(),
        ));
        loop {
            let event = match stream.try_next().await {
                Ok(Some(event)) => event,
                // Look for prior fault to get cause
                Ok(None) => return Err(tonic::Status::internal("Kubernetes watching failure")),
                Err(e) => {
                    tracing::error!("Kubernetes watching fault: {}", e);
                    continue;
                }
            };

            match event {
                watcher::Event::Applied(key) if check_label(&key) => {
                    if let Some(c) = get_instance_config(key) {
                        tracing::info!("Applied: {:?}", c);
                        if let Err(e) = self.model_registry.add_model(c).await {
                            tracing::error!("Failed to add model: {}", e);
                        }
                    }
                }
                watcher::Event::Deleted(key) if check_label(&key) => {
                    if let Some(c) = get_instance_config(key) {
                        tracing::info!("Deleted: {:?}", c);
                        let handler_names: Vec<_> = match c.handler_names.clone() {
                            names if !names.is_empty() => names,
                            _ => vec![c.name.clone()],
                        };
                        for handler_name in handler_names {
                            if let Err(e) = self.model_registry.delete_model(&handler_name).await {
                                tracing::error!("Failed to delete model: {}", e);
                            }
                        }
                    }
                }
                watcher::Event::Restarted(keys) => {
                    let configs = keys
                        .into_iter()
                        .flat_map(|key| {
                            if check_label(&key) {
                                get_instance_config(key)
                            } else {
                                None
                            }
                        })
                        .collect_vec();
                    tracing::info!("Restarted: {:?}", configs);
                    if let Err(e) = self.model_registry.update_models(configs).await {
                        tracing::error!("Failed to update models: {}", e);
                    }
                }
                _ => {}
            }
        }
    }
}
