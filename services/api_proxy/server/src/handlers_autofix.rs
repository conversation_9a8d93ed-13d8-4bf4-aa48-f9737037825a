use actix_web::{HttpRequest, HttpResponse};
use request_insight_publisher::request_insight;
use tracing_actix_web::RootSpan;

use crate::augment::model_instance_config::ModelType;
use crate::autofix;
use crate::generation_clients::Client;
use crate::handler_utils::{
    convert_blobs_and_names, gate_on_circuit_breaker, get_model, request_context_from_req,
    <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,
};
use crate::model_registry::ModelRegistry;
use crate::public_api_proto;
use crate::request_insight_util::extract_request_metadata;
use content_manager_client::ContentManagerClient;

// For now use a single circuit breaker for all autofix endpoints since there's no point letting
// users do only part of the workflow
pub const CB_AUTOFIX: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_autofix_v1", false);

pub const MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("autofix_model", "");

// TODO: share this with handlers_next_edit.rs!
fn convert_vcs_change(
    vcs_change: &public_api_proto::VcsChange,
) -> Result<autofix::VcsChange, tonic::Status> {
    Ok(autofix::VcsChange {
        working_directory_changes: vcs_change
            .working_directory_changes
            .iter()
            .map(|wdc| autofix::WorkingDirectoryChange {
                before_path: wdc.before_path.clone(),
                after_path: wdc.after_path.clone(),
                change_type: wdc.change_type,
                head_blob_name: wdc.head_blob_name.clone(),
                indexed_blob_name: wdc.indexed_blob_name.clone(),
                current_blob_name: wdc.current_blob_name.clone(),
            })
            .collect(),
    })
}

// API-type-to-model-host-type conversions
impl From<public_api_proto::AutofixCommand> for autofix::Command {
    fn from(from: public_api_proto::AutofixCommand) -> autofix::Command {
        autofix::Command {
            input: from.input,
            output: from.output,
            exit_code: from.exit_code,
        }
    }
}

impl TryFrom<public_api_proto::AutofixCheckRequest> for autofix::CheckRequest {
    type Error = tonic::Status;

    fn try_from(
        from: public_api_proto::AutofixCheckRequest,
    ) -> Result<autofix::CheckRequest, Self::Error> {
        let command = from
            .command
            .ok_or_else(|| tonic::Status::invalid_argument("missing command"))?;
        Ok(autofix::CheckRequest {
            command: Some(command.into()),
        })
    }
}

impl From<autofix::CheckResponse> for public_api_proto::AutofixCheckResponse {
    fn from(from: autofix::CheckResponse) -> public_api_proto::AutofixCheckResponse {
        public_api_proto::AutofixCheckResponse {
            contains_failure: from.contains_failure,
            is_code_related: from.is_code_related,
        }
    }
}

impl TryFrom<public_api_proto::AutofixPlanRequest> for autofix::PlanRequest {
    type Error = tonic::Status;

    fn try_from(
        from: public_api_proto::AutofixPlanRequest,
    ) -> Result<autofix::PlanRequest, Self::Error> {
        let command = from
            .command
            .ok_or_else(|| tonic::Status::invalid_argument("missing command"))?;
        let vcs_change = convert_vcs_change(&from.vcs_change.unwrap_or_default())?;
        let blobs = convert_blobs_and_names(&from.blobs, None)?;

        let exchanges = from
            .steering_history
            .into_iter()
            .map(|e| autofix::UserSteeringExchange {
                request_message: e.request_message,
                summary: e.summary,
                replacements: e
                    .replacements
                    .into_iter()
                    .map(|r| autofix::TextReplacement {
                        description: r.description,
                        path: r.path,
                        text: r.text,
                        start_line: r.start_line,
                        end_line: r.end_line,
                        old_text: r.old_text,
                        sequence_id: r.sequence_id,
                        old_blob_name: r.old_blob_name,
                    })
                    .collect(),
                request_id: e.request_id,
            })
            .collect();
        Ok(autofix::PlanRequest {
            command: Some(command.into()),
            vcs_change: Some(vcs_change),
            blobs: Some(blobs),
            steering_history: exchanges,
        })
    }
}

impl From<autofix::PlanResponse> for public_api_proto::AutofixPlanResponse {
    fn from(from: autofix::PlanResponse) -> public_api_proto::AutofixPlanResponse {
        public_api_proto::AutofixPlanResponse {
            unknown_blob_names: from.unknown_blob_names,
            checkpoint_not_found: from.checkpoint_not_found,
            summary: from.summary,
            replacements: from
                .replacements
                .into_iter()
                .map(|r| public_api_proto::TextReplacement {
                    description: r.description,
                    path: r.path,
                    text: r.text,
                    start_line: r.start_line,
                    end_line: r.end_line,
                    old_text: r.old_text,
                    sequence_id: r.sequence_id,
                    old_blob_name: r.old_blob_name,
                })
                .collect(),
        }
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::AutofixCheckRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::AutofixCheckRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("autofix/check request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_AUTOFIX, &feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::AutofixCheck,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let (com, _mi) = get_model::<MR>(
                None, // TODO support model selection?
                &self.model_registry,
                &feature_flags,
                &MODEL_FLAG,
                ModelType::Autofix,
            )
            .await?;
            let com = match com {
                Client::Autofix(c) => c.clone(),
                _ => return Err(tonic::Status::internal("Model is not an autofix model")),
            };
            let autofix_request = autofix::CheckRequest::try_from(request)?;
            let autofix_result = com.check(&request_context, autofix_request).await?;
            let result = public_api_proto::AutofixCheckResponse::from(autofix_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::AutofixPlanRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::AutofixPlanRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("autofix/plan request");

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_AUTOFIX, &feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::AutofixPlan,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let (com, _mi) = get_model::<MR>(
                None, // TODO support model selection?
                &self.model_registry,
                &feature_flags,
                &MODEL_FLAG,
                ModelType::Autofix,
            )
            .await?;
            let com = match com {
                Client::Autofix(c) => c.clone(),
                _ => return Err(tonic::Status::internal("Model is not an autofix model")),
            };
            let autofix_request = autofix::PlanRequest::try_from(request)?;
            let autofix_result = com.plan(&request_context, autofix_request).await?;
            let result = public_api_proto::AutofixPlanResponse::from(autofix_result);
            Ok(HttpResponse::Ok().json(result))
        })
        .await
    }
}

#[cfg(test)]
mod tests {
    use std::sync::Arc;

    use super::*;
    use crate::handler_utils::handle_api_auth;
    use crate::handler_utils::tests::{
        create_model_instance_config, new_root_span, setup_app_state, setup_req,
    };
    use crate::model_registry::tests::FakeClientFactory;
    use crate::model_registry::{DynamicModelRegistry, ModelRegistry};
    use actix_web::{body, http, web};
    use content_manager_client::MockContentManagerClient;

    async fn add_autofix_model(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![create_model_instance_config(
                "model1Autofix",
                ModelType::Autofix,
                1,
            )])
            .await
            .unwrap();
    }

    pub async fn run_autofix_request_raw<T: Clone>(
        request: &T,
    ) -> (HttpResponse, Arc<FakeClientFactory>)
    where
        Handler<DynamicModelRegistry, MockContentManagerClient>: EndpointHandler<T>,
    {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        {
            let registry = app_state.model_registry.clone();
            add_autofix_model(&registry).await;
        }

        let resp = handle_api_auth(
            app_state.clone(),
            setup_req(),
            web::Json(request.clone()),
            new_root_span(),
        )
        .await;
        (resp, fake_client_factory)
    }

    async fn run_autofix_check(
        request: &public_api_proto::AutofixCheckRequest,
    ) -> (
        autofix::CheckRequest,
        public_api_proto::AutofixCheckResponse,
    ) {
        let (resp, fake_client_factory) = run_autofix_request_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let result: public_api_proto::AutofixCheckResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        let modelhost_request = get_last_autofix_check_request(fake_client_factory).await;
        (modelhost_request, result)
    }

    async fn get_last_autofix_check_request(
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> autofix::CheckRequest {
        match fake_client_factory
            .fake_autofix_clients
            .lock()
            .unwrap()
            .last()
        {
            Some(client) => client.get_last_autofix_check_request(),
            None => panic!("No client"),
        }
    }

    async fn run_autofix_plan(
        request: &public_api_proto::AutofixPlanRequest,
    ) -> (autofix::PlanRequest, public_api_proto::AutofixPlanResponse) {
        let (resp, fake_client_factory) = run_autofix_request_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let result: public_api_proto::AutofixPlanResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        let modelhost_request = get_last_autofix_plan_request(fake_client_factory).await;
        (modelhost_request, result)
    }

    async fn get_last_autofix_plan_request(
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> autofix::PlanRequest {
        match fake_client_factory
            .fake_autofix_clients
            .lock()
            .unwrap()
            .last()
        {
            Some(client) => client.get_last_autofix_plan_request(),
            None => panic!("No client"),
        }
    }

    #[actix_rt::test]
    async fn test_autofix_check() {
        let request = &public_api_proto::AutofixCheckRequest {
            command: Some(public_api_proto::AutofixCommand {
                input: "cargo test".to_string(),
                output: "test result: FAILED\nerror: test failed".to_string(),
                exit_code: Some(1),
            }),
        };

        let (modelhost_request, _result) = run_autofix_check(request).await;
        let command = modelhost_request.command.unwrap();

        // Verify the modelhost request was converted correctly
        assert_eq!(command.input, "cargo test");
        assert_eq!(command.output, "test result: FAILED\nerror: test failed");
        assert_eq!(command.exit_code, Some(1));
    }

    #[actix_rt::test]
    async fn test_autofix_plan() {
        let request = &public_api_proto::AutofixPlanRequest {
            command: Some(public_api_proto::AutofixCommand {
                input: "cargo test".to_string(),
                output: "test result: FAILED\nerror: test failed".to_string(),
                exit_code: Some(1),
            }),
            vcs_change: Some(public_api_proto::VcsChange {
                working_directory_changes: vec![public_api_proto::WorkingDirectoryChange {
                    before_path: Some("src/main.rs".to_string()),
                    after_path: Some("src/main.rs".to_string()),
                    change_type: public_api_proto::ChangeType::Modified.into(),
                    head_blob_name: Some("blob1".to_string()),
                    indexed_blob_name: Some("blob2".to_string()),
                    current_blob_name: Some("blob3".to_string()),
                }],
            }),
            blobs: Some(public_api_proto::Blobs {
                checkpoint_id: None,
                added_blobs: vec!["cafe01".to_string()],
                deleted_blobs: vec![],
            }),
            steering_history: vec![public_api_proto::UserSteeringExchange {
                request_message: "Prefer not to change src/sacred.rs".to_string(),
                summary: "Rewrite function in src/sacred.rs".to_string(),
                replacements: vec![public_api_proto::TextReplacement {
                    description: "Rewrite function in src/sacred.rs".to_string(),
                    path: "src/sacred.rs".to_string(),
                    text: "def foo():\n    assert pass\n".to_string(),
                    start_line: 1,
                    end_line: 3,
                    old_text: "def foo():\n    assert False\n".to_string(),
                    sequence_id: 1,
                    old_blob_name: Some("blob4".to_string()),
                }],
                request_id: "prev_request".to_string(),
            }],
        };

        let (modelhost_request, _result) = run_autofix_plan(request).await;
        let command = modelhost_request.command.unwrap();
        let vcs_change = modelhost_request.vcs_change.unwrap();
        let hist = &modelhost_request.steering_history[0];

        // Verify the modelhost request was converted correctly
        assert_eq!(command.input, "cargo test");
        assert_eq!(command.output, "test result: FAILED\nerror: test failed");
        assert_eq!(vcs_change.working_directory_changes.len(), 1);
        let wdc = vcs_change.working_directory_changes[0].clone();
        assert_eq!(wdc.before_path, Some("src/main.rs".to_string()));
        assert_eq!(wdc.after_path, Some("src/main.rs".to_string()));
        assert_eq!(wdc.change_type, autofix::ChangeType::Modified as i32);
        assert_eq!(wdc.head_blob_name, Some("blob1".to_string()));
        assert_eq!(wdc.indexed_blob_name, Some("blob2".to_string()));
        assert_eq!(wdc.current_blob_name, Some("blob3".to_string()));
        assert_eq!(hist.request_id, "prev_request".to_string());
        assert_eq!(
            hist.summary,
            "Rewrite function in src/sacred.rs".to_string()
        );
        assert_eq!(
            hist.request_message,
            "Prefer not to change src/sacred.rs".to_string()
        );
        assert_eq!(hist.replacements.len(), 1);
        let r = hist.replacements[0].clone();
        assert_eq!(
            r.description,
            "Rewrite function in src/sacred.rs".to_string()
        );
        assert_eq!(r.path, "src/sacred.rs".to_string());
        assert_eq!(r.text, "def foo():\n    assert pass\n".to_string());
        assert_eq!(r.start_line, 1);
        assert_eq!(r.end_line, 3);
        assert_eq!(r.old_text, "def foo():\n    assert False\n".to_string());
        assert_eq!(r.sequence_id, 1);
        assert_eq!(r.old_blob_name, Some("blob4".to_string()));
    }

    #[test]
    fn test_deserialize_autofix_plan_request() {
        let json_data = r#"{
            "command": {
                "input": "pytest /Users/<USER>/devrepos/simple/test_sum.py",
                "output": "============================= test session starts ==============================\nplatform darwin -- Python 3.13.0, pytest-8.3.3, pluggy-1.5.0\nrootdir: /Users/<USER>/devrepos/simple\ncollected 4 items\n\nUsers/itkeman/devrepos/simple/test_sum.py .F..                           [100%]\n\n=================================== FAILURES ===================================\n____________________________________ test_b ____________________________________\n\n    def test_b():\n      time.sleep(1)\n>     assert 1+2 == 4\nE     assert (1 + 2) == 4\n\nUsers/itkeman/devrepos/simple/test_sum.py:9: AssertionError\n=========================== short test summary info ============================\nFAILED Users/itkeman/devrepos/simple/test_sum.py::test_b - assert (1 + 2) == 4\n========================= 1 failed, 3 passed in 4.04s ==========================\n"
            },
            "vcs_change": {
                "working_directory_changes": [
                    {
                        "before_path": "test_sum.py",
                        "after_path": "test_sum.py",
                        "change_type": "MODIFIED",
                        "head_blob_name": "000000625675ec46ff25c9ecc2dc0a254745459301ebeba235d9af3a100b18b7",
                        "indexed_blob_name": "9ffa782c40124638ff3f2b93016f9056e526c3dedf7ee26bad6f6c7c29074375",
                        "current_blob_name": "0000002c40124638ff3f2b93016f9056e526c3dedf7ee26bad6f6c7c29074375"
                    }
                ]
            },
            "blobs": {
                "added_blobs": [
                    "46ccb0625675ec46ff25c9ecc2dc0a254745459301ebeba235d9af3a100b18b7",
                    "9ffa782c40124638ff3f2b93016f9056e526c3dedf7ee26bad6f6c7c29074375",
                    "d75b7add19ee5d75174af1a17a82d5d013e6290e4d0d5507ff9712cf513e31f4"
                ],
                "deleted_blobs": [
                    "000000625675ec46ff25c9ecc2dc0a254745459301ebeba235d9af3a100b18b7"
                ]
            },
            "steering_history": [
                {
                    "request_message": "You should add parenthesis please",
                    "summary": "To fix the error we need to change the assertion in test_b() to correctly test that 1+2 equals 3 instead of 4.",
                    "replacements": [
                        {
                            "description": "Update the assertion in test_b() function to correctly test that 1+2 equals 3 instead of 4. The change is on line 8.",
                            "path": "test_sum.py",
                            "text": "  assert 1+2 == 3\n  \ndef test_c():\n  time.sleep(1)\n  assert 2+2 == 4\n\ndef test_d():\n  time.sleep(1)\n  assert 2+5 == 7\n",
                            "old_text": "  assert 1+2 == 4\n  \ndef test_c():\n  time.sleep(1)\n  assert 2+2 == 4\n\ndef test_d():\n  time.sleep(1)\n  assert 2+5 == 7\n",
                            "start_line": 9,
                            "end_line": 18,
                            "sequence_id": 0,
                            "old_blob_name": "d75b7add19ee5d75174af1a17a82d5d013e6290e4d0d5507ff9712cf513e31f4"
                        }
                    ],
                    "request_id": "b6190f7c-7fba-41ba-b04c-be9018b549b8"
                }
            ]
        }"#;
        let request: public_api_proto::AutofixPlanRequest =
            serde_json::from_str(json_data).unwrap();
        let autofix_request = autofix::PlanRequest::try_from(request).unwrap();
        assert_eq!(
            autofix_request.steering_history[0].replacements[0].path,
            "test_sum.py"
        );
        assert_eq!(
            autofix_request.steering_history[0].replacements[0].start_line,
            9
        );
    }
}
