package server

import (
	"context"
	"fmt"
	"os"
	"testing"

	"cloud.google.com/go/storage"

	tenantcrypto "github.com/augmentcode/augment/services/lib/encryption"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantproto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/fsouza/fake-gcs-server/fakestorage"
)

// Test utils for testing the GCS proxy service -- idea is this can be expanded upon as other services need it
// for integration tests etc.
// Keeping these in the server package to give them the appropriate visibility to set up mocks -- moving to
// a separate package either means we need to have test util mocks in the server package anyway, with just
// light wrapping here, or to make visible unnecessary fields in server.
// This is considered preferable & logical to keep server + test abstractions together.

var (
	riEnterpriseBucket    = "enterprise-bucket"
	riNonEnterpriseBucket = "non-enterprise-bucket"
	projectID             = "test-project"
)

var (
	enterpriseTenantID          = "enterprise-tenant"
	enterpriseEncryptedTenantID = "enterprise-encrypted-tenant"
	communityTenantID           = "community-tenant"
	communityEncryptedTenantID  = "community-encrypted-tenant"
	dogfoodTenantID             = "dogfood-tenant"
	migratedTenantID            = "migrated-tenant"
	testNamespace               = "test-namespace"
)

type mockBucketHandle struct {
	bucketName string
}

func (m *mockBucketHandle) BucketName() string {
	return m.bucketName
}

func (m *mockBucketHandle) Object(name string) *storage.ObjectHandle {
	return nil
}

func (m *mockBucketHandle) Objects(ctx context.Context, q *storage.Query) *storage.ObjectIterator {
	return nil
}

func NewDefaultMockGcsObjectsForTesting() gcsObjects {
	return gcsObjects{
		riEnterpriseBucket:    &mockBucketHandle{bucketName: riEnterpriseBucket},
		riNonEnterpriseBucket: &mockBucketHandle{bucketName: riNonEnterpriseBucket},
		gcsClient:             nil,
	}
}

func NewGCSEmulatorForTesting(t *testing.T) *fakestorage.Server {
	// Start the GCS emulator
	storageRoot := "/tmp/fake-gcs-server/" + t.Name()
	fakeStorageOptions := fakestorage.Options{
		StorageRoot: storageRoot,
		Host:        "127.0.0.1",
		Port:        0, // Assigns to a random open port.
	}
	var err error
	gcsEmulator, err := fakestorage.NewServerWithOptions(fakeStorageOptions)
	if err != nil {
		t.Fatalf("failed to start fake GCS server: %v", err)
	}
	t.Cleanup(func() {
		gcsEmulator.Stop()
		err := os.RemoveAll(storageRoot) // apparently Stop does not do this...
		if err != nil {
			t.Logf("failed to remove storage root %s: %v", storageRoot, err)
		}
	})
	return gcsEmulator
}

func NewGcsEmulatorWithDefaultObjectsForTesting(t *testing.T) (*fakestorage.Server, gcsObjects) {
	gcsEmulator := NewGCSEmulatorForTesting(t)
	var gcsClient *storage.Client = nil

	gcsClient = gcsEmulator.Client()

	if gcsClient == nil {
		gcsEmulator.Stop()
		t.Fatalf("failed to create GCS client: client is nil")
	}

	gcsEmulator.CreateBucketWithOpts(fakestorage.CreateBucketOpts{
		Name: riEnterpriseBucket,
	})
	gcsEmulator.CreateBucketWithOpts(fakestorage.CreateBucketOpts{
		Name: riNonEnterpriseBucket,
	})
	gcsObjects := gcsObjects{
		riEnterpriseBucket:    gcsClient.Bucket(riEnterpriseBucket),
		riNonEnterpriseBucket: gcsClient.Bucket(riNonEnterpriseBucket),
		gcsClient:             gcsClient,
	}
	return gcsEmulator, gcsObjects
}

func NewDefaultMockTenantWatcherClientForTesting() tenantwatcherclient.TenantWatcherClient {
	tenants := map[string]*tenantproto.Tenant{
		enterpriseTenantID: {
			Id:             enterpriseTenantID,
			ShardNamespace: testNamespace,
			Tier:           tenantproto.TenantTier_ENTERPRISE,
		},
		communityTenantID: {
			Id:             communityTenantID,
			ShardNamespace: testNamespace,
			Tier:           tenantproto.TenantTier_COMMUNITY,
		},
		dogfoodTenantID: {
			Id:             dogfoodTenantID,
			ShardNamespace: testNamespace,
			Tier:           tenantproto.TenantTier_ENTERPRISE,
			Config: &tenantproto.Config{
				Configs: map[string]string{
					"override_data_export_tier": "\"COMMUNITY\"",
				},
			},
		},
		enterpriseEncryptedTenantID: {
			Id:                enterpriseEncryptedTenantID,
			ShardNamespace:    testNamespace,
			Tier:              tenantproto.TenantTier_ENTERPRISE,
			EncryptionKeyName: "projects/test-enterprise-project/locations/global/keyRings/test-keyring/cryptoKeys/test-key",
		},
		communityEncryptedTenantID: {
			Id:                communityEncryptedTenantID,
			ShardNamespace:    testNamespace,
			Tier:              tenantproto.TenantTier_COMMUNITY,
			EncryptionKeyName: "projects/test-community-project/locations/global/keyRings/test-keyring/cryptoKeys/test-key",
		},
		migratedTenantID: {
			Id:             migratedTenantID,
			ShardNamespace: testNamespace,
			OtherNamespace: "other-namespace",
			Tier:           tenantproto.TenantTier_ENTERPRISE,
		},
	}

	return tenantwatcherclient.MakeMockTenantWatcherClient(tenants)
}

func NewTestService(
	ctx context.Context,
	gcsObjects gcsObjects,
	tenantClient tenantwatcherclient.TenantWatcherClient,
) *GcsProxyService {
	// Create a tenant cache directly with the mock client
	tenantCache := tenantwatcherclient.NewTenantCache(tenantClient, testNamespace)

	// Create a mock KMS client
	kmsClient := tenantcrypto.NewMockKMSClient()

	// Initialize tenant crypto
	tenantCrypto, err := tenantcrypto.New(ctx, kmsClient)
	if err != nil {
		panic(fmt.Sprintf("Failed to initialize tenant crypto: %v", err))
	}

	return &GcsProxyService{
		ctx: ctx,
		config: GcsProxyConfig{
			RIEnterpriseBucket:    riEnterpriseBucket,
			RINonEnterpriseBucket: riNonEnterpriseBucket,
			ProjectId:             projectID,
			UseMockKMS:            true,
		},
		tenantCache:  tenantCache,
		tenantCrypto: tenantCrypto,
		gcsObjects:   gcsObjects,
	}
}
