"""System test that does API calls against a full deployment via the api proxy.

By default, it will create or update the application shard in Kubernetes
based on the user namespace.

This is a broad end-to-end test as it hits the api proxy, one or more
completion/chat/nextedit host, and inference instances.
"""

import logging
import pathlib
import random
import time
import typing
import uuid
from contextlib import contextmanager
from dataclasses import asdict
from hashlib import sha256
from typing import Any, Callable, Generator, Iterable, Sequence, Union, cast

import pytest
import urllib3

import base.python.k8s_test_helper.k8s_test_helper as k8s_test_helper
from base.augment_client.client import (
    AugmentClient,
    AugmentModelClient,
    BlobsJson,
    ChatResponse,
    ClientException,
    CompleteResponse,
    CompletionResolution,
    EditResponse,
    Exchange,
    AutofixCommand,
    AutofixPlanResponse,
    InstructionResponse,
    NextEditResponse,
    Retry,
    UploadContent,
    UserTier,
    VCSChange,
    UserSteeringExchange,
)
from base.blob_names.python.blob_names import Blobs, get_blob_name
from base.test_utils.testing_utils import assert_str_eq
from services.api_proxy.client.memorize_path import memorize_path
from services.api_proxy import public_api_pb2
from services.lib.request_context.request_context import RequestContext
from services.test.conftest import ApplicationShard
from services.test.fake_feature_flags.client import FakeFeatureFlagsClient
from services.test.tokens import TokenUtil

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# NOTE(arun/costa): We specify a default token budget to make expected times of tests
#   more deterministic and make it small to prevent RPC timeouts.
DEFAULT_TOKEN_BUDGET = 64


def check_retry_exception(e: Exception) -> bool:
    """Check if the exception should be retries."""
    if isinstance(e, AssertionError):
        return False
    return True


def run_with_retries(
    func: Callable,
    retry_policy: Retry = Retry(retry_count=8, retry_sleep=0.1),
    check_fn: Callable[[Exception], bool] = check_retry_exception,
):
    """Run a function with retries based on the retry policy.

    Args:
        func: The function to run.
        retry_policy: The retry policy to use.
        check_fn: A function that checks if an exception should be retried.

    Returns:
        The result of the function.

    Raises:
        Exception: If the function fails after all retries.
    """
    sleep = retry_policy.retry_sleep
    for retry in range(retry_policy.retry_count + 1):
        try:
            return func()
        except Exception as e:  # pylint: disable=broad-exception-caught
            if not check_fn(e) or retry == retry_policy.retry_count:
                raise e
        time.sleep(sleep)
        sleep *= 2


def _blobs_json_single_blob(name: str) -> BlobsJson:
    return BlobsJson(
        checkpoint_id=None,
        added_blobs=[name],
        deleted_blobs=[],
    )


def _upload_one(augment_client: AugmentClient, content: str, path_name: str) -> str:
    blob_names = augment_client.batch_upload(
        blobs=[UploadContent(content=content, path_name=path_name)]
    )
    assert len(blob_names) == 1
    return blob_names.pop(0)


def _upload_and_wait_for_indexing(
    client: AugmentClient,
    model_name: str,
    blobs: Sequence[UploadContent],
    retry_policy: Retry | None = None,
    wait_interval_sec: float = 10.0,
    max_wait_retries: int = 10,
):
    """Uploads the contents and waits for indexing to complete."""
    blob_names = client.batch_upload(blobs, retry_policy=retry_policy)
    pending = blob_names
    for i in range(max_wait_retries):
        response = client.find_missing(model_name, pending, retry_policy=retry_policy)
        assert not response.unknown_memory_names, f"{response.unknown_memory_names=}"
        pending = response.nonindexed_blob_names
        if not pending:
            break
        msg = f"[{i}] Waiting for indexing {len(blob_names)} blobs."
        logging.info(msg)
        time.sleep(wait_interval_sec)
    return blob_names


# Testing that the legacy `text` field still works
def test_completion_api_auth_legacy(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth completion endpoint without uploading the file first."""
    print("completion_model_name", completion_model_name, flush=True)
    print("test_completion_api_auth_legacy", flush=True)
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    # note prompted on "void quicksort(std::vector<int> &i)" starcoder-7B will only produce ";", so the prefix can matter
    # in certain situations.
    response = augment_model_client.complete(
        "void quicksort(std::vector<int> &i) {",
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="quicksort.cc",
    )
    text = response.text
    # Different models return different completions, so we can't do an exact match.
    # Instead, we just make sure we get a non-empty completion.
    assert len(text) > 0, "text is empty"


# Testing results in the new completion_items field
def test_completion_api_auth(augment_client: AugmentClient, completion_model_name: str):
    """Tests the API auth completion endpoint."""
    print("test_completion_api_auth", flush=True)
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    # note prompted on "void quicksort(std::vector<int> &i)" starcoder-7B will only produce ";", so the prefix can matter
    # in certain situations.
    response = augment_model_client.complete(
        "void quicksort(std::vector<int> &i) {",
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="quicksort.cc",
    )
    assert len(response.completion_items) > 0, "no completion items"
    text = response.completion_items[0].text
    assert len(text) >= 20, "text is too short"


def test_completion_null_file(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth completion endpoint with a prompt of zeros (which is valid UTF-8)"""
    print("test_completion_null_file", flush=True)
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    null_prompt = bytes([0] * 16 * 1024).decode("utf-8")
    augment_model_client.complete(
        null_prompt,
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="quicksort.cc",
        timeout=10,
    )
    # if we get here, the request succeeded


def test_completion_large_prompt(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth completion endpoint with a large prompt.

    The request should fail.
    """
    print("test_completion_large_prompt", flush=True)
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    large_prompt = "1234567890" * (100 * 1024)
    with pytest.raises(ClientException) as ex:
        augment_model_client.complete(
            large_prompt,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            path="quicksort.cc",
            retry_policy=Retry(retry_count=2, retry_sleep=0.1),
        )
    assert ex.value.response.status_code == 400


# Testing post processing filtering works as expected.
# The filter score is 0.74256 for the completion. We expect no filtering if the filter threshold is 1) None/out of bounds (the default is 1.0 in this case) or 2) above this value.
@pytest.mark.parametrize(
    "filter_threshold, expect_filtering",
    [
        (threshold, expect_filtering)
        for threshold, expect_filtering in [
            (-0.2, False),
            (0, True),
            (0.4, True),
            (0.9, False),
            (1, False),
            (1.2, False),
            (None, False),
        ]
    ],
)
def test_completion_filter_threshold(
    augment_client: AugmentClient,
    completion_model_name: str,
    filter_threshold: float | None,
    expect_filtering: bool,
):
    """Tests the API auth completion endpoint."""
    print("test_completion_api_auth", flush=True)
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    # note prompted on "void quicksort(std::vector<int> &i)" starcoder-7B will only produce ";", so the prefix can matter
    # in certain situations.
    response = augment_model_client.complete(
        "void quicksort(std::vector<int> &i) {",
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="quicksort.cc",
        filter_threshold=filter_threshold,
    )
    # The filter score is 0.74256 for the completion, and we expect no filtering if the filter threshold is out of bounds or above this value.
    if expect_filtering:
        assert len(response.completion_items) > 0, "no completion items"
        text = response.completion_items[0].text
        assert len(text) == 0, "text is filtered out"
    else:
        assert len(response.completion_items) > 0, "no completion items"
        text = response.completion_items[0].text
        assert len(text) >= 20, "text is too short"


# Testing results in the new completion_items field
def test_completion_with_recency(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth completion endpoint."""
    print("test_completion_with_recency", flush=True)
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    # note prompted on "void quicksort(std::vector<int> &i)" starcoder-7B will only produce ";", so the prefix can matter
    # in certain situations.
    response = augment_model_client.complete(
        "void quicksort(std::vector<int> &i) {",
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="quicksort.cc",
        recency_info={
            "tab_switch_events": [
                {"path": "quicksort.cc", "file_blob_name": "my-blob-name"}
            ],
            "git_diff_file_info": [
                {
                    "content_blob_name": "diff-blob-name",
                    "file_blob_name": "my-blob-name",
                }
            ],
        },
    )
    assert len(response.completion_items) > 0, "no completion items"
    text = response.completion_items[0].text
    assert len(text) >= 20, "text is too short"


def test_skip_completion(augment_client: AugmentClient, completion_model_name: str):
    """Test a completion that involves skip token generation."""
    print("test_skip_completion", flush=True)
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    #
    response = augment_model_client.complete(
        """
def add_two_float(a: float, b: float) -> float:
    return a + b

def add_two_int(""",
        suffix=")\n\n",
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="arithmatic.py",
    )
    assert len(response.completion_items) > 0, "no completion items"
    result = response.completion_items[0]
    assert result.skipped_suffix == ")", "skipped suffix is not )"
    assert result.text == "a: int, b: int", "text is not a: int, b: int"
    assert result.suffix_replacement_text.startswith(
        ") -> int:\n"
    ), "suffix replacement text is not ) -> int:\n"


# Memorize is deprecated, but while around should probably still be tested


def test_memorize_api_auth(augment_client: AugmentClient):
    """Tests the API auth memorize endpoint."""
    path_name = "some/path/name.py"
    contents = "#include <vector>"
    local_blob_name = get_blob_name(path_name, contents.encode("utf-8"))
    blob_name = augment_client.memorize(
        contents, path=path_name, blob_name=local_blob_name
    )
    assert blob_name == local_blob_name, "blob name does not match"


def test_memorize_api_auth_no_blob_name(augment_client: AugmentClient):
    """Tests the API auth memorize endpoint without blob names."""
    path_name = "some/path/name.py"
    contents = "#include <vector>"
    blob_name = augment_client.memorize(contents, path=path_name, blob_name=None)
    local_blob_name = get_blob_name(path_name, contents.encode("utf-8"))
    assert blob_name == local_blob_name, "blob name does not match"


def test_batch_memorize_api_auth_no_blob_name(augment_client: AugmentClient):
    """Tests the API auth batch memorize endpoint without blob names."""
    blobs = [
        UploadContent(path_name="some/path/name.py", content="#include <vector>"),
        UploadContent(path_name="some/path/name2.py", content="#include <deque>"),
    ]
    blob_names = augment_client.batch_upload(blobs)
    assert len(blob_names) == 2, "incorrect number of blob names"
    assert blob_names[0] != blob_names[1], "blob names are the same"
    assert blob_names[0] == get_blob_name(
        blobs[0].path_name, blobs[0].content.encode("utf-8")
    ), "blob name does not match"
    assert blob_names[1] == get_blob_name(
        blobs[1].path_name, blobs[1].content.encode("utf-8")
    ), "blob name does not match"


def _wait_for_upload(func: Callable[[], CompleteResponse]) -> CompleteResponse:
    response = None
    for _ in range(900):
        response = func()
        if len(response.unknown_memory_names) == 0:
            break
        # the content is uploaded, but the indexer might not yet processed it
        # However, eventually, we should find it
        time.sleep(1)
    else:
        assert response, "response is None"
        pytest.fail(f"Memory name still missing: {response.unknown_memory_names}")
    assert response, "response is None"
    return response


def test_report_error(
    augment_client: AugmentClient,
):
    """Tests that we return a without an exception when sending an error report."""
    augment_client.report_error(
        original_request_id=None,
        sanitized_message="some error",
        stack_trace="some stack trace",
        diagnostics=[("some_key", "some_value")],
    )


def test_client_metrics(
    augment_client: AugmentClient,
):
    """Tests that we return a without an exception when sending client metrics."""
    augment_client.client_metrics(
        metrics=[
            ("generate_completion_count", 1),
            ("generate_completion_latency", 310),
            ("generate_completion_count", 1),
            ("webview__chat__chat-mention-folder", 1),
            ("generate_completion_latency", 320),
            ("webview__chat__chat-mention-folder", 1),
            ("unsupported_metric_example", 5),
        ]
    )


def test_memorize_and_complete_api_auth(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth memorize and completion endpoint together on a specific model."""
    memory_content = """a = {}\na['hello'] = 'world'\na['clue'] = 'dart'\na['sport'] = 'football'\n"""
    prompt = "from data import a\n\nassert a['clue'] =="
    blob_name = augment_client.memorize(memory_content, path="data.py")

    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    response = _wait_for_upload(
        lambda: augment_model_client.complete(
            prompt,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blobs=_blobs_json_single_blob(blob_name),
            path="main.py",
        )
    )
    actual_completion = response.text

    # don't do an exact match due to sampling
    assert "dart" in actual_completion, "dart not in actual_completion"


def test_memorize_and_complete_null_file(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth memorize and completion endpoint together on a specific model."""
    memory_content = bytearray([0] * 256 * 1024)
    prompt = "from data import a\n\nassert a['clue'] =="
    blob_name = augment_client.memorize(
        memory_content.decode("utf-8"),
        path=f"{random.randint(0, 1000000)}_data.py",
        retry_policy=Retry(retry_count=2, retry_sleep=0.1),
    )

    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    response = _wait_for_upload(
        lambda: augment_model_client.complete(
            prompt,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blobs=_blobs_json_single_blob(blob_name),
            path="main.py",
        )
    )
    assert response


def test_memorize_and_complete_large_file(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth memorize and completion endpoint together on a specific model using
    a very large file."""
    large_file_content = f"//{random.randint(0, 1000000)}\n" + pathlib.Path(
        "services/test/test_data/http_network_transaction_unittest.cc"
    ).read_text(encoding="utf-8")
    prompt = "// Copyright 2013 The"
    blob_name = augment_client.memorize(
        large_file_content, path="http_network_transaction_unittest.cc"
    )

    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    response = _wait_for_upload(
        lambda: augment_model_client.complete(
            prompt,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blobs=_blobs_json_single_blob(blob_name),
            path="http_network_transaction_unittest2.cc",
        )
    )
    actual_completion = response.text

    # don't do an exact match due to sampling
    assert "Chromium" in actual_completion, "Chromium not in actual_completion"


def test_overlap(augment_client: AugmentClient, completion_model_name: str):
    """Tests that overlap detection is marking out memory."""
    path_name = "data.py"
    memory_content = """a = {}\na['hello'] = 'world'\na['clue'] = 'dart'\na['sport'] = 'football'\n"""
    prompt = "a['clue'] = 'world'\na['sport'] = 'football'\nassert a['clue'] =="

    blob_name = _upload_one(augment_client, memory_content, path_name)

    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    response = _wait_for_upload(
        lambda: augment_model_client.complete(
            prompt,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blobs=_blobs_json_single_blob(blob_name),
            path=path_name,
            blob_name=blob_name,
            prefix_begin=0,
            cursor_position=len(prompt),
            suffix_end=len(prompt),
        )
    )

    actual_completion = response.text

    # the model has no way of knowing that dart was assigned to clue as
    # the overlap detection masked it out
    assert "dart" not in actual_completion, "dart in actual_completion"


def test_completion_api_auth_in_lm_harness_repo(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth memorize and completion endpoint together on a specific model.

    This test is memorizing a significant amount of code and is thus kind of
    a mini-stress test.
    """
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    p = pathlib.Path("../lm_evaluation_harness/lm-evaluation-harness")
    path_for_blobs = {
        path: blob_name for _, path, blob_name in memorize_path(p, augment_client)
    }
    blob_names = list(path_for_blobs.values())
    assert len(blob_names) > 1, "incorrect number of blob names"

    # Upload the current file so we have a blob name and position for it.
    path = "../lm_evaluation_harness/lm-evaluation-harness/lm_eval/models/starcoder.py"
    previous_content = "".join(
        [
            "import random\n",
            "from lm_eval.base import LM\n",
        ]
    )

    blob_name = _upload_one(augment_client, previous_content, path)
    assert path not in path_for_blobs, "path in path_for_blobs"
    path_for_blobs[path] = blob_name
    blob_names.append(blob_name)

    prompt_prefix = "".join(
        [
            "\n",
            "class StarCoderLM(LM):\n",
            "   def __init__(self):\n",
        ]
    )

    def _complete():
        return augment_model_client.complete(
            prompt=prompt_prefix,
            path=path,
            blobs=BlobsJson(
                checkpoint_id=None,
                added_blobs=blob_names,
                deleted_blobs=[],
            ),
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blob_name=blob_name,
            prefix_begin=0,
            cursor_position=len(prompt_prefix),
            suffix_end=len(prompt_prefix),
        )

    _wait_for_upload(_complete)

    start = time.time()
    response = _complete()
    end = time.time()
    actual_completion = response.text
    # don't do an exact match due to sampling
    assert (
        "super().__init__()" in actual_completion
        or "loglikelihood" in actual_completion
        or "pass" in actual_completion
    ), actual_completion
    latency_s = end - start
    assert latency_s < 4.0, "latency too high"


def test_memorize_and_complete_with_missing_memories(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth memorize and completion endpoint with missing memories."""
    # a random memory content to make it likely that the memory content was not seen before.
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    path_name = "foo/bar.txt"
    memory_content = (
        """a = {}\na['flower'] = 'world'\na['clue'] = 'dart'\na['sport'] = """
        + str(random.random())
    )

    local_blob_name = get_blob_name(path_name, memory_content.encode("utf-8"))
    prompt = "assert a['clue'] =="
    response = augment_model_client.complete(
        prompt,
        max_tokens=DEFAULT_TOKEN_BUDGET,
        blobs=_blobs_json_single_blob(local_blob_name),
        path=path_name,
    )
    assert (
        local_blob_name in response.unknown_memory_names
    ), "local_blob_name not in unknown_memory_names"

    blob_name = _upload_one(augment_client, memory_content, path_name)
    assert blob_name == local_blob_name, "blob_name does not match"

    for _ in range(120):
        response = augment_model_client.complete(
            prompt,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blobs=_blobs_json_single_blob(local_blob_name),
            path=path_name,
        )
        if len(response.unknown_memory_names) == 0:
            break
        # the content is uploaded, but the indexer might not yet processed it
        # However, eventually, we should find it
        time.sleep(1)
    else:
        pytest.fail("Memory name still missing")


def test_completion_api_auth_with_too_long_context(
    augment_model_client: AugmentModelClient,
):
    """Tests the API auth completion with a very long context."""
    prompt = str(list(range(10000)))[:5000]
    # setting max_tokens to 64 to make sure that the request does not time out on the test GPUs.
    response = augment_model_client.complete(
        prompt, max_tokens=DEFAULT_TOKEN_BUDGET, path=""
    )
    # it should have responded with a meaningful completion
    assert len(response.text) > 10, "text is too short"
    assert len(response.completion_items) > 0, "no completion items"
    assert (
        len(response.completion_items[0].text) > 10
    ), "completion item text is too short"


def test_find_missing_api_auth(augment_client: AugmentClient):
    """Tests the find_missing api."""
    # a random memory content to make it likely that the memory content was not seen before.
    path_name = "test_find_missing.txt"
    memory_content = "test_find_missing_api_auth" + str(random.random())
    local_blob_name = get_blob_name(path_name, memory_content.encode("utf-8"))
    bogus_blob_name = "bogus_blob_name"

    to_check = [local_blob_name, bogus_blob_name]

    # We have not uploaded anything yet, so both blob names should be reported missing
    response = augment_client.find_missing(model_name="", memory_object_names=to_check)
    assert len(response.unknown_memory_names) == 2, "unknown_memory_names length"
    assert (
        local_blob_name in response.unknown_memory_names
    ), "local_blob_name not in unknown_memory_names"
    assert (
        bogus_blob_name in response.unknown_memory_names
    ), "bogus_blob_name not in unknown_memory_names"

    # Upload content for local_blob_name only. It should no longer be reported as missing, but
    # it may be reported as nonindexed for a short amount of time. bogus_blob_name should always
    # be reported as missing.
    _upload_one(augment_client, memory_content, path_name)
    for _ in range(120):
        response = augment_client.find_missing(
            model_name="", memory_object_names=to_check
        )
        assert len(response.unknown_memory_names) == 1, "unknown_memory_names length"
        assert (
            bogus_blob_name in response.unknown_memory_names
        ), "bogus_blob_name not in unknown_memory_names"

        if not response.nonindexed_blob_names:
            break
        time.sleep(1)
    else:
        pytest.fail("Timeout")


def test_find_missing_api_invalid_blob_names(augment_client: AugmentClient):
    """Tests the find_missing api with invalid blob names."""
    valid_name = get_blob_name(
        "test_find_missing.txt", b"test_find_missing_api_invalid_blob_names"
    )
    too_short = valid_name[:-1]
    too_long = valid_name + valid_name[0]
    not_hex = valid_name[:-1] + "g"
    invalid_names = [too_short, too_long, not_hex]

    # Debatable whether we should return the invalid names in unknown_memory_names
    # and never fail, vs making this a client error.
    # It has historically been a client error to pass a blob name longer than 64 characters,
    # but other invalid names were permitted.
    # Now all names are permitted.
    response = augment_client.find_missing(
        model_name="", memory_object_names=[too_short, too_long, not_hex]
    )
    assert all(
        invalid_name in response.unknown_memory_names for invalid_name in invalid_names
    )


def test_hashtag(augment_model_client: AugmentModelClient):
    """Tests that the app pipeline can handle hashtags correctly.

    Note: We there was a bug where we incorrectly used token id 2 (aka "#") as end of stream token.
    """
    prompt = 'Example", "Value"],\n        }\n\n    def check_configuration(self, configuration):\n        """\n        Triggers when the configuration is checked, shortly before activation\n\n        Raise a errbot.ValidationException in case of an error\n\n        You should delete it if you\'re not using it to override any default behaviour\n        """\n        super(Augtest, self).check_configuration(configuration)\n        # log the contents of configuration\n        for key in configuration.keys():\n            print'
    response = augment_model_client.complete(
        prompt, max_tokens=DEFAULT_TOKEN_BUDGET, path=""
    )
    assert len(response.text) > 20, "text length"
    assert len(response.completion_items) > 0, "completion_items length"
    assert (
        len(response.completion_items[0].text) > 20
    ), "completion_items[0].text length"


def test_fill_in_the_middle(augment_client: AugmentClient, completion_model_name: str):
    """Tests that the app pipeline can handle (fill-in-the-middle) prefixes correctly.

    Correctly here means: it finishes and it generates a chunk of output.
    """
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    prompt = "def quickSort(array, low, high):"
    suffix = "quickSort(array, pi + 1, high)"
    response = augment_model_client.complete(
        prompt,
        max_tokens=DEFAULT_TOKEN_BUDGET,
        suffix=suffix,
        path="quicksort.py",
    )
    assert len(response.text) > 0, "text length"
    assert len(response.completion_items) > 0, "completion_items length"
    assert len(response.completion_items[0].text) > 0, "completion_items[0].text length"


def test_chat(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app can handle chat requests.

    Correctly here means: it finishes and it generates output that includes expected text.
    """
    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)
    response = augment_model_client.chat(
        selected_code="import enum\nimport json\nfrom typing import Optional\n\nfrom base.prompt_format_chat.prompt_formatter import (\n    ChatPromptFormatter,\n    ChatPromptInput,\n    ChatPromptOutput\n)",
        message="What are the imports in the code?",
        prefix="",
        suffix="",
        path="base/prompt_format_chat/binks_prompt_formatter.py",
    )
    text = response.text.lower()
    assert len(text) > 50, "text is too short"
    assert "json" in text, "json not in text"
    assert "enum" in text, "enum not in text"
    assert response.stop_reason == "end_turn"


def test_chat_stream(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app can handle chat requests.

    Correctly here means: it finishes and it generates output that includes expected text.
    """
    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)

    def try_chat_stream():
        response_stream = augment_model_client.chat_stream(
            selected_code="import enum\nimport json\nfrom typing import Optional\n\nfrom base.prompt_format_chat.prompt_formatter import (\n    ChatPromptFormatter,\n    ChatPromptInput,\n    ChatPromptOutput\n)",
            message="What are the imports in the code?",
            prefix="",
            suffix="",
            path="base/prompt_format_chat/binks_prompt_formatter.py",
            blob_names=[],
        )
        request_ids = set()
        text = ""
        stop_reasons = set()
        for response in response_stream:
            request_ids.add(response.request_id)
            stop_reasons.add(response.stop_reason)
            text += response.text
        text = text.lower()
        assert len(request_ids) == 1
        assert len(text) > 50
        assert "json" in text
        assert "enum" in text
        assert stop_reasons == {"end_turn", None}

    run_with_retries(try_chat_stream)


def test_chat_with_nodes(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app can handle chat requests with nodes."""
    if chat_model_name == "gemini-2-flash-001-simple-port":
        # We should make as many backend models as possible able to handle any valid chat request, but
        # for now it's important that we get some coverage across the models that we currently use to
        # to handle these full-featured requests. That means sonnet at the moment
        pytest.xfail(
            "gemini-2-flash-001-simple-port doesn't handle any non-text nodes in user message"
        )
    request_nodes = [
        public_api_pb2.ChatRequestNode(
            id=1,
            type=public_api_pb2.ChatRequestNodeType.TEXT,
            text_node=public_api_pb2.ChatRequestText(content="Hello, world!"),
        ),
        public_api_pb2.ChatRequestNode(
            id=2,
            type=public_api_pb2.ChatRequestNodeType.IDE_STATE,
            ide_state_node=public_api_pb2.ChatRequestIdeState(
                workspace_folders=[
                    public_api_pb2.WorkspaceFolderInfo(
                        repository_root="/home/<USER>/projects/repo",
                        folder_root="/home/<USER>/projects/repo/src",
                    )
                ],
                current_terminal=public_api_pb2.TerminalInfo(terminal_id=1),
            ),
        ),
        public_api_pb2.ChatRequestNode(
            id=3,
            type=public_api_pb2.ChatRequestNodeType.EDIT_EVENTS,
            edit_events_node=public_api_pb2.ChatRequestEditEvents(
                edit_events=[
                    public_api_pb2.ChatRequestFileEdit(
                        path="/home/<USER>/projects/repo/src/main.py",
                        before_blob_name="abc123",
                        after_blob_name="def456",
                        edits=[
                            public_api_pb2.ChatRequestSingleEdit(
                                before_line_start=10,
                                before_text="old code",
                                after_line_start=10,
                                after_text="new code",
                            ),
                        ],
                    ),
                ],
            ),
        ),
        public_api_pb2.ChatRequestNode(
            id=4,
            type=public_api_pb2.ChatRequestNodeType.CHECKPOINT_REF,
        ),
    ]
    request = public_api_pb2.ChatRequest(
        model=chat_model_name,
        message="",
        selected_code="",
        prefix="",
        suffix="",
        path="",
        feature_detection_flags=public_api_pb2.ChatFeatureDetectionFlags(
            support_raw_output=True,
        ),
        blobs=public_api_pb2.Blobs(
            checkpoint_id=None,
            added_blobs=[],
            deleted_blobs=[],
        ),
        nodes=request_nodes
        + [
            public_api_pb2.ChatRequestNode(
                id=5,
                type=public_api_pb2.ChatRequestNodeType.TEXT,
                text_node=public_api_pb2.ChatRequestText(
                    content="Include the word hamburger in your reply"
                ),
            )
        ],
        chat_history=[
            public_api_pb2.Exchange(
                request_id="434f8980-8490-453c-8ba7-4eb4a7702d49",
                request_nodes=request_nodes,
                response_nodes=[
                    public_api_pb2.ChatResultNode(
                        id=1,
                        type=public_api_pb2.ChatResultNodeType.RAW_RESPONSE,
                        content='<augment_code_snippet path="hello.py" mode="EXCERPT">\n```python\nprint(\'Hello world!\')\n```\n</augment_code_snippet>\n\n',
                    ),
                ],
            )
        ],
    )
    chat_response, chat_request_id = augment_client.post_proto(
        "chat",
        request,
        public_api_pb2.ChatResponse(),
        include_default_value_fields=False,
    )
    assert "hamburger" in chat_response.text.lower()


def test_chat_stream_too_large(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app fails with payload too large on large input.

    Correctly here means: it finishes and it generates an error.
    """
    if chat_model_name == "claude-sonnet-v17-c4-p2-chat":
        pytest.skip(
            "claude-sonnet-v17-c4-p2-chat is too big to fail on payload too large"
        )
    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)
    response_stream = augment_model_client.chat_stream(
        selected_code="import enum\nimport json\nfrom typing import Optional\n\nfrom base.prompt_format_chat.prompt_formatter import (\n    ChatPromptFormatter,\n    ChatPromptInput,\n    ChatPromptOutput\n)",
        message="This is a really big message\n" * 16 * 1024,
        prefix="",
        suffix="",
        path="base/prompt_format_chat/binks_prompt_formatter.py",
        blob_names=[],
    )
    with pytest.raises(ClientException) as ex:
        for response in response_stream:
            print(response)
    assert ex.value.response.status_code == 413


def test_chat_with_history(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app can handle chat requests with chat history.

    Correctly here means: it finishes and it generates output that includes expected text based on the previous exchange.
    """
    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)
    exchange1 = Exchange(
        "What are the imports in the code?",
        "The codebase uses two external Python packages: json, enum",
    )
    response = augment_model_client.chat(
        selected_code="",
        message="Tell me about these two packages",
        chat_history=[exchange1],
        prefix="",
        suffix="",
        path="base/prompt_format_chat/binks_prompt_formatter.py",
    )
    text = response.text.lower()
    assert len(text) > 50, "text is too short"
    assert "json" in text, "json not in text"
    assert "enum" in text, "enum not in text"


def test_chat_stream_with_history(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app can handle chat requests with chat history.

    Correctly here means: it finishes and it generates output that includes expected text based on the previous exchange.
    """
    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)
    exchange1 = Exchange(
        "What are the imports in the code?",
        "The codebase uses two external Python packages: json, enum",
    )

    def try_chat_stream():
        response_stream = augment_model_client.chat_stream(
            selected_code="",
            message="Tell me about these two packages",
            chat_history=[exchange1],
            prefix="",
            suffix="",
            path="",
            blob_names=[],
        )
        request_ids = set()
        text = ""
        for response in response_stream:
            request_ids.add(response.request_id)
            text += response.text
        text = text.lower()
        assert len(request_ids) == 1
        assert len(text) > 50
        assert "json" in text
        assert "enum" in text

    run_with_retries(try_chat_stream)


def test_fim_retrieval(
    augment_client: AugmentClient, augment_model_client: AugmentModelClient
):
    """Tests that the memorization protocol works correctly."""
    path_name = "test.py"
    context = "def testPrint():"
    blob_name = _upload_one(augment_client, context, path_name)
    local_blob_name = get_blob_name(path_name, context.encode("utf-8"))
    assert blob_name == local_blob_name, "blob_name does not match"

    prompt = "def testPrint():"
    suffix = "print('2')\nprint('3')"
    response = augment_model_client.complete(
        prompt,
        max_tokens=DEFAULT_TOKEN_BUDGET,
        blobs=_blobs_json_single_blob(blob_name),
        suffix=suffix,
        path=path_name,
    )
    filter_score = response.completion_items[0].filter_score
    assert filter_score is not None, "filter_score is None"
    assert "print('1')" in response.text, "print('1') not in text"
    assert len(response.text) >= 10, "text is too short"
    assert len(response.completion_items) > 0, "completion_items is empty"
    assert (
        len(response.completion_items[0].text) >= 10
    ), "completion_item text is too short"


def test_get_models(augment_client: AugmentClient, completion_model_name: str):
    """Tests the get models api endpoint."""
    if not completion_model_name:
        pytest.skip("Skipping test when deploy model is tested")
        return
    response = augment_client.get_models()
    model_names = set(m.name for m in response.models)
    internal_model_names = set(m.internal_name for m in response.models)
    assert response.default_model, "default_model is not set"
    assert (
        sha256(completion_model_name.encode()).hexdigest() in model_names
    ), "sha256 of model_name not in model_names"
    assert (
        completion_model_name in internal_model_names
    ), "completion_model_name not in internal_model_names"
    assert (
        response.user_tier == UserTier.ENTERPRISE_TIER
    ), "user_tier is not ENTERPRISE_TIER"


def test_completion_with_special_token(
    augment_client: AugmentClient, completion_model_name: str
):
    """Tests the API auth completion endpoint with the prompt containing special tokens."""
    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )
    response = augment_model_client.complete(
        """def is_special_token(token: str):
    return token in ["<|fim-eos|>",
                     "<|endoftext|>",
                     """,
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="token_utils.py",
    )
    text = response.text
    # if endoftext was processed as special token, the model wouldn't generate a token
    assert len(text) > 0, "text is too short"
    assert len(response.completion_items) > 0, "completion_items is empty"
    assert (
        len(response.completion_items[0].text) > 0
    ), "completion_item text is too short"


def test_memorize_and_complete_with_special_tokens_in_blob(
    augment_client: AugmentClient, augment_model_client: AugmentModelClient
):
    """Tests the API auth memorize and completion endpoint with special token text in the blob content."""
    blob_content = """special_tokens = ["<|endoftext|>"]"""
    prompt = """from foo.token_list import special_tokens

def test_special_token_len():
    assert special_tokens == """
    blob_name = augment_client.memorize(blob_content, path="foo/token_list.py")

    for _ in range(120):
        response = augment_model_client.complete(
            prompt,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blobs=_blobs_json_single_blob(blob_name),
            path="foo/bar.py",
        )
        if response.unknown_memory_names:
            time.sleep(1)
            continue
        actual_completion = response.text
        # if endoftext was processed as special token, the model wouldn't generate a token
        assert len(actual_completion) > 0, "text is too short"
        assert len(response.completion_items) > 0, "completion_items is empty"
        assert (
            len(response.completion_items[0].text) > 0
        ), "completion_item text is too short"
        break
    else:
        pytest.fail("Timeout")


def test_language(augment_client: AugmentClient):
    """Tests if the supported languages show up for a given model."""
    response = augment_client.get_models()
    assert len(response.languages) > 0, "languages is empty"
    # realistically any model we use will support Python
    assert any(
        m for m in response.languages if m.name == "Python"
    ), "Python not in languages"


def test_completion_resolutions(augment_client: AugmentClient) -> None:
    uuid0 = uuid.uuid4()
    uuid1 = uuid.uuid4()
    resolutions: list[CompletionResolution] = [
        CompletionResolution(
            request_id=str(uuid0),
            emit_time_sec=100,
            emit_time_nsec=101,
            resolve_time_sec=102,
            resolve_time_nsec=103,
            accepted_idx=0,
        ),
        CompletionResolution(
            request_id=str(uuid1),
            emit_time_sec=200,
            emit_time_nsec=201,
            resolve_time_sec=202,
            resolve_time_nsec=203,
            accepted_idx=-1,
        ),
    ]
    augment_client.resolve_completions(resolutions)


def test_completion_with_checkpoint(
    augment_client: AugmentClient, completion_model_name: str
):
    """Ensure that completions work when the checkpoint is provided.

    Also tests that a bad checkpoint is handled correctly.
    """
    # set up the initial checkpoint of blob names
    contents = [
        "hello,",
        "is it me",
        "you're",
        "looking for?",
    ]
    blob_names = [
        get_blob_name(f"file_{i}", x.encode("utf-8")) for i, x in enumerate(contents)
    ]
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=blob_names[:1],
        deleted_blobs=[],
    )
    # note: this checkout doesn't have its blobs actually uploaded
    checkpoint = augment_client.checkpoint_blobs(blobs)
    assert checkpoint, "checkpoint is empty"

    blobs = BlobsJson(
        checkpoint_id=checkpoint,
        added_blobs=blob_names[1:2],
        deleted_blobs=[],
    )

    # Setup the prompt correctly for the signature retriever, if available.
    p = pathlib.Path("base/static_analysis")
    path_content_blobs = {
        path: (content, blob_name)
        for content, path, blob_name in memorize_path(p, augment_client)
    }
    blob_names = list(c[1] for c in path_content_blobs.values())
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=blob_names,
        deleted_blobs=[],
    )
    checkpoint = augment_client.checkpoint_blobs(blobs)
    assert checkpoint, "checkpoint is empty"
    blobs = BlobsJson(
        checkpoint_id=checkpoint,
        added_blobs=[],
        deleted_blobs=[],
    )

    path = "testdata/sort_people_test.py"
    previous_content, blob_name = path_content_blobs[path]
    unseen_previous_content = ("import people\nimport sort_people\n",)

    prompt_prefix = previous_content[len(unseen_previous_content) :]

    augment_model_client = augment_client.client_for_model(
        model_name=completion_model_name
    )

    def _complete(blobs: BlobsJson):
        return augment_model_client.complete(
            prompt=prompt_prefix,
            path=path,
            max_tokens=DEFAULT_TOKEN_BUDGET,
            blob_name=blob_name,
            prefix_begin=len(unseen_previous_content),
            cursor_position=len(previous_content),
            suffix_end=len(previous_content),
            blobs=blobs,
        )

    # We don't care if the blobs have been indexed, just that there is a completion.
    response = _complete(blobs)

    assert len(response.completion_items) > 0, "completion_items is empty"
    assert not response.checkpoint_not_found, "checkpoint_not_found is set"

    # Ensure a bad checkpoint is handled
    bad_checkpoint = checkpoint[::-1]
    assert bad_checkpoint != checkpoint, "bad_checkpoint == checkpoint"
    blobs = BlobsJson(
        checkpoint_id=bad_checkpoint,
        added_blobs=blob_names[1:2],
        deleted_blobs=[],
    )
    response = _complete(blobs)
    assert response.checkpoint_not_found, "checkpoint_not_found is not set"


def test_completion_api_auth_with_blob_names_that_resolve_to_empty(
    augment_client: AugmentClient,
):
    """Ensure that completions work when the blob names resolve to empty.

    This can happen when the checkpointed blobs + added/removed blobs result in an empty set.
    """
    contents = [
        "hello,",
        "is it me",
        "you're",
        "looking for?",
    ]
    blob_names = [
        get_blob_name(f"file_{i}", x.encode("utf-8")) for i, x in enumerate(contents)
    ]
    blob_names.sort()
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=blob_names,
        deleted_blobs=[],
    )
    checkpoint = augment_client.checkpoint_blobs(blobs)
    assert checkpoint, "checkpoint is empty"

    # delete all checkpointed blob names
    blobs = BlobsJson(
        checkpoint_id=checkpoint,
        added_blobs=[],
        deleted_blobs=blob_names,
    )
    augment_model_client = augment_client.client_for_model(model_name="")
    response = augment_model_client.complete(
        "void quicksort(std::vector<int> &i) {",
        max_tokens=DEFAULT_TOKEN_BUDGET,
        path="quicksort.cc",
        blobs=blobs,
    )
    assert len(response.completion_items) > 0, "completion_items is empty"


def test_checkpoint_blobs_api_auth_empty_blobs(augment_client: AugmentClient):
    """Ensure that we handle checkpointing an empty Blobs set.

    It's possible that a Blobs set is logically empty, either as input
    or as a result of adding/deleting blobs from a checkpoint. Here we
    test that that the resulting checkpoint is an empty string.

    Also test the case where the feature is disabled, to ensure that we return
    the right status code.
    """
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=[],
        deleted_blobs=[],
    )

    checkpoint = augment_client.checkpoint_blobs(blobs)
    assert checkpoint == "", "checkpoint is not empty"


def test_checkpoint_blobs_sorting(augment_client: AugmentClient):
    """Test that blobs names don't have to be sorted in /checkpoint-blobs requests."""
    blob_names1 = [
        get_blob_name(f"path_1_{i}", str(random.random()).encode("utf-8"))
        for i in range(5)
    ]
    blob_names1.sort()

    # unsorted blob names should pass, without a checkpoint ID
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=list(reversed(blob_names1)),
        deleted_blobs=[],
    )
    # Use the raw API to force sending unsorted blobs
    response, _ = augment_client._post(
        "checkpoint-blobs", json={"blobs": asdict(blobs)}
    )
    assert response.ok, "response is not ok"
    checkpoint = response.json()["new_checkpoint_id"]
    assert checkpoint, "checkpoint is empty"

    blob_names2 = [
        get_blob_name(f"path_2_{i}", str(random.random()).encode("utf-8"))
        for i in range(5)
    ]
    blob_names2.sort()

    # unsorted blob names should also pass when there is a checkpoint ID
    blobs = BlobsJson(
        checkpoint_id=checkpoint,
        added_blobs=list(reversed(blob_names2)),
        deleted_blobs=[],
    )
    response, _ = augment_client._post(
        "checkpoint-blobs", json={"blobs": asdict(blobs)}
    )
    assert response.ok, "response is not ok"
    checkpoint = response.json()["new_checkpoint_id"]
    assert checkpoint, "checkpoint is empty"

    blobs = BlobsJson(
        checkpoint_id=checkpoint,
        added_blobs=[],
        deleted_blobs=list(reversed(blob_names1)),
    )
    response, _ = augment_client._post(
        "checkpoint-blobs", json={"blobs": asdict(blobs)}
    )
    assert response.ok, "response is not ok"
    checkpoint = response.json()["new_checkpoint_id"]
    assert checkpoint, "checkpoint is empty"


def test_completion_with_probe_only(augment_model_client: AugmentModelClient):
    """Tests that unknown memory names are returned when probe_only is set."""
    path_name = "fake/path.txt"
    memory_content = str(random.random())

    local_blob_name = get_blob_name(path_name, memory_content.encode("utf-8"))
    response = augment_model_client.complete(
        prompt="",
        max_tokens=DEFAULT_TOKEN_BUDGET,
        blobs=_blobs_json_single_blob(local_blob_name),
        path=path_name,
        probe_only=True,
        blob_name=None,
        prefix_begin=0,
        cursor_position=0,
        suffix_end=0,
    )

    assert (
        local_blob_name in response.unknown_memory_names
    ), "local_blob_name not in unknown_memory_names"


def test_completion_with_extension_status_check(
    augment_model_client: AugmentModelClient,
):
    """Tests that we return a response in the extension status check."""
    response = augment_model_client.complete(
        prompt="this is the prefix",
        suffix="this is the suffix",
        path="/this/is/the/path",
        blob_name=None,
        prefix_begin=0,
        cursor_position=0,
        suffix_end=0,
        probe_only=False,
    )
    # We just want to make sure we return a completion without an exception.
    assert response.request_id, "response.request_id is empty"


def _do_operation(
    operation_type,
    client: AugmentModelClient,
    blobs: BlobsJson,
):
    if operation_type == "completion":
        return client.complete(
            prompt="this is the prefix",
            suffix="this is the suffix",
            path="some/path",
            blob_name=None,
            prefix_begin=0,
            cursor_position=0,
            suffix_end=0,
            probe_only=False,
            blobs=blobs,
        )
    elif operation_type == "autofix":
        # TODO: also do the check call here?
        return client.autofix_plan(
            command=AutofixCommand(
                input="cargo test",
                output="test result: FAILED\nerror: test failed",
                exit_code=1,
            ),
            vcs_change=VCSChange(
                # TODO: populate
                working_directory_changes=[],
                commits=[],
            ),
            blobs=blobs,
        )
    elif operation_type == "chat":
        return client.chat(
            selected_code="def test_sort_people():",
            message="What does this function do?",
            prefix="",
            suffix="",
            path="some/path",
            blobs=blobs,
        )
    elif operation_type == "next_edit":
        path = "some/path"
        code = "def hello_world"
        # For testing missing blobs behavior, we use CURSOR scope instead of FILE
        # scope because the latter always tries to retrieve locations from the current
        # file, which may have not been indexed yet.
        stream = client.next_edit_stream(
            mode="FORCED",
            scope="CURSOR",
            instruction="",
            prefix="",
            suffix="",
            # Provide something to edit
            selected_text=code,
            path=path,
            blobs=blobs,
        )
        resp_list = list(stream)
        assert len(resp_list) > 0, "response list is empty"
        return resp_list[-1]
    elif operation_type == "smart_paste":
        stream = client.smart_paste_stream(
            selected_text='print("Hello, world!")',  # No selection, we're pasting new code
            prefix=" {",
            suffix="",
            path="some/path",
            code_block='print("Hello, world!")',
            blobs=blobs,
        )
        resp_list = list(stream)
        assert len(resp_list) > 0, "response list is empty"
        return resp_list[-1]
    else:
        assert False, f"unknown operation type {operation_type}"


def test_operation_with_missing_blob_or_checkpoint(
    augment_client: AugmentClient, op_type, model_name
):
    """Test that operations correctly return missing blobs/checkpoints."""
    if model_name == "gemini-2-flash-001-simple-port":
        pytest.skip("gemini-2-flash-001-simple-port doesn't do retrieval")

    augment_model_client = augment_client.client_for_model(model_name=model_name)
    new_blob = get_blob_name("new_path", str(random.random()).encode("utf-8"))
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=[new_blob],
        deleted_blobs=[],
    )

    response = _do_operation(op_type, augment_model_client, blobs)
    assert not response.checkpoint_not_found, "checkpoint_not_found is set"
    if op_type == "completion":
        completion_response = cast(CompleteResponse, response)
        assert completion_response.unknown_memory_names == [new_blob]
    elif op_type in ["chat", "next_edit", "autofix"]:
        other_response = cast(
            Union[
                EditResponse,
                ChatResponse,
                NextEditResponse,
                AutofixPlanResponse,
                InstructionResponse,
            ],
            response,
        )
        assert other_response.unknown_blob_names == [new_blob]
    elif op_type == "smart_paste":
        # smart paste doesn't use blobs
        other_response = cast(InstructionResponse, response)
        assert (
            other_response.unknown_blob_names == []
        ), "unknown_blob_names is not empty"
    else:
        assert False, f"unknown operation type {op_type}"

    blobs = BlobsJson(
        checkpoint_id="missing-checkpoint",
        added_blobs=[],
        deleted_blobs=[],
    )

    response = _do_operation(op_type, augment_model_client, blobs)
    if op_type in ["completion", "chat", "next_edit", "autofix"]:
        assert response.checkpoint_not_found, "checkpoint_not_found is set"
    elif op_type == "smart_paste":
        # smart paste doesn't use blobs
        assert not response.checkpoint_not_found, "checkpoint_not_found is set"
    else:
        assert False, f"unknown operation type {op_type}"
    if op_type == "completion":
        completion_response = cast(CompleteResponse, response)
        assert (
            completion_response.unknown_memory_names == []
        ), "unknown_memory_names is not empty"
    elif op_type in ["chat", "next_edit", "autofix"]:
        other_response = cast(
            Union[EditResponse, ChatResponse, NextEditResponse, AutofixPlanResponse],
            response,
        )
        assert (
            other_response.unknown_blob_names == []
        ), "unknown_blob_names is not empty"
    elif op_type == "smart_paste":
        # smart paste doesn't use blobs
        other_response = cast(InstructionResponse, response)
        assert not other_response.checkpoint_not_found, "checkpoint_not_found is set"
        assert (
            other_response.unknown_blob_names == []
        ), "unknown_blob_names is not empty"
    else:
        assert False, f"unknown operation type {op_type}"


def test_operation_with_checkpoint(
    augment_client: AugmentClient, op_type: str, model_name: str
):
    """Test that operations with checkpoints succeed."""
    # Initial op with no checkpoint
    augment_model_client = augment_client.client_for_model(model_name=model_name)
    blob_names1 = [
        get_blob_name(f"path_1_{i}", str(random.random()).encode("utf-8"))
        for i in range(5)
    ]
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=blob_names1,
        deleted_blobs=[],
    )
    response = _do_operation(op_type, augment_model_client, blobs)
    assert not response.checkpoint_not_found, "checkpoint_not_found is set"

    checkpoint = augment_client.checkpoint_blobs(blobs)

    # Second op, with checkpoint
    blob_names2 = [
        get_blob_name(f"path_2_{i}", str(random.random()).encode("utf-8"))
        for i in range(5)
    ]
    blobs = BlobsJson(
        checkpoint_id=checkpoint,
        added_blobs=blob_names2,
        deleted_blobs=blob_names1,
    )
    response = _do_operation(op_type, augment_model_client, blobs)
    assert not response.checkpoint_not_found, "checkpoint_not_found is set"


def test_multi_tenant_completion_simple(
    application_shard: ApplicationShard,
    application_shard_deploy: k8s_test_helper.DeployInfo,
    tokens: TokenUtil | None,
):
    """Tests that the app can handle multiple tenants."""
    if tokens is None:
        pytest.skip("tokens not available")
        return

    with k8s_test_helper.new_tenant(application_shard_deploy, "tenant1"):
        token = tokens.add_token("user2", "tenant1")

        second_tenant_client = AugmentClient(
            application_shard.url,
            token=token,
            retry_count=16,
            retry_sleep=10,
            verify=False,
        )
        print("Using session", second_tenant_client.request_session_id)

        memory_content = """a = {}\na['hello'] = 'world'\na['clue'] = 'dart'\na['sport'] = 'football'\n"""
        prompt = "from data import a\n\nassert a['clue'] =="
        blob_name = _upload_one(second_tenant_client, memory_content, "data.py")

        augment_model_client = second_tenant_client.client_for_model(model_name="")
        response = _wait_for_upload(
            lambda: augment_model_client.complete(
                prompt,
                max_tokens=DEFAULT_TOKEN_BUDGET,
                blobs=_blobs_json_single_blob(blob_name),
                path="main.py",
            )
        )
        actual_completion = response.text

        # don't do an exact match due to sampling
        assert "dart" in actual_completion, "dart not in actual_completion"


@pytest.mark.skip(reason="flaky")
def test_multi_tenant_completion_no_data_sharing(
    application_shard: ApplicationShard,
    application_shard_deploy: k8s_test_helper.DeployInfo,
    tokens: TokenUtil | None,
):
    """Tests that the app can handle multiple tenants."""
    if tokens is None:
        pytest.skip("tokens not available")
        return

    with k8s_test_helper.new_tenant(application_shard_deploy, "tenant1"):
        with k8s_test_helper.new_tenant(application_shard_deploy, "tenant2"):
            token = tokens.add_token("user2", "tenant1")

            first_tenant_client = AugmentClient(
                application_shard.url,
                token=token,
                retry_count=16,
                retry_sleep=10,
                verify=False,
            )
            print("Using session", first_tenant_client.request_session_id)

            memory_content = """a = {}\na['hello'] = 'world'\na['clue'] = 'dart'\na['sport'] = 'football'\n"""
            prompt = "from data import a\n\nassert a['clue'] =="
            blob_name = _upload_one(first_tenant_client, memory_content, "data.py")

            augment_model_client = first_tenant_client.client_for_model(model_name="")
            response = _wait_for_upload(
                lambda: augment_model_client.complete(
                    prompt,
                    max_tokens=DEFAULT_TOKEN_BUDGET,
                    blobs=_blobs_json_single_blob(blob_name),
                    path="main.py",
                )
            )
            actual_completion = response.text

            # don't do an exact match due to sampling
            assert "dart" in actual_completion, "dart not in actual_completion"

            token = tokens.add_token("user3", "tenant2")

            second_tenant_client = AugmentClient(
                application_shard.url,
                token=token,
                retry_count=16,
                retry_sleep=10,
                verify=False,
            )
            print("Using session", second_tenant_client.request_session_id)

            augment_model_client = second_tenant_client.client_for_model(model_name="")
            completion_no_content = augment_model_client.complete(
                prompt,
                max_tokens=DEFAULT_TOKEN_BUDGET,
                blobs=_blobs_json_single_blob(blob_name),
                path="main.py",
            )
            # Expect the completion not to find the data.py memory content or leak tenant1's data
            assert (
                len(completion_no_content.unknown_memory_names) == 1
            ), "unknown_memory_names is not empty"
            assert (
                "dart" not in completion_no_content.text
            ), "dart in completion_no_content.text"

            # Once tenant2 memorizes data.py we do expect its completions to see the content
            blob_name = _upload_one(second_tenant_client, memory_content, "data.py")
            response = _wait_for_upload(
                lambda: augment_model_client.complete(
                    prompt,
                    max_tokens=DEFAULT_TOKEN_BUDGET,
                    blobs=_blobs_json_single_blob(blob_name),
                    path="main.py",
                )
            )
            assert "dart" in response.text, "dart not in response.text"


def test_list_external_source_types(augment_client: AugmentClient):
    """Tests the list-external-source-types api endpoint."""
    response = augment_client.list_external_source_types()
    assert len(response) > 0, "response is empty"
    assert "DOCUMENTATION_SET" in response, "DOCUMENTATION_SET not in response"


def test_search_external_sources(augment_client: AugmentClient):
    """Tests the search-external-sources api endpoint."""
    # Right now, dev is hardcoded with this dataset
    # TODO: make this test more robust even if docsets change
    response = augment_client.search_external_sources(
        query="graphite", source_types=["DOCUMENTATION_SET"]
    )

    assert len(response) > 0, "response is empty"
    assert response[0].id == "docset://graphite.dev", "id is not docset://graphite.dev"
    assert response[0].name == "Graphite", "name is not Graphite"
    assert (
        response[0].title.lower() == "Documentation for Graphite".lower()
    ), "title is not Documentation for Graphite"
    assert (
        response[0].source_type == "DOCUMENTATION_SET"
    ), "source_type is not DOCUMENTATION_SET"


def test_search_implicit_external_sources(augment_client: AugmentClient):
    """Tests the get-implicit-external-sources api endpoint."""
    response = augment_client.get_implicit_external_sources(
        message="what are the flags for `gt move`?"
    )

    assert len(response) == 1, "response is empty"
    assert response[0].id == "docset://graphite.dev", "id is not docset://graphite.dev"
    assert (
        response[0].source_type == "DOCUMENTATION_SET"
    ), "source_type is not DOCUMENTATION_SET"


def _flatten(values: list[Any]):
    r = []
    for e in values:
        if isinstance(e, list):
            r.extend(_flatten(e))
        else:
            r.append(e)
    return r


def test_chat_stream_with_docsets(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app can handle chat requests with a documentation set (docset)

    Correctly here means: it finishes and it generates output that includes expected text.
    """
    if chat_model_name == "gemini-2-flash-001-simple-port":
        pytest.skip("gemini-2-flash-001-simple-port doesn't do retrieval")

    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)

    for _ in range(8):

        def try_chat_stream():
            response_stream = augment_model_client.chat_stream(
                selected_code="",
                message="what are the flags for `gt move` ?",
                prefix="",
                suffix="",
                path="",
                blob_names=[],
                external_source_ids=["docset://graphite.dev"],
                timeout=120,
            )
            text = ""
            external_sources = set()
            for response_elem in response_stream:
                text += response_elem.text
                if response_elem.incorporated_external_sources:
                    external_sources.update(
                        [
                            s.source_name
                            for s in response_elem.incorporated_external_sources
                        ]
                    )
            return "onto" in text and external_sources == {"graphite.dev"}

        ok = run_with_retries(try_chat_stream)

        if ok:
            break
        else:
            # retry to give potential content manager reads time to catch up
            time.sleep(15)
    else:
        pytest.fail(
            "chat request did not return expected response after multiple attempts"
        )


def test_chat_with_docsets_non_streamed(
    augment_client: AugmentClient, chat_model_name: str
):
    """Tests that the app can handle non-streamed chat requests with a documentation set (docset)

    Correctly here means: it finishes and it generates output that includes expected text.
    """
    if chat_model_name == "gemini-2-flash-001-simple-port":
        pytest.skip("gemini-2-flash-001-simple-port doesn't do retrieval")

    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)

    for _ in range(8):

        def try_chat():
            response_stream = augment_model_client.chat(
                selected_code="",
                message="what are the flags for `gt move` ?",
                prefix="",
                suffix="",
                path="",
                blob_names=[],
                external_source_ids=["docset://graphite.dev"],
                timeout=120,
            )
            text = response_stream.text
            external_sources = response_stream.incorporated_external_sources
            external_source_names = [s.source_name for s in (external_sources or [])]
            assert (
                response_stream.workspace_file_chunks is None
                or len(response_stream.workspace_file_chunks) == 0
            )
            return "onto" in text and external_source_names == ["graphite.dev"]

        ok = run_with_retries(try_chat)

        if ok:
            break
        else:
            # retry to give potential content manager reads time to catch up
            time.sleep(15)
    else:
        pytest.fail(
            "chat request did not return expected response after multiple attempts"
        )


def test_chat_stream_with_context(augment_client: AugmentClient, chat_model_name: str):
    """Tests that the app can handle chat requests with local blob context.

    This is also at time of writing, the first test of chat with retrieval.

    Correctly here means: it finishes and it generates output that includes expected text.
    """
    if chat_model_name == "gemini-2-flash-001-simple-port":
        pytest.skip("gemini-2-flash-001-simple-port doesn't do retrieval")

    print(
        "test_chat_stream_with_context: chat_model_name: ", chat_model_name, flush=True
    )
    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)

    path_name = "people/people_manager.py"
    context = "import people\n\ndef get_person():\n    return Person('Alice', 42, 'Engineer')\n"
    blob_name = _upload_one(augment_client, context, path_name)

    # test case where it has to retrieve from the context (no docsets)
    for _ in range(16):

        def try_chat_stream():
            response_stream = augment_model_client.chat_stream(
                selected_code="",
                message="Does the function get_person in people_manager.py take an argument? Answer yes or no. Please don't generate more than a single sentence.",
                prefix="",
                suffix="",
                path="",
                blob_names=[blob_name],
                timeout=120,
            )
            has_unknown_blobs = False
            workspace_file_chunks = set()
            for response_elem in response_stream:
                if len(response_elem.unknown_blob_names) > 0:
                    has_unknown_blobs = True
                if response_elem.workspace_file_chunks:
                    workspace_file_chunks.update(
                        [
                            chunk.blob_name
                            for chunk in response_elem.workspace_file_chunks
                        ]
                    )
            # we don't check for the text (because most small chat models don't get it right), but
            # we check if the right blob was retrieved and used
            return blob_name in workspace_file_chunks and not has_unknown_blobs

        ok = run_with_retries(try_chat_stream)

        if ok:
            break
        else:
            # retry to give indexing and content manager reads time to catch up
            time.sleep(15)
    else:
        pytest.fail(
            "chat request did not return expected response after multiple attempts"
        )


def test_chat_stream_with_docsets_and_context(
    augment_client: AugmentClient, chat_model_name: str
):
    """Tests that the app can handle chat requests with a documentation set (docset) with local blob context.

    Correctly here means: it finishes and it generates output that includes expected text.
    """
    if chat_model_name == "gemini-2-flash-001-simple-port":
        pytest.skip("gemini-2-flash-001-simple-port doesn't do retrieval")

    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)

    path_name = "people/people_manager.py"
    context = "import people\n\ndef get_person():\n    return Person('Alice', 42, 'Engineer')\n"
    blob_name = _upload_one(augment_client, context, path_name)

    # test case where it has to retrieve from the context (no docsets)
    for _ in range(16):

        def try_chat_stream():
            response_stream = augment_model_client.chat_stream(
                selected_code="",
                message="Does the function get_person in people_manager.py take an argument? Answer yes or no. Please don't generate more than a single sentence.",
                prefix="",
                suffix="",
                path="",
                blob_names=[blob_name],
                timeout=120,
            )
            workspace_file_chunks = set()
            has_unknown_blobs = False
            for response_elem in response_stream:
                if len(response_elem.unknown_blob_names) > 0:
                    has_unknown_blobs = True
                if response_elem.workspace_file_chunks:
                    workspace_file_chunks.update(
                        [
                            chunk.blob_name
                            for chunk in response_elem.workspace_file_chunks
                        ]
                    )
            # we don't check for the text (because most small chat models don't get it right), but
            # we check if the right blob was retrieved and used
            return blob_name in workspace_file_chunks and not has_unknown_blobs

        ok = run_with_retries(try_chat_stream)

        if ok:
            break
        else:
            # retry to give indexing and content manager reads time to catch up
            time.sleep(15)
    else:
        pytest.fail(
            "chat request did not return expected response after multiple attempts"
        )

    # answer still okay when docset is added. No major confusion
    for _ in range(8):

        def try_chat_stream():
            response_stream = augment_model_client.chat_stream(
                selected_code="",
                message="Does the function sort_people take an argument? Answer yes or no. Please don't generate more than a single sentence.",
                prefix="",
                suffix="",
                path="",
                blob_names=[blob_name],
                external_source_ids=["docset://graphite.dev"],
                timeout=120,
            )
            workspace_file_chunks = set()
            has_unknown_blobs = False
            for response_elem in response_stream:
                if response_elem.workspace_file_chunks:
                    workspace_file_chunks.update(
                        [
                            chunk.blob_name
                            for chunk in response_elem.workspace_file_chunks
                        ]
                    )
                if len(response_elem.unknown_blob_names) > 0:
                    has_unknown_blobs = True
            return blob_name in workspace_file_chunks and not has_unknown_blobs

        ok = run_with_retries(try_chat_stream)

        if ok:
            break
        else:
            # retry to give indexing and content manager reads time to catch up
            time.sleep(15)
    else:
        pytest.fail(
            "chat request did not return expected response after multiple attempts"
        )

    # test case where it has to retrieve from the docset
    for _ in range(8):

        def try_chat_stream():
            response_stream = augment_model_client.chat_stream(
                selected_code="",
                message="what are the flags for `gt move` ?",
                prefix="",
                suffix="",
                path="",
                blob_names=[blob_name],
                external_source_ids=["docset://graphite.dev"],
                timeout=120,
            )
            text = ""
            for response_elem in response_stream:
                text += response_elem.text
            return "onto" in text

        ok = run_with_retries(try_chat_stream)

        if ok:
            break
        else:
            # retry to give potential content manager reads time to catch up
            time.sleep(15)
    else:
        pytest.fail(
            "chat request did not return expected response after multiple attempts"
        )


def test_chat_stream_with_bogus_docset(
    augment_client: AugmentClient, chat_model_name: str
):
    """Tests that the app can handle chat requests with a docset that doesn't exist.

    For now, we just want it to return something meaningful and not blow up.
    """

    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)

    def try_chat_stream():
        response_stream = augment_model_client.chat_stream(
            selected_code="",
            message="what are the flags for `gt move` ?",
            prefix="",
            suffix="",
            path="",
            blob_names=[],
            external_source_ids=["docset://graphite-devvvvv"],
        )

        request_ids = set()
        text = ""
        for response_elem in response_stream:
            text += response_elem.text
            request_ids.add(response_elem.request_id)
        # we want a non-empty non-error response, just check that we didn't
        # somehow pass the bad docset id to the chat model
        assert "graphite" not in text and len(request_ids) == 1

    run_with_retries(try_chat_stream)


def test_chat_with_custom_system_prompts(
    augment_client: AugmentClient, chat_model_name: str
):
    """Tests that the app can handle chat requests with user rules and workspace rules."""
    custom_system_prompt = "Add YOLO! to every response."
    augment_model_client = augment_client.client_for_model(model_name=chat_model_name)

    response = augment_model_client.chat(
        selected_code="""\
import enum
import json
from typing import Optional

from base.prompt_format_chat.prompt_formatter import (
    ChatPromptFormatter,
    ChatPromptInput,
    ChatPromptOutput
)
""",
        message="What are the imports in the code?",
        prefix="",
        suffix="",
        path="base/prompt_format_chat/binks_prompt_formatter.py",
        user_guidelines=custom_system_prompt,
    )
    assert "YOLO" in response.text, "YOLO not in response"

    response = augment_model_client.chat(
        selected_code="""\
import enum
import json
from typing import Optional

from base.prompt_format_chat.prompt_formatter import (
    ChatPromptFormatter,
    ChatPromptInput,
    ChatPromptOutput
)
""",
        message="What are the imports in the code?",
        prefix="",
        suffix="",
        path="base/prompt_format_chat/binks_prompt_formatter.py",
        workspace_guidelines=custom_system_prompt,
    )
    assert "YOLO" in response.text, "YOLO not in response"


def test_agents_codebase_retrieval(augment_client: AugmentClient):
    prompt_blob, sqlite_blob = _upload_and_wait_for_indexing(
        augment_client,
        "",
        [
            UploadContent(
                path_name="some/path/name.py",
                content="import base.prompt_format_chat.prompt_formatter",
            ),
            UploadContent(path_name="some/path/name2.py", content="import sqlite3"),
        ],
    )
    request = public_api_pb2.CodebaseRetrievalRequest(
        information_request="Which files makes use of prompt formatter code?",
        dialog=[],
        blobs=public_api_pb2.Blobs(
            checkpoint_id=None,
            added_blobs=sorted([prompt_blob, sqlite_blob]),
            deleted_blobs=[],
        ),
    )
    response, req_id = augment_client.post_proto(
        "agents/codebase-retrieval", request, public_api_pb2.CodebaseRetrievalResponse()
    )
    ret = response.formatted_retrieval
    assert "prompt_formatter" in ret
    assert "sqlite3" in ret

    # The former is more relevant than the latter
    # I'm not convinced that lowering the max_output_length would necessarily avoid 'sqlite3' being returned,
    # as we just don't have a large enough workspace
    assert ret.index("prompt_formatter") < ret.index("sqlite3")


def test_agents_list_tools(augment_client: AugmentClient):
    response, req_id = augment_client.post_proto(
        "agents/list-remote-tools",
        public_api_pb2.ListRemoteToolsRequest(),
        public_api_pb2.ListRemoteToolsResponse(),
    )
    assert any(
        tool.remote_tool_id == public_api_pb2.RemoteToolId.WEB_SEARCH
        for tool in response.tools
    )


@pytest.mark.skip(reason="We don't deploy an edit host, and they require h100s")
def test_agents_edit_file(augment_client: AugmentClient):
    pass


@pytest.mark.skip(reason="Autofix cases are disabled")
def test_autofix_check(augment_client: AugmentClient, autofix_model_name: str):
    """Minimal test that the app can handle autofix check requests correctly."""
    augment_model_client = augment_client.client_for_model(
        model_name=autofix_model_name
    )
    response = augment_model_client.autofix_check(
        command=AutofixCommand(
            input="cargo test",
            output="test result: FAILED\nerror: test failed",
            exit_code=1,
        )
    )
    assert response.is_code_related
    assert response.contains_failure


@pytest.mark.skip(reason="Not all test dependencies are deployed properly")
def test_autofix_plan(augment_client: AugmentClient, autofix_model_name: str):
    """Two-round plan session with user steering on the second round"""
    augment_model_client = augment_client.client_for_model(
        model_name=autofix_model_name
    )
    lib_path = "lib.py"
    test_path = "test_lib.py"
    old_lib = """\
def count_occurrences(dat):
    values = set(dat)
    return {v: dat.count(v) for v in values}
"""
    old_test = """\
import lib

def test_count_occurrences():
    assert lib.count_occurrences([1, 2, 3, 2, 1]) == {1: 2, 2: 2, 3: 1}
"""

    new_lib = """\
def count_occurrences(dat):
    result = {}
    for v in dat:
        result[v] += 1
    return result
"""
    old_lib_blob, new_lib_blob, old_test_blob = _upload_and_wait_for_indexing(
        augment_client,
        autofix_model_name,
        [
            UploadContent(old_lib, lib_path),
            UploadContent(new_lib, lib_path),
            UploadContent(old_test, test_path),
        ],
    )

    input = "pytest test_lib.py"
    output = """\
platform linux2 -- Python 2.7.18, pytest-4.6.9, py-1.8.1, pluggy-0.13.0
collected 1 item
test.py F
______________________________________________________ test_count_occurrences _______________________________________________________
    def test_count_occurrences():
>       assert lib.count_occurrences([1, 2, 3, 2, 1]) == {1: 2, 2: 2, 3: 1}
test.py:4:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
dat = [1, 2, 3, 2, 1]
    def count_occurrences(dat):
        result = {}
        for v in dat:
>           result[v] += 1
E           KeyError: 1
lib.py:4: KeyError
"""
    command = AutofixCommand(
        input=input,
        output=output,
        exit_code=1,
    )
    vcs = VCSChange(
        working_directory_changes=[
            {
                "before_path": lib_path,
                "after_path": lib_path,
                "change_type": "MODIFIED",
                "head_blob_name": old_lib_blob,
                "indexed_blob_name": new_lib_blob,
                "current_blob_name": new_lib_blob,
            },
        ],
        commits=[],
    )
    blobs = BlobsJson(
        checkpoint_id=None,
        added_blobs=sorted([new_lib_blob, old_test_blob]),
        deleted_blobs=[],
    )

    # Request with no steering; when test introduced, the model kept
    # it simple by replacing
    # `result[v] += 1` with `result[v] = result.get(v, 0) + 1`
    response = augment_model_client.autofix_plan(
        command=command,
        vcs_change=vcs,
        blobs=blobs,
    )
    # Expect a fix to lib.py
    lib_fix = next(r for r in response.replacements if r.path == lib_path)
    # Too strict?
    assert "defaultdict" not in lib_fix.text

    # Request with steering
    steering = UserSteeringExchange(
        request_message="Can you fix the bug using defaultdict",
        summary=response.summary or "",
        replacements=response.replacements,
        request_id="",
    )
    response = augment_model_client.autofix_plan(
        command=command, vcs_change=vcs, blobs=blobs, steering_history=[steering]
    )
    lib_fix = next(r for r in response.replacements if r.path == lib_path)
    assert "defaultdict(int)" in lib_fix.text


@pytest.mark.parametrize("api_version", [0, 1])
def test_next_edit_stream(
    api_version: int, augment_client: AugmentClient, next_edit_model_name: str
):
    """Tests that the app can handle next_edit requests correctly.

    This test uses a simple renaming example that any reasonable next edit model
    should be able to edit correctly.
    """

    augment_model_client = augment_client.client_for_model(
        model_name=next_edit_model_name
    )

    file_name = "example.py"

    old_code = """\
def quicksort(nums):
    if len(nums) <= 1:
        return nums
    pivot = nums[0]
    left = []
    right = []
    for i in range(1, len(nums)):
        if nums[i] < pivot:
            left.append(nums[i])
        else:
            right.append(nums[i])
    return quicksort(left) + [pivot] + quicksort(right)
"""
    old_file_blob_name = get_blob_name(file_name, old_code.encode("utf-8"))

    # rename function name to `qsort`
    current_code = """\
def qsort(nums):
    if len(nums) <= 1:
        return nums
    pivot = nums[0]
    left = []
    right = []
    for i in range(1, len(nums)):
        if nums[i] < pivot:
            left.append(nums[i])
        else:
            right.append(nums[i])
    return quicksort(left) + [pivot] + quicksort(right)
"""
    current_file_blob_name = get_blob_name(file_name, current_code.encode("utf-8"))

    # the set of events that brings the old code to the current code
    edit_events = [
        {
            "path": file_name,
            "before_blob_name": old_file_blob_name,
            "after_blob_name": current_file_blob_name,
            "edits": [
                {
                    "before_start": 4,
                    "after_start": 4,
                    "before_text": "quick",
                    "after_text": "q",
                }
            ],
        }
    ]

    # we expect the model to finish the renaming of the two function call sites
    expected_new_code = """\
def qsort(nums):
    if len(nums) <= 1:
        return nums
    pivot = nums[0]
    left = []
    right = []
    for i in range(1, len(nums)):
        if nums[i] < pivot:
            left.append(nums[i])
        else:
            right.append(nums[i])
    return qsort(left) + [pivot] + qsort(right)
"""

    # we also fake an indexed code to test if the model can correctly reconstruct
    # current code in the backend
    indexed_code = "indexed code (outdated)"
    indexed_file_blob_name = get_blob_name(file_name, indexed_code.encode("utf-8"))
    # only upload the indexed code; old_code and current_code will be reconstructed
    # in the backend.

    usage_current_code = """\
def test_qsort_usage():
    assert quicksort([1, 2, 3]) == [1, 2, 3]
    assert quicksort([3, 2, 1]) == [1, 2, 3]
"""
    usage_file_name = "test_example.py"
    usage_file_blob_name = get_blob_name(
        usage_file_name, usage_current_code.encode("utf-8")
    )

    usage_indexed_code = "indexed usage code (outdated)"
    usage_indexed_file_blob_name = get_blob_name(
        usage_file_name, usage_indexed_code.encode("utf-8")
    )
    # only upload the indexed code; old_code and current_code will be reconstructed
    _upload_and_wait_for_indexing(
        augment_client,
        next_edit_model_name,
        [
            UploadContent(indexed_code, file_name),
            UploadContent(usage_indexed_code, usage_file_name),
        ],
    )

    # put the cursor in the middle of the file
    cursor_position = len(current_code) // 2
    # check that streams with no edits get an empty response stream
    for scope in ["CURSOR", "FILE", "WORKSPACE"]:
        response_stream = augment_model_client.next_edit_stream(
            mode="BACKGROUND",
            scope=scope,
            instruction="",
            prefix=current_code[:cursor_position],
            suffix=current_code[cursor_position:],
            selected_text="",
            path=file_name,
            blobs=BlobsJson(
                checkpoint_id=None,
                added_blobs=[indexed_file_blob_name, usage_indexed_file_blob_name],
                deleted_blobs=[],
            ),
            edit_events=[],
            timeout=120,
            api_version=api_version,
        )
        assert len(list(response_stream)) == 0

    expected_usage_code = """\
def test_qsort_usage():
    assert qsort([1, 2, 3]) == [1, 2, 3]
    assert qsort([3, 2, 1]) == [1, 2, 3]
"""

    # recent changes should modify indexed version into current version
    recent_changes = [
        {
            "blob_name": indexed_file_blob_name,
            "path": file_name,
            "char_start": 0,
            "char_end": len(indexed_code),
            "replacement_text": current_code,
            "present_in_blob": False,
            "expected_blob_name": current_file_blob_name,
        },
        {
            "blob_name": usage_indexed_file_blob_name,
            "path": usage_file_name,
            "char_start": 0,
            "char_end": len(usage_indexed_code),
            "replacement_text": usage_current_code,
            "present_in_blob": False,
            "expected_blob_name": usage_file_blob_name,
        },
    ]

    def _check_next_edit_response_stream(
        response_stream: Iterable[NextEditResponse], scope: str, mode: str
    ):
        path_to_suggested_code = dict[str, str]()
        for response in response_stream:
            if response.next_edit is None:
                continue
            path = response.next_edit["path"]
            path_to_suggested_code.setdefault(path, "")
            path_to_suggested_code[path] += response.next_edit["suggested_code"]
        expected_result = {
            file_name: expected_new_code,
            usage_file_name: expected_usage_code,
        }
        if scope == "WORKSPACE":
            if mode == "BACKGROUND":
                # Since we currently set max_changes_per_workspace_request = 1
                expected_num_result = 1
            else:
                expected_num_result = 2
        else:
            expected_num_result = 1

        assert (
            len(path_to_suggested_code) == expected_num_result
        ), "Incorrect number of suggestions."
        for path, suggested_code in path_to_suggested_code.items():
            assert_str_eq(
                suggested_code,
                expected_result[path],
                lambda: f"Incorrect suggestion for {path=}",
            )

    for scope in ["CURSOR", "FILE", "WORKSPACE"]:
        for mode in ["FORCED", "FOREGROUND", "BACKGROUND"]:
            response_stream = augment_model_client.next_edit_stream(
                mode=mode,
                scope=scope,
                instruction="",
                prefix=current_code[:cursor_position],
                suffix=current_code[cursor_position:],
                # simulate an empty selection, which is the common case
                selected_text="",
                path=file_name,
                blobs=BlobsJson(
                    checkpoint_id=None,
                    added_blobs=[indexed_file_blob_name, usage_indexed_file_blob_name],
                    deleted_blobs=[],
                ),
                edit_events=edit_events,
                recent_changes=recent_changes,
                timeout=120,
                api_version=api_version,
            )
            _check_next_edit_response_stream(response_stream, scope=scope, mode=mode)

    ## Rest of this test is for blocked locations ##

    # block everything but 1 character in both files
    blocked_locations = [
        {
            "path": file_name,  # path of the indexed file
            "char_start": 0,  # start at 0 to contain the entire location
            "char_end": len(current_code),
        },
        {
            "path": usage_file_name,  # path of the usage file
            "char_start": 1,  # start at 1 to not contain the entire location
            "char_end": len(usage_current_code),
        },
    ]

    def _check_blocked_response_stream(
        response_stream: Iterable[NextEditResponse], scope: str, mode: str
    ):
        path_to_suggested_code = dict[str, str]()
        for response in response_stream:
            if response.next_edit is None:
                continue

            if mode != "FORCED":
                # we should get no changes
                assert_str_eq(
                    response.next_edit["suggested_code"],
                    response.next_edit["existing_code"],
                    lambda: f"Incorrect suggestion for {path=}",
                )
                if response.next_edit["path"] == file_name:
                    # we are fully blocking this location
                    assert False

            path = response.next_edit["path"]
            path_to_suggested_code.setdefault(path, "")
            path_to_suggested_code[path] += response.next_edit["suggested_code"]

        expected_result = {
            file_name: expected_new_code,
            usage_file_name: expected_usage_code,
        }

        if mode == "FORCED":
            # in forced mode, we should still get the expected response
            for path, suggested_code in path_to_suggested_code.items():
                assert_str_eq(
                    suggested_code,
                    expected_result[path],
                    lambda: f"Incorrect suggestion for {path=}",
                )

    for scope in ["CURSOR", "FILE", "WORKSPACE"]:
        for mode in ["FORCED", "FOREGROUND", "BACKGROUND"]:
            response_stream = augment_model_client.next_edit_stream(
                mode=mode,
                scope=scope,
                instruction="",
                prefix=current_code[:cursor_position],
                suffix=current_code[cursor_position:],
                # simulate an empty selection, which is the common case
                selected_text="",
                path=file_name,
                blobs=BlobsJson(
                    checkpoint_id=None,
                    added_blobs=[indexed_file_blob_name, usage_indexed_file_blob_name],
                    deleted_blobs=[],
                ),
                edit_events=edit_events,
                recent_changes=recent_changes,
                blocked_locations=blocked_locations,
                timeout=120,
                api_version=api_version,
            )
            _check_blocked_response_stream(response_stream, scope=scope, mode=mode)


def test_smart_paste(augment_client: AugmentClient, smart_paste_model_name: str):
    """Tests that the app can handle smart paste requests.

    This test verifies that smart paste functionality works end-to-end by:
    1. Providing a quick_sort implementation to paste
    2. Providing target file with other sorting functions
    3. Verifying the response contains valid edit suggestions
    """
    augment_model_client = augment_client.client_for_model(
        model_name=smart_paste_model_name
    )

    # Target file containing various sorting functions
    target_content = """# This file includes various sorting functions

def bubble_sort(arr):
    print(f"Sorting: {arr}")

def bogo_sort(arr):
    print(f"Sorting: {arr}")

def quick_sort(arr):
"""

    # Code block to smart paste - quick_sort implementation
    code_block = """def quick_sort(arr):
    print(f"Actually sorting {arr} for real")
"""

    def try_smart_paste():
        response_stream = augment_model_client.smart_paste_stream(
            code_block=code_block,
            selected_text="",  # No selection, we're pasting new code
            prefix=target_content,
            suffix="",
            path="sorting.py",
            target_file_path="sorting.py",
            target_file_content=target_content,
        )

        # Process each response as a potential replacement operation
        result_content = target_content.splitlines(keepends=True)

        for response in response_stream:
            if (
                response.replacement_text is not None
                or response.replacement_start_line is not None
                or response.replacement_end_line is not None
            ):
                start_line = (
                    response.replacement_start_line
                    if response.replacement_start_line is not None
                    else len(result_content) + 1
                )
                end_line = (
                    response.replacement_end_line
                    if response.replacement_end_line is not None
                    else start_line
                )
                start_idx = max(0, start_line - 1)
                end_idx = max(0, end_line - 1)

                replacement_lines = []
                if response.replacement_text:
                    replacement_lines = response.replacement_text.splitlines(
                        keepends=True
                    )

                # Merge content
                if start_idx <= end_idx:
                    result_content = (
                        result_content[:start_idx]
                        + list(replacement_lines)
                        + result_content[end_idx + 1 :]
                    )
                else:
                    result_content = result_content + list(replacement_lines)

        final_content = "".join(result_content)

        # Verify the final content has the expected changes
        assert (
            'print(f"Actually sorting {arr} for real")' in final_content
        ), "Final content doesn't contain the pasted quick_sort implementation"

        # Verify structure is maintained
        assert (
            "def quick_sort(arr):" in final_content
        ), "Function definition is missing or incorrect"
        assert (
            "def bubble_sort(arr):" in final_content
        ), "Original bubble_sort function was modified"
        assert (
            "def bogo_sort(arr):" in final_content
        ), "Original bogo_sort function was modified"

    run_with_retries(try_smart_paste)
