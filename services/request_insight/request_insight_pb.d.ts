// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/request_insight/request_insight.proto (package request_insight, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { Blobs } from "../../base/blob_names/blob_names_pb.js";
import type { RecencyInfo } from "../completion_host/completion_pb.js";
import type { GranularEditEvent } from "../../base/diff_utils/edit_events_pb.js";
import type { TenantInvitation, User, UserId } from "../auth/central/server/auth_entities_pb.js";
import type { EditRequest, EditResponse, InstructionAggregateResponse, InstructionRequest } from "../edit_host/edit_pb.js";
import type { ChatRequest, ChatResponse, Exchange } from "../chat_host/chat_pb.js";
import type { CharRange, NextEditRequest, NextEditResponse } from "../next_edit_host/next_edit_pb.js";
import type { SlackEvent } from "../integrations/slack_bot/slack_event_pb.js";
import type { GetChatConversationRequest, GetChatConversationResponse, SaveChatConversationRequest, SaveChatConversationResponse } from "../share/share_pb.js";
import type { GithubEvent } from "../integrations/github/github_event_pb.js";
import type { Status } from "../../google/rpc/status_pb.js";
import type { SearchRequest, SearchResponse } from "../integrations/glean/glean_pb.js";
import type { CodebaseRetrievalRequest, CodebaseRetrievalResponse, EditFileRequest, EditFileResponse, LLMGenerateRequest, LLMGenerateResponse, RemoteToolId, RunRemoteToolResponse } from "../agents/agents_pb.js";
import type { Tenant } from "../tenant_watcher/tenant_watcher_pb.js";
import type { AddSSHKeyRequest, AddSSHKeyResponse, ChatRequest as ChatRequest$1, ChatResponse as ChatResponse$1, CreateAgentRequest, CreateAgentResponse, DeleteAgentRequest, DeleteAgentResponse, InterruptAgentRequest, InterruptAgentResponse, PauseAgentRequest, PauseAgentResponse, ResumeAgentRequest, ResumeAgentResponse } from "../remote_agents/remote_agents_pb.js";

/**
 * @generated from enum request_insight.FeedbackRating
 */
export declare enum FeedbackRating {
  /**
   * @generated from enum value: UNSET = 0;
   */
  UNSET = 0,

  /**
   * @generated from enum value: POSITIVE = 1;
   */
  POSITIVE = 1,

  /**
   * @generated from enum value: NEGATIVE = 2;
   */
  NEGATIVE = 2,
}

/**
 * @generated from enum request_insight.RetrievalType
 */
export declare enum RetrievalType {
  /**
   * @generated from enum value: UNKNOWN_RETRIEVAL_TYPE = 0;
   */
  UNKNOWN_RETRIEVAL_TYPE = 0,

  /**
   * @generated from enum value: DENSE = 1;
   */
  DENSE = 1,

  /**
   * @generated from enum value: SIGNATURE = 2;
   */
  SIGNATURE = 2,

  /**
   * @generated from enum value: RECENCY = 3;
   */
  RECENCY = 3,

  /**
   * @generated from enum value: USER_GUIDED = 4;
   */
  USER_GUIDED = 4,
}

/**
 * @generated from enum request_insight.RequestType
 */
export declare enum RequestType {
  /**
   * @generated from enum value: UNKNOWN_REQUEST_TYPE = 0;
   */
  UNKNOWN_REQUEST_TYPE = 0,

  /**
   * @generated from enum value: COMPLETION = 1;
   */
  COMPLETION = 1,

  /**
   * @generated from enum value: EDIT = 2;
   */
  EDIT = 2,

  /**
   * @generated from enum value: CHAT = 3;
   */
  CHAT = 3,

  /**
   * @generated from enum value: EXTENSION_ERROR = 4;
   */
  EXTENSION_ERROR = 4,

  /**
   * @generated from enum value: NEXT_EDIT = 5;
   */
  NEXT_EDIT = 5,

  /**
   * @generated from enum value: SLACKBOT_CHAT = 6;
   */
  SLACKBOT_CHAT = 6,

  /**
   * @generated from enum value: SHARE_SAVE_CHAT = 7;
   */
  SHARE_SAVE_CHAT = 7,

  /**
   * @generated from enum value: SHARE_GET_CHAT = 8;
   */
  SHARE_GET_CHAT = 8,

  /**
   * @generated from enum value: AUTOFIX_CHECK = 9;
   */
  AUTOFIX_CHECK = 9,

  /**
   * @generated from enum value: AUTOFIX_PLAN = 10;
   */
  AUTOFIX_PLAN = 10,

  /**
   * @generated from enum value: LLM_GENERATE = 11;
   */
  LLM_GENERATE = 11,

  /**
   * @generated from enum value: REMOTE_TOOL_CALL = 12;
   */
  REMOTE_TOOL_CALL = 12,

  /**
   * @generated from enum value: REMOTE_AGENT = 13;
   */
  REMOTE_AGENT = 13,

  /**
   * @generated from enum value: AGENT_CHAT = 14;
   */
  AGENT_CHAT = 14,

  /**
   * @generated from enum value: LINEAR_OAUTH = 15;
   */
  LINEAR_OAUTH = 15,

  /**
   * @generated from enum value: MEMORIES = 16;
   */
  MEMORIES = 16,

  /**
   * @generated from enum value: ORIENTATION = 17;
   */
  ORIENTATION = 17,

  /**
   * @generated from enum value: MEMORIES_COMPRESSION = 18;
   */
  MEMORIES_COMPRESSION = 18,

  /**
   * @generated from enum value: REMOTE_AGENT_CHAT = 19;
   */
  REMOTE_AGENT_CHAT = 19,

  /**
   * @generated from enum value: REMOTE_AGENT_LOG = 20;
   */
  REMOTE_AGENT_LOG = 20,
}

/**
 * @generated from enum request_insight.InstallEventType
 */
export declare enum InstallEventType {
  /**
   * @generated from enum value: UNKNOWN_INSTALL_EVENT_TYPE = 0;
   */
  UNKNOWN_INSTALL_EVENT_TYPE = 0,

  /**
   * @generated from enum value: UNINSTALL = 1;
   */
  UNINSTALL = 1,

  /**
   * @generated from enum value: INSTALL = 2;
   */
  INSTALL = 2,
}

/**
 * @generated from message request_insight.TenantInfo
 */
export declare class TenantInfo extends Message<TenantInfo> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string tenant_name = 2;
   */
  tenantName: string;

  constructor(data?: PartialMessage<TenantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.TenantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantInfo;

  static equals(a: TenantInfo | PlainMessage<TenantInfo> | undefined, b: TenantInfo | PlainMessage<TenantInfo> | undefined): boolean;
}

/**
 * @generated from message request_insight.UpdateRequestInfoRequest
 */
export declare class UpdateRequestInfoRequest extends Message<UpdateRequestInfoRequest> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: repeated request_insight.RequestEvent events = 3;
   */
  events: RequestEvent[];

  /**
   * @generated from field: request_insight.TenantInfo tenant_info = 4;
   */
  tenantInfo?: TenantInfo;

  /**
   * @generated from field: optional string session_id = 5;
   */
  sessionId?: string;

  constructor(data?: PartialMessage<UpdateRequestInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.UpdateRequestInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRequestInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRequestInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRequestInfoRequest;

  static equals(a: UpdateRequestInfoRequest | PlainMessage<UpdateRequestInfoRequest> | undefined, b: UpdateRequestInfoRequest | PlainMessage<UpdateRequestInfoRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RequestEvent
 */
export declare class RequestEvent extends Message<RequestEvent> {
  /**
   * @generated from field: google.protobuf.Timestamp time = 1;
   */
  time?: Timestamp;

  /**
   * @generated from oneof request_insight.RequestEvent.event
   */
  event: {
    /**
     * @generated from field: request_insight.InferRequest infer_request = 2;
     */
    value: InferRequest;
    case: "inferRequest";
  } | {
    /**
     * @generated from field: request_insight.CompletionHostRequest completion_host_request = 8;
     */
    value: CompletionHostRequest;
    case: "completionHostRequest";
  } | {
    /**
     * @generated from field: request_insight.InferenceHostResponse inference_host_response = 10;
     */
    value: InferenceHostResponse;
    case: "inferenceHostResponse";
  } | {
    /**
     * @generated from field: request_insight.CompletionHostResponse completion_host_response = 9;
     */
    value: CompletionHostResponse;
    case: "completionHostResponse";
  } | {
    /**
     * @generated from field: request_insight.ApiHttpResponse api_http_response = 7;
     */
    value: ApiHttpResponse;
    case: "apiHttpResponse";
  } | {
    /**
     * @generated from field: request_insight.EmbeddingsSearchRequest embeddings_search_request = 11;
     */
    value: EmbeddingsSearchRequest;
    case: "embeddingsSearchRequest";
  } | {
    /**
     * @generated from field: request_insight.EmbeddingsSearchResponse embeddings_search_response = 12;
     */
    value: EmbeddingsSearchResponse;
    case: "embeddingsSearchResponse";
  } | {
    /**
     * @generated from field: request_insight.CompletionEmit completion_emit = 13;
     */
    value: CompletionEmit;
    case: "completionEmit";
  } | {
    /**
     * @generated from field: request_insight.CompletionResolution completion_resolution = 14;
     */
    value: CompletionResolution;
    case: "completionResolution";
  } | {
    /**
     * @generated from field: request_insight.RIEditRequest edit_host_request = 15;
     */
    value: RIEditRequest;
    case: "editHostRequest";
  } | {
    /**
     * @generated from field: request_insight.RIEditResponse edit_host_response = 16;
     */
    value: RIEditResponse;
    case: "editHostResponse";
  } | {
    /**
     * @generated from field: request_insight.CompletionFeedback completion_feedback = 17;
     */
    value: CompletionFeedback;
    case: "completionFeedback";
  } | {
    /**
     * @generated from field: request_insight.EditEmit edit_emit = 18;
     */
    value: EditEmit;
    case: "editEmit";
  } | {
    /**
     * @generated from field: request_insight.EditResolution edit_resolution = 19;
     */
    value: EditResolution;
    case: "editResolution";
  } | {
    /**
     * @generated from field: request_insight.RequestMetadata request_metadata = 20;
     */
    value: RequestMetadata;
    case: "requestMetadata";
  } | {
    /**
     * @generated from field: request_insight.RetrievalResponse retrieval_response = 21;
     */
    value: RetrievalResponse;
    case: "retrievalResponse";
  } | {
    /**
     * @generated from field: request_insight.RIChatRequest chat_host_request = 22;
     */
    value: RIChatRequest;
    case: "chatHostRequest";
  } | {
    /**
     * @generated from field: request_insight.RIChatResponse chat_host_response = 23;
     */
    value: RIChatResponse;
    case: "chatHostResponse";
  } | {
    /**
     * @generated from field: request_insight.ExtensionError extension_error = 24;
     */
    value: ExtensionError;
    case: "extensionError";
  } | {
    /**
     * @generated from field: request_insight.CompletionPostProcess completion_post_process = 25;
     */
    value: CompletionPostProcess;
    case: "completionPostProcess";
  } | {
    /**
     * @generated from field: request_insight.PreferenceSample preference_sample = 27;
     */
    value: PreferenceSample;
    case: "preferenceSample";
  } | {
    /**
     * @generated from field: request_insight.ChatFeedback chat_feedback = 28;
     */
    value: ChatFeedback;
    case: "chatFeedback";
  } | {
    /**
     * @generated from field: request_insight.RerankerResponse reranker_response = 29;
     */
    value: RerankerResponse;
    case: "rerankerResponse";
  } | {
    /**
     * @generated from field: request_insight.RINextEditRequest next_edit_host_request = 30;
     */
    value: RINextEditRequest;
    case: "nextEditHostRequest";
  } | {
    /**
     * @generated from field: request_insight.RINextEditResponse next_edit_host_response = 31;
     */
    value: RINextEditResponse;
    case: "nextEditHostResponse";
  } | {
    /**
     * @generated from field: request_insight.ClientCompletionTimeline client_completion_timeline = 32;
     */
    value: ClientCompletionTimeline;
    case: "clientCompletionTimeline";
  } | {
    /**
     * @generated from field: request_insight.NextEditFeedback next_edit_feedback = 33;
     */
    value: NextEditFeedback;
    case: "nextEditFeedback";
  } | {
    /**
     * @generated from field: request_insight.NextEditResolution next_edit_resolution = 34;
     */
    value: NextEditResolution;
    case: "nextEditResolution";
  } | {
    /**
     * @generated from field: request_insight.NextEditEmit next_edit_emit = 35;
     */
    value: NextEditEmit;
    case: "nextEditEmit";
  } | {
    /**
     * @generated from field: request_insight.RIInstructionRequest instruction_host_request = 38;
     */
    value: RIInstructionRequest;
    case: "instructionHostRequest";
  } | {
    /**
     * @generated from field: request_insight.RIInstructionResponse instruction_host_response = 39;
     */
    value: RIInstructionResponse;
    case: "instructionHostResponse";
  } | {
    /**
     * @generated from field: request_insight.RISlackbotRequest slackbot_request = 40;
     */
    value: RISlackbotRequest;
    case: "slackbotRequest";
  } | {
    /**
     * @generated from field: request_insight.RISlackbotResponse slackbot_response = 41;
     */
    value: RISlackbotResponse;
    case: "slackbotResponse";
  } | {
    /**
     * @generated from field: request_insight.RIGithubEvent github_event = 42;
     */
    value: RIGithubEvent;
    case: "githubEvent";
  } | {
    /**
     * @generated from field: request_insight.RIGithubProcessingResult github_processing_result = 43;
     */
    value: RIGithubProcessingResult;
    case: "githubProcessingResult";
  } | {
    /**
     * @generated from field: request_insight.RIShareSaveChatRequest share_save_chat_request = 44;
     */
    value: RIShareSaveChatRequest;
    case: "shareSaveChatRequest";
  } | {
    /**
     * @generated from field: request_insight.RIShareSaveChatResponse share_save_chat_response = 45;
     */
    value: RIShareSaveChatResponse;
    case: "shareSaveChatResponse";
  } | {
    /**
     * @generated from field: request_insight.RIShareGetChatRequest share_get_chat_request = 46;
     */
    value: RIShareGetChatRequest;
    case: "shareGetChatRequest";
  } | {
    /**
     * @generated from field: request_insight.RIShareGetChatResponse share_get_chat_response = 47;
     */
    value: RIShareGetChatResponse;
    case: "shareGetChatResponse";
  } | {
    /**
     * @generated from field: request_insight.SmartPasteClientTimeline smart_paste_client_timeline = 48;
     */
    value: SmartPasteClientTimeline;
    case: "smartPasteClientTimeline";
  } | {
    /**
     * @generated from field: request_insight.SmartPasteResolution smart_paste_resolution = 49;
     */
    value: SmartPasteResolution;
    case: "smartPasteResolution";
  } | {
    /**
     * @generated from field: request_insight.RIGithubAppInstallationEvent github_app_installation_event = 50;
     */
    value: RIGithubAppInstallationEvent;
    case: "githubAppInstallationEvent";
  } | {
    /**
     * @generated from field: request_insight.RISlackbotInstallationEvent slackbot_installation_event = 51;
     */
    value: RISlackbotInstallationEvent;
    case: "slackbotInstallationEvent";
  } | {
    /**
     * @generated from field: request_insight.InstructionResolution instruction_resolution = 52;
     */
    value: InstructionResolution;
    case: "instructionResolution";
  } | {
    /**
     * @generated from field: request_insight.InstructionEmit instruction_emit = 53;
     */
    value: InstructionEmit;
    case: "instructionEmit";
  } | {
    /**
     * @generated from field: request_insight.SlackbotFeedback slackbot_feedback = 54;
     */
    value: SlackbotFeedback;
    case: "slackbotFeedback";
  } | {
    /**
     * @generated from field: request_insight.RIGleanRequest glean_request = 59;
     */
    value: RIGleanRequest;
    case: "gleanRequest";
  } | {
    /**
     * @generated from field: request_insight.RIGleanResponse glean_response = 60;
     */
    value: RIGleanResponse;
    case: "gleanResponse";
  } | {
    /**
     * @generated from field: request_insight.RIGleanOAuthURLRequest glean_oauth_url_request = 61;
     */
    value: RIGleanOAuthURLRequest;
    case: "gleanOauthUrlRequest";
  } | {
    /**
     * @generated from field: request_insight.RIGleanOAuthURLResponse glean_oauth_url_response = 62;
     */
    value: RIGleanOAuthURLResponse;
    case: "gleanOauthUrlResponse";
  } | {
    /**
     * @generated from field: request_insight.RILLMGenerateRequest llm_generate_request = 63;
     */
    value: RILLMGenerateRequest;
    case: "llmGenerateRequest";
  } | {
    /**
     * @generated from field: request_insight.RILLMGenerateResponse llm_generate_response = 64;
     */
    value: RILLMGenerateResponse;
    case: "llmGenerateResponse";
  } | {
    /**
     * @generated from field: request_insight.RIRemoteToolCallRequest remote_tool_call_request = 65;
     */
    value: RIRemoteToolCallRequest;
    case: "remoteToolCallRequest";
  } | {
    /**
     * @generated from field: request_insight.RIRemoteToolCallResponse remote_tool_call_response = 66;
     */
    value: RIRemoteToolCallResponse;
    case: "remoteToolCallResponse";
  } | {
    /**
     * @generated from field: request_insight.SubAgentDialog sub_agent_dialog = 67;
     */
    value: SubAgentDialog;
    case: "subAgentDialog";
  } | {
    /**
     * @generated from field: request_insight.PromptCacheUsage prompt_cache_usage = 68;
     */
    value: PromptCacheUsage;
    case: "promptCacheUsage";
  } | {
    /**
     * @generated from field: request_insight.ToolUseData tool_use_data = 69;
     */
    value: ToolUseData;
    case: "toolUseData";
  } | {
    /**
     * @generated from field: request_insight.RouterResponse router_response = 70;
     */
    value: RouterResponse;
    case: "routerResponse";
  } | {
    /**
     * @generated from field: request_insight.PostprocessResponse postprocess_response = 71;
     */
    value: PostprocessResponse;
    case: "postprocessResponse";
  } | {
    /**
     * @generated from field: request_insight.AgentRequestEvent agent_request_event = 72;
     */
    value: AgentRequestEvent;
    case: "agentRequestEvent";
  } | {
    /**
     * @generated from field: request_insight.AgentFeedback agent_feedback = 73;
     */
    value: AgentFeedback;
    case: "agentFeedback";
  } | {
    /**
     * @generated from field: request_insight.RequestBlocked request_blocked = 74;
     */
    value: RequestBlocked;
    case: "requestBlocked";
  } | {
    /**
     * @generated from field: request_insight.RequestSuspicious request_suspicious = 75;
     */
    value: RequestSuspicious;
    case: "requestSuspicious";
  } | {
    /**
     * @generated from field: request_insight.TokenExchangeError token_exchange_error = 76;
     */
    value: TokenExchangeError;
    case: "tokenExchangeError";
  } | {
    /**
     * @generated from field: request_insight.RIFindMissing find_missing = 77;
     */
    value: RIFindMissing;
    case: "findMissing";
  } | {
    /**
     * @generated from field: request_insight.RIBatchUpload batch_upload = 78;
     */
    value: RIBatchUpload;
    case: "batchUpload";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsCreateRequest remote_agents_create_request = 79;
     */
    value: RemoteAgentsCreateRequest;
    case: "remoteAgentsCreateRequest";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsCreateResponse remote_agents_create_response = 80;
     */
    value: RemoteAgentsCreateResponse;
    case: "remoteAgentsCreateResponse";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsChatRequest remote_agents_chat_request = 81;
     */
    value: RemoteAgentsChatRequest;
    case: "remoteAgentsChatRequest";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsChatResponse remote_agents_chat_response = 82;
     */
    value: RemoteAgentsChatResponse;
    case: "remoteAgentsChatResponse";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsInterruptRequest remote_agents_interrupt_request = 83;
     */
    value: RemoteAgentsInterruptRequest;
    case: "remoteAgentsInterruptRequest";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsInterruptResponse remote_agents_interrupt_response = 84;
     */
    value: RemoteAgentsInterruptResponse;
    case: "remoteAgentsInterruptResponse";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsDeleteRequest remote_agents_delete_request = 85;
     */
    value: RemoteAgentsDeleteRequest;
    case: "remoteAgentsDeleteRequest";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsDeleteResponse remote_agents_delete_response = 86;
     */
    value: RemoteAgentsDeleteResponse;
    case: "remoteAgentsDeleteResponse";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsAddSSHKeyRequest remote_agents_add_ssh_key_request = 87;
     */
    value: RemoteAgentsAddSSHKeyRequest;
    case: "remoteAgentsAddSshKeyRequest";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsAddSSHKeyResponse remote_agents_add_ssh_key_response = 88;
     */
    value: RemoteAgentsAddSSHKeyResponse;
    case: "remoteAgentsAddSshKeyResponse";
  } | {
    /**
     * @generated from field: request_insight.DailyRequestLimitExceeded daily_request_limit_exceeded = 89;
     */
    value: DailyRequestLimitExceeded;
    case: "dailyRequestLimitExceeded";
  } | {
    /**
     * @generated from field: request_insight.ChatUserMessage chat_user_message = 90;
     */
    value: ChatUserMessage;
    case: "chatUserMessage";
  } | {
    /**
     * @generated from field: request_insight.ParenthesisTruncation parenthesis_truncation = 91;
     */
    value: ParenthesisTruncation;
    case: "parenthesisTruncation";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentFeedback remote_agent_feedback = 92;
     */
    value: RemoteAgentFeedback;
    case: "remoteAgentFeedback";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsPauseRequest remote_agents_pause_request = 93;
     */
    value: RemoteAgentsPauseRequest;
    case: "remoteAgentsPauseRequest";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsPauseResponse remote_agents_pause_response = 94;
     */
    value: RemoteAgentsPauseResponse;
    case: "remoteAgentsPauseResponse";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsResumeRequest remote_agents_resume_request = 95;
     */
    value: RemoteAgentsResumeRequest;
    case: "remoteAgentsResumeRequest";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentsResumeResponse remote_agents_resume_response = 96;
     */
    value: RemoteAgentsResumeResponse;
    case: "remoteAgentsResumeResponse";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: optional string event_id = 37;
   */
  eventId?: string;

  constructor(data?: PartialMessage<RequestEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RequestEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestEvent;

  static equals(a: RequestEvent | PlainMessage<RequestEvent> | undefined, b: RequestEvent | PlainMessage<RequestEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.InferRequest
 */
export declare class InferRequest extends Message<InferRequest> {
  /**
   * @generated from field: string model = 1;
   */
  model: string;

  /**
   * @generated from field: string prompt = 2;
   */
  prompt: string;

  /**
   * @generated from field: string path = 3;
   */
  path: string;

  /**
   * @generated from field: string suffix = 4;
   */
  suffix: string;

  /**
   * @generated from field: repeated string memories = 5;
   */
  memories: string[];

  /**
   * @generated from field: int32 top_k = 6;
   */
  topK: number;

  /**
   * @generated from field: float top_p = 7;
   */
  topP: number;

  /**
   * @generated from field: float temperature = 8;
   */
  temperature: number;

  /**
   * @generated from field: int32 max_tokens = 9;
   */
  maxTokens: number;

  /**
   * @generated from field: string user_id = 10;
   */
  userId: string;

  /**
   * @generated from field: string session_id = 11;
   */
  sessionId: string;

  /**
   * @generated from field: string lang = 12;
   */
  lang: string;

  /**
   * @generated from field: string user_agent = 13;
   */
  userAgent: string;

  /**
   * @generated from field: uint32 sequence_id = 14;
   */
  sequenceId: number;

  /**
   * @generated from field: bool probe_only = 15;
   */
  probeOnly: boolean;

  /**
   * @generated from field: base.blob_names.Blobs blobs = 16;
   */
  blobs?: Blobs;

  /**
   * @generated from field: completion.RecencyInfo recency_info = 17;
   */
  recencyInfo?: RecencyInfo;

  /**
   * @generated from field: optional float user_filter_threshold = 18;
   */
  userFilterThreshold?: number;

  /**
   * @generated from field: repeated base.diff_utils.GranularEditEvent edit_events = 19;
   */
  editEvents: GranularEditEvent[];

  constructor(data?: PartialMessage<InferRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.InferRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InferRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InferRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InferRequest;

  static equals(a: InferRequest | PlainMessage<InferRequest> | undefined, b: InferRequest | PlainMessage<InferRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.Tokenization
 */
export declare class Tokenization extends Message<Tokenization> {
  /**
   * @generated from field: repeated int32 token_ids = 1;
   */
  tokenIds: number[];

  /**
   * @generated from field: repeated uint32 offsets = 2;
   */
  offsets: number[];

  /**
   * @generated from field: repeated float log_probs = 3;
   */
  logProbs: number[];

  /**
   * @generated from field: string text = 4;
   */
  text: string;

  constructor(data?: PartialMessage<Tokenization>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.Tokenization";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Tokenization;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Tokenization;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Tokenization;

  static equals(a: Tokenization | PlainMessage<Tokenization> | undefined, b: Tokenization | PlainMessage<Tokenization> | undefined): boolean;
}

/**
 * @generated from message request_insight.EmbeddingsSearchRequest
 */
export declare class EmbeddingsSearchRequest extends Message<EmbeddingsSearchRequest> {
  /**
   * @generated from field: repeated string blob_names = 1;
   */
  blobNames: string[];

  /**
   * @generated from field: int32 num_results = 2;
   */
  numResults: number;

  /**
   * @generated from field: string transformation_key = 3;
   */
  transformationKey: string;

  /**
   * @generated from field: string sub_key = 4;
   */
  subKey: string;

  /**
   * @generated from field: repeated base.blob_names.Blobs blobs = 5;
   */
  blobs: Blobs[];

  constructor(data?: PartialMessage<EmbeddingsSearchRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.EmbeddingsSearchRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EmbeddingsSearchRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EmbeddingsSearchRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EmbeddingsSearchRequest;

  static equals(a: EmbeddingsSearchRequest | PlainMessage<EmbeddingsSearchRequest> | undefined, b: EmbeddingsSearchRequest | PlainMessage<EmbeddingsSearchRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.EmbeddingsSearchResult
 */
export declare class EmbeddingsSearchResult extends Message<EmbeddingsSearchResult> {
  /**
   * @generated from field: string blob_name = 1;
   */
  blobName: string;

  /**
   * @generated from field: uint32 chunk_index = 2;
   */
  chunkIndex: number;

  /**
   * @generated from field: float value = 3;
   */
  value: number;

  constructor(data?: PartialMessage<EmbeddingsSearchResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.EmbeddingsSearchResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EmbeddingsSearchResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EmbeddingsSearchResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EmbeddingsSearchResult;

  static equals(a: EmbeddingsSearchResult | PlainMessage<EmbeddingsSearchResult> | undefined, b: EmbeddingsSearchResult | PlainMessage<EmbeddingsSearchResult> | undefined): boolean;
}

/**
 * @generated from message request_insight.EmbeddingsSearchResponse
 */
export declare class EmbeddingsSearchResponse extends Message<EmbeddingsSearchResponse> {
  /**
   * @generated from field: repeated string missing_blob_names = 1;
   */
  missingBlobNames: string[];

  /**
   * @generated from field: repeated request_insight.EmbeddingsSearchResult results = 2;
   */
  results: EmbeddingsSearchResult[];

  constructor(data?: PartialMessage<EmbeddingsSearchResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.EmbeddingsSearchResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EmbeddingsSearchResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EmbeddingsSearchResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EmbeddingsSearchResponse;

  static equals(a: EmbeddingsSearchResponse | PlainMessage<EmbeddingsSearchResponse> | undefined, b: EmbeddingsSearchResponse | PlainMessage<EmbeddingsSearchResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.CompletionEmit
 */
export declare class CompletionEmit extends Message<CompletionEmit> {
  constructor(data?: PartialMessage<CompletionEmit>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionEmit";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionEmit;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionEmit;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionEmit;

  static equals(a: CompletionEmit | PlainMessage<CompletionEmit> | undefined, b: CompletionEmit | PlainMessage<CompletionEmit> | undefined): boolean;
}

/**
 * @generated from message request_insight.CompletionResolution
 */
export declare class CompletionResolution extends Message<CompletionResolution> {
  /**
   * @generated from field: int32 accepted_idx = 1;
   */
  acceptedIdx: number;

  constructor(data?: PartialMessage<CompletionResolution>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionResolution";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionResolution;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionResolution;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionResolution;

  static equals(a: CompletionResolution | PlainMessage<CompletionResolution> | undefined, b: CompletionResolution | PlainMessage<CompletionResolution> | undefined): boolean;
}

/**
 * @generated from message request_insight.CompletionFeedback
 */
export declare class CompletionFeedback extends Message<CompletionFeedback> {
  /**
   * @generated from field: request_insight.FeedbackRating rating = 1;
   */
  rating: FeedbackRating;

  /**
   * @generated from field: string note = 2;
   */
  note: string;

  constructor(data?: PartialMessage<CompletionFeedback>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionFeedback";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionFeedback;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionFeedback;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionFeedback;

  static equals(a: CompletionFeedback | PlainMessage<CompletionFeedback> | undefined, b: CompletionFeedback | PlainMessage<CompletionFeedback> | undefined): boolean;
}

/**
 * @generated from message request_insight.ChatFeedback
 */
export declare class ChatFeedback extends Message<ChatFeedback> {
  /**
   * @generated from field: request_insight.FeedbackRating rating = 1;
   */
  rating: FeedbackRating;

  /**
   * @generated from field: string note = 2;
   */
  note: string;

  /**
   * @generated from field: string user_agent = 3;
   */
  userAgent: string;

  constructor(data?: PartialMessage<ChatFeedback>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ChatFeedback";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatFeedback;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatFeedback;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatFeedback;

  static equals(a: ChatFeedback | PlainMessage<ChatFeedback> | undefined, b: ChatFeedback | PlainMessage<ChatFeedback> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentFeedback
 */
export declare class AgentFeedback extends Message<AgentFeedback> {
  /**
   * @generated from field: request_insight.FeedbackRating rating = 1;
   */
  rating: FeedbackRating;

  /**
   * @generated from field: string note = 2;
   */
  note: string;

  /**
   * @generated from field: string user_agent = 3;
   */
  userAgent: string;

  constructor(data?: PartialMessage<AgentFeedback>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentFeedback";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentFeedback;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentFeedback;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentFeedback;

  static equals(a: AgentFeedback | PlainMessage<AgentFeedback> | undefined, b: AgentFeedback | PlainMessage<AgentFeedback> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentFeedback
 */
export declare class RemoteAgentFeedback extends Message<RemoteAgentFeedback> {
  /**
   * @generated from field: request_insight.FeedbackRating rating = 1;
   */
  rating: FeedbackRating;

  /**
   * @generated from field: string note = 2;
   */
  note: string;

  /**
   * @generated from field: string user_agent = 3;
   */
  userAgent: string;

  constructor(data?: PartialMessage<RemoteAgentFeedback>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentFeedback";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentFeedback;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentFeedback;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentFeedback;

  static equals(a: RemoteAgentFeedback | PlainMessage<RemoteAgentFeedback> | undefined, b: RemoteAgentFeedback | PlainMessage<RemoteAgentFeedback> | undefined): boolean;
}

/**
 * @generated from message request_insight.NextEditFeedback
 */
export declare class NextEditFeedback extends Message<NextEditFeedback> {
  /**
   * @generated from field: request_insight.FeedbackRating rating = 1;
   */
  rating: FeedbackRating;

  /**
   * @generated from field: string note = 2;
   */
  note: string;

  constructor(data?: PartialMessage<NextEditFeedback>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.NextEditFeedback";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NextEditFeedback;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NextEditFeedback;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NextEditFeedback;

  static equals(a: NextEditFeedback | PlainMessage<NextEditFeedback> | undefined, b: NextEditFeedback | PlainMessage<NextEditFeedback> | undefined): boolean;
}

/**
 * @generated from message request_insight.SlackbotFeedback
 */
export declare class SlackbotFeedback extends Message<SlackbotFeedback> {
  /**
   * @generated from field: string slack_response_timestamp = 1;
   */
  slackResponseTimestamp: string;

  /**
   * @generated from field: string slack_channel_id = 2;
   */
  slackChannelId: string;

  /**
   * @generated from field: request_insight.FeedbackRating rating = 3;
   */
  rating: FeedbackRating;

  /**
   * @generated from field: string note = 4;
   */
  note: string;

  constructor(data?: PartialMessage<SlackbotFeedback>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.SlackbotFeedback";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SlackbotFeedback;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SlackbotFeedback;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SlackbotFeedback;

  static equals(a: SlackbotFeedback | PlainMessage<SlackbotFeedback> | undefined, b: SlackbotFeedback | PlainMessage<SlackbotFeedback> | undefined): boolean;
}

/**
 * @generated from message request_insight.EditEmit
 */
export declare class EditEmit extends Message<EditEmit> {
  constructor(data?: PartialMessage<EditEmit>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.EditEmit";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditEmit;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditEmit;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditEmit;

  static equals(a: EditEmit | PlainMessage<EditEmit> | undefined, b: EditEmit | PlainMessage<EditEmit> | undefined): boolean;
}

/**
 * @generated from message request_insight.EditResolution
 */
export declare class EditResolution extends Message<EditResolution> {
  /**
   * @generated from field: bool is_accepted = 1;
   */
  isAccepted: boolean;

  /**
   * @generated from field: optional string annotated_text = 2;
   */
  annotatedText?: string;

  /**
   * @generated from field: optional string annotated_instruction = 3;
   */
  annotatedInstruction?: string;

  constructor(data?: PartialMessage<EditResolution>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.EditResolution";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditResolution;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditResolution;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditResolution;

  static equals(a: EditResolution | PlainMessage<EditResolution> | undefined, b: EditResolution | PlainMessage<EditResolution> | undefined): boolean;
}

/**
 * @generated from message request_insight.NextEditEmit
 */
export declare class NextEditEmit extends Message<NextEditEmit> {
  constructor(data?: PartialMessage<NextEditEmit>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.NextEditEmit";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NextEditEmit;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NextEditEmit;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NextEditEmit;

  static equals(a: NextEditEmit | PlainMessage<NextEditEmit> | undefined, b: NextEditEmit | PlainMessage<NextEditEmit> | undefined): boolean;
}

/**
 * @generated from message request_insight.InstructionResolution
 */
export declare class InstructionResolution extends Message<InstructionResolution> {
  /**
   * @generated from field: repeated bool is_accepted_chunks = 1;
   */
  isAcceptedChunks: boolean[];

  /**
   * @generated from field: bool is_accept_all = 2;
   */
  isAcceptAll: boolean;

  /**
   * @generated from field: bool is_reject_all = 3;
   */
  isRejectAll: boolean;

  constructor(data?: PartialMessage<InstructionResolution>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.InstructionResolution";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InstructionResolution;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InstructionResolution;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InstructionResolution;

  static equals(a: InstructionResolution | PlainMessage<InstructionResolution> | undefined, b: InstructionResolution | PlainMessage<InstructionResolution> | undefined): boolean;
}

/**
 * @generated from message request_insight.InstructionEmit
 */
export declare class InstructionEmit extends Message<InstructionEmit> {
  constructor(data?: PartialMessage<InstructionEmit>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.InstructionEmit";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InstructionEmit;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InstructionEmit;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InstructionEmit;

  static equals(a: InstructionEmit | PlainMessage<InstructionEmit> | undefined, b: InstructionEmit | PlainMessage<InstructionEmit> | undefined): boolean;
}

/**
 * @generated from message request_insight.ClientMetric
 */
export declare class ClientMetric extends Message<ClientMetric> {
  /**
   * @generated from field: string event_name = 1;
   */
  eventName: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  /**
   * @generated from field: string client_metric = 3;
   */
  clientMetric: string;

  /**
   * @generated from field: uint64 value = 4;
   */
  value: bigint;

  constructor(data?: PartialMessage<ClientMetric>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ClientMetric";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClientMetric;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClientMetric;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClientMetric;

  static equals(a: ClientMetric | PlainMessage<ClientMetric> | undefined, b: ClientMetric | PlainMessage<ClientMetric> | undefined): boolean;
}

/**
 * @generated from message request_insight.SmartPasteClientTimeline
 */
export declare class SmartPasteClientTimeline extends Message<SmartPasteClientTimeline> {
  /**
   * @generated from field: google.protobuf.Timestamp initial_request_time = 1;
   */
  initialRequestTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp stream_finish_time = 2;
   */
  streamFinishTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp apply_time = 3;
   */
  applyTime?: Timestamp;

  constructor(data?: PartialMessage<SmartPasteClientTimeline>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.SmartPasteClientTimeline";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SmartPasteClientTimeline;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SmartPasteClientTimeline;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SmartPasteClientTimeline;

  static equals(a: SmartPasteClientTimeline | PlainMessage<SmartPasteClientTimeline> | undefined, b: SmartPasteClientTimeline | PlainMessage<SmartPasteClientTimeline> | undefined): boolean;
}

/**
 * @generated from message request_insight.SmartPasteResolution
 */
export declare class SmartPasteResolution extends Message<SmartPasteResolution> {
  /**
   * @generated from field: repeated bool is_accepted_chunks = 1;
   */
  isAcceptedChunks: boolean[];

  /**
   * @generated from field: bool is_accept_all = 2;
   */
  isAcceptAll: boolean;

  /**
   * @generated from field: bool is_reject_all = 3;
   */
  isRejectAll: boolean;

  constructor(data?: PartialMessage<SmartPasteResolution>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.SmartPasteResolution";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SmartPasteResolution;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SmartPasteResolution;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SmartPasteResolution;

  static equals(a: SmartPasteResolution | PlainMessage<SmartPasteResolution> | undefined, b: SmartPasteResolution | PlainMessage<SmartPasteResolution> | undefined): boolean;
}

/**
 * @generated from message request_insight.NextEditResolution
 */
export declare class NextEditResolution extends Message<NextEditResolution> {
  /**
   * @generated from field: bool is_accepted = 1;
   */
  isAccepted: boolean;

  constructor(data?: PartialMessage<NextEditResolution>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.NextEditResolution";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NextEditResolution;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NextEditResolution;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NextEditResolution;

  static equals(a: NextEditResolution | PlainMessage<NextEditResolution> | undefined, b: NextEditResolution | PlainMessage<NextEditResolution> | undefined): boolean;
}

/**
 * @generated from message request_insight.PreferenceSample
 */
export declare class PreferenceSample extends Message<PreferenceSample> {
  /**
   * @generated from field: repeated string request_ids = 1;
   */
  requestIds: string[];

  /**
   * @generated from field: map<string, int32> scores = 2;
   */
  scores: { [key: string]: number };

  /**
   * @generated from field: string feedback = 3;
   */
  feedback: string;

  constructor(data?: PartialMessage<PreferenceSample>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.PreferenceSample";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PreferenceSample;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PreferenceSample;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PreferenceSample;

  static equals(a: PreferenceSample | PlainMessage<PreferenceSample> | undefined, b: PreferenceSample | PlainMessage<PreferenceSample> | undefined): boolean;
}

/**
 * @generated from message request_insight.CompletionHostRequestPosition
 */
export declare class CompletionHostRequestPosition extends Message<CompletionHostRequestPosition> {
  /**
   * @generated from field: int32 prefix_begin = 1;
   */
  prefixBegin: number;

  /**
   * @generated from field: int32 cursor_position = 2;
   */
  cursorPosition: number;

  /**
   * @generated from field: int32 suffix_end = 3;
   */
  suffixEnd: number;

  /**
   * @generated from field: string blob_name = 4;
   */
  blobName: string;

  constructor(data?: PartialMessage<CompletionHostRequestPosition>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionHostRequestPosition";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionHostRequestPosition;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionHostRequestPosition;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionHostRequestPosition;

  static equals(a: CompletionHostRequestPosition | PlainMessage<CompletionHostRequestPosition> | undefined, b: CompletionHostRequestPosition | PlainMessage<CompletionHostRequestPosition> | undefined): boolean;
}

/**
 * @generated from message request_insight.CompletionHostRequest
 */
export declare class CompletionHostRequest extends Message<CompletionHostRequest> {
  /**
   * @generated from field: uint64 seed = 2;
   */
  seed: bigint;

  /**
   * @generated from field: repeated int32 eos_token_id = 4;
   */
  eosTokenId: number[];

  /**
   * @generated from field: int32 top_k = 5;
   */
  topK: number;

  /**
   * @generated from field: float top_p = 6;
   */
  topP: number;

  /**
   * @generated from field: float temperature = 7;
   */
  temperature: number;

  /**
   * @generated from field: int32 output_len = 8;
   */
  outputLen: number;

  /**
   * @generated from field: bool enable_path_prefix = 9;
   */
  enablePathPrefix: boolean;

  /**
   * @generated from field: bool enable_preference_tokens = 10;
   */
  enablePreferenceTokens: boolean;

  /**
   * @generated from field: bool enable_fill_in_the_middle = 11;
   */
  enableFillInTheMiddle: boolean;

  /**
   * @generated from field: bool enable_bm25 = 13;
   */
  enableBm25: boolean;

  /**
   * @generated from field: bool enable_dense_retrieval = 14;
   */
  enableDenseRetrieval: boolean;

  /**
   * @generated from field: string model = 15;
   */
  model: string;

  /**
   * @generated from field: string prefix = 16;
   */
  prefix: string;

  /**
   * @generated from field: string path = 17;
   */
  path: string;

  /**
   * @generated from field: string suffix = 18;
   */
  suffix: string;

  /**
   * @generated from field: repeated string blob_names = 19;
   */
  blobNames: string[];

  /**
   * @generated from field: string lang = 20;
   */
  lang: string;

  /**
   * @generated from field: request_insight.CompletionHostRequestPosition position = 21;
   */
  position?: CompletionHostRequestPosition;

  /**
   * @generated from field: bool probe_only = 22;
   */
  probeOnly: boolean;

  /**
   * @generated from field: base.blob_names.Blobs blobs = 23;
   */
  blobs?: Blobs;

  /**
   * @generated from field: completion.RecencyInfo recency_info = 24;
   */
  recencyInfo?: RecencyInfo;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 25;
   */
  tokenization?: Tokenization;

  /**
   * @generated from field: optional float user_filter_threshold = 26;
   */
  userFilterThreshold?: number;

  /**
   * @generated from field: repeated base.diff_utils.GranularEditEvent edit_events = 27;
   */
  editEvents: GranularEditEvent[];

  constructor(data?: PartialMessage<CompletionHostRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionHostRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionHostRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionHostRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionHostRequest;

  static equals(a: CompletionHostRequest | PlainMessage<CompletionHostRequest> | undefined, b: CompletionHostRequest | PlainMessage<CompletionHostRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.InferenceHostResponse
 */
export declare class InferenceHostResponse extends Message<InferenceHostResponse> {
  /**
   * @generated from field: float cum_log_probs = 2;
   */
  cumLogProbs: number;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 3;
   */
  tokenization?: Tokenization;

  constructor(data?: PartialMessage<InferenceHostResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.InferenceHostResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InferenceHostResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InferenceHostResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InferenceHostResponse;

  static equals(a: InferenceHostResponse | PlainMessage<InferenceHostResponse> | undefined, b: InferenceHostResponse | PlainMessage<InferenceHostResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RetrievalChunk
 */
export declare class RetrievalChunk extends Message<RetrievalChunk> {
  /**
   * @generated from field: string text = 5;
   */
  text: string;

  /**
   * @generated from field: string path = 6;
   */
  path: string;

  /**
   * @generated from field: int32 char_offset = 4;
   */
  charOffset: number;

  /**
   * @generated from field: int32 char_end = 7;
   */
  charEnd: number;

  /**
   * @generated from field: string blob_name = 2;
   */
  blobName: string;

  /**
   * @generated from field: int32 chunk_index = 3;
   */
  chunkIndex: number;

  /**
   * @generated from field: string origin = 8;
   */
  origin: string;

  /**
   * @generated from field: float score = 9;
   */
  score: number;

  constructor(data?: PartialMessage<RetrievalChunk>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RetrievalChunk";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetrievalChunk;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetrievalChunk;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetrievalChunk;

  static equals(a: RetrievalChunk | PlainMessage<RetrievalChunk> | undefined, b: RetrievalChunk | PlainMessage<RetrievalChunk> | undefined): boolean;
}

/**
 * @generated from message request_insight.RerankerChunk
 */
export declare class RerankerChunk extends Message<RerankerChunk> {
  /**
   * @generated from field: string text = 1;
   */
  text: string;

  /**
   * @generated from field: string path = 2;
   */
  path: string;

  /**
   * @generated from field: int32 char_offset = 3;
   */
  charOffset: number;

  /**
   * @generated from field: int32 char_end = 4;
   */
  charEnd: number;

  /**
   * @generated from field: string blob_name = 5;
   */
  blobName: string;

  /**
   * @generated from field: int32 chunk_index = 6;
   */
  chunkIndex: number;

  /**
   * @generated from field: string origin = 7;
   */
  origin: string;

  /**
   * @generated from field: float score = 8;
   */
  score: number;

  /**
   * @generated from field: string short_description = 9;
   */
  shortDescription: string;

  constructor(data?: PartialMessage<RerankerChunk>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RerankerChunk";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RerankerChunk;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RerankerChunk;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RerankerChunk;

  static equals(a: RerankerChunk | PlainMessage<RerankerChunk> | undefined, b: RerankerChunk | PlainMessage<RerankerChunk> | undefined): boolean;
}

/**
 * @generated from message request_insight.CompletionHostResponse
 */
export declare class CompletionHostResponse extends Message<CompletionHostResponse> {
  /**
   * @generated from field: string text = 2;
   */
  text: string;

  /**
   * @generated from field: repeated string unknown_blob_names = 3;
   */
  unknownBlobNames: string[];

  /**
   * @generated from field: string skipped_suffix = 6;
   */
  skippedSuffix: string;

  /**
   * @generated from field: string suffix_replacement_text = 7;
   */
  suffixReplacementText: string;

  /**
   * @generated from field: bool checkpoint_not_found = 8;
   */
  checkpointNotFound: boolean;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 9;
   */
  tokenization?: Tokenization;

  constructor(data?: PartialMessage<CompletionHostResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionHostResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionHostResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionHostResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionHostResponse;

  static equals(a: CompletionHostResponse | PlainMessage<CompletionHostResponse> | undefined, b: CompletionHostResponse | PlainMessage<CompletionHostResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.CompletionPostProcess
 */
export declare class CompletionPostProcess extends Message<CompletionPostProcess> {
  /**
   * @generated from field: optional bool is_low_quality = 1 [deprecated = true];
   * @deprecated
   */
  isLowQuality?: boolean;

  /**
   * @generated from field: float filter_score = 12;
   */
  filterScore: number;

  /**
   * @generated from field: optional float applied_filter_threshold = 13;
   */
  appliedFilterThreshold?: number;

  /**
   * @generated from field: optional request_insight.CompletionPostProcess.FilterReason filter_reason = 14;
   */
  filterReason?: CompletionPostProcess_FilterReason;

  constructor(data?: PartialMessage<CompletionPostProcess>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionPostProcess";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionPostProcess;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionPostProcess;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionPostProcess;

  static equals(a: CompletionPostProcess | PlainMessage<CompletionPostProcess> | undefined, b: CompletionPostProcess | PlainMessage<CompletionPostProcess> | undefined): boolean;
}

/**
 * @generated from enum request_insight.CompletionPostProcess.FilterReason
 */
export declare enum CompletionPostProcess_FilterReason {
  /**
   * @generated from enum value: UNKNOWN_FILTER_REASON = 0;
   */
  UNKNOWN_FILTER_REASON = 0,

  /**
   * @generated from enum value: NOT_FILTERED = 1;
   */
  NOT_FILTERED = 1,

  /**
   * @generated from enum value: LOW_QUALITY = 2;
   */
  LOW_QUALITY = 2,

  /**
   * @generated from enum value: DENY_LIST = 3;
   */
  DENY_LIST = 3,
}

/**
 * @generated from message request_insight.ParenthesisTruncation
 */
export declare class ParenthesisTruncation extends Message<ParenthesisTruncation> {
  /**
   * @generated from field: string original_text = 1;
   */
  originalText: string;

  /**
   * @generated from field: string truncated_text = 2;
   */
  truncatedText: string;

  /**
   * @generated from field: bool was_truncated = 3;
   */
  wasTruncated: boolean;

  /**
   * @generated from field: bool could_have_truncated = 4;
   */
  couldHaveTruncated: boolean;

  /**
   * @generated from field: string path = 5;
   */
  path: string;

  constructor(data?: PartialMessage<ParenthesisTruncation>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ParenthesisTruncation";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ParenthesisTruncation;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ParenthesisTruncation;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ParenthesisTruncation;

  static equals(a: ParenthesisTruncation | PlainMessage<ParenthesisTruncation> | undefined, b: ParenthesisTruncation | PlainMessage<ParenthesisTruncation> | undefined): boolean;
}

/**
 * @generated from message request_insight.RetrievalResponse
 */
export declare class RetrievalResponse extends Message<RetrievalResponse> {
  /**
   * @generated from field: request_insight.RetrievalType retrieval_type = 1;
   */
  retrievalType: RetrievalType;

  /**
   * @generated from field: repeated request_insight.RetrievalChunk retrieved_chunks = 3;
   */
  retrievedChunks: RetrievalChunk[];

  /**
   * @generated from field: request_insight.Tokenization query_prompt = 4;
   */
  queryPrompt?: Tokenization;

  constructor(data?: PartialMessage<RetrievalResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RetrievalResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RetrievalResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RetrievalResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RetrievalResponse;

  static equals(a: RetrievalResponse | PlainMessage<RetrievalResponse> | undefined, b: RetrievalResponse | PlainMessage<RetrievalResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RerankerResponse
 */
export declare class RerankerResponse extends Message<RerankerResponse> {
  /**
   * @generated from field: repeated request_insight.RerankerChunk reranked_chunks = 1;
   */
  rerankedChunks: RerankerChunk[];

  /**
   * @generated from field: repeated request_insight.Tokenization reranker_prompts = 3;
   */
  rerankerPrompts: Tokenization[];

  constructor(data?: PartialMessage<RerankerResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RerankerResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RerankerResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RerankerResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RerankerResponse;

  static equals(a: RerankerResponse | PlainMessage<RerankerResponse> | undefined, b: RerankerResponse | PlainMessage<RerankerResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.ApiHttpResponse
 */
export declare class ApiHttpResponse extends Message<ApiHttpResponse> {
  /**
   * @generated from field: uint32 code = 1;
   */
  code: number;

  /**
   * @generated from field: string error_message = 2;
   */
  errorMessage: string;

  constructor(data?: PartialMessage<ApiHttpResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ApiHttpResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ApiHttpResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ApiHttpResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ApiHttpResponse;

  static equals(a: ApiHttpResponse | PlainMessage<ApiHttpResponse> | undefined, b: ApiHttpResponse | PlainMessage<ApiHttpResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.UpdateRequestInfoResponse
 */
export declare class UpdateRequestInfoResponse extends Message<UpdateRequestInfoResponse> {
  constructor(data?: PartialMessage<UpdateRequestInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.UpdateRequestInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateRequestInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateRequestInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateRequestInfoResponse;

  static equals(a: UpdateRequestInfoResponse | PlainMessage<UpdateRequestInfoResponse> | undefined, b: UpdateRequestInfoResponse | PlainMessage<UpdateRequestInfoResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RecordSessionEventsRequest
 */
export declare class RecordSessionEventsRequest extends Message<RecordSessionEventsRequest> {
  /**
   * @generated from field: string session_id = 1;
   */
  sessionId: string;

  /**
   * @generated from field: request_insight.TenantInfo tenant_info = 2;
   */
  tenantInfo?: TenantInfo;

  /**
   * @generated from field: repeated request_insight.SessionEvent events = 3;
   */
  events: SessionEvent[];

  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 4;
   */
  opaqueUserId?: UserId;

  constructor(data?: PartialMessage<RecordSessionEventsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RecordSessionEventsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecordSessionEventsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecordSessionEventsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecordSessionEventsRequest;

  static equals(a: RecordSessionEventsRequest | PlainMessage<RecordSessionEventsRequest> | undefined, b: RecordSessionEventsRequest | PlainMessage<RecordSessionEventsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.SessionEvent
 */
export declare class SessionEvent extends Message<SessionEvent> {
  /**
   * @generated from field: google.protobuf.Timestamp time = 1;
   */
  time?: Timestamp;

  /**
   * @generated from field: optional string event_id = 3;
   */
  eventId?: string;

  /**
   * @generated from oneof request_insight.SessionEvent.event
   */
  event: {
    /**
     * @generated from field: request_insight.NextEditSessionEvent next_edit_session_event = 2;
     */
    value: NextEditSessionEvent;
    case: "nextEditSessionEvent";
  } | {
    /**
     * @generated from field: request_insight.OnboardingSessionEvent onboarding_session_event = 4;
     */
    value: OnboardingSessionEvent;
    case: "onboardingSessionEvent";
  } | {
    /**
     * @generated from field: request_insight.ClientMetric client_metric = 5;
     */
    value: ClientMetric;
    case: "clientMetric";
  } | {
    /**
     * @generated from field: request_insight.ExtensionSessionEvent extension_session_event = 10;
     */
    value: ExtensionSessionEvent;
    case: "extensionSessionEvent";
  } | {
    /**
     * @generated from field: request_insight.CustomerUISessionEvent customer_ui_session_event = 11;
     */
    value: CustomerUISessionEvent;
    case: "customerUiSessionEvent";
  } | {
    /**
     * @generated from field: request_insight.AgentSessionEvent agent_session_event = 12;
     */
    value: AgentSessionEvent;
    case: "agentSessionEvent";
  } | {
    /**
     * @generated from field: request_insight.ContentManagerUploadBlobs content_manager_upload_blobs = 13;
     */
    value: ContentManagerUploadBlobs;
    case: "contentManagerUploadBlobs";
  } | {
    /**
     * @generated from field: request_insight.ContentManagerCheckpointBlobs content_manager_checkpoint_blobs = 14;
     */
    value: ContentManagerCheckpointBlobs;
    case: "contentManagerCheckpointBlobs";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentSessionEvent remote_agent_session_event = 15;
     */
    value: RemoteAgentSessionEvent;
    case: "remoteAgentSessionEvent";
  } | {
    /**
     * @generated from field: request_insight.PurchaseCredits purchase_credits = 16;
     */
    value: PurchaseCredits;
    case: "purchaseCredits";
  } | {
    /**
     * @generated from field: request_insight.CancelSubscription cancel_subscription = 17;
     */
    value: CancelSubscription;
    case: "cancelSubscription";
  } | {
    /**
     * @generated from field: request_insight.UnschedulePendingSubscriptionCancellation unschedule_pending_subscription_cancellation = 18;
     */
    value: UnschedulePendingSubscriptionCancellation;
    case: "unschedulePendingSubscriptionCancellation";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentLog remote_agent_log = 19;
     */
    value: RemoteAgentLog;
    case: "remoteAgentLog";
  } | {
    /**
     * @generated from field: request_insight.FeatureVectorReport feature_vector_report = 20;
     */
    value: FeatureVectorReport;
    case: "featureVectorReport";
  } | {
    /**
     * @generated from field: request_insight.UnschedulePlanChanges unschedule_plan_changes = 21;
     */
    value: UnschedulePlanChanges;
    case: "unschedulePlanChanges";
  } | {
    /**
     * @generated from field: request_insight.TextEditEvent text_edit = 6;
     */
    value: TextEditEvent;
    case: "textEdit";
  } | {
    /**
     * @generated from field: request_insight.CompletionRequestIdIssuedEvent completion_request_id_issued = 7;
     */
    value: CompletionRequestIdIssuedEvent;
    case: "completionRequestIdIssued";
  } | {
    /**
     * @generated from field: request_insight.EditRequestIdIssuedEvent edit_request_id_issued = 8;
     */
    value: EditRequestIdIssuedEvent;
    case: "editRequestIdIssued";
  } | {
    /**
     * @generated from field: request_insight.NextEditRequestIdIssuedEvent next_edit_request_id_issued = 9;
     */
    value: NextEditRequestIdIssuedEvent;
    case: "nextEditRequestIdIssued";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<SessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.SessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SessionEvent;

  static equals(a: SessionEvent | PlainMessage<SessionEvent> | undefined, b: SessionEvent | PlainMessage<SessionEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.PurchaseCredits
 */
export declare class PurchaseCredits extends Message<PurchaseCredits> {
  /**
   * @generated from field: string orb_subscription_id = 1;
   */
  orbSubscriptionId: string;

  /**
   * @generated from field: float credits = 2;
   */
  credits: number;

  constructor(data?: PartialMessage<PurchaseCredits>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.PurchaseCredits";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PurchaseCredits;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PurchaseCredits;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PurchaseCredits;

  static equals(a: PurchaseCredits | PlainMessage<PurchaseCredits> | undefined, b: PurchaseCredits | PlainMessage<PurchaseCredits> | undefined): boolean;
}

/**
 * @generated from message request_insight.CancelSubscription
 */
export declare class CancelSubscription extends Message<CancelSubscription> {
  /**
   * @generated from field: string orb_subscription_id = 1;
   */
  orbSubscriptionId: string;

  constructor(data?: PartialMessage<CancelSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CancelSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CancelSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CancelSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CancelSubscription;

  static equals(a: CancelSubscription | PlainMessage<CancelSubscription> | undefined, b: CancelSubscription | PlainMessage<CancelSubscription> | undefined): boolean;
}

/**
 * @generated from message request_insight.UnschedulePendingSubscriptionCancellation
 */
export declare class UnschedulePendingSubscriptionCancellation extends Message<UnschedulePendingSubscriptionCancellation> {
  /**
   * @generated from field: string orb_subscription_id = 1;
   */
  orbSubscriptionId: string;

  constructor(data?: PartialMessage<UnschedulePendingSubscriptionCancellation>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.UnschedulePendingSubscriptionCancellation";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnschedulePendingSubscriptionCancellation;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnschedulePendingSubscriptionCancellation;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnschedulePendingSubscriptionCancellation;

  static equals(a: UnschedulePendingSubscriptionCancellation | PlainMessage<UnschedulePendingSubscriptionCancellation> | undefined, b: UnschedulePendingSubscriptionCancellation | PlainMessage<UnschedulePendingSubscriptionCancellation> | undefined): boolean;
}

/**
 * @generated from message request_insight.UnschedulePlanChanges
 */
export declare class UnschedulePlanChanges extends Message<UnschedulePlanChanges> {
  /**
   * @generated from field: string orb_subscription_id = 1;
   */
  orbSubscriptionId: string;

  constructor(data?: PartialMessage<UnschedulePlanChanges>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.UnschedulePlanChanges";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnschedulePlanChanges;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnschedulePlanChanges;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnschedulePlanChanges;

  static equals(a: UnschedulePlanChanges | PlainMessage<UnschedulePlanChanges> | undefined, b: UnschedulePlanChanges | PlainMessage<UnschedulePlanChanges> | undefined): boolean;
}

/**
 * @generated from message request_insight.CustomerUISessionEvent
 */
export declare class CustomerUISessionEvent extends Message<CustomerUISessionEvent> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from oneof request_insight.CustomerUISessionEvent.event
   */
  event: {
    /**
     * @generated from field: request_insight.CustomerUISessionEvent.SessionStart session_start = 2;
     */
    value: CustomerUISessionEvent_SessionStart;
    case: "sessionStart";
  } | {
    /**
     * @generated from field: request_insight.CustomerUISessionEvent.SessionEnd session_end = 3;
     */
    value: CustomerUISessionEvent_SessionEnd;
    case: "sessionEnd";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<CustomerUISessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CustomerUISessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CustomerUISessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CustomerUISessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CustomerUISessionEvent;

  static equals(a: CustomerUISessionEvent | PlainMessage<CustomerUISessionEvent> | undefined, b: CustomerUISessionEvent | PlainMessage<CustomerUISessionEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.CustomerUISessionEvent.SessionStart
 */
export declare class CustomerUISessionEvent_SessionStart extends Message<CustomerUISessionEvent_SessionStart> {
  /**
   * @generated from field: string redirect_url = 1;
   */
  redirectUrl: string;

  constructor(data?: PartialMessage<CustomerUISessionEvent_SessionStart>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CustomerUISessionEvent.SessionStart";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CustomerUISessionEvent_SessionStart;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CustomerUISessionEvent_SessionStart;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CustomerUISessionEvent_SessionStart;

  static equals(a: CustomerUISessionEvent_SessionStart | PlainMessage<CustomerUISessionEvent_SessionStart> | undefined, b: CustomerUISessionEvent_SessionStart | PlainMessage<CustomerUISessionEvent_SessionStart> | undefined): boolean;
}

/**
 * @generated from message request_insight.CustomerUISessionEvent.SessionEnd
 */
export declare class CustomerUISessionEvent_SessionEnd extends Message<CustomerUISessionEvent_SessionEnd> {
  /**
   * @generated from field: request_insight.CustomerUISessionEvent.SessionEnd.Reason reason = 1;
   */
  reason: CustomerUISessionEvent_SessionEnd_Reason;

  constructor(data?: PartialMessage<CustomerUISessionEvent_SessionEnd>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CustomerUISessionEvent.SessionEnd";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CustomerUISessionEvent_SessionEnd;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CustomerUISessionEvent_SessionEnd;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CustomerUISessionEvent_SessionEnd;

  static equals(a: CustomerUISessionEvent_SessionEnd | PlainMessage<CustomerUISessionEvent_SessionEnd> | undefined, b: CustomerUISessionEvent_SessionEnd | PlainMessage<CustomerUISessionEvent_SessionEnd> | undefined): boolean;
}

/**
 * @generated from enum request_insight.CustomerUISessionEvent.SessionEnd.Reason
 */
export declare enum CustomerUISessionEvent_SessionEnd_Reason {
  /**
   * @generated from enum value: UNKNOWN_SESSION_END_REASON = 0;
   */
  UNKNOWN_SESSION_END_REASON = 0,

  /**
   * @generated from enum value: LOGOUT = 1;
   */
  LOGOUT = 1,

  /**
   * @generated from enum value: TIMEOUT = 2;
   */
  TIMEOUT = 2,
}

/**
 * @generated from message request_insight.AgentInterruptionData
 */
export declare class AgentInterruptionData extends Message<AgentInterruptionData> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: uint32 curr_conversation_length = 2;
   */
  currConversationLength: number;

  constructor(data?: PartialMessage<AgentInterruptionData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentInterruptionData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentInterruptionData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentInterruptionData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentInterruptionData;

  static equals(a: AgentInterruptionData | PlainMessage<AgentInterruptionData> | undefined, b: AgentInterruptionData | PlainMessage<AgentInterruptionData> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentReversionData
 */
export declare class AgentReversionData extends Message<AgentReversionData> {
  constructor(data?: PartialMessage<AgentReversionData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentReversionData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentReversionData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentReversionData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentReversionData;

  static equals(a: AgentReversionData | PlainMessage<AgentReversionData> | undefined, b: AgentReversionData | PlainMessage<AgentReversionData> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentTracingData
 */
export declare class AgentTracingData extends Message<AgentTracingData> {
  /**
   * @generated from field: map<string, request_insight.AgentTracingData.TimedBoolean> flags = 3;
   */
  flags: { [key: string]: AgentTracingData_TimedBoolean };

  /**
   * @generated from field: map<string, request_insight.AgentTracingData.TimedNumber> nums = 4;
   */
  nums: { [key: string]: AgentTracingData_TimedNumber };

  /**
   * @generated from field: map<string, request_insight.AgentTracingData.TimedStringStats> string_stats = 5;
   */
  stringStats: { [key: string]: AgentTracingData_TimedStringStats };

  /**
   * @generated from field: map<string, request_insight.AgentTracingData.TimedString> request_ids = 6;
   */
  requestIds: { [key: string]: AgentTracingData_TimedString };

  constructor(data?: PartialMessage<AgentTracingData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentTracingData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentTracingData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentTracingData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentTracingData;

  static equals(a: AgentTracingData | PlainMessage<AgentTracingData> | undefined, b: AgentTracingData | PlainMessage<AgentTracingData> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentTracingData.StringStats
 */
export declare class AgentTracingData_StringStats extends Message<AgentTracingData_StringStats> {
  /**
   * @generated from field: int32 num_lines = 1;
   */
  numLines: number;

  /**
   * @generated from field: int32 num_chars = 2;
   */
  numChars: number;

  constructor(data?: PartialMessage<AgentTracingData_StringStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentTracingData.StringStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentTracingData_StringStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentTracingData_StringStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentTracingData_StringStats;

  static equals(a: AgentTracingData_StringStats | PlainMessage<AgentTracingData_StringStats> | undefined, b: AgentTracingData_StringStats | PlainMessage<AgentTracingData_StringStats> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentTracingData.TimedBoolean
 */
export declare class AgentTracingData_TimedBoolean extends Message<AgentTracingData_TimedBoolean> {
  /**
   * @generated from field: bool value = 1;
   */
  value: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp timestamp = 2;
   */
  timestamp?: Timestamp;

  constructor(data?: PartialMessage<AgentTracingData_TimedBoolean>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentTracingData.TimedBoolean";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentTracingData_TimedBoolean;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentTracingData_TimedBoolean;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentTracingData_TimedBoolean;

  static equals(a: AgentTracingData_TimedBoolean | PlainMessage<AgentTracingData_TimedBoolean> | undefined, b: AgentTracingData_TimedBoolean | PlainMessage<AgentTracingData_TimedBoolean> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentTracingData.TimedNumber
 */
export declare class AgentTracingData_TimedNumber extends Message<AgentTracingData_TimedNumber> {
  /**
   * @generated from field: double value = 1;
   */
  value: number;

  /**
   * @generated from field: google.protobuf.Timestamp timestamp = 2;
   */
  timestamp?: Timestamp;

  constructor(data?: PartialMessage<AgentTracingData_TimedNumber>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentTracingData.TimedNumber";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentTracingData_TimedNumber;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentTracingData_TimedNumber;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentTracingData_TimedNumber;

  static equals(a: AgentTracingData_TimedNumber | PlainMessage<AgentTracingData_TimedNumber> | undefined, b: AgentTracingData_TimedNumber | PlainMessage<AgentTracingData_TimedNumber> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentTracingData.TimedStringStats
 */
export declare class AgentTracingData_TimedStringStats extends Message<AgentTracingData_TimedStringStats> {
  /**
   * @generated from field: request_insight.AgentTracingData.StringStats value = 1;
   */
  value?: AgentTracingData_StringStats;

  /**
   * @generated from field: google.protobuf.Timestamp timestamp = 2;
   */
  timestamp?: Timestamp;

  constructor(data?: PartialMessage<AgentTracingData_TimedStringStats>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentTracingData.TimedStringStats";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentTracingData_TimedStringStats;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentTracingData_TimedStringStats;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentTracingData_TimedStringStats;

  static equals(a: AgentTracingData_TimedStringStats | PlainMessage<AgentTracingData_TimedStringStats> | undefined, b: AgentTracingData_TimedStringStats | PlainMessage<AgentTracingData_TimedStringStats> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentTracingData.TimedString
 */
export declare class AgentTracingData_TimedString extends Message<AgentTracingData_TimedString> {
  /**
   * @generated from field: string value = 1;
   */
  value: string;

  /**
   * @generated from field: google.protobuf.Timestamp timestamp = 2;
   */
  timestamp?: Timestamp;

  constructor(data?: PartialMessage<AgentTracingData_TimedString>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentTracingData.TimedString";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentTracingData_TimedString;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentTracingData_TimedString;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentTracingData_TimedString;

  static equals(a: AgentTracingData_TimedString | PlainMessage<AgentTracingData_TimedString> | undefined, b: AgentTracingData_TimedString | PlainMessage<AgentTracingData_TimedString> | undefined): boolean;
}

/**
 * @generated from message request_insight.RememberToolCallData
 */
export declare class RememberToolCallData extends Message<RememberToolCallData> {
  /**
   * @generated from field: request_insight.RememberToolCallData.RememberToolCaller caller = 1;
   */
  caller: RememberToolCallData_RememberToolCaller;

  /**
   * @generated from field: bool is_complex_new_memory = 2;
   */
  isComplexNewMemory: boolean;

  /**
   * @generated from field: request_insight.AgentTracingData tracing_data = 3;
   */
  tracingData?: AgentTracingData;

  constructor(data?: PartialMessage<RememberToolCallData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RememberToolCallData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RememberToolCallData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RememberToolCallData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RememberToolCallData;

  static equals(a: RememberToolCallData | PlainMessage<RememberToolCallData> | undefined, b: RememberToolCallData | PlainMessage<RememberToolCallData> | undefined): boolean;
}

/**
 * @generated from enum request_insight.RememberToolCallData.RememberToolCaller
 */
export declare enum RememberToolCallData_RememberToolCaller {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: CLASSIFY_AND_DISTILL = 1;
   */
  CLASSIFY_AND_DISTILL = 1,

  /**
   * @generated from enum value: ORIENTATION = 2;
   */
  ORIENTATION = 2,
}

/**
 * @generated from message request_insight.MemoriesFileOpenData
 */
export declare class MemoriesFileOpenData extends Message<MemoriesFileOpenData> {
  /**
   * @generated from field: bool memories_path_undefined = 1;
   */
  memoriesPathUndefined: boolean;

  constructor(data?: PartialMessage<MemoriesFileOpenData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.MemoriesFileOpenData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MemoriesFileOpenData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MemoriesFileOpenData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MemoriesFileOpenData;

  static equals(a: MemoriesFileOpenData | PlainMessage<MemoriesFileOpenData> | undefined, b: MemoriesFileOpenData | PlainMessage<MemoriesFileOpenData> | undefined): boolean;
}

/**
 * @generated from message request_insight.MemoriesMoveData
 */
export declare class MemoriesMoveData extends Message<MemoriesMoveData> {
  /**
   * @generated from field: request_insight.MemoriesMoveData.Target target = 1;
   */
  target: MemoriesMoveData_Target;

  constructor(data?: PartialMessage<MemoriesMoveData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.MemoriesMoveData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MemoriesMoveData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MemoriesMoveData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MemoriesMoveData;

  static equals(a: MemoriesMoveData | PlainMessage<MemoriesMoveData> | undefined, b: MemoriesMoveData | PlainMessage<MemoriesMoveData> | undefined): boolean;
}

/**
 * @generated from enum request_insight.MemoriesMoveData.Target
 */
export declare enum MemoriesMoveData_Target {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: USER_GUIDELINES = 1;
   */
  USER_GUIDELINES = 1,

  /**
   * @generated from enum value: AUGMENT_GUIDELINES = 2;
   */
  AUGMENT_GUIDELINES = 2,

  /**
   * @generated from enum value: RULES = 3;
   */
  RULES = 3,
}

/**
 * @generated from message request_insight.RulesImportedData
 */
export declare class RulesImportedData extends Message<RulesImportedData> {
  /**
   * @generated from field: request_insight.RulesImportedData.Type type = 1;
   */
  type: RulesImportedData_Type;

  /**
   * @generated from field: uint32 num_files = 2;
   */
  numFiles: number;

  /**
   * @generated from field: string source = 3;
   */
  source: string;

  constructor(data?: PartialMessage<RulesImportedData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RulesImportedData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RulesImportedData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RulesImportedData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RulesImportedData;

  static equals(a: RulesImportedData | PlainMessage<RulesImportedData> | undefined, b: RulesImportedData | PlainMessage<RulesImportedData> | undefined): boolean;
}

/**
 * @generated from enum request_insight.RulesImportedData.Type
 */
export declare enum RulesImportedData_Type {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: MANUALLY_CREATED = 1;
   */
  MANUALLY_CREATED = 1,

  /**
   * @generated from enum value: AUTO = 2;
   */
  AUTO = 2,

  /**
   * @generated from enum value: SELECTED_DIRECTORY = 3;
   */
  SELECTED_DIRECTORY = 3,

  /**
   * @generated from enum value: SELECTED_FILE = 4;
   */
  SELECTED_FILE = 4,
}

/**
 * @generated from message request_insight.InitialOrientationData
 */
export declare class InitialOrientationData extends Message<InitialOrientationData> {
  /**
   * @generated from field: request_insight.InitialOrientationData.OrientationCaller caller = 1;
   */
  caller: InitialOrientationData_OrientationCaller;

  /**
   * @generated from field: request_insight.AgentTracingData tracing_data = 2;
   */
  tracingData?: AgentTracingData;

  constructor(data?: PartialMessage<InitialOrientationData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.InitialOrientationData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InitialOrientationData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InitialOrientationData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InitialOrientationData;

  static equals(a: InitialOrientationData | PlainMessage<InitialOrientationData> | undefined, b: InitialOrientationData | PlainMessage<InitialOrientationData> | undefined): boolean;
}

/**
 * @generated from enum request_insight.InitialOrientationData.OrientationCaller
 */
export declare enum InitialOrientationData_OrientationCaller {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: ONBOARDING = 1;
   */
  ONBOARDING = 1,

  /**
   * @generated from enum value: COMMAND = 2;
   */
  COMMAND = 2,
}

/**
 * @generated from message request_insight.ClassifyAndDistillData
 */
export declare class ClassifyAndDistillData extends Message<ClassifyAndDistillData> {
  /**
   * @generated from field: request_insight.AgentTracingData tracing_data = 1;
   */
  tracingData?: AgentTracingData;

  constructor(data?: PartialMessage<ClassifyAndDistillData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ClassifyAndDistillData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClassifyAndDistillData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClassifyAndDistillData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClassifyAndDistillData;

  static equals(a: ClassifyAndDistillData | PlainMessage<ClassifyAndDistillData> | undefined, b: ClassifyAndDistillData | PlainMessage<ClassifyAndDistillData> | undefined): boolean;
}

/**
 * @generated from message request_insight.FlushMemoriesData
 */
export declare class FlushMemoriesData extends Message<FlushMemoriesData> {
  /**
   * @generated from field: request_insight.AgentTracingData tracing_data = 1;
   */
  tracingData?: AgentTracingData;

  constructor(data?: PartialMessage<FlushMemoriesData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FlushMemoriesData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FlushMemoriesData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FlushMemoriesData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FlushMemoriesData;

  static equals(a: FlushMemoriesData | PlainMessage<FlushMemoriesData> | undefined, b: FlushMemoriesData | PlainMessage<FlushMemoriesData> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentSessionEvent
 */
export declare class AgentSessionEvent extends Message<AgentSessionEvent> {
  /**
   * @generated from field: string event_name = 1;
   */
  eventName: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  /**
   * @generated from field: string conversation_id = 3;
   */
  conversationId: string;

  /**
   * @generated from oneof request_insight.AgentSessionEvent.event_data
   */
  eventData: {
    /**
     * @generated from field: request_insight.AgentReversionData agent_reversion_data = 4;
     */
    value: AgentReversionData;
    case: "agentReversionData";
  } | {
    /**
     * @generated from field: request_insight.AgentInterruptionData agent_interruption_data = 5;
     */
    value: AgentInterruptionData;
    case: "agentInterruptionData";
  } | {
    /**
     * @generated from field: request_insight.RememberToolCallData remember_tool_call_data = 6;
     */
    value: RememberToolCallData;
    case: "rememberToolCallData";
  } | {
    /**
     * @generated from field: request_insight.MemoriesFileOpenData memories_file_open_data = 7;
     */
    value: MemoriesFileOpenData;
    case: "memoriesFileOpenData";
  } | {
    /**
     * @generated from field: request_insight.InitialOrientationData initial_orientation_data = 8;
     */
    value: InitialOrientationData;
    case: "initialOrientationData";
  } | {
    /**
     * @generated from field: request_insight.ClassifyAndDistillData classify_and_distill_data = 9;
     */
    value: ClassifyAndDistillData;
    case: "classifyAndDistillData";
  } | {
    /**
     * @generated from field: request_insight.FlushMemoriesData flush_memories_data = 10;
     */
    value: FlushMemoriesData;
    case: "flushMemoriesData";
  } | {
    /**
     * @generated from field: request_insight.MemoriesMoveData memories_move_data = 11;
     */
    value: MemoriesMoveData;
    case: "memoriesMoveData";
  } | {
    /**
     * @generated from field: request_insight.RulesImportedData rules_imported_data = 12;
     */
    value: RulesImportedData;
    case: "rulesImportedData";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<AgentSessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentSessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentSessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentSessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentSessionEvent;

  static equals(a: AgentSessionEvent | PlainMessage<AgentSessionEvent> | undefined, b: AgentSessionEvent | PlainMessage<AgentSessionEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentSetupData
 */
export declare class RemoteAgentSetupData extends Message<RemoteAgentSetupData> {
  /**
   * @generated from field: bool used_generated_setup_script = 1;
   */
  usedGeneratedSetupScript: boolean;

  /**
   * @generated from field: request_insight.RemoteAgentSetupData.RemoteAgentSetupState setup_state = 2;
   */
  setupState: RemoteAgentSetupData_RemoteAgentSetupState;

  constructor(data?: PartialMessage<RemoteAgentSetupData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentSetupData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentSetupData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentSetupData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentSetupData;

  static equals(a: RemoteAgentSetupData | PlainMessage<RemoteAgentSetupData> | undefined, b: RemoteAgentSetupData | PlainMessage<RemoteAgentSetupData> | undefined): boolean;
}

/**
 * @generated from enum request_insight.RemoteAgentSetupData.RemoteAgentSetupState
 */
export declare enum RemoteAgentSetupData_RemoteAgentSetupState {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: COMPLETED = 1;
   */
  COMPLETED = 1,

  /**
   * @generated from enum value: FAILED = 2;
   */
  FAILED = 2,
}

/**
 * @generated from message request_insight.RemoteAgentSetupScriptData
 */
export declare class RemoteAgentSetupScriptData extends Message<RemoteAgentSetupScriptData> {
  /**
   * @generated from field: int32 num_tries = 1;
   */
  numTries: number;

  /**
   * @generated from field: int32 num_messages_sent = 3;
   */
  numMessagesSent: number;

  /**
   * @generated from field: int32 generation_time_ms = 4;
   */
  generationTimeMs: number;

  /**
   * @generated from field: bool manual_modification = 5;
   */
  manualModification: boolean;

  constructor(data?: PartialMessage<RemoteAgentSetupScriptData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentSetupScriptData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentSetupScriptData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentSetupScriptData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentSetupScriptData;

  static equals(a: RemoteAgentSetupScriptData | PlainMessage<RemoteAgentSetupScriptData> | undefined, b: RemoteAgentSetupScriptData | PlainMessage<RemoteAgentSetupScriptData> | undefined): boolean;
}

/**
 * @generated from message request_insight.SSHInteractionData
 */
export declare class SSHInteractionData extends Message<SSHInteractionData> {
  /**
   * @generated from field: request_insight.SSHInteractionData.InteractionType interaction_type = 1;
   */
  interactionType: SSHInteractionData_InteractionType;

  constructor(data?: PartialMessage<SSHInteractionData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.SSHInteractionData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SSHInteractionData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SSHInteractionData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SSHInteractionData;

  static equals(a: SSHInteractionData | PlainMessage<SSHInteractionData> | undefined, b: SSHInteractionData | PlainMessage<SSHInteractionData> | undefined): boolean;
}

/**
 * @generated from enum request_insight.SSHInteractionData.InteractionType
 */
export declare enum SSHInteractionData_InteractionType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: CLICKED = 1;
   */
  CLICKED = 1,

  /**
   * @generated from enum value: REMOTE_SESSION_STARTED = 2;
   */
  REMOTE_SESSION_STARTED = 2,
}

/**
 * @generated from message request_insight.NotificationBellData
 */
export declare class NotificationBellData extends Message<NotificationBellData> {
  /**
   * @generated from field: request_insight.NotificationBellData.NotificationBellState bell_state = 1;
   */
  bellState: NotificationBellData_NotificationBellState;

  constructor(data?: PartialMessage<NotificationBellData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.NotificationBellData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotificationBellData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotificationBellData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotificationBellData;

  static equals(a: NotificationBellData | PlainMessage<NotificationBellData> | undefined, b: NotificationBellData | PlainMessage<NotificationBellData> | undefined): boolean;
}

/**
 * @generated from enum request_insight.NotificationBellData.NotificationBellState
 */
export declare enum NotificationBellData_NotificationBellState {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ACTIVATED = 1;
   */
  ACTIVATED = 1,

  /**
   * @generated from enum value: DEACTIVATED = 2;
   */
  DEACTIVATED = 2,

  /**
   * @generated from enum value: NOTIFIED = 4;
   */
  NOTIFIED = 4,

  /**
   * @generated from enum value: ACCEPTED = 3;
   */
  ACCEPTED = 3,
}

/**
 * @generated from message request_insight.DiffPanelData
 */
export declare class DiffPanelData extends Message<DiffPanelData> {
  /**
   * @generated from field: int32 loading_time_ms = 1;
   */
  loadingTimeMs: number;

  /**
   * @generated from field: bool applied = 2;
   */
  applied: boolean;

  constructor(data?: PartialMessage<DiffPanelData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.DiffPanelData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DiffPanelData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DiffPanelData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DiffPanelData;

  static equals(a: DiffPanelData | PlainMessage<DiffPanelData> | undefined, b: DiffPanelData | PlainMessage<DiffPanelData> | undefined): boolean;
}

/**
 * @generated from message request_insight.SetupPageOpened
 */
export declare class SetupPageOpened extends Message<SetupPageOpened> {
  constructor(data?: PartialMessage<SetupPageOpened>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.SetupPageOpened";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetupPageOpened;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetupPageOpened;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetupPageOpened;

  static equals(a: SetupPageOpened | PlainMessage<SetupPageOpened> | undefined, b: SetupPageOpened | PlainMessage<SetupPageOpened> | undefined): boolean;
}

/**
 * @generated from message request_insight.GithubAPIFailure
 */
export declare class GithubAPIFailure extends Message<GithubAPIFailure> {
  /**
   * @generated from field: int32 error_code = 1;
   */
  errorCode: number;

  constructor(data?: PartialMessage<GithubAPIFailure>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.GithubAPIFailure";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GithubAPIFailure;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GithubAPIFailure;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GithubAPIFailure;

  static equals(a: GithubAPIFailure | PlainMessage<GithubAPIFailure> | undefined, b: GithubAPIFailure | PlainMessage<GithubAPIFailure> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentCreated
 */
export declare class RemoteAgentCreated extends Message<RemoteAgentCreated> {
  /**
   * @generated from field: bool changed_repo = 1;
   */
  changedRepo: boolean;

  /**
   * @generated from field: bool changed_branch = 2;
   */
  changedBranch: boolean;

  constructor(data?: PartialMessage<RemoteAgentCreated>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentCreated";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentCreated;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentCreated;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentCreated;

  static equals(a: RemoteAgentCreated | PlainMessage<RemoteAgentCreated> | undefined, b: RemoteAgentCreated | PlainMessage<RemoteAgentCreated> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentSessionEvent
 */
export declare class RemoteAgentSessionEvent extends Message<RemoteAgentSessionEvent> {
  /**
   * @generated from field: string event_name = 1;
   */
  eventName: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  /**
   * @generated from field: string remote_agent_id = 3;
   */
  remoteAgentId: string;

  /**
   * @generated from oneof request_insight.RemoteAgentSessionEvent.event_data
   */
  eventData: {
    /**
     * @generated from field: request_insight.RemoteAgentSetupData remote_agent_setup_data = 4;
     */
    value: RemoteAgentSetupData;
    case: "remoteAgentSetupData";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentSetupScriptData setup_script_data = 5;
     */
    value: RemoteAgentSetupScriptData;
    case: "setupScriptData";
  } | {
    /**
     * @generated from field: request_insight.SSHInteractionData ssh_interaction_data = 6;
     */
    value: SSHInteractionData;
    case: "sshInteractionData";
  } | {
    /**
     * @generated from field: request_insight.NotificationBellData notification_bell_data = 7;
     */
    value: NotificationBellData;
    case: "notificationBellData";
  } | {
    /**
     * @generated from field: request_insight.DiffPanelData diff_panel_data = 8;
     */
    value: DiffPanelData;
    case: "diffPanelData";
  } | {
    /**
     * @generated from field: request_insight.SetupPageOpened setup_page_opened = 9;
     */
    value: SetupPageOpened;
    case: "setupPageOpened";
  } | {
    /**
     * @generated from field: request_insight.GithubAPIFailure github_api_failure = 10;
     */
    value: GithubAPIFailure;
    case: "githubApiFailure";
  } | {
    /**
     * @generated from field: request_insight.RemoteAgentCreated remote_agent_created = 11;
     */
    value: RemoteAgentCreated;
    case: "remoteAgentCreated";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<RemoteAgentSessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentSessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentSessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentSessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentSessionEvent;

  static equals(a: RemoteAgentSessionEvent | PlainMessage<RemoteAgentSessionEvent> | undefined, b: RemoteAgentSessionEvent | PlainMessage<RemoteAgentSessionEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.AgentRequestEvent
 */
export declare class AgentRequestEvent extends Message<AgentRequestEvent> {
  /**
   * @generated from field: string event_name = 1;
   */
  eventName: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  /**
   * @generated from field: string conversation_id = 3;
   */
  conversationId: string;

  /**
   * @generated from field: uint32 chat_history_length = 4;
   */
  chatHistoryLength: number;

  constructor(data?: PartialMessage<AgentRequestEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AgentRequestEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AgentRequestEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AgentRequestEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AgentRequestEvent;

  static equals(a: AgentRequestEvent | PlainMessage<AgentRequestEvent> | undefined, b: AgentRequestEvent | PlainMessage<AgentRequestEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.OnboardingSessionEvent
 */
export declare class OnboardingSessionEvent extends Message<OnboardingSessionEvent> {
  /**
   * @generated from field: string event_name = 1;
   */
  eventName: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  constructor(data?: PartialMessage<OnboardingSessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.OnboardingSessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnboardingSessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnboardingSessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnboardingSessionEvent;

  static equals(a: OnboardingSessionEvent | PlainMessage<OnboardingSessionEvent> | undefined, b: OnboardingSessionEvent | PlainMessage<OnboardingSessionEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.ExtensionSessionEvent
 */
export declare class ExtensionSessionEvent extends Message<ExtensionSessionEvent> {
  /**
   * @generated from field: string event_name = 1;
   */
  eventName: string;

  /**
   * @generated from field: string user_agent = 2;
   */
  userAgent: string;

  /**
   * @generated from field: repeated request_insight.ExtensionSessionEvent.KeyValue additional_data = 3;
   */
  additionalData: ExtensionSessionEvent_KeyValue[];

  constructor(data?: PartialMessage<ExtensionSessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ExtensionSessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExtensionSessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExtensionSessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExtensionSessionEvent;

  static equals(a: ExtensionSessionEvent | PlainMessage<ExtensionSessionEvent> | undefined, b: ExtensionSessionEvent | PlainMessage<ExtensionSessionEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.ExtensionSessionEvent.KeyValue
 */
export declare class ExtensionSessionEvent_KeyValue extends Message<ExtensionSessionEvent_KeyValue> {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: string value = 2;
   */
  value: string;

  constructor(data?: PartialMessage<ExtensionSessionEvent_KeyValue>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ExtensionSessionEvent.KeyValue";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExtensionSessionEvent_KeyValue;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExtensionSessionEvent_KeyValue;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExtensionSessionEvent_KeyValue;

  static equals(a: ExtensionSessionEvent_KeyValue | PlainMessage<ExtensionSessionEvent_KeyValue> | undefined, b: ExtensionSessionEvent_KeyValue | PlainMessage<ExtensionSessionEvent_KeyValue> | undefined): boolean;
}

/**
 * @generated from message request_insight.NextEditSessionEvent
 */
export declare class NextEditSessionEvent extends Message<NextEditSessionEvent> {
  /**
   * @generated from field: optional string related_request_id = 1;
   */
  relatedRequestId?: string;

  /**
   * @generated from field: optional string related_suggestion_id = 2;
   */
  relatedSuggestionId?: string;

  /**
   * @generated from field: string event_name = 3;
   */
  eventName: string;

  /**
   * @generated from field: optional string event_source = 4;
   */
  eventSource?: string;

  /**
   * @generated from field: string user_agent = 5;
   */
  userAgent: string;

  constructor(data?: PartialMessage<NextEditSessionEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.NextEditSessionEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NextEditSessionEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NextEditSessionEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NextEditSessionEvent;

  static equals(a: NextEditSessionEvent | PlainMessage<NextEditSessionEvent> | undefined, b: NextEditSessionEvent | PlainMessage<NextEditSessionEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.ContentManagerUploadBlobs
 */
export declare class ContentManagerUploadBlobs extends Message<ContentManagerUploadBlobs> {
  /**
   * @generated from field: repeated request_insight.ContentManagerUploadBlobs.UploadedBlobInfo uploaded_blobs = 1;
   */
  uploadedBlobs: ContentManagerUploadBlobs_UploadedBlobInfo[];

  constructor(data?: PartialMessage<ContentManagerUploadBlobs>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ContentManagerUploadBlobs";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ContentManagerUploadBlobs;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ContentManagerUploadBlobs;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ContentManagerUploadBlobs;

  static equals(a: ContentManagerUploadBlobs | PlainMessage<ContentManagerUploadBlobs> | undefined, b: ContentManagerUploadBlobs | PlainMessage<ContentManagerUploadBlobs> | undefined): boolean;
}

/**
 * @generated from message request_insight.ContentManagerUploadBlobs.UploadedBlobInfo
 */
export declare class ContentManagerUploadBlobs_UploadedBlobInfo extends Message<ContentManagerUploadBlobs_UploadedBlobInfo> {
  /**
   * @generated from field: string blob_name = 1;
   */
  blobName: string;

  /**
   * @generated from field: uint64 size_bytes = 2;
   */
  sizeBytes: bigint;

  /**
   * @generated from field: bool existed = 3;
   */
  existed: boolean;

  constructor(data?: PartialMessage<ContentManagerUploadBlobs_UploadedBlobInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ContentManagerUploadBlobs.UploadedBlobInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ContentManagerUploadBlobs_UploadedBlobInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ContentManagerUploadBlobs_UploadedBlobInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ContentManagerUploadBlobs_UploadedBlobInfo;

  static equals(a: ContentManagerUploadBlobs_UploadedBlobInfo | PlainMessage<ContentManagerUploadBlobs_UploadedBlobInfo> | undefined, b: ContentManagerUploadBlobs_UploadedBlobInfo | PlainMessage<ContentManagerUploadBlobs_UploadedBlobInfo> | undefined): boolean;
}

/**
 * @generated from message request_insight.ContentManagerCheckpointBlobs
 */
export declare class ContentManagerCheckpointBlobs extends Message<ContentManagerCheckpointBlobs> {
  /**
   * @generated from field: string checkpoint_id = 1;
   */
  checkpointId: string;

  /**
   * @generated from field: string baseline_checkpoint_id = 2;
   */
  baselineCheckpointId: string;

  /**
   * @generated from field: uint32 added_blobs_count = 3;
   */
  addedBlobsCount: number;

  /**
   * @generated from field: uint32 deleted_blobs_count = 4;
   */
  deletedBlobsCount: number;

  constructor(data?: PartialMessage<ContentManagerCheckpointBlobs>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ContentManagerCheckpointBlobs";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ContentManagerCheckpointBlobs;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ContentManagerCheckpointBlobs;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ContentManagerCheckpointBlobs;

  static equals(a: ContentManagerCheckpointBlobs | PlainMessage<ContentManagerCheckpointBlobs> | undefined, b: ContentManagerCheckpointBlobs | PlainMessage<ContentManagerCheckpointBlobs> | undefined): boolean;
}

/**
 * @generated from message request_insight.RequestInfo
 */
export declare class RequestInfo extends Message<RequestInfo> {
  /**
   * @generated from field: request_insight.InferRequest infer_request = 1;
   */
  inferRequest?: InferRequest;

  /**
   * @generated from field: google.protobuf.Timestamp infer_request_time = 2;
   */
  inferRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.CompletionHostRequest completion_host_request = 11;
   */
  completionHostRequest?: CompletionHostRequest;

  /**
   * @generated from field: google.protobuf.Timestamp completion_host_request_time = 12;
   */
  completionHostRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.InferenceHostResponse inference_host_response = 15;
   */
  inferenceHostResponse?: InferenceHostResponse;

  /**
   * @generated from field: google.protobuf.Timestamp inference_host_response_time = 16;
   */
  inferenceHostResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.CompletionHostResponse completion_host_response = 13;
   */
  completionHostResponse?: CompletionHostResponse;

  /**
   * @generated from field: google.protobuf.Timestamp completion_host_response_time = 14;
   */
  completionHostResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.ApiHttpResponse api_http_response = 9;
   */
  apiHttpResponse?: ApiHttpResponse;

  /**
   * @generated from field: google.protobuf.Timestamp api_http_response_time = 10;
   */
  apiHttpResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.EmbeddingsSearchRequest embeddings_search_request = 17;
   */
  embeddingsSearchRequest?: EmbeddingsSearchRequest;

  /**
   * @generated from field: google.protobuf.Timestamp embeddings_search_request_time = 18;
   */
  embeddingsSearchRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.EmbeddingsSearchResponse embeddings_search_response = 19;
   */
  embeddingsSearchResponse?: EmbeddingsSearchResponse;

  /**
   * @generated from field: google.protobuf.Timestamp embeddings_search_response_time = 20;
   */
  embeddingsSearchResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.CompletionEmit completion_emit = 21;
   */
  completionEmit?: CompletionEmit;

  /**
   * @generated from field: google.protobuf.Timestamp completion_emit_time = 22;
   */
  completionEmitTime?: Timestamp;

  /**
   * @generated from field: request_insight.CompletionResolution completion_resolution = 23;
   */
  completionResolution?: CompletionResolution;

  /**
   * @generated from field: google.protobuf.Timestamp completion_resolution_time = 24;
   */
  completionResolutionTime?: Timestamp;

  /**
   * @generated from field: request_insight.CompletionFeedback completion_feedback = 25;
   */
  completionFeedback?: CompletionFeedback;

  /**
   * @generated from field: google.protobuf.Timestamp completion_feedback_time = 26;
   */
  completionFeedbackTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIEditRequest edit_host_request = 27;
   */
  editHostRequest?: RIEditRequest;

  /**
   * @generated from field: google.protobuf.Timestamp edit_host_request_time = 28;
   */
  editHostRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIEditResponse edit_host_response = 29;
   */
  editHostResponse?: RIEditResponse;

  /**
   * @generated from field: google.protobuf.Timestamp edit_host_response_time = 30;
   */
  editHostResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.EditEmit edit_emit = 31;
   */
  editEmit?: EditEmit;

  /**
   * @generated from field: google.protobuf.Timestamp edit_emit_time = 32;
   */
  editEmitTime?: Timestamp;

  /**
   * @generated from field: request_insight.EditResolution edit_resolution = 33;
   */
  editResolution?: EditResolution;

  /**
   * @generated from field: google.protobuf.Timestamp edit_resolution_time = 34;
   */
  editResolutionTime?: Timestamp;

  /**
   * @generated from field: request_insight.RequestMetadata request_metadata = 35;
   */
  requestMetadata?: RequestMetadata;

  /**
   * @generated from field: google.protobuf.Timestamp request_metadata_time = 36;
   */
  requestMetadataTime?: Timestamp;

  /**
   * @generated from field: repeated request_insight.RetrievalResponse retrieval_response_list = 37;
   */
  retrievalResponseList: RetrievalResponse[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp retrieval_response_time_list = 38;
   */
  retrievalResponseTimeList: Timestamp[];

  /**
   * @generated from field: request_insight.RIChatRequest chat_host_request = 39;
   */
  chatHostRequest?: RIChatRequest;

  /**
   * @generated from field: google.protobuf.Timestamp chat_host_request_time = 40;
   */
  chatHostRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIChatResponse chat_host_response = 41;
   */
  chatHostResponse?: RIChatResponse;

  /**
   * @generated from field: google.protobuf.Timestamp chat_host_response_time = 42;
   */
  chatHostResponseTime?: Timestamp;

  /**
   * @generated from field: repeated request_insight.ExtensionError extension_error_list = 43;
   */
  extensionErrorList: ExtensionError[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp extension_error_time_list = 44;
   */
  extensionErrorTimeList: Timestamp[];

  /**
   * @generated from field: request_insight.CompletionPostProcess completion_post_process = 45;
   */
  completionPostProcess?: CompletionPostProcess;

  /**
   * @generated from field: google.protobuf.Timestamp completion_post_process_time = 46;
   */
  completionPostProcessTime?: Timestamp;

  /**
   * @generated from field: request_insight.TenantInfo tenant_info = 47;
   */
  tenantInfo?: TenantInfo;

  /**
   * @generated from field: request_insight.ChatFeedback chat_feedback = 48;
   */
  chatFeedback?: ChatFeedback;

  /**
   * @generated from field: google.protobuf.Timestamp chat_feedback_time = 49;
   */
  chatFeedbackTime?: Timestamp;

  /**
   * @generated from field: repeated request_insight.RerankerResponse reranker_response_list = 50;
   */
  rerankerResponseList: RerankerResponse[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp reranker_response_time_list = 51;
   */
  rerankerResponseTimeList: Timestamp[];

  /**
   * @generated from field: request_insight.RINextEditRequest next_edit_host_request = 52;
   */
  nextEditHostRequest?: RINextEditRequest;

  /**
   * @generated from field: google.protobuf.Timestamp next_edit_host_request_time = 53;
   */
  nextEditHostRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RINextEditResponse next_edit_host_response = 54;
   */
  nextEditHostResponse?: RINextEditResponse;

  /**
   * @generated from field: google.protobuf.Timestamp next_edit_host_response_time = 55;
   */
  nextEditHostResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.ClientCompletionTimeline client_completion_timeline = 56;
   */
  clientCompletionTimeline?: ClientCompletionTimeline;

  /**
   * @generated from field: google.protobuf.Timestamp client_completion_timeline_time = 57;
   */
  clientCompletionTimelineTime?: Timestamp;

  /**
   * @generated from field: request_insight.NextEditFeedback next_edit_feedback = 58;
   */
  nextEditFeedback?: NextEditFeedback;

  /**
   * @generated from field: google.protobuf.Timestamp next_edit_feedback_time = 59;
   */
  nextEditFeedbackTime?: Timestamp;

  /**
   * @generated from field: request_insight.NextEditResolution next_edit_resolution = 60;
   */
  nextEditResolution?: NextEditResolution;

  /**
   * @generated from field: google.protobuf.Timestamp next_edit_resolution_time = 61;
   */
  nextEditResolutionTime?: Timestamp;

  /**
   * @generated from field: request_insight.NextEditEmit next_edit_emit = 62;
   */
  nextEditEmit?: NextEditEmit;

  /**
   * @generated from field: google.protobuf.Timestamp next_edit_emit_time = 63;
   */
  nextEditEmitTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIInstructionRequest instruction_host_request = 66;
   */
  instructionHostRequest?: RIInstructionRequest;

  /**
   * @generated from field: google.protobuf.Timestamp instruction_host_request_time = 67;
   */
  instructionHostRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIInstructionResponse instruction_host_response = 68;
   */
  instructionHostResponse?: RIInstructionResponse;

  /**
   * @generated from field: google.protobuf.Timestamp instruction_host_response_time = 69;
   */
  instructionHostResponseTime?: Timestamp;

  /**
   * @generated from field: repeated request_insight.RISlackbotRequest slackbot_request_list = 70;
   */
  slackbotRequestList: RISlackbotRequest[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp slackbot_request_time_list = 71;
   */
  slackbotRequestTimeList: Timestamp[];

  /**
   * @generated from field: repeated request_insight.RISlackbotResponse slackbot_response_list = 72;
   */
  slackbotResponseList: RISlackbotResponse[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp slackbot_response_time_list = 73;
   */
  slackbotResponseTimeList: Timestamp[];

  /**
   * @generated from field: request_insight.RIGithubEvent github_event = 74;
   */
  githubEvent?: RIGithubEvent;

  /**
   * @generated from field: google.protobuf.Timestamp github_event_time = 75;
   */
  githubEventTime?: Timestamp;

  /**
   * @generated from field: repeated request_insight.RIGithubProcessingResult github_processing_result_list = 76;
   */
  githubProcessingResultList: RIGithubProcessingResult[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp github_processing_result_time_list = 77;
   */
  githubProcessingResultTimeList: Timestamp[];

  /**
   * @generated from field: request_insight.RIShareSaveChatRequest share_save_chat_request = 78;
   */
  shareSaveChatRequest?: RIShareSaveChatRequest;

  /**
   * @generated from field: google.protobuf.Timestamp share_save_chat_request_time = 79;
   */
  shareSaveChatRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIShareSaveChatResponse share_save_chat_response = 80;
   */
  shareSaveChatResponse?: RIShareSaveChatResponse;

  /**
   * @generated from field: google.protobuf.Timestamp share_save_chat_response_time = 81;
   */
  shareSaveChatResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIShareGetChatRequest share_get_chat_request = 82;
   */
  shareGetChatRequest?: RIShareGetChatRequest;

  /**
   * @generated from field: google.protobuf.Timestamp share_get_chat_request_time = 83;
   */
  shareGetChatRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIShareGetChatResponse share_get_chat_response = 84;
   */
  shareGetChatResponse?: RIShareGetChatResponse;

  /**
   * @generated from field: google.protobuf.Timestamp share_get_chat_response_time = 85;
   */
  shareGetChatResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.SmartPasteClientTimeline smart_paste_client_timeline = 86;
   */
  smartPasteClientTimeline?: SmartPasteClientTimeline;

  /**
   * @generated from field: google.protobuf.Timestamp smart_paste_client_timeline_time = 87;
   */
  smartPasteClientTimelineTime?: Timestamp;

  /**
   * @generated from field: request_insight.SmartPasteResolution smart_paste_resolution = 88;
   */
  smartPasteResolution?: SmartPasteResolution;

  /**
   * @generated from field: google.protobuf.Timestamp smart_paste_resolution_time = 89;
   */
  smartPasteResolutionTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIGithubAppInstallationEvent github_app_installation_event = 90;
   */
  githubAppInstallationEvent?: RIGithubAppInstallationEvent;

  /**
   * @generated from field: google.protobuf.Timestamp github_app_installation_event_time = 91;
   */
  githubAppInstallationEventTime?: Timestamp;

  /**
   * @generated from field: request_insight.RISlackbotInstallationEvent slackbot_installation_event = 92;
   */
  slackbotInstallationEvent?: RISlackbotInstallationEvent;

  /**
   * @generated from field: google.protobuf.Timestamp slackbot_installation_event_time = 93;
   */
  slackbotInstallationEventTime?: Timestamp;

  /**
   * @generated from field: request_insight.InstructionResolution instruction_resolution = 94;
   */
  instructionResolution?: InstructionResolution;

  /**
   * @generated from field: google.protobuf.Timestamp instruction_resolution_time = 95;
   */
  instructionResolutionTime?: Timestamp;

  /**
   * @generated from field: request_insight.InstructionEmit instruction_emit = 96;
   */
  instructionEmit?: InstructionEmit;

  /**
   * @generated from field: google.protobuf.Timestamp instruction_emit_time = 97;
   */
  instructionEmitTime?: Timestamp;

  /**
   * @generated from field: request_insight.SlackbotFeedback slackbot_feedback = 98;
   */
  slackbotFeedback?: SlackbotFeedback;

  /**
   * @generated from field: google.protobuf.Timestamp slackbot_feedback_time = 99;
   */
  slackbotFeedbackTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIGleanRequest glean_request = 108;
   */
  gleanRequest?: RIGleanRequest;

  /**
   * @generated from field: google.protobuf.Timestamp glean_request_time = 109;
   */
  gleanRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIGleanResponse glean_response = 110;
   */
  gleanResponse?: RIGleanResponse;

  /**
   * @generated from field: google.protobuf.Timestamp glean_response_time = 111;
   */
  gleanResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RILLMGenerateRequest llm_generate_request = 112;
   */
  llmGenerateRequest?: RILLMGenerateRequest;

  /**
   * @generated from field: google.protobuf.Timestamp llm_generate_request_time = 113;
   */
  llmGenerateRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RILLMGenerateResponse llm_generate_response = 114;
   */
  llmGenerateResponse?: RILLMGenerateResponse;

  /**
   * @generated from field: google.protobuf.Timestamp llm_generate_response_time = 115;
   */
  llmGenerateResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIRemoteToolCallRequest remote_tool_call_request = 116;
   */
  remoteToolCallRequest?: RIRemoteToolCallRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_tool_call_request_time = 117;
   */
  remoteToolCallRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RIRemoteToolCallResponse remote_tool_call_response = 118;
   */
  remoteToolCallResponse?: RIRemoteToolCallResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_tool_call_response_time = 119;
   */
  remoteToolCallResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.SubAgentDialog sub_agent_dialog = 120;
   */
  subAgentDialog?: SubAgentDialog;

  /**
   * @generated from field: google.protobuf.Timestamp sub_agent_dialog_time = 121;
   */
  subAgentDialogTime?: Timestamp;

  /**
   * @generated from field: repeated request_insight.PromptCacheUsage prompt_cache_usage_list = 122;
   */
  promptCacheUsageList: PromptCacheUsage[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp prompt_cache_usage_time_list = 123;
   */
  promptCacheUsageTimeList: Timestamp[];

  /**
   * @generated from field: repeated request_insight.ToolUseData tool_use_data_list = 124;
   */
  toolUseDataList: ToolUseData[];

  /**
   * @generated from field: repeated google.protobuf.Timestamp tool_use_data_time_list = 125;
   */
  toolUseDataTimeList: Timestamp[];

  /**
   * @generated from field: request_insight.RouterResponse router_response = 126;
   */
  routerResponse?: RouterResponse;

  /**
   * @generated from field: google.protobuf.Timestamp router_response_time = 127;
   */
  routerResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.PostprocessResponse postprocess_response = 128;
   */
  postprocessResponse?: PostprocessResponse;

  /**
   * @generated from field: google.protobuf.Timestamp postprocess_response_time = 129;
   */
  postprocessResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.AgentFeedback agent_feedback = 130;
   */
  agentFeedback?: AgentFeedback;

  /**
   * @generated from field: google.protobuf.Timestamp agent_feedback_time = 131;
   */
  agentFeedbackTime?: Timestamp;

  /**
   * @generated from field: request_insight.AgentRequestEvent agent_request_event = 132;
   */
  agentRequestEvent?: AgentRequestEvent;

  /**
   * @generated from field: google.protobuf.Timestamp agent_request_event_time = 133;
   */
  agentRequestEventTime?: Timestamp;

  /**
   * @generated from field: request_insight.RequestBlocked request_blocked = 134;
   */
  requestBlocked?: RequestBlocked;

  /**
   * @generated from field: google.protobuf.Timestamp request_blocked_time = 135;
   */
  requestBlockedTime?: Timestamp;

  /**
   * @generated from field: request_insight.RequestSuspicious request_suspicious = 136;
   */
  requestSuspicious?: RequestSuspicious;

  /**
   * @generated from field: google.protobuf.Timestamp request_suspicious_time = 137;
   */
  requestSuspiciousTime?: Timestamp;

  /**
   * @generated from field: request_insight.TokenExchangeError token_exchange_error = 138;
   */
  tokenExchangeError?: TokenExchangeError;

  /**
   * @generated from field: google.protobuf.Timestamp token_exchange_error_time = 139;
   */
  tokenExchangeErrorTime?: Timestamp;

  /**
   * @generated from field: request_insight.DailyRequestLimitExceeded daily_request_limit_exceeded = 140;
   */
  dailyRequestLimitExceeded?: DailyRequestLimitExceeded;

  /**
   * @generated from field: google.protobuf.Timestamp daily_request_limit_exceeded_time = 141;
   */
  dailyRequestLimitExceededTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsCreateRequest remote_agents_create_request = 142;
   */
  remoteAgentsCreateRequest?: RemoteAgentsCreateRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_create_request_time = 143;
   */
  remoteAgentsCreateRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsCreateResponse remote_agents_create_response = 144;
   */
  remoteAgentsCreateResponse?: RemoteAgentsCreateResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_create_response_time = 145;
   */
  remoteAgentsCreateResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsChatRequest remote_agents_chat_request = 146;
   */
  remoteAgentsChatRequest?: RemoteAgentsChatRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_chat_request_time = 147;
   */
  remoteAgentsChatRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsChatResponse remote_agents_chat_response = 148;
   */
  remoteAgentsChatResponse?: RemoteAgentsChatResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_chat_response_time = 149;
   */
  remoteAgentsChatResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsInterruptRequest remote_agents_interrupt_request = 150;
   */
  remoteAgentsInterruptRequest?: RemoteAgentsInterruptRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_interrupt_request_time = 151;
   */
  remoteAgentsInterruptRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsInterruptResponse remote_agents_interrupt_response = 152;
   */
  remoteAgentsInterruptResponse?: RemoteAgentsInterruptResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_interrupt_response_time = 153;
   */
  remoteAgentsInterruptResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsDeleteRequest remote_agents_delete_request = 154;
   */
  remoteAgentsDeleteRequest?: RemoteAgentsDeleteRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_delete_request_time = 155;
   */
  remoteAgentsDeleteRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsDeleteResponse remote_agents_delete_response = 156;
   */
  remoteAgentsDeleteResponse?: RemoteAgentsDeleteResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_delete_response_time = 157;
   */
  remoteAgentsDeleteResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsAddSSHKeyRequest remote_agents_add_ssh_key_request = 158;
   */
  remoteAgentsAddSshKeyRequest?: RemoteAgentsAddSSHKeyRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_add_ssh_key_request_time = 159;
   */
  remoteAgentsAddSshKeyRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsAddSSHKeyResponse remote_agents_add_ssh_key_response = 160;
   */
  remoteAgentsAddSshKeyResponse?: RemoteAgentsAddSSHKeyResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_add_ssh_key_response_time = 161;
   */
  remoteAgentsAddSshKeyResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.ChatUserMessage chat_user_message = 162;
   */
  chatUserMessage?: ChatUserMessage;

  /**
   * @generated from field: google.protobuf.Timestamp chat_user_message_time = 163;
   */
  chatUserMessageTime?: Timestamp;

  /**
   * @generated from field: request_insight.ParenthesisTruncation parenthesis_truncation = 164;
   */
  parenthesisTruncation?: ParenthesisTruncation;

  /**
   * @generated from field: google.protobuf.Timestamp parenthesis_truncation_time = 165;
   */
  parenthesisTruncationTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentFeedback remote_agent_feedback = 166;
   */
  remoteAgentFeedback?: RemoteAgentFeedback;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agent_feedback_time = 167;
   */
  remoteAgentFeedbackTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsPauseRequest remote_agents_pause_request = 168;
   */
  remoteAgentsPauseRequest?: RemoteAgentsPauseRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_pause_request_time = 169;
   */
  remoteAgentsPauseRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsPauseResponse remote_agents_pause_response = 170;
   */
  remoteAgentsPauseResponse?: RemoteAgentsPauseResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_pause_response_time = 171;
   */
  remoteAgentsPauseResponseTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsResumeRequest remote_agents_resume_request = 172;
   */
  remoteAgentsResumeRequest?: RemoteAgentsResumeRequest;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_resume_request_time = 173;
   */
  remoteAgentsResumeRequestTime?: Timestamp;

  /**
   * @generated from field: request_insight.RemoteAgentsResumeResponse remote_agents_resume_response = 174;
   */
  remoteAgentsResumeResponse?: RemoteAgentsResumeResponse;

  /**
   * @generated from field: google.protobuf.Timestamp remote_agents_resume_response_time = 175;
   */
  remoteAgentsResumeResponseTime?: Timestamp;

  constructor(data?: PartialMessage<RequestInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RequestInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestInfo;

  static equals(a: RequestInfo | PlainMessage<RequestInfo> | undefined, b: RequestInfo | PlainMessage<RequestInfo> | undefined): boolean;
}

/**
 * @generated from message request_insight.GetRequestInfoRequest
 */
export declare class GetRequestInfoRequest extends Message<GetRequestInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string request_id = 2;
   */
  requestId: string;

  constructor(data?: PartialMessage<GetRequestInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.GetRequestInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRequestInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRequestInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRequestInfoRequest;

  static equals(a: GetRequestInfoRequest | PlainMessage<GetRequestInfoRequest> | undefined, b: GetRequestInfoRequest | PlainMessage<GetRequestInfoRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.GetRequestInfoResponse
 */
export declare class GetRequestInfoResponse extends Message<GetRequestInfoResponse> {
  /**
   * @generated from field: request_insight.RequestInfo info = 1;
   */
  info?: RequestInfo;

  constructor(data?: PartialMessage<GetRequestInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.GetRequestInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetRequestInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetRequestInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetRequestInfoResponse;

  static equals(a: GetRequestInfoResponse | PlainMessage<GetRequestInfoResponse> | undefined, b: GetRequestInfoResponse | PlainMessage<GetRequestInfoResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.FindRequestsRequest
 * @deprecated
 */
export declare class FindRequestsRequest extends Message<FindRequestsRequest> {
  /**
   * @generated from field: google.protobuf.Timestamp start_time = 1;
   */
  startTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_time = 2;
   */
  endTime?: Timestamp;

  /**
   * @generated from field: uint32 limit = 3;
   */
  limit: number;

  constructor(data?: PartialMessage<FindRequestsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FindRequestsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindRequestsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindRequestsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindRequestsRequest;

  static equals(a: FindRequestsRequest | PlainMessage<FindRequestsRequest> | undefined, b: FindRequestsRequest | PlainMessage<FindRequestsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.FindRequestsResponse
 * @deprecated
 */
export declare class FindRequestsResponse extends Message<FindRequestsResponse> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  constructor(data?: PartialMessage<FindRequestsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FindRequestsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindRequestsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindRequestsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindRequestsResponse;

  static equals(a: FindRequestsResponse | PlainMessage<FindRequestsResponse> | undefined, b: FindRequestsResponse | PlainMessage<FindRequestsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.FindSessionRequestsRequest
 */
export declare class FindSessionRequestsRequest extends Message<FindSessionRequestsRequest> {
  /**
   * @generated from field: string session_id = 1;
   */
  sessionId: string;

  /**
   * @generated from field: uint32 limit = 2;
   */
  limit: number;

  constructor(data?: PartialMessage<FindSessionRequestsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FindSessionRequestsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindSessionRequestsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindSessionRequestsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindSessionRequestsRequest;

  static equals(a: FindSessionRequestsRequest | PlainMessage<FindSessionRequestsRequest> | undefined, b: FindSessionRequestsRequest | PlainMessage<FindSessionRequestsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.FindSessionRequestsResponse
 */
export declare class FindSessionRequestsResponse extends Message<FindSessionRequestsResponse> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  constructor(data?: PartialMessage<FindSessionRequestsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FindSessionRequestsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindSessionRequestsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindSessionRequestsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindSessionRequestsResponse;

  static equals(a: FindSessionRequestsResponse | PlainMessage<FindSessionRequestsResponse> | undefined, b: FindSessionRequestsResponse | PlainMessage<FindSessionRequestsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.FindUserRequestsRequest
 */
export declare class FindUserRequestsRequest extends Message<FindUserRequestsRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: uint32 limit = 2;
   */
  limit: number;

  /**
   * @generated from field: request_insight.RequestType request_type = 3;
   */
  requestType: RequestType;

  constructor(data?: PartialMessage<FindUserRequestsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FindUserRequestsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindUserRequestsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindUserRequestsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindUserRequestsRequest;

  static equals(a: FindUserRequestsRequest | PlainMessage<FindUserRequestsRequest> | undefined, b: FindUserRequestsRequest | PlainMessage<FindUserRequestsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.FindUserRequestsResponse
 */
export declare class FindUserRequestsResponse extends Message<FindUserRequestsResponse> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  constructor(data?: PartialMessage<FindUserRequestsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FindUserRequestsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FindUserRequestsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FindUserRequestsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FindUserRequestsResponse;

  static equals(a: FindUserRequestsResponse | PlainMessage<FindUserRequestsResponse> | undefined, b: FindUserRequestsResponse | PlainMessage<FindUserRequestsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RequestMetadata
 */
export declare class RequestMetadata extends Message<RequestMetadata> {
  /**
   * @generated from field: request_insight.RequestType request_type = 1;
   */
  requestType: RequestType;

  /**
   * @generated from field: string session_id = 2;
   */
  sessionId: string;

  /**
   * @generated from field: string user_id = 3;
   */
  userId: string;

  /**
   * @generated from field: string user_agent = 4;
   */
  userAgent: string;

  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 5;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: optional string user_email = 6;
   */
  userEmail?: string;

  /**
   * @generated from field: string source_ip = 7;
   */
  sourceIp: string;

  constructor(data?: PartialMessage<RequestMetadata>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RequestMetadata";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestMetadata;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestMetadata;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestMetadata;

  static equals(a: RequestMetadata | PlainMessage<RequestMetadata> | undefined, b: RequestMetadata | PlainMessage<RequestMetadata> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIEditRequest
 */
export declare class RIEditRequest extends Message<RIEditRequest> {
  /**
   * @generated from field: edit.EditRequest request = 1;
   */
  request?: EditRequest;

  /**
   * @generated from field: repeated request_insight.RetrievalChunk retrieved_chunks = 3;
   */
  retrievedChunks: RetrievalChunk[];

  /**
   * @generated from field: request_insight.Tokenization tokenization = 4;
   */
  tokenization?: Tokenization;

  constructor(data?: PartialMessage<RIEditRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIEditRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIEditRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIEditRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIEditRequest;

  static equals(a: RIEditRequest | PlainMessage<RIEditRequest> | undefined, b: RIEditRequest | PlainMessage<RIEditRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIEditResponse
 */
export declare class RIEditResponse extends Message<RIEditResponse> {
  /**
   * @generated from field: edit.EditResponse response = 1;
   */
  response?: EditResponse;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 3;
   */
  tokenization?: Tokenization;

  constructor(data?: PartialMessage<RIEditResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIEditResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIEditResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIEditResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIEditResponse;

  static equals(a: RIEditResponse | PlainMessage<RIEditResponse> | undefined, b: RIEditResponse | PlainMessage<RIEditResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIChatRequest
 */
export declare class RIChatRequest extends Message<RIChatRequest> {
  /**
   * @generated from field: chat.ChatRequest request = 1;
   */
  request?: ChatRequest;

  /**
   * @generated from field: repeated request_insight.RetrievalChunk retrieved_chunks = 3;
   */
  retrievedChunks: RetrievalChunk[];

  /**
   * @generated from field: request_insight.Tokenization tokenization = 4;
   */
  tokenization?: Tokenization;

  /**
   * @generated from field: repeated string external_source_ids = 5;
   */
  externalSourceIds: string[];

  /**
   * @generated from field: string request_source = 6;
   */
  requestSource: string;

  constructor(data?: PartialMessage<RIChatRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIChatRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIChatRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIChatRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIChatRequest;

  static equals(a: RIChatRequest | PlainMessage<RIChatRequest> | undefined, b: RIChatRequest | PlainMessage<RIChatRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIChatResponse
 */
export declare class RIChatResponse extends Message<RIChatResponse> {
  /**
   * @generated from field: chat.ChatResponse response = 1;
   */
  response?: ChatResponse;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 3;
   */
  tokenization?: Tokenization;

  /**
   * @generated from field: optional string error_message = 4;
   */
  errorMessage?: string;

  constructor(data?: PartialMessage<RIChatResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIChatResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIChatResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIChatResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIChatResponse;

  static equals(a: RIChatResponse | PlainMessage<RIChatResponse> | undefined, b: RIChatResponse | PlainMessage<RIChatResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RINextEditRequest
 */
export declare class RINextEditRequest extends Message<RINextEditRequest> {
  /**
   * @generated from field: next_edit.NextEditRequest request = 1;
   */
  request?: NextEditRequest;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 2 [deprecated = true];
   * @deprecated
   */
  tokenization?: Tokenization;

  /**
   * @generated from field: next_edit.CharRange expanded_range = 3 [deprecated = true];
   * @deprecated
   */
  expandedRange?: CharRange;

  constructor(data?: PartialMessage<RINextEditRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RINextEditRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RINextEditRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RINextEditRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RINextEditRequest;

  static equals(a: RINextEditRequest | PlainMessage<RINextEditRequest> | undefined, b: RINextEditRequest | PlainMessage<RINextEditRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RINextEditSuggestion
 */
export declare class RINextEditSuggestion extends Message<RINextEditSuggestion> {
  /**
   * @generated from field: string generation_id = 1;
   */
  generationId: string;

  /**
   * @generated from field: request_insight.Tokenization description_prompt = 2;
   */
  descriptionPrompt?: Tokenization;

  /**
   * @generated from field: request_insight.Tokenization description_output = 3;
   */
  descriptionOutput?: Tokenization;

  /**
   * @generated from field: next_edit.NextEditResponse result = 4;
   */
  result?: NextEditResponse;

  /**
   * @generated from field: uint32 suggestion_order = 5;
   */
  suggestionOrder: number;

  /**
   * @generated from field: request_insight.RINextEditSuggestion.PostProcessResult post_process_result = 6;
   */
  postProcessResult: RINextEditSuggestion_PostProcessResult;

  constructor(data?: PartialMessage<RINextEditSuggestion>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RINextEditSuggestion";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RINextEditSuggestion;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RINextEditSuggestion;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RINextEditSuggestion;

  static equals(a: RINextEditSuggestion | PlainMessage<RINextEditSuggestion> | undefined, b: RINextEditSuggestion | PlainMessage<RINextEditSuggestion> | undefined): boolean;
}

/**
 * @generated from enum request_insight.RINextEditSuggestion.PostProcessResult
 */
export declare enum RINextEditSuggestion_PostProcessResult {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: NOOP = 1;
   */
  NOOP = 1,

  /**
   * @generated from enum value: TOO_MANY_REMOVED_LINES = 2;
   */
  TOO_MANY_REMOVED_LINES = 2,

  /**
   * @generated from enum value: INSERTED_TODO = 3;
   */
  INSERTED_TODO = 3,

  /**
   * @generated from enum value: BLOCKED_LOCATION = 4;
   */
  BLOCKED_LOCATION = 4,

  /**
   * @generated from enum value: DELETED_IMPORTS = 5;
   */
  DELETED_IMPORTS = 5,
}

/**
 * @generated from message request_insight.RINextEditGeneration
 */
export declare class RINextEditGeneration extends Message<RINextEditGeneration> {
  /**
   * @generated from field: string generation_id = 1;
   */
  generationId: string;

  /**
   * @generated from field: repeated request_insight.RetrievalChunk retrieved_chunks = 2;
   */
  retrievedChunks: RetrievalChunk[];

  /**
   * @generated from field: request_insight.Tokenization generation_prompt = 3;
   */
  generationPrompt?: Tokenization;

  /**
   * @generated from field: request_insight.Tokenization generation_output = 4;
   */
  generationOutput?: Tokenization;

  /**
   * @generated from field: request_insight.RetrievalChunk location_chunk = 5;
   */
  locationChunk?: RetrievalChunk;

  /**
   * @generated from field: request_insight.RINextEditGeneration.PostProcessResult post_process_result = 6;
   */
  postProcessResult: RINextEditGeneration_PostProcessResult;

  /**
   * @generated from field: float editing_score = 7;
   */
  editingScore: number;

  constructor(data?: PartialMessage<RINextEditGeneration>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RINextEditGeneration";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RINextEditGeneration;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RINextEditGeneration;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RINextEditGeneration;

  static equals(a: RINextEditGeneration | PlainMessage<RINextEditGeneration> | undefined, b: RINextEditGeneration | PlainMessage<RINextEditGeneration> | undefined): boolean;
}

/**
 * @generated from enum request_insight.RINextEditGeneration.PostProcessResult
 */
export declare enum RINextEditGeneration_PostProcessResult {
  /**
   * @generated from enum value: NOOP = 0;
   */
  NOOP = 0,

  /**
   * @generated from enum value: LOW_PROB_CHANGED = 1;
   */
  LOW_PROB_CHANGED = 1,

  /**
   * @generated from enum value: UNDO_RECENT_CHANGES = 2;
   */
  UNDO_RECENT_CHANGES = 2,
}

/**
 * @generated from message request_insight.RINextEditResponse
 */
export declare class RINextEditResponse extends Message<RINextEditResponse> {
  /**
   * @generated from field: next_edit.NextEditResponse response = 1 [deprecated = true];
   * @deprecated
   */
  response?: NextEditResponse;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 2 [deprecated = true];
   * @deprecated
   */
  tokenization?: Tokenization;

  /**
   * @generated from field: repeated request_insight.RetrievalChunk retrieved_locations = 3;
   */
  retrievedLocations: RetrievalChunk[];

  /**
   * @generated from field: repeated request_insight.RINextEditGeneration generation = 4;
   */
  generation: RINextEditGeneration[];

  /**
   * @generated from field: repeated request_insight.RINextEditSuggestion suggestions = 5;
   */
  suggestions: RINextEditSuggestion[];

  /**
   * @generated from field: repeated request_insight.RetrievalChunk blocked_locations = 6;
   */
  blockedLocations: RetrievalChunk[];

  constructor(data?: PartialMessage<RINextEditResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RINextEditResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RINextEditResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RINextEditResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RINextEditResponse;

  static equals(a: RINextEditResponse | PlainMessage<RINextEditResponse> | undefined, b: RINextEditResponse | PlainMessage<RINextEditResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIInstructionRequest
 */
export declare class RIInstructionRequest extends Message<RIInstructionRequest> {
  /**
   * @generated from field: edit.InstructionRequest request = 1;
   */
  request?: InstructionRequest;

  /**
   * @generated from field: repeated request_insight.RetrievalChunk retrieved_chunks = 2;
   */
  retrievedChunks: RetrievalChunk[];

  /**
   * @generated from field: request_insight.Tokenization tokenization = 3;
   */
  tokenization?: Tokenization;

  constructor(data?: PartialMessage<RIInstructionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIInstructionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIInstructionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIInstructionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIInstructionRequest;

  static equals(a: RIInstructionRequest | PlainMessage<RIInstructionRequest> | undefined, b: RIInstructionRequest | PlainMessage<RIInstructionRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIInstructionResponse
 */
export declare class RIInstructionResponse extends Message<RIInstructionResponse> {
  /**
   * @generated from field: edit.InstructionAggregateResponse response = 1;
   */
  response?: InstructionAggregateResponse;

  /**
   * @generated from field: request_insight.Tokenization tokenization = 2;
   */
  tokenization?: Tokenization;

  /**
   * @generated from field: optional string error_message = 3;
   */
  errorMessage?: string;

  constructor(data?: PartialMessage<RIInstructionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIInstructionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIInstructionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIInstructionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIInstructionResponse;

  static equals(a: RIInstructionResponse | PlainMessage<RIInstructionResponse> | undefined, b: RIInstructionResponse | PlainMessage<RIInstructionResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.ExtensionError
 */
export declare class ExtensionError extends Message<ExtensionError> {
  /**
   * @generated from field: optional string original_request_id = 1;
   */
  originalRequestId?: string;

  /**
   * @generated from field: string message = 2;
   */
  message: string;

  /**
   * @generated from field: string stack_trace = 3;
   */
  stackTrace: string;

  /**
   * @generated from field: map<string, string> diagnostics = 4;
   */
  diagnostics: { [key: string]: string };

  constructor(data?: PartialMessage<ExtensionError>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ExtensionError";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExtensionError;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExtensionError;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExtensionError;

  static equals(a: ExtensionError | PlainMessage<ExtensionError> | undefined, b: ExtensionError | PlainMessage<ExtensionError> | undefined): boolean;
}

/**
 * @generated from message request_insight.RISlackbotRequest
 */
export declare class RISlackbotRequest extends Message<RISlackbotRequest> {
  /**
   * @generated from field: slackbot.SlackEvent slack_event = 1;
   */
  slackEvent?: SlackEvent;

  /**
   * @generated from field: request_insight.RISlackbotRequest.ChannelType channel_type = 2;
   */
  channelType: RISlackbotRequest_ChannelType;

  constructor(data?: PartialMessage<RISlackbotRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RISlackbotRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RISlackbotRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RISlackbotRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RISlackbotRequest;

  static equals(a: RISlackbotRequest | PlainMessage<RISlackbotRequest> | undefined, b: RISlackbotRequest | PlainMessage<RISlackbotRequest> | undefined): boolean;
}

/**
 * @generated from enum request_insight.RISlackbotRequest.ChannelType
 */
export declare enum RISlackbotRequest_ChannelType {
  /**
   * @generated from enum value: UNKNOWN_CHANNEL_TYPE = 0;
   */
  UNKNOWN_CHANNEL_TYPE = 0,

  /**
   * @generated from enum value: IM = 1;
   */
  IM = 1,

  /**
   * @generated from enum value: MPIM = 2;
   */
  MPIM = 2,

  /**
   * @generated from enum value: SHARED_CHANNEL = 3;
   */
  SHARED_CHANNEL = 3,

  /**
   * @generated from enum value: PRIVATE_CHANNEL = 4;
   */
  PRIVATE_CHANNEL = 4,

  /**
   * @generated from enum value: GENERAL_CHANNEL = 5;
   */
  GENERAL_CHANNEL = 5,

  /**
   * @generated from enum value: CHANNEL = 6;
   */
  CHANNEL = 6,
}

/**
 * @generated from message request_insight.RISlackbotResponse
 */
export declare class RISlackbotResponse extends Message<RISlackbotResponse> {
  /**
   * @generated from field: string channel = 1;
   */
  channel: string;

  /**
   * @generated from field: string thread_timestamp = 2;
   */
  threadTimestamp: string;

  /**
   * @generated from field: string request_message_timestamp = 3;
   */
  requestMessageTimestamp: string;

  /**
   * @generated from field: repeated request_insight.RISlackbotResponse.Event response_events = 4;
   */
  responseEvents: RISlackbotResponse_Event[];

  constructor(data?: PartialMessage<RISlackbotResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RISlackbotResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RISlackbotResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RISlackbotResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RISlackbotResponse;

  static equals(a: RISlackbotResponse | PlainMessage<RISlackbotResponse> | undefined, b: RISlackbotResponse | PlainMessage<RISlackbotResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RISlackbotResponse.Event
 */
export declare class RISlackbotResponse_Event extends Message<RISlackbotResponse_Event> {
  /**
   * @generated from field: google.protobuf.Timestamp time = 1;
   */
  time?: Timestamp;

  /**
   * @generated from oneof request_insight.RISlackbotResponse.Event.event
   */
  event: {
    /**
     * @generated from field: request_insight.RISlackbotResponse.PostMessage post_message = 2;
     */
    value: RISlackbotResponse_PostMessage;
    case: "postMessage";
  } | {
    /**
     * @generated from field: request_insight.RISlackbotResponse.UpdateMessage update_message = 3;
     */
    value: RISlackbotResponse_UpdateMessage;
    case: "updateMessage";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<RISlackbotResponse_Event>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RISlackbotResponse.Event";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RISlackbotResponse_Event;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RISlackbotResponse_Event;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RISlackbotResponse_Event;

  static equals(a: RISlackbotResponse_Event | PlainMessage<RISlackbotResponse_Event> | undefined, b: RISlackbotResponse_Event | PlainMessage<RISlackbotResponse_Event> | undefined): boolean;
}

/**
 * @generated from message request_insight.RISlackbotResponse.PostMessage
 */
export declare class RISlackbotResponse_PostMessage extends Message<RISlackbotResponse_PostMessage> {
  /**
   * @generated from field: string response_message_timestamp = 1;
   */
  responseMessageTimestamp: string;

  /**
   * @generated from field: string text = 2;
   */
  text: string;

  constructor(data?: PartialMessage<RISlackbotResponse_PostMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RISlackbotResponse.PostMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RISlackbotResponse_PostMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RISlackbotResponse_PostMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RISlackbotResponse_PostMessage;

  static equals(a: RISlackbotResponse_PostMessage | PlainMessage<RISlackbotResponse_PostMessage> | undefined, b: RISlackbotResponse_PostMessage | PlainMessage<RISlackbotResponse_PostMessage> | undefined): boolean;
}

/**
 * @generated from message request_insight.RISlackbotResponse.UpdateMessage
 */
export declare class RISlackbotResponse_UpdateMessage extends Message<RISlackbotResponse_UpdateMessage> {
  /**
   * @generated from field: string response_message_timestamp = 1;
   */
  responseMessageTimestamp: string;

  /**
   * @generated from field: string text = 2;
   */
  text: string;

  constructor(data?: PartialMessage<RISlackbotResponse_UpdateMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RISlackbotResponse.UpdateMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RISlackbotResponse_UpdateMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RISlackbotResponse_UpdateMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RISlackbotResponse_UpdateMessage;

  static equals(a: RISlackbotResponse_UpdateMessage | PlainMessage<RISlackbotResponse_UpdateMessage> | undefined, b: RISlackbotResponse_UpdateMessage | PlainMessage<RISlackbotResponse_UpdateMessage> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIShareSaveChatRequest
 */
export declare class RIShareSaveChatRequest extends Message<RIShareSaveChatRequest> {
  /**
   * @generated from field: share.SaveChatConversationRequest request = 1;
   */
  request?: SaveChatConversationRequest;

  constructor(data?: PartialMessage<RIShareSaveChatRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIShareSaveChatRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIShareSaveChatRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIShareSaveChatRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIShareSaveChatRequest;

  static equals(a: RIShareSaveChatRequest | PlainMessage<RIShareSaveChatRequest> | undefined, b: RIShareSaveChatRequest | PlainMessage<RIShareSaveChatRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIShareSaveChatResponse
 */
export declare class RIShareSaveChatResponse extends Message<RIShareSaveChatResponse> {
  /**
   * @generated from field: share.SaveChatConversationResponse response = 1;
   */
  response?: SaveChatConversationResponse;

  constructor(data?: PartialMessage<RIShareSaveChatResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIShareSaveChatResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIShareSaveChatResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIShareSaveChatResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIShareSaveChatResponse;

  static equals(a: RIShareSaveChatResponse | PlainMessage<RIShareSaveChatResponse> | undefined, b: RIShareSaveChatResponse | PlainMessage<RIShareSaveChatResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIShareGetChatRequest
 */
export declare class RIShareGetChatRequest extends Message<RIShareGetChatRequest> {
  /**
   * @generated from field: share.GetChatConversationRequest request = 1;
   */
  request?: GetChatConversationRequest;

  constructor(data?: PartialMessage<RIShareGetChatRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIShareGetChatRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIShareGetChatRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIShareGetChatRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIShareGetChatRequest;

  static equals(a: RIShareGetChatRequest | PlainMessage<RIShareGetChatRequest> | undefined, b: RIShareGetChatRequest | PlainMessage<RIShareGetChatRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIShareGetChatResponse
 */
export declare class RIShareGetChatResponse extends Message<RIShareGetChatResponse> {
  /**
   * @generated from field: share.GetChatConversationResponse response = 1;
   */
  response?: GetChatConversationResponse;

  constructor(data?: PartialMessage<RIShareGetChatResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIShareGetChatResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIShareGetChatResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIShareGetChatResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIShareGetChatResponse;

  static equals(a: RIShareGetChatResponse | PlainMessage<RIShareGetChatResponse> | undefined, b: RIShareGetChatResponse | PlainMessage<RIShareGetChatResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RecordFullExportUserEventsRequest
 */
export declare class RecordFullExportUserEventsRequest extends Message<RecordFullExportUserEventsRequest> {
  /**
   * @generated from field: request_insight.ExtensionData extension_data = 1;
   */
  extensionData?: ExtensionData;

  /**
   * @generated from field: optional string user_id = 2;
   */
  userId?: string;

  /**
   * @generated from field: optional string session_id = 3;
   */
  sessionId?: string;

  /**
   * @generated from field: optional request_insight.TenantInfo tenant_info = 4;
   */
  tenantInfo?: TenantInfo;

  constructor(data?: PartialMessage<RecordFullExportUserEventsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RecordFullExportUserEventsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecordFullExportUserEventsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecordFullExportUserEventsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecordFullExportUserEventsRequest;

  static equals(a: RecordFullExportUserEventsRequest | PlainMessage<RecordFullExportUserEventsRequest> | undefined, b: RecordFullExportUserEventsRequest | PlainMessage<RecordFullExportUserEventsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RecordFullExportUserEventsResponse
 */
export declare class RecordFullExportUserEventsResponse extends Message<RecordFullExportUserEventsResponse> {
  constructor(data?: PartialMessage<RecordFullExportUserEventsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RecordFullExportUserEventsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecordFullExportUserEventsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecordFullExportUserEventsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecordFullExportUserEventsResponse;

  static equals(a: RecordFullExportUserEventsResponse | PlainMessage<RecordFullExportUserEventsResponse> | undefined, b: RecordFullExportUserEventsResponse | PlainMessage<RecordFullExportUserEventsResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.ExtensionData
 */
export declare class ExtensionData extends Message<ExtensionData> {
  /**
   * @generated from field: repeated request_insight.FullExportUserEvent user_events = 1;
   */
  userEvents: FullExportUserEvent[];

  constructor(data?: PartialMessage<ExtensionData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ExtensionData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExtensionData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExtensionData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExtensionData;

  static equals(a: ExtensionData | PlainMessage<ExtensionData> | undefined, b: ExtensionData | PlainMessage<ExtensionData> | undefined): boolean;
}

/**
 * @generated from message request_insight.FullExportUserEvent
 */
export declare class FullExportUserEvent extends Message<FullExportUserEvent> {
  /**
   * @generated from field: google.protobuf.Timestamp time = 1;
   */
  time?: Timestamp;

  /**
   * @generated from field: optional string file_path = 2;
   */
  filePath?: string;

  /**
   * @generated from oneof request_insight.FullExportUserEvent.event
   */
  event: {
    /**
     * @generated from field: request_insight.TextEditEvent text_edit = 3;
     */
    value: TextEditEvent;
    case: "textEdit";
  } | {
    /**
     * @generated from field: request_insight.CompletionRequestIdIssuedEvent completion_request_id_issued = 4;
     */
    value: CompletionRequestIdIssuedEvent;
    case: "completionRequestIdIssued";
  } | {
    /**
     * @generated from field: request_insight.EditRequestIdIssuedEvent edit_request_id_issued = 5;
     */
    value: EditRequestIdIssuedEvent;
    case: "editRequestIdIssued";
  } | {
    /**
     * @generated from field: request_insight.NextEditRequestIdIssuedEvent next_edit_request_id_issued = 6;
     */
    value: NextEditRequestIdIssuedEvent;
    case: "nextEditRequestIdIssued";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<FullExportUserEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FullExportUserEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FullExportUserEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FullExportUserEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FullExportUserEvent;

  static equals(a: FullExportUserEvent | PlainMessage<FullExportUserEvent> | undefined, b: FullExportUserEvent | PlainMessage<FullExportUserEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.TextEditEvent
 */
export declare class TextEditEvent extends Message<TextEditEvent> {
  /**
   * @generated from field: optional request_insight.TextEditEvent.Reason reason = 1;
   */
  reason?: TextEditEvent_Reason;

  /**
   * @generated from field: repeated request_insight.ContentChange content_changes = 2;
   */
  contentChanges: ContentChange[];

  /**
   * @generated from field: optional string file_path = 3;
   */
  filePath?: string;

  /**
   * @generated from field: optional string after_changes_hash = 4;
   */
  afterChangesHash?: string;

  /**
   * @generated from field: repeated request_insight.Range hash_char_ranges = 5;
   */
  hashCharRanges: Range[];

  /**
   * @generated from field: optional string source_folder_root = 6;
   */
  sourceFolderRoot?: string;

  /**
   * @generated from field: optional uint32 after_doc_length = 7;
   */
  afterDocLength?: number;

  constructor(data?: PartialMessage<TextEditEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.TextEditEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TextEditEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TextEditEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TextEditEvent;

  static equals(a: TextEditEvent | PlainMessage<TextEditEvent> | undefined, b: TextEditEvent | PlainMessage<TextEditEvent> | undefined): boolean;
}

/**
 * @generated from enum request_insight.TextEditEvent.Reason
 */
export declare enum TextEditEvent_Reason {
  /**
   * @generated from enum value: UNKNOWN_REASON = 0;
   */
  UNKNOWN_REASON = 0,

  /**
   * @generated from enum value: UNDO = 1;
   */
  UNDO = 1,

  /**
   * @generated from enum value: REDO = 2;
   */
  REDO = 2,
}

/**
 * @generated from message request_insight.CompletionRequestIdIssuedEvent
 */
export declare class CompletionRequestIdIssuedEvent extends Message<CompletionRequestIdIssuedEvent> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: optional string file_path = 2;
   */
  filePath?: string;

  constructor(data?: PartialMessage<CompletionRequestIdIssuedEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CompletionRequestIdIssuedEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CompletionRequestIdIssuedEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CompletionRequestIdIssuedEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CompletionRequestIdIssuedEvent;

  static equals(a: CompletionRequestIdIssuedEvent | PlainMessage<CompletionRequestIdIssuedEvent> | undefined, b: CompletionRequestIdIssuedEvent | PlainMessage<CompletionRequestIdIssuedEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.EditRequestIdIssuedEvent
 */
export declare class EditRequestIdIssuedEvent extends Message<EditRequestIdIssuedEvent> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: optional string file_path = 2;
   */
  filePath?: string;

  constructor(data?: PartialMessage<EditRequestIdIssuedEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.EditRequestIdIssuedEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EditRequestIdIssuedEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EditRequestIdIssuedEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EditRequestIdIssuedEvent;

  static equals(a: EditRequestIdIssuedEvent | PlainMessage<EditRequestIdIssuedEvent> | undefined, b: EditRequestIdIssuedEvent | PlainMessage<EditRequestIdIssuedEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.NextEditRequestIdIssuedEvent
 */
export declare class NextEditRequestIdIssuedEvent extends Message<NextEditRequestIdIssuedEvent> {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: optional string file_path = 2;
   */
  filePath?: string;

  constructor(data?: PartialMessage<NextEditRequestIdIssuedEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.NextEditRequestIdIssuedEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NextEditRequestIdIssuedEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NextEditRequestIdIssuedEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NextEditRequestIdIssuedEvent;

  static equals(a: NextEditRequestIdIssuedEvent | PlainMessage<NextEditRequestIdIssuedEvent> | undefined, b: NextEditRequestIdIssuedEvent | PlainMessage<NextEditRequestIdIssuedEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.ContentChange
 */
export declare class ContentChange extends Message<ContentChange> {
  /**
   * @generated from field: string text = 1;
   */
  text: string;

  /**
   * @generated from field: request_insight.Range range = 2;
   */
  range?: Range;

  constructor(data?: PartialMessage<ContentChange>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ContentChange";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ContentChange;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ContentChange;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ContentChange;

  static equals(a: ContentChange | PlainMessage<ContentChange> | undefined, b: ContentChange | PlainMessage<ContentChange> | undefined): boolean;
}

/**
 * @generated from message request_insight.Range
 */
export declare class Range extends Message<Range> {
  /**
   * @generated from field: int32 start = 1;
   */
  start: number;

  /**
   * @generated from field: int32 end = 2;
   */
  end: number;

  constructor(data?: PartialMessage<Range>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.Range";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Range;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Range;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Range;

  static equals(a: Range | PlainMessage<Range> | undefined, b: Range | PlainMessage<Range> | undefined): boolean;
}

/**
 * @generated from message request_insight.RecordTenantEventsRequest
 */
export declare class RecordTenantEventsRequest extends Message<RecordTenantEventsRequest> {
  /**
   * @generated from field: request_insight.TenantInfo tenant_info = 2;
   */
  tenantInfo?: TenantInfo;

  /**
   * @generated from field: repeated request_insight.TenantEvent events = 3;
   */
  events: TenantEvent[];

  constructor(data?: PartialMessage<RecordTenantEventsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RecordTenantEventsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecordTenantEventsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecordTenantEventsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecordTenantEventsRequest;

  static equals(a: RecordTenantEventsRequest | PlainMessage<RecordTenantEventsRequest> | undefined, b: RecordTenantEventsRequest | PlainMessage<RecordTenantEventsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.TenantEvent
 */
export declare class TenantEvent extends Message<TenantEvent> {
  /**
   * @generated from field: google.protobuf.Timestamp time = 1;
   */
  time?: Timestamp;

  /**
   * @generated from field: string event_id = 2;
   */
  eventId: string;

  /**
   * @generated from oneof request_insight.TenantEvent.event
   */
  event: {
    /**
     * @generated from field: request_insight.AddUserToTenant add_user_to_tenant = 3;
     */
    value: AddUserToTenant;
    case: "addUserToTenant";
  } | {
    /**
     * @generated from field: request_insight.RemoveUserFromTenant remove_user_from_tenant = 4;
     */
    value: RemoveUserFromTenant;
    case: "removeUserFromTenant";
  } | {
    /**
     * @generated from field: request_insight.InviteUserToTenant invite_user_to_tenant = 5;
     */
    value: InviteUserToTenant;
    case: "inviteUserToTenant";
  } | {
    /**
     * @generated from field: request_insight.DeleteInvitation delete_invitation = 6;
     */
    value: DeleteInvitation;
    case: "deleteInvitation";
  } | {
    /**
     * @generated from field: request_insight.UpdateSubscription update_subscription = 7;
     */
    value: UpdateSubscription;
    case: "updateSubscription";
  } | {
    /**
     * @generated from field: request_insight.CreateTenantForTeam create_tenant_for_team = 8;
     */
    value: CreateTenantForTeam;
    case: "createTenantForTeam";
  } | {
    /**
     * @generated from field: request_insight.AcceptInvitation accept_invitation = 9;
     */
    value: AcceptInvitation;
    case: "acceptInvitation";
  } | {
    /**
     * @generated from field: request_insight.DeclineInvitation decline_invitation = 10;
     */
    value: DeclineInvitation;
    case: "declineInvitation";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<TenantEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.TenantEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TenantEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TenantEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TenantEvent;

  static equals(a: TenantEvent | PlainMessage<TenantEvent> | undefined, b: TenantEvent | PlainMessage<TenantEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.AddUserToTenant
 */
export declare class AddUserToTenant extends Message<AddUserToTenant> {
  /**
   * @generated from field: auth_entities.User user = 1;
   */
  user?: User;

  constructor(data?: PartialMessage<AddUserToTenant>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AddUserToTenant";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddUserToTenant;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddUserToTenant;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddUserToTenant;

  static equals(a: AddUserToTenant | PlainMessage<AddUserToTenant> | undefined, b: AddUserToTenant | PlainMessage<AddUserToTenant> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoveUserFromTenant
 */
export declare class RemoveUserFromTenant extends Message<RemoveUserFromTenant> {
  /**
   * @generated from field: auth_entities.User user = 1;
   */
  user?: User;

  constructor(data?: PartialMessage<RemoveUserFromTenant>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoveUserFromTenant";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveUserFromTenant;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveUserFromTenant;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveUserFromTenant;

  static equals(a: RemoveUserFromTenant | PlainMessage<RemoveUserFromTenant> | undefined, b: RemoveUserFromTenant | PlainMessage<RemoveUserFromTenant> | undefined): boolean;
}

/**
 * @generated from message request_insight.RecordGenericEventsRequest
 */
export declare class RecordGenericEventsRequest extends Message<RecordGenericEventsRequest> {
  /**
   * @generated from field: string session_id = 1;
   */
  sessionId: string;

  /**
   * @generated from field: repeated request_insight.GenericEvent events = 2;
   */
  events: GenericEvent[];

  constructor(data?: PartialMessage<RecordGenericEventsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RecordGenericEventsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RecordGenericEventsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RecordGenericEventsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RecordGenericEventsRequest;

  static equals(a: RecordGenericEventsRequest | PlainMessage<RecordGenericEventsRequest> | undefined, b: RecordGenericEventsRequest | PlainMessage<RecordGenericEventsRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.GenericEvent
 */
export declare class GenericEvent extends Message<GenericEvent> {
  /**
   * @generated from field: google.protobuf.Timestamp time = 1;
   */
  time?: Timestamp;

  /**
   * @generated from field: optional string event_id = 2;
   */
  eventId?: string;

  /**
   * @generated from oneof request_insight.GenericEvent.event
   */
  event: {
    /**
     * @generated from field: request_insight.Recaptcha recaptcha = 3;
     */
    value: Recaptcha;
    case: "recaptcha";
  } | {
    /**
     * @generated from field: request_insight.Verisoul verisoul = 4;
     */
    value: Verisoul;
    case: "verisoul";
  } | {
    /**
     * @generated from field: request_insight.Verosint verosint = 5;
     */
    value: Verosint;
    case: "verosint";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<GenericEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.GenericEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GenericEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GenericEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GenericEvent;

  static equals(a: GenericEvent | PlainMessage<GenericEvent> | undefined, b: GenericEvent | PlainMessage<GenericEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.Recaptcha
 */
export declare class Recaptcha extends Message<Recaptcha> {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string assessment_name = 2;
   */
  assessmentName: string;

  /**
   * @generated from field: repeated string assessment_reasons = 3;
   */
  assessmentReasons: string[];

  /**
   * @generated from field: float score = 4;
   */
  score: number;

  /**
   * @generated from field: string action = 5;
   */
  action: string;

  /**
   * @generated from field: string user_agent = 6;
   */
  userAgent: string;

  /**
   * @generated from field: string source_ip = 7;
   */
  sourceIp: string;

  constructor(data?: PartialMessage<Recaptcha>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.Recaptcha";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Recaptcha;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Recaptcha;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Recaptcha;

  static equals(a: Recaptcha | PlainMessage<Recaptcha> | undefined, b: Recaptcha | PlainMessage<Recaptcha> | undefined): boolean;
}

/**
 * @generated from message request_insight.Verisoul
 */
export declare class Verisoul extends Message<Verisoul> {
  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 1;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: string report = 2;
   */
  report: string;

  constructor(data?: PartialMessage<Verisoul>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.Verisoul";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Verisoul;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Verisoul;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Verisoul;

  static equals(a: Verisoul | PlainMessage<Verisoul> | undefined, b: Verisoul | PlainMessage<Verisoul> | undefined): boolean;
}

/**
 * @generated from message request_insight.Verosint
 */
export declare class Verosint extends Message<Verosint> {
  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 1;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: string report = 2;
   */
  report: string;

  constructor(data?: PartialMessage<Verosint>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.Verosint";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Verosint;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Verosint;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Verosint;

  static equals(a: Verosint | PlainMessage<Verosint> | undefined, b: Verosint | PlainMessage<Verosint> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentLogEntry
 */
export declare class RemoteAgentLogEntry extends Message<RemoteAgentLogEntry> {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * @generated from field: string transport = 2;
   */
  transport: string;

  /**
   * @generated from field: google.protobuf.Timestamp timestamp = 3;
   */
  timestamp?: Timestamp;

  constructor(data?: PartialMessage<RemoteAgentLogEntry>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentLogEntry";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentLogEntry;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentLogEntry;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentLogEntry;

  static equals(a: RemoteAgentLogEntry | PlainMessage<RemoteAgentLogEntry> | undefined, b: RemoteAgentLogEntry | PlainMessage<RemoteAgentLogEntry> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentLog
 */
export declare class RemoteAgentLog extends Message<RemoteAgentLog> {
  /**
   * @generated from field: repeated request_insight.RemoteAgentLogEntry entries = 1;
   */
  entries: RemoteAgentLogEntry[];

  /**
   * @generated from field: string remote_agent_id = 2;
   */
  remoteAgentId: string;

  /**
   * @generated from field: string component = 3;
   */
  component: string;

  /**
   * @generated from field: google.protobuf.Timestamp start_timestamp = 4;
   */
  startTimestamp?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp end_timestamp = 5;
   */
  endTimestamp?: Timestamp;

  constructor(data?: PartialMessage<RemoteAgentLog>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentLog";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentLog;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentLog;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentLog;

  static equals(a: RemoteAgentLog | PlainMessage<RemoteAgentLog> | undefined, b: RemoteAgentLog | PlainMessage<RemoteAgentLog> | undefined): boolean;
}

/**
 * @generated from message request_insight.FeatureVectorReport
 */
export declare class FeatureVectorReport extends Message<FeatureVectorReport> {
  /**
   * @generated from field: map<int32, string> feature_vector = 1;
   */
  featureVector: { [key: number]: string };

  /**
   * @generated from field: string source_ip = 2;
   */
  sourceIp: string;

  /**
   * @generated from field: string user_agent = 3;
   */
  userAgent: string;

  /**
   * @generated from field: map<string, string> headers = 4;
   */
  headers: { [key: string]: string };

  constructor(data?: PartialMessage<FeatureVectorReport>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.FeatureVectorReport";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FeatureVectorReport;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FeatureVectorReport;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FeatureVectorReport;

  static equals(a: FeatureVectorReport | PlainMessage<FeatureVectorReport> | undefined, b: FeatureVectorReport | PlainMessage<FeatureVectorReport> | undefined): boolean;
}

/**
 * @generated from message request_insight.RequestInsightMessage
 */
export declare class RequestInsightMessage extends Message<RequestInsightMessage> {
  /**
   * @generated from oneof request_insight.RequestInsightMessage.message
   */
  message: {
    /**
     * @generated from field: request_insight.UpdateRequestInfoRequest update_request_info_request = 1;
     */
    value: UpdateRequestInfoRequest;
    case: "updateRequestInfoRequest";
  } | {
    /**
     * @generated from field: request_insight.RecordFullExportUserEventsRequest record_full_export_user_events_request = 2;
     */
    value: RecordFullExportUserEventsRequest;
    case: "recordFullExportUserEventsRequest";
  } | {
    /**
     * @generated from field: request_insight.RecordSessionEventsRequest record_session_events_request = 3;
     */
    value: RecordSessionEventsRequest;
    case: "recordSessionEventsRequest";
  } | {
    /**
     * @generated from field: request_insight.RecordTenantEventsRequest record_tenant_events_request = 4;
     */
    value: RecordTenantEventsRequest;
    case: "recordTenantEventsRequest";
  } | {
    /**
     * @generated from field: request_insight.RecordGenericEventsRequest record_generic_events_request = 5;
     */
    value: RecordGenericEventsRequest;
    case: "recordGenericEventsRequest";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<RequestInsightMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RequestInsightMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestInsightMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestInsightMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestInsightMessage;

  static equals(a: RequestInsightMessage | PlainMessage<RequestInsightMessage> | undefined, b: RequestInsightMessage | PlainMessage<RequestInsightMessage> | undefined): boolean;
}

/**
 * @generated from message request_insight.ClientCompletionTimeline
 */
export declare class ClientCompletionTimeline extends Message<ClientCompletionTimeline> {
  /**
   * @generated from field: google.protobuf.Timestamp initial_request_time = 2;
   */
  initialRequestTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp api_start_time = 3;
   */
  apiStartTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp api_end_time = 4;
   */
  apiEndTime?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp emit_time = 5;
   */
  emitTime?: Timestamp;

  constructor(data?: PartialMessage<ClientCompletionTimeline>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ClientCompletionTimeline";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClientCompletionTimeline;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClientCompletionTimeline;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClientCompletionTimeline;

  static equals(a: ClientCompletionTimeline | PlainMessage<ClientCompletionTimeline> | undefined, b: ClientCompletionTimeline | PlainMessage<ClientCompletionTimeline> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGithubEvent
 */
export declare class RIGithubEvent extends Message<RIGithubEvent> {
  /**
   * @generated from field: github_event.GithubEvent github_event = 1;
   */
  githubEvent?: GithubEvent;

  constructor(data?: PartialMessage<RIGithubEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGithubEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGithubEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGithubEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGithubEvent;

  static equals(a: RIGithubEvent | PlainMessage<RIGithubEvent> | undefined, b: RIGithubEvent | PlainMessage<RIGithubEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGithubProcessingResult
 */
export declare class RIGithubProcessingResult extends Message<RIGithubProcessingResult> {
  /**
   * @generated from field: google.rpc.Status status = 1;
   */
  status?: Status;

  /**
   * @generated from field: string last_updated_commit = 2;
   */
  lastUpdatedCommit: string;

  /**
   * @generated from field: repeated request_insight.RIGithubProcessingResult.DiffInfo diff_infos = 3;
   */
  diffInfos: RIGithubProcessingResult_DiffInfo[];

  /**
   * @generated from field: repeated string filenames_to_download = 4;
   */
  filenamesToDownload: string[];

  /**
   * @generated from field: repeated request_insight.RIGithubProcessingResult.DiffInfo ignore_diff_infos = 7;
   */
  ignoreDiffInfos: RIGithubProcessingResult_DiffInfo[];

  /**
   * @generated from field: repeated string ignore_filenames_to_download = 8;
   */
  ignoreFilenamesToDownload: string[];

  /**
   * @generated from field: base.blob_names.Blobs checkpoint_blobs = 5;
   */
  checkpointBlobs?: Blobs;

  /**
   * @generated from field: string git_apply_error = 6;
   */
  gitApplyError: string;

  /**
   * @generated from field: string file_download_error = 9;
   */
  fileDownloadError: string;

  /**
   * @generated from field: string diff_blob_name = 10;
   */
  diffBlobName: string;

  /**
   * @generated from field: repeated string ignored_files = 11;
   */
  ignoredFiles: string[];

  constructor(data?: PartialMessage<RIGithubProcessingResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGithubProcessingResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGithubProcessingResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGithubProcessingResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGithubProcessingResult;

  static equals(a: RIGithubProcessingResult | PlainMessage<RIGithubProcessingResult> | undefined, b: RIGithubProcessingResult | PlainMessage<RIGithubProcessingResult> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGithubProcessingResult.DiffInfo
 */
export declare class RIGithubProcessingResult_DiffInfo extends Message<RIGithubProcessingResult_DiffInfo> {
  /**
   * @generated from field: string filename = 1;
   */
  filename: string;

  /**
   * @generated from field: string old_blobname = 2;
   */
  oldBlobname: string;

  /**
   * @generated from field: string new_blobname = 3;
   */
  newBlobname: string;

  /**
   * @generated from field: string old_filename = 4;
   */
  oldFilename: string;

  /**
   * @generated from field: bool removed = 5;
   */
  removed: boolean;

  /**
   * @generated from field: bool renamed = 6;
   */
  renamed: boolean;

  constructor(data?: PartialMessage<RIGithubProcessingResult_DiffInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGithubProcessingResult.DiffInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGithubProcessingResult_DiffInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGithubProcessingResult_DiffInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGithubProcessingResult_DiffInfo;

  static equals(a: RIGithubProcessingResult_DiffInfo | PlainMessage<RIGithubProcessingResult_DiffInfo> | undefined, b: RIGithubProcessingResult_DiffInfo | PlainMessage<RIGithubProcessingResult_DiffInfo> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGithubAppInstallationEvent
 */
export declare class RIGithubAppInstallationEvent extends Message<RIGithubAppInstallationEvent> {
  /**
   * @generated from field: google.rpc.Status status = 1;
   */
  status?: Status;

  /**
   * @generated from field: request_insight.InstallEventType event_type = 2;
   */
  eventType: InstallEventType;

  constructor(data?: PartialMessage<RIGithubAppInstallationEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGithubAppInstallationEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGithubAppInstallationEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGithubAppInstallationEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGithubAppInstallationEvent;

  static equals(a: RIGithubAppInstallationEvent | PlainMessage<RIGithubAppInstallationEvent> | undefined, b: RIGithubAppInstallationEvent | PlainMessage<RIGithubAppInstallationEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.RISlackbotInstallationEvent
 */
export declare class RISlackbotInstallationEvent extends Message<RISlackbotInstallationEvent> {
  /**
   * @generated from field: google.rpc.Status status = 1;
   */
  status?: Status;

  /**
   * @generated from field: request_insight.InstallEventType event_type = 2;
   */
  eventType: InstallEventType;

  constructor(data?: PartialMessage<RISlackbotInstallationEvent>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RISlackbotInstallationEvent";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RISlackbotInstallationEvent;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RISlackbotInstallationEvent;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RISlackbotInstallationEvent;

  static equals(a: RISlackbotInstallationEvent | PlainMessage<RISlackbotInstallationEvent> | undefined, b: RISlackbotInstallationEvent | PlainMessage<RISlackbotInstallationEvent> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGleanRequest
 */
export declare class RIGleanRequest extends Message<RIGleanRequest> {
  /**
   * @generated from field: glean.SearchRequest request = 1;
   */
  request?: SearchRequest;

  /**
   * @generated from field: string request_source = 2;
   */
  requestSource: string;

  /**
   * @generated from field: request_insight.RIGleanRequest.RIGleanQueryProcessorConfig query_processor_config = 3;
   */
  queryProcessorConfig?: RIGleanRequest_RIGleanQueryProcessorConfig;

  constructor(data?: PartialMessage<RIGleanRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGleanRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGleanRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGleanRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGleanRequest;

  static equals(a: RIGleanRequest | PlainMessage<RIGleanRequest> | undefined, b: RIGleanRequest | PlainMessage<RIGleanRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGleanRequest.RIGleanQueryProcessorConfig
 */
export declare class RIGleanRequest_RIGleanQueryProcessorConfig extends Message<RIGleanRequest_RIGleanQueryProcessorConfig> {
  /**
   * @generated from field: string gcp_project_id = 1;
   */
  gcpProjectId: string;

  /**
   * @generated from field: string gcp_region = 2;
   */
  gcpRegion: string;

  /**
   * @generated from field: string model_name = 3;
   */
  modelName: string;

  /**
   * @generated from field: float temperature = 4;
   */
  temperature: number;

  /**
   * @generated from field: int32 max_output_tokens = 5;
   */
  maxOutputTokens: number;

  /**
   * @generated from field: int32 max_results = 6;
   */
  maxResults: number;

  constructor(data?: PartialMessage<RIGleanRequest_RIGleanQueryProcessorConfig>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGleanRequest.RIGleanQueryProcessorConfig";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGleanRequest_RIGleanQueryProcessorConfig;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGleanRequest_RIGleanQueryProcessorConfig;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGleanRequest_RIGleanQueryProcessorConfig;

  static equals(a: RIGleanRequest_RIGleanQueryProcessorConfig | PlainMessage<RIGleanRequest_RIGleanQueryProcessorConfig> | undefined, b: RIGleanRequest_RIGleanQueryProcessorConfig | PlainMessage<RIGleanRequest_RIGleanQueryProcessorConfig> | undefined): boolean;
}

/**
 * @generated from message request_insight.RILLMGenerateRequest
 */
export declare class RILLMGenerateRequest extends Message<RILLMGenerateRequest> {
  /**
   * @generated from field: agents.LLMGenerateRequest request = 1;
   */
  request?: LLMGenerateRequest;

  constructor(data?: PartialMessage<RILLMGenerateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RILLMGenerateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RILLMGenerateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RILLMGenerateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RILLMGenerateRequest;

  static equals(a: RILLMGenerateRequest | PlainMessage<RILLMGenerateRequest> | undefined, b: RILLMGenerateRequest | PlainMessage<RILLMGenerateRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RILLMGenerateResponse
 */
export declare class RILLMGenerateResponse extends Message<RILLMGenerateResponse> {
  /**
   * @generated from field: agents.LLMGenerateResponse response = 1;
   */
  response?: LLMGenerateResponse;

  constructor(data?: PartialMessage<RILLMGenerateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RILLMGenerateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RILLMGenerateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RILLMGenerateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RILLMGenerateResponse;

  static equals(a: RILLMGenerateResponse | PlainMessage<RILLMGenerateResponse> | undefined, b: RILLMGenerateResponse | PlainMessage<RILLMGenerateResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIRunRemoteToolRequest
 */
export declare class RIRunRemoteToolRequest extends Message<RIRunRemoteToolRequest> {
  /**
   * @generated from field: string tool_name = 1 [deprecated = true];
   * @deprecated
   */
  toolName: string;

  /**
   * @generated from field: string tool_input_json = 2;
   */
  toolInputJson: string;

  /**
   * @generated from field: agents.RemoteToolId tool_id = 3;
   */
  toolId: RemoteToolId;

  constructor(data?: PartialMessage<RIRunRemoteToolRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIRunRemoteToolRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIRunRemoteToolRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIRunRemoteToolRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIRunRemoteToolRequest;

  static equals(a: RIRunRemoteToolRequest | PlainMessage<RIRunRemoteToolRequest> | undefined, b: RIRunRemoteToolRequest | PlainMessage<RIRunRemoteToolRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIRemoteToolCallRequest
 */
export declare class RIRemoteToolCallRequest extends Message<RIRemoteToolCallRequest> {
  /**
   * @generated from oneof request_insight.RIRemoteToolCallRequest.request
   */
  request: {
    /**
     * @generated from field: agents.CodebaseRetrievalRequest codebase_retrieval_request = 1;
     */
    value: CodebaseRetrievalRequest;
    case: "codebaseRetrievalRequest";
  } | {
    /**
     * @generated from field: agents.EditFileRequest edit_file_request = 2;
     */
    value: EditFileRequest;
    case: "editFileRequest";
  } | {
    /**
     * @generated from field: request_insight.RIRunRemoteToolRequest ri_run_remote_tool_request = 4;
     */
    value: RIRunRemoteToolRequest;
    case: "riRunRemoteToolRequest";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<RIRemoteToolCallRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIRemoteToolCallRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIRemoteToolCallRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIRemoteToolCallRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIRemoteToolCallRequest;

  static equals(a: RIRemoteToolCallRequest | PlainMessage<RIRemoteToolCallRequest> | undefined, b: RIRemoteToolCallRequest | PlainMessage<RIRemoteToolCallRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIRemoteToolCallResponse
 */
export declare class RIRemoteToolCallResponse extends Message<RIRemoteToolCallResponse> {
  /**
   * @generated from oneof request_insight.RIRemoteToolCallResponse.response
   */
  response: {
    /**
     * @generated from field: agents.CodebaseRetrievalResponse codebase_retrieval_response = 1;
     */
    value: CodebaseRetrievalResponse;
    case: "codebaseRetrievalResponse";
  } | {
    /**
     * @generated from field: agents.EditFileResponse edit_file_response = 2;
     */
    value: EditFileResponse;
    case: "editFileResponse";
  } | {
    /**
     * @generated from field: agents.RunRemoteToolResponse run_remote_tool_response = 3;
     */
    value: RunRemoteToolResponse;
    case: "runRemoteToolResponse";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<RIRemoteToolCallResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIRemoteToolCallResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIRemoteToolCallResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIRemoteToolCallResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIRemoteToolCallResponse;

  static equals(a: RIRemoteToolCallResponse | PlainMessage<RIRemoteToolCallResponse> | undefined, b: RIRemoteToolCallResponse | PlainMessage<RIRemoteToolCallResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.SubAgentDialog
 */
export declare class SubAgentDialog extends Message<SubAgentDialog> {
  /**
   * @generated from field: repeated chat.Exchange dialog = 3;
   */
  dialog: Exchange[];

  constructor(data?: PartialMessage<SubAgentDialog>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.SubAgentDialog";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SubAgentDialog;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SubAgentDialog;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SubAgentDialog;

  static equals(a: SubAgentDialog | PlainMessage<SubAgentDialog> | undefined, b: SubAgentDialog | PlainMessage<SubAgentDialog> | undefined): boolean;
}

/**
 * @generated from message request_insight.ToolUseData
 */
export declare class ToolUseData extends Message<ToolUseData> {
  /**
   * @generated from field: string tool_name = 1;
   */
  toolName: string;

  /**
   * @generated from field: string tool_use_id = 2;
   */
  toolUseId: string;

  /**
   * @generated from field: optional bool tool_output_is_error = 3;
   */
  toolOutputIsError?: boolean;

  /**
   * @generated from field: uint32 tool_run_duration_ms = 4;
   */
  toolRunDurationMs: number;

  /**
   * @generated from field: string tool_input = 5;
   */
  toolInput: string;

  /**
   * @generated from field: optional bool is_mcp_tool = 6;
   */
  isMcpTool?: boolean;

  /**
   * @generated from field: optional string conversation_id = 7;
   */
  conversationId?: string;

  /**
   * @generated from field: optional uint32 chat_history_length = 8;
   */
  chatHistoryLength?: number;

  /**
   * @generated from field: optional string tool_request_id = 9;
   */
  toolRequestId?: string;

  /**
   * @generated from field: optional uint32 tool_output_len = 10;
   */
  toolOutputLen?: number;

  /**
   * @generated from field: optional uint32 tool_input_len = 11;
   */
  toolInputLen?: number;

  constructor(data?: PartialMessage<ToolUseData>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ToolUseData";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ToolUseData;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ToolUseData;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ToolUseData;

  static equals(a: ToolUseData | PlainMessage<ToolUseData> | undefined, b: ToolUseData | PlainMessage<ToolUseData> | undefined): boolean;
}

/**
 * @generated from message request_insight.ThirdPartyModelMessageHistory
 */
export declare class ThirdPartyModelMessageHistory extends Message<ThirdPartyModelMessageHistory> {
  /**
   * @generated from field: string user_message = 1;
   */
  userMessage: string;

  /**
   * @generated from field: string assistant_message = 2;
   */
  assistantMessage: string;

  constructor(data?: PartialMessage<ThirdPartyModelMessageHistory>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ThirdPartyModelMessageHistory";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ThirdPartyModelMessageHistory;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ThirdPartyModelMessageHistory;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ThirdPartyModelMessageHistory;

  static equals(a: ThirdPartyModelMessageHistory | PlainMessage<ThirdPartyModelMessageHistory> | undefined, b: ThirdPartyModelMessageHistory | PlainMessage<ThirdPartyModelMessageHistory> | undefined): boolean;
}

/**
 * @generated from message request_insight.ThirdPartyModelRequest
 */
export declare class ThirdPartyModelRequest extends Message<ThirdPartyModelRequest> {
  /**
   * @generated from field: string prompt = 1;
   */
  prompt: string;

  /**
   * @generated from field: string system_prompt = 2;
   */
  systemPrompt: string;

  /**
   * @generated from field: repeated request_insight.ThirdPartyModelMessageHistory messages = 3;
   */
  messages: ThirdPartyModelMessageHistory[];

  constructor(data?: PartialMessage<ThirdPartyModelRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ThirdPartyModelRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ThirdPartyModelRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ThirdPartyModelRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ThirdPartyModelRequest;

  static equals(a: ThirdPartyModelRequest | PlainMessage<ThirdPartyModelRequest> | undefined, b: ThirdPartyModelRequest | PlainMessage<ThirdPartyModelRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.PromptCacheUsage
 */
export declare class PromptCacheUsage extends Message<PromptCacheUsage> {
  /**
   * @generated from field: uint32 input_tokens = 1;
   */
  inputTokens: number;

  /**
   * @generated from field: uint32 cache_read_input_tokens = 2;
   */
  cacheReadInputTokens: number;

  /**
   * @generated from field: uint32 cache_creation_input_tokens = 3;
   */
  cacheCreationInputTokens: number;

  /**
   * @generated from field: uint32 text_input_tokens = 4;
   */
  textInputTokens: number;

  /**
   * @generated from field: uint32 tool_input_tokens = 5;
   */
  toolInputTokens: number;

  /**
   * @generated from field: uint32 text_output_tokens = 6;
   */
  textOutputTokens: number;

  /**
   * @generated from field: uint32 tool_output_tokens = 7;
   */
  toolOutputTokens: number;

  /**
   * @generated from field: optional string model_caller = 8;
   */
  modelCaller?: string;

  constructor(data?: PartialMessage<PromptCacheUsage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.PromptCacheUsage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PromptCacheUsage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PromptCacheUsage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PromptCacheUsage;

  static equals(a: PromptCacheUsage | PlainMessage<PromptCacheUsage> | undefined, b: PromptCacheUsage | PlainMessage<PromptCacheUsage> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGleanResponse
 */
export declare class RIGleanResponse extends Message<RIGleanResponse> {
  /**
   * @generated from field: glean.SearchResponse response = 1;
   */
  response?: SearchResponse;

  /**
   * @generated from field: request_insight.ThirdPartyModelRequest generate_search_queries_request = 2;
   */
  generateSearchQueriesRequest?: ThirdPartyModelRequest;

  /**
   * @generated from field: repeated string generate_search_queries_response = 3;
   */
  generateSearchQueriesResponse: string[];

  constructor(data?: PartialMessage<RIGleanResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGleanResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGleanResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGleanResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGleanResponse;

  static equals(a: RIGleanResponse | PlainMessage<RIGleanResponse> | undefined, b: RIGleanResponse | PlainMessage<RIGleanResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGleanOAuthURLRequest
 */
export declare class RIGleanOAuthURLRequest extends Message<RIGleanOAuthURLRequest> {
  constructor(data?: PartialMessage<RIGleanOAuthURLRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGleanOAuthURLRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGleanOAuthURLRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGleanOAuthURLRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGleanOAuthURLRequest;

  static equals(a: RIGleanOAuthURLRequest | PlainMessage<RIGleanOAuthURLRequest> | undefined, b: RIGleanOAuthURLRequest | PlainMessage<RIGleanOAuthURLRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIGleanOAuthURLResponse
 */
export declare class RIGleanOAuthURLResponse extends Message<RIGleanOAuthURLResponse> {
  /**
   * @generated from field: string oauth_url = 1;
   */
  oauthUrl: string;

  constructor(data?: PartialMessage<RIGleanOAuthURLResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIGleanOAuthURLResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIGleanOAuthURLResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIGleanOAuthURLResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIGleanOAuthURLResponse;

  static equals(a: RIGleanOAuthURLResponse | PlainMessage<RIGleanOAuthURLResponse> | undefined, b: RIGleanOAuthURLResponse | PlainMessage<RIGleanOAuthURLResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.ParsedRouterResponse
 */
export declare class ParsedRouterResponse extends Message<ParsedRouterResponse> {
  /**
   * @generated from field: string category = 1;
   */
  category: string;

  /**
   * @generated from field: repeated string filenames = 2;
   */
  filenames: string[];

  /**
   * @generated from field: repeated string docsets = 3;
   */
  docsets: string[];

  constructor(data?: PartialMessage<ParsedRouterResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ParsedRouterResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ParsedRouterResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ParsedRouterResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ParsedRouterResponse;

  static equals(a: ParsedRouterResponse | PlainMessage<ParsedRouterResponse> | undefined, b: ParsedRouterResponse | PlainMessage<ParsedRouterResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RouterResponse
 */
export declare class RouterResponse extends Message<RouterResponse> {
  /**
   * @generated from field: request_insight.Tokenization prompt_tokens = 1;
   */
  promptTokens?: Tokenization;

  /**
   * @generated from field: optional request_insight.Tokenization response_tokens = 2;
   */
  responseTokens?: Tokenization;

  /**
   * @generated from field: optional request_insight.ParsedRouterResponse parsed_response = 3;
   */
  parsedResponse?: ParsedRouterResponse;

  /**
   * @generated from field: optional string error_message = 4;
   */
  errorMessage?: string;

  constructor(data?: PartialMessage<RouterResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RouterResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RouterResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RouterResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RouterResponse;

  static equals(a: RouterResponse | PlainMessage<RouterResponse> | undefined, b: RouterResponse | PlainMessage<RouterResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.PostprocessResponse
 */
export declare class PostprocessResponse extends Message<PostprocessResponse> {
  /**
   * @generated from field: optional request_insight.Tokenization tokenization = 1;
   */
  tokenization?: Tokenization;

  /**
   * @generated from field: optional string error_message = 2;
   */
  errorMessage?: string;

  constructor(data?: PartialMessage<PostprocessResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.PostprocessResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PostprocessResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PostprocessResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PostprocessResponse;

  static equals(a: PostprocessResponse | PlainMessage<PostprocessResponse> | undefined, b: PostprocessResponse | PlainMessage<PostprocessResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RequestBlocked
 */
export declare class RequestBlocked extends Message<RequestBlocked> {
  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 1;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: string user_email = 2;
   */
  userEmail: string;

  /**
   * @generated from field: string check_type = 3;
   */
  checkType: string;

  constructor(data?: PartialMessage<RequestBlocked>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RequestBlocked";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestBlocked;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestBlocked;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestBlocked;

  static equals(a: RequestBlocked | PlainMessage<RequestBlocked> | undefined, b: RequestBlocked | PlainMessage<RequestBlocked> | undefined): boolean;
}

/**
 * @generated from message request_insight.RequestSuspicious
 */
export declare class RequestSuspicious extends Message<RequestSuspicious> {
  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 1;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: string user_email = 2;
   */
  userEmail: string;

  /**
   * @generated from field: string check_type = 3;
   */
  checkType: string;

  constructor(data?: PartialMessage<RequestSuspicious>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RequestSuspicious";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RequestSuspicious;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RequestSuspicious;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RequestSuspicious;

  static equals(a: RequestSuspicious | PlainMessage<RequestSuspicious> | undefined, b: RequestSuspicious | PlainMessage<RequestSuspicious> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIFindMissing
 */
export declare class RIFindMissing extends Message<RIFindMissing> {
  /**
   * @generated from field: string model_name = 1;
   */
  modelName: string;

  /**
   * @generated from field: int32 blob_count = 2;
   */
  blobCount: number;

  /**
   * @generated from field: int32 missing_count = 3;
   */
  missingCount: number;

  /**
   * @generated from field: int32 nonindexed_count = 4;
   */
  nonindexedCount: number;

  constructor(data?: PartialMessage<RIFindMissing>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIFindMissing";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIFindMissing;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIFindMissing;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIFindMissing;

  static equals(a: RIFindMissing | PlainMessage<RIFindMissing> | undefined, b: RIFindMissing | PlainMessage<RIFindMissing> | undefined): boolean;
}

/**
 * @generated from message request_insight.RIBatchUpload
 */
export declare class RIBatchUpload extends Message<RIBatchUpload> {
  /**
   * @generated from field: int32 blob_count = 1;
   */
  blobCount: number;

  /**
   * @generated from field: int64 total_size = 2;
   */
  totalSize: bigint;

  constructor(data?: PartialMessage<RIBatchUpload>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RIBatchUpload";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RIBatchUpload;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RIBatchUpload;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RIBatchUpload;

  static equals(a: RIBatchUpload | PlainMessage<RIBatchUpload> | undefined, b: RIBatchUpload | PlainMessage<RIBatchUpload> | undefined): boolean;
}

/**
 * @generated from message request_insight.TokenExchangeError
 */
export declare class TokenExchangeError extends Message<TokenExchangeError> {
  /**
   * @generated from field: request_insight.TokenExchangeError.Reason reason = 1;
   */
  reason: TokenExchangeError_Reason;

  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 2;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: string user_email = 3;
   */
  userEmail: string;

  constructor(data?: PartialMessage<TokenExchangeError>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.TokenExchangeError";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TokenExchangeError;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TokenExchangeError;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TokenExchangeError;

  static equals(a: TokenExchangeError | PlainMessage<TokenExchangeError> | undefined, b: TokenExchangeError | PlainMessage<TokenExchangeError> | undefined): boolean;
}

/**
 * @generated from enum request_insight.TokenExchangeError.Reason
 */
export declare enum TokenExchangeError_Reason {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: TENANT_NOT_IN_SHARD = 1;
   */
  TENANT_NOT_IN_SHARD = 1,
}

/**
 * @generated from message request_insight.DailyRequestLimitExceeded
 */
export declare class DailyRequestLimitExceeded extends Message<DailyRequestLimitExceeded> {
  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 1;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: int32 limit = 2;
   */
  limit: number;

  constructor(data?: PartialMessage<DailyRequestLimitExceeded>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.DailyRequestLimitExceeded";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DailyRequestLimitExceeded;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DailyRequestLimitExceeded;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DailyRequestLimitExceeded;

  static equals(a: DailyRequestLimitExceeded | PlainMessage<DailyRequestLimitExceeded> | undefined, b: DailyRequestLimitExceeded | PlainMessage<DailyRequestLimitExceeded> | undefined): boolean;
}

/**
 * @generated from message request_insight.InviteUserToTenant
 */
export declare class InviteUserToTenant extends Message<InviteUserToTenant> {
  /**
   * @generated from field: auth_entities.TenantInvitation invitation = 1;
   */
  invitation?: TenantInvitation;

  constructor(data?: PartialMessage<InviteUserToTenant>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.InviteUserToTenant";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InviteUserToTenant;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InviteUserToTenant;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InviteUserToTenant;

  static equals(a: InviteUserToTenant | PlainMessage<InviteUserToTenant> | undefined, b: InviteUserToTenant | PlainMessage<InviteUserToTenant> | undefined): boolean;
}

/**
 * @generated from message request_insight.DeleteInvitation
 */
export declare class DeleteInvitation extends Message<DeleteInvitation> {
  /**
   * @generated from field: string invitation_id = 1;
   */
  invitationId: string;

  constructor(data?: PartialMessage<DeleteInvitation>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.DeleteInvitation";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteInvitation;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteInvitation;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteInvitation;

  static equals(a: DeleteInvitation | PlainMessage<DeleteInvitation> | undefined, b: DeleteInvitation | PlainMessage<DeleteInvitation> | undefined): boolean;
}

/**
 * @generated from message request_insight.UpdateSubscription
 */
export declare class UpdateSubscription extends Message<UpdateSubscription> {
  /**
   * @generated from field: string subscription_id = 1;
   */
  subscriptionId: string;

  /**
   * @generated from field: int32 seats = 2;
   */
  seats: number;

  constructor(data?: PartialMessage<UpdateSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.UpdateSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSubscription;

  static equals(a: UpdateSubscription | PlainMessage<UpdateSubscription> | undefined, b: UpdateSubscription | PlainMessage<UpdateSubscription> | undefined): boolean;
}

/**
 * @generated from message request_insight.CreateTenantForTeam
 */
export declare class CreateTenantForTeam extends Message<CreateTenantForTeam> {
  /**
   * @generated from field: services.tenant.Tenant tenant = 1;
   */
  tenant?: Tenant;

  /**
   * @generated from field: string admin_user_id = 2;
   */
  adminUserId: string;

  /**
   * @generated from field: string subscription_id = 3;
   */
  subscriptionId: string;

  constructor(data?: PartialMessage<CreateTenantForTeam>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.CreateTenantForTeam";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTenantForTeam;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTenantForTeam;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTenantForTeam;

  static equals(a: CreateTenantForTeam | PlainMessage<CreateTenantForTeam> | undefined, b: CreateTenantForTeam | PlainMessage<CreateTenantForTeam> | undefined): boolean;
}

/**
 * @generated from message request_insight.AcceptInvitation
 */
export declare class AcceptInvitation extends Message<AcceptInvitation> {
  /**
   * @generated from field: string invitation_id = 1;
   */
  invitationId: string;

  /**
   * @generated from field: auth_entities.User user = 2;
   */
  user?: User;

  constructor(data?: PartialMessage<AcceptInvitation>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.AcceptInvitation";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AcceptInvitation;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AcceptInvitation;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AcceptInvitation;

  static equals(a: AcceptInvitation | PlainMessage<AcceptInvitation> | undefined, b: AcceptInvitation | PlainMessage<AcceptInvitation> | undefined): boolean;
}

/**
 * @generated from message request_insight.DeclineInvitation
 */
export declare class DeclineInvitation extends Message<DeclineInvitation> {
  /**
   * @generated from field: string invitation_id = 1;
   */
  invitationId: string;

  /**
   * @generated from field: string invitee_email = 2;
   */
  inviteeEmail: string;

  constructor(data?: PartialMessage<DeclineInvitation>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.DeclineInvitation";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeclineInvitation;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeclineInvitation;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeclineInvitation;

  static equals(a: DeclineInvitation | PlainMessage<DeclineInvitation> | undefined, b: DeclineInvitation | PlainMessage<DeclineInvitation> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsCreateRequest
 */
export declare class RemoteAgentsCreateRequest extends Message<RemoteAgentsCreateRequest> {
  /**
   * @generated from field: remote_agents.CreateAgentRequest request = 1;
   */
  request?: CreateAgentRequest;

  constructor(data?: PartialMessage<RemoteAgentsCreateRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsCreateRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsCreateRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsCreateRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsCreateRequest;

  static equals(a: RemoteAgentsCreateRequest | PlainMessage<RemoteAgentsCreateRequest> | undefined, b: RemoteAgentsCreateRequest | PlainMessage<RemoteAgentsCreateRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsCreateResponse
 */
export declare class RemoteAgentsCreateResponse extends Message<RemoteAgentsCreateResponse> {
  /**
   * @generated from field: remote_agents.CreateAgentResponse response = 1;
   */
  response?: CreateAgentResponse;

  constructor(data?: PartialMessage<RemoteAgentsCreateResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsCreateResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsCreateResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsCreateResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsCreateResponse;

  static equals(a: RemoteAgentsCreateResponse | PlainMessage<RemoteAgentsCreateResponse> | undefined, b: RemoteAgentsCreateResponse | PlainMessage<RemoteAgentsCreateResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsChatRequest
 */
export declare class RemoteAgentsChatRequest extends Message<RemoteAgentsChatRequest> {
  /**
   * @generated from field: remote_agents.ChatRequest request = 1;
   */
  request?: ChatRequest$1;

  constructor(data?: PartialMessage<RemoteAgentsChatRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsChatRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsChatRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsChatRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsChatRequest;

  static equals(a: RemoteAgentsChatRequest | PlainMessage<RemoteAgentsChatRequest> | undefined, b: RemoteAgentsChatRequest | PlainMessage<RemoteAgentsChatRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsChatResponse
 */
export declare class RemoteAgentsChatResponse extends Message<RemoteAgentsChatResponse> {
  /**
   * @generated from field: remote_agents.ChatResponse response = 1;
   */
  response?: ChatResponse$1;

  constructor(data?: PartialMessage<RemoteAgentsChatResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsChatResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsChatResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsChatResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsChatResponse;

  static equals(a: RemoteAgentsChatResponse | PlainMessage<RemoteAgentsChatResponse> | undefined, b: RemoteAgentsChatResponse | PlainMessage<RemoteAgentsChatResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsInterruptRequest
 */
export declare class RemoteAgentsInterruptRequest extends Message<RemoteAgentsInterruptRequest> {
  /**
   * @generated from field: remote_agents.InterruptAgentRequest request = 1;
   */
  request?: InterruptAgentRequest;

  constructor(data?: PartialMessage<RemoteAgentsInterruptRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsInterruptRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsInterruptRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsInterruptRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsInterruptRequest;

  static equals(a: RemoteAgentsInterruptRequest | PlainMessage<RemoteAgentsInterruptRequest> | undefined, b: RemoteAgentsInterruptRequest | PlainMessage<RemoteAgentsInterruptRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsInterruptResponse
 */
export declare class RemoteAgentsInterruptResponse extends Message<RemoteAgentsInterruptResponse> {
  /**
   * @generated from field: remote_agents.InterruptAgentResponse response = 1;
   */
  response?: InterruptAgentResponse;

  constructor(data?: PartialMessage<RemoteAgentsInterruptResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsInterruptResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsInterruptResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsInterruptResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsInterruptResponse;

  static equals(a: RemoteAgentsInterruptResponse | PlainMessage<RemoteAgentsInterruptResponse> | undefined, b: RemoteAgentsInterruptResponse | PlainMessage<RemoteAgentsInterruptResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsDeleteRequest
 */
export declare class RemoteAgentsDeleteRequest extends Message<RemoteAgentsDeleteRequest> {
  /**
   * @generated from field: remote_agents.DeleteAgentRequest request = 1;
   */
  request?: DeleteAgentRequest;

  constructor(data?: PartialMessage<RemoteAgentsDeleteRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsDeleteRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsDeleteRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsDeleteRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsDeleteRequest;

  static equals(a: RemoteAgentsDeleteRequest | PlainMessage<RemoteAgentsDeleteRequest> | undefined, b: RemoteAgentsDeleteRequest | PlainMessage<RemoteAgentsDeleteRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsDeleteResponse
 */
export declare class RemoteAgentsDeleteResponse extends Message<RemoteAgentsDeleteResponse> {
  /**
   * @generated from field: remote_agents.DeleteAgentResponse response = 1;
   */
  response?: DeleteAgentResponse;

  constructor(data?: PartialMessage<RemoteAgentsDeleteResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsDeleteResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsDeleteResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsDeleteResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsDeleteResponse;

  static equals(a: RemoteAgentsDeleteResponse | PlainMessage<RemoteAgentsDeleteResponse> | undefined, b: RemoteAgentsDeleteResponse | PlainMessage<RemoteAgentsDeleteResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsAddSSHKeyRequest
 */
export declare class RemoteAgentsAddSSHKeyRequest extends Message<RemoteAgentsAddSSHKeyRequest> {
  /**
   * @generated from field: remote_agents.AddSSHKeyRequest request = 1;
   */
  request?: AddSSHKeyRequest;

  constructor(data?: PartialMessage<RemoteAgentsAddSSHKeyRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsAddSSHKeyRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsAddSSHKeyRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsAddSSHKeyRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsAddSSHKeyRequest;

  static equals(a: RemoteAgentsAddSSHKeyRequest | PlainMessage<RemoteAgentsAddSSHKeyRequest> | undefined, b: RemoteAgentsAddSSHKeyRequest | PlainMessage<RemoteAgentsAddSSHKeyRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsAddSSHKeyResponse
 */
export declare class RemoteAgentsAddSSHKeyResponse extends Message<RemoteAgentsAddSSHKeyResponse> {
  /**
   * @generated from field: remote_agents.AddSSHKeyResponse response = 1;
   */
  response?: AddSSHKeyResponse;

  constructor(data?: PartialMessage<RemoteAgentsAddSSHKeyResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsAddSSHKeyResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsAddSSHKeyResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsAddSSHKeyResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsAddSSHKeyResponse;

  static equals(a: RemoteAgentsAddSSHKeyResponse | PlainMessage<RemoteAgentsAddSSHKeyResponse> | undefined, b: RemoteAgentsAddSSHKeyResponse | PlainMessage<RemoteAgentsAddSSHKeyResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsPauseRequest
 */
export declare class RemoteAgentsPauseRequest extends Message<RemoteAgentsPauseRequest> {
  /**
   * @generated from field: remote_agents.PauseAgentRequest request = 1;
   */
  request?: PauseAgentRequest;

  constructor(data?: PartialMessage<RemoteAgentsPauseRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsPauseRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsPauseRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsPauseRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsPauseRequest;

  static equals(a: RemoteAgentsPauseRequest | PlainMessage<RemoteAgentsPauseRequest> | undefined, b: RemoteAgentsPauseRequest | PlainMessage<RemoteAgentsPauseRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsPauseResponse
 */
export declare class RemoteAgentsPauseResponse extends Message<RemoteAgentsPauseResponse> {
  /**
   * @generated from field: remote_agents.PauseAgentResponse response = 1;
   */
  response?: PauseAgentResponse;

  constructor(data?: PartialMessage<RemoteAgentsPauseResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsPauseResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsPauseResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsPauseResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsPauseResponse;

  static equals(a: RemoteAgentsPauseResponse | PlainMessage<RemoteAgentsPauseResponse> | undefined, b: RemoteAgentsPauseResponse | PlainMessage<RemoteAgentsPauseResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsResumeRequest
 */
export declare class RemoteAgentsResumeRequest extends Message<RemoteAgentsResumeRequest> {
  /**
   * @generated from field: remote_agents.ResumeAgentRequest request = 1;
   */
  request?: ResumeAgentRequest;

  constructor(data?: PartialMessage<RemoteAgentsResumeRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsResumeRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsResumeRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsResumeRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsResumeRequest;

  static equals(a: RemoteAgentsResumeRequest | PlainMessage<RemoteAgentsResumeRequest> | undefined, b: RemoteAgentsResumeRequest | PlainMessage<RemoteAgentsResumeRequest> | undefined): boolean;
}

/**
 * @generated from message request_insight.RemoteAgentsResumeResponse
 */
export declare class RemoteAgentsResumeResponse extends Message<RemoteAgentsResumeResponse> {
  /**
   * @generated from field: remote_agents.ResumeAgentResponse response = 1;
   */
  response?: ResumeAgentResponse;

  constructor(data?: PartialMessage<RemoteAgentsResumeResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.RemoteAgentsResumeResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoteAgentsResumeResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoteAgentsResumeResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoteAgentsResumeResponse;

  static equals(a: RemoteAgentsResumeResponse | PlainMessage<RemoteAgentsResumeResponse> | undefined, b: RemoteAgentsResumeResponse | PlainMessage<RemoteAgentsResumeResponse> | undefined): boolean;
}

/**
 * @generated from message request_insight.ChatUserMessage
 */
export declare class ChatUserMessage extends Message<ChatUserMessage> {
  /**
   * @generated from field: request_insight.ChatUserMessage.ChatMode chat_mode = 1;
   */
  chatMode: ChatUserMessage_ChatMode;

  /**
   * @generated from field: uint32 chat_history_length = 2;
   */
  chatHistoryLength: number;

  /**
   * @generated from field: uint32 image_node_count = 3;
   */
  imageNodeCount: number;

  /**
   * @generated from field: uint32 character_count = 4;
   */
  characterCount: number;

  /**
   * @generated from field: uint32 line_count = 5;
   */
  lineCount: number;

  /**
   * @generated from field: auth_entities.UserId opaque_user_id = 6;
   */
  opaqueUserId?: UserId;

  /**
   * @generated from field: optional string model_name = 7;
   */
  modelName?: string;

  constructor(data?: PartialMessage<ChatUserMessage>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "request_insight.ChatUserMessage";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ChatUserMessage;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ChatUserMessage;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ChatUserMessage;

  static equals(a: ChatUserMessage | PlainMessage<ChatUserMessage> | undefined, b: ChatUserMessage | PlainMessage<ChatUserMessage> | undefined): boolean;
}

/**
 * @generated from enum request_insight.ChatUserMessage.ChatMode
 */
export declare enum ChatUserMessage_ChatMode {
  /**
   * @generated from enum value: CHAT_MODE_CHAT = 0;
   */
  CHAT = 0,

  /**
   * @generated from enum value: CHAT_MODE_AGENT = 1;
   */
  AGENT = 1,

  /**
   * @generated from enum value: CHAT_MODE_MEMORIES = 2;
   */
  MEMORIES = 2,

  /**
   * @generated from enum value: CHAT_MODE_ORIENTATION = 3;
   */
  ORIENTATION = 3,

  /**
   * @generated from enum value: CHAT_MODE_MEMORIES_COMPRESSION = 4;
   */
  MEMORIES_COMPRESSION = 4,

  /**
   * @generated from enum value: CHAT_MODE_REMOTE_AGENT = 5;
   */
  REMOTE_AGENT = 5,
}

