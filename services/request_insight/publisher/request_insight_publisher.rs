use std::collections::HashMap;
use std::fs::File;
use std::path::Path;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};

use async_trait::async_trait;
use lazy_static::lazy_static;
use prometheus::{register_int_counter_vec, IntCounterVec, Opts};

use crate::request_insight::RecordFullExportUserEventsRequest;
use crate::request_insight::RecordSessionEventsRequest;
use crate::request_insight::UpdateRequestInfoRequest;
use google_cloud_googleapis::pubsub::v1::PubsubMessage;
use google_cloud_pubsub::{
    client::{Client, ClientConfig},
    publisher::Publisher,
};
use prost::Message;
use request_context::{RequestContext, RequestId, RequestSessionId, TenantInfo};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

pub mod request_insight {
    tonic::include_proto!("request_insight");
}

pub mod agents {
    tonic::include_proto!("agents");
}

pub mod autofix {
    tonic::include_proto!("autofix");
}

pub mod chat {
    tonic::include_proto!("chat");
}

pub mod edit {
    tonic::include_proto!("edit");
}

pub mod completion {
    tonic::include_proto!("completion");
}

pub mod next_edit {
    tonic::include_proto!("next_edit");
}

pub mod share {
    tonic::include_proto!("share");
}

pub mod stream_mux {
    tonic::include_proto!("stream_mux");
}

pub mod base {
    pub mod blob_names {
        tonic::include_proto!("base.blob_names");
    }

    pub mod diff_utils {
        tonic::include_proto!("base.diff_utils");
    }
}

pub mod slackbot {
    tonic::include_proto!("slackbot");
}
#[allow(clippy::module_inception)]
pub mod github_event {
    tonic::include_proto!("github_event");
}

pub mod glean {
    tonic::include_proto!("glean");
}

pub mod google {
    pub mod rpc {
        tonic::include_proto!("google.rpc");
    }
}

pub mod auth_entities {
    tonic::include_proto!("auth_entities");
}

pub mod services {
    pub mod tenant {
        tonic::include_proto!("services.tenant");
    }
}

pub mod remote_agents {
    tonic::include_proto!("remote_agents");
}

/// structure representing the configuration information in the configuration file.
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RequestInsightPublisherConfig {
    pub project_id: String,
    pub topic_name: String,
}

impl RequestInsightPublisherConfig {
    /// read the configuration from a file
    pub fn read(path: &Path) -> Result<RequestInsightPublisherConfig, tonic::Status> {
        let file = File::open(path).map_err(|e| tonic::Status::internal(e.to_string()))?;

        let config: RequestInsightPublisherConfig =
            serde_json::from_reader(file).map_err(|e| tonic::Status::internal(e.to_string()))?;
        Ok(config)
    }
}

#[async_trait]
pub trait RequestInsightPublisher {
    async fn record_infer_request(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        infer_request: request_insight::InferRequest,
    );

    async fn forward_completion_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        accepted_idx: i32,
    );

    async fn record_completion_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    );

    async fn record_chat_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    );

    async fn record_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    );

    async fn record_remote_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    );

    async fn record_next_edit_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    );

    async fn record_slackbot_feedback(
        &self,
        tenant_info: &TenantInfo,
        slack_response_timestamp: &str,
        slack_channel_id: &str,
        rating: i32,
        note: &str,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
        annotated_text: Option<String>,
        annotated_instruction: Option<String>,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_instruction_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_smart_paste_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        initial_request_time: prost_wkt_types::Timestamp,
        stream_finish_time: prost_wkt_types::Timestamp,
        apply_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_client_metric(
        &self,
        tenant_info: &TenantInfo,
        session_id: &RequestSessionId,
        event_time: prost_wkt_types::Timestamp,
        event_name: String,
        user_agent: String,
        client_metric: String,
        value: u64,
    );

    async fn record_next_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
    );

    #[allow(clippy::too_many_arguments)]
    async fn record_preference_sample(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        request_ids: &[RequestId],
        scores: &HashMap<String, i32>,
        feedback: String,
    );

    async fn record_embeddings_search(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        request: request_insight::EmbeddingsSearchRequest,
        response: request_insight::EmbeddingsSearchResponse,
    );

    async fn record_http_response(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        code: u16,
        error_message: Option<&str>,
    );

    async fn record_request_events(&self, request: UpdateRequestInfoRequest);

    async fn record_full_export_user_events(&self, request: RecordFullExportUserEventsRequest);

    async fn record_session_events(&self, request: RecordSessionEventsRequest);

    async fn record_request_metadata(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        metadata: request_insight::RequestMetadata,
    );

    async fn record_extension_error(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        original_request_id: Option<String>,
        message: String,
        stack_trace: String,
        diagnostics: HashMap<String, String>,
    );

    async fn record_client_completion_timeline(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        initial_request_time: prost_wkt_types::Timestamp,
        api_start_time: prost_wkt_types::Timestamp,
        api_end_time: prost_wkt_types::Timestamp,
        emit_time: prost_wkt_types::Timestamp,
    );
}

#[async_trait]
trait RequestInsightPublisherInternal {
    // Low-level helpers to actually send messages out on the pubsub queue, or not in the case of
    // mocking. This way the real and mock implementations can share the per-update-type logic in
    // the various RequestInsightPublisher record_* functions.
    async fn update_request_info_internal(&self, request: UpdateRequestInfoRequest);
    async fn record_full_export_user_events_internal(
        &self,
        request: RecordFullExportUserEventsRequest,
    );
    async fn record_session_events_internal(&self, request: RecordSessionEventsRequest);
}

struct RequestInsightPublisherInternalImpl {
    publisher: Publisher,
}

#[async_trait]
impl RequestInsightPublisherInternal for RequestInsightPublisherInternalImpl {
    async fn update_request_info_internal(&self, request: UpdateRequestInfoRequest) {
        let ri_msg = request_insight::RequestInsightMessage {
            message: Some(
                request_insight::request_insight_message::Message::UpdateRequestInfoRequest(
                    request,
                ),
            ),
        };
        let msg = PubsubMessage {
            data: ri_msg.encode_to_vec(),
            ..Default::default()
        };

        let awaiter = self.publisher.publish(msg).await;

        // Wait for the publish to finish in the background, and update
        // metrics/logs accordingly
        // TODO(aswin): have one background waiter to wait for all publishes to
        // finish, which will save some tasks
        tokio::spawn(async move {
            match awaiter.get().await {
                Ok(_) => {
                    PUBSUB_PUBLISH_COUNT
                        .with_label_values(&["request-insight", "success"])
                        .inc();
                }
                Err(e) => {
                    // TODO: retry this message?
                    tracing::warn!("Publishing UpdateRequestInfo message failed: {}", e);
                    PUBSUB_PUBLISH_COUNT
                        .with_label_values(&["request-insight", "exception"])
                        .inc();
                }
            }
        });
    }

    async fn record_session_events_internal(&self, request: RecordSessionEventsRequest) {
        let ri_msg = request_insight::RequestInsightMessage {
            message: Some(
                request_insight::request_insight_message::Message::RecordSessionEventsRequest(
                    request.clone(),
                ),
            ),
        };
        let msg = PubsubMessage {
            data: ri_msg.encode_to_vec(),
            ..Default::default()
        };

        let awaiter = self.publisher.publish(msg).await;

        // Wait for the publish to finish in the background, and update
        // metrics/logs accordingly
        // TODO(aswin): have one background waiter to wait for all publishes to
        // finish, which will save some tasks
        tokio::spawn(async move {
            match awaiter.get().await {
                Ok(_) => {
                    PUBSUB_PUBLISH_COUNT
                        .with_label_values(&["session-events", "success"])
                        .inc();
                }
                Err(e) => {
                    // TODO: retry this message?
                    tracing::warn!("Publishing RecordSessionEvents message failed: {}", e);
                    PUBSUB_PUBLISH_COUNT
                        .with_label_values(&["session-events", "exception"])
                        .inc();
                }
            }
        });
    }

    async fn record_full_export_user_events_internal(
        &self,
        request: RecordFullExportUserEventsRequest,
    ) {
        let ri_msg = request_insight::RequestInsightMessage {
            message: Some(
                request_insight::request_insight_message::Message::RecordFullExportUserEventsRequest(request),
            ),
        };
        let msg = PubsubMessage {
            data: ri_msg.encode_to_vec(),
            ..Default::default()
        };

        let awaiter = self.publisher.publish(msg).await;

        // Wait for the publish to finish in the background, and update
        // metrics/logs accordingly
        // TODO(aswin): have one background waiter to wait for all publishes to
        // finish, which will save some tasks
        tokio::spawn(async move {
            match awaiter.get().await {
                Ok(_) => {
                    PUBSUB_PUBLISH_COUNT
                        .with_label_values(&["user-events", "success"])
                        .inc();
                }
                Err(e) => {
                    // TODO: retry this message?
                    tracing::warn!("Publishing RecordUserEvents message failed: {}", e);
                    PUBSUB_PUBLISH_COUNT
                        .with_label_values(&["user-events", "exception"])
                        .inc();
                }
            }
        });
    }
}

pub struct RequestInsightPublisherInternalMockImpl {
    pub last_update_request_info_request: std::sync::Mutex<Option<UpdateRequestInfoRequest>>,
    pub last_record_session_events_request: std::sync::Mutex<Option<RecordSessionEventsRequest>>,
    pub last_record_full_export_user_events_request:
        std::sync::Mutex<Option<RecordFullExportUserEventsRequest>>,
}

#[async_trait]
impl RequestInsightPublisherInternal for RequestInsightPublisherInternalMockImpl {
    async fn update_request_info_internal(&self, request: UpdateRequestInfoRequest) {
        {
            let mut d = self.last_update_request_info_request.lock().unwrap();
            *d = Some(request);
        }
    }
    async fn record_session_events_internal(&self, request: RecordSessionEventsRequest) {
        {
            let mut d = self.last_record_session_events_request.lock().unwrap();
            *d = Some(request);
        }
    }
    async fn record_full_export_user_events_internal(
        &self,
        request: RecordFullExportUserEventsRequest,
    ) {
        {
            let mut d = self
                .last_record_full_export_user_events_request
                .lock()
                .unwrap();
            *d = Some(request);
        }
    }
}

impl Default for RequestInsightPublisherInternalMockImpl {
    fn default() -> Self {
        Self {
            last_update_request_info_request: std::sync::Mutex::new(None),
            last_record_session_events_request: std::sync::Mutex::new(None),
            last_record_full_export_user_events_request: std::sync::Mutex::new(None),
        }
    }
}

#[derive(Clone)]
pub struct RequestInsightPublisherImpl {
    inner: Arc<dyn RequestInsightPublisherInternal + Send + Sync + 'static>,
}

pub fn to_tenant_info_proto(tenant_info: &TenantInfo) -> Option<request_insight::TenantInfo> {
    Some(request_insight::TenantInfo {
        tenant_id: tenant_info
            .tenant_id
            .as_ref()
            .map(|id| id.to_string())
            .unwrap_or_default(),
        tenant_name: tenant_info.tenant_name.to_string(),
    })
}

async fn create_publisher(
    project_id: String,
    topic_name: String,
) -> Result<Publisher, Box<dyn std::error::Error>> {
    tracing::info!(
        "Creating request_insight client: project_id={} topic_name={}",
        project_id,
        topic_name,
    );

    let config = ClientConfig {
        project_id: Some(project_id.clone()),
        ..Default::default()
    }
    .with_auth()
    .await?;

    let client = Client::new(config).await?;
    let topic = client.topic(topic_name.as_str());
    let publisher = topic.new_publisher(None);

    Ok(publisher)
}

#[async_trait]
impl RequestInsightPublisher for RequestInsightPublisherImpl {
    async fn record_infer_request(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        infer_request: request_insight::InferRequest,
    ) {
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::InferRequest(
                infer_request,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_context.request_id().to_string(),
            session_id: request_context.request_session_id().option_to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn forward_completion_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        accepted_idx: i32,
    ) {
        let emit = request_insight::CompletionEmit {};
        let emit_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(emit_time),
            event: Some(request_insight::request_event::Event::CompletionEmit(emit)),
        };

        let resolve = request_insight::CompletionResolution { accepted_idx };
        let resolve_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(resolve_time),
            event: Some(request_insight::request_event::Event::CompletionResolution(
                resolve,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: original_request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![emit_event, resolve_event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_completion_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    ) {
        let feedback = request_insight::CompletionFeedback {
            rating,
            note: note.to_string(),
        };
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::CompletionFeedback(
                feedback,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_chat_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    ) {
        let feedback = request_insight::ChatFeedback {
            rating,
            note: note.to_string(),
            user_agent: user_agent.to_string(),
        };
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::ChatFeedback(
                feedback,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    ) {
        let feedback = request_insight::AgentFeedback {
            rating,
            note: note.to_string(),
            user_agent: user_agent.to_string(),
        };
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::AgentFeedback(
                feedback,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_remote_agent_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
        user_agent: &str,
    ) {
        let feedback = request_insight::RemoteAgentFeedback {
            rating,
            note: note.to_string(),
            user_agent: user_agent.to_string(),
        };
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::RemoteAgentFeedback(
                feedback,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_client_completion_timeline(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        initial_request_time: prost_wkt_types::Timestamp,
        api_start_time: prost_wkt_types::Timestamp,
        api_end_time: prost_wkt_types::Timestamp,
        emit_time: prost_wkt_types::Timestamp,
    ) {
        let completion_timeline = request_insight::ClientCompletionTimeline {
            initial_request_time: Some(initial_request_time),
            api_start_time: Some(api_start_time),
            api_end_time: Some(api_end_time),
            emit_time: Some(emit_time),
        };
        let time = self.get_time();
        let update_ri_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(
                request_insight::request_event::Event::ClientCompletionTimeline(
                    completion_timeline,
                ),
            ),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: original_request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![update_ri_event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_next_edit_feedback(
        &self,
        tenant_info: &TenantInfo,
        request_id: &RequestId,
        rating: i32,
        note: &str,
    ) {
        let feedback = request_insight::NextEditFeedback {
            rating,
            note: note.to_string(),
        };
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::NextEditFeedback(
                feedback,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_slackbot_feedback(
        &self,
        tenant_info: &TenantInfo,
        slack_response_timestamp: &str,
        slack_channel_id: &str,
        rating: i32,
        note: &str,
    ) {
        // using a new random request id for slackbot feedback since we don't yet have the request id for the message the feedback is for
        // we can use the slack_response_timestamp and slack_channel_id to look up the request id in the slackbot_response_lookup view when processing feedback
        let request_id = RequestId::create_random();
        let feedback = request_insight::SlackbotFeedback {
            slack_response_timestamp: slack_response_timestamp.to_string(),
            slack_channel_id: slack_channel_id.to_string(),
            rating,
            note: note.to_string(),
        };
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::SlackbotFeedback(
                feedback,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_preference_sample(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        request_ids: &[RequestId],
        scores: &HashMap<String, i32>,
        feedback: String,
    ) {
        let time = self.get_time();
        let sample = request_insight::PreferenceSample {
            request_ids: request_ids.iter().map(|id| id.to_string()).collect(),
            scores: scores.clone(),
            feedback,
        };
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::PreferenceSample(
                sample,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_context.request_id().to_string(),
            session_id: request_context.request_session_id().option_to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await;
    }

    async fn record_client_metric(
        &self,
        tenant_info: &TenantInfo,
        session_id: &RequestSessionId,
        event_time: prost_wkt_types::Timestamp,
        event_name: String,
        user_agent: String,
        client_metric: String,
        value: u64,
    ) {
        let request = request_insight::RecordSessionEventsRequest {
            session_id: session_id.to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![request_insight::SessionEvent {
                event_id: Some(Uuid::new_v4().to_string()),
                time: Some(event_time),
                event: Some(request_insight::session_event::Event::ClientMetric(
                    request_insight::ClientMetric {
                        event_name,
                        user_agent,
                        client_metric,
                        value,
                    },
                )),
            }],
            opaque_user_id: None,
        };

        self.inner.record_session_events_internal(request).await;
    }

    async fn record_instruction_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    ) {
        let emit: request_insight::InstructionEmit = request_insight::InstructionEmit {};
        let emit_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(emit_time),
            event: Some(request_insight::request_event::Event::InstructionEmit(emit)),
        };

        let resolve = request_insight::InstructionResolution {
            is_accepted_chunks,
            is_accept_all,
            is_reject_all,
        };
        let resolve_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(resolve_time),
            event: Some(request_insight::request_event::Event::InstructionResolution(resolve)),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: original_request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![emit_event, resolve_event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
        annotated_text: Option<String>,
        annotated_instruction: Option<String>,
    ) {
        let emit: request_insight::EditEmit = request_insight::EditEmit {};
        let emit_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(emit_time),
            event: Some(request_insight::request_event::Event::EditEmit(emit)),
        };

        let resolve = request_insight::EditResolution {
            is_accepted,
            annotated_text,
            annotated_instruction,
        };
        let resolve_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(resolve_time),
            event: Some(request_insight::request_event::Event::EditResolution(
                resolve,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: original_request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![emit_event, resolve_event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_smart_paste_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        is_accepted_chunks: Vec<bool>,
        is_accept_all: bool,
        is_reject_all: bool,
        initial_request_time: prost_wkt_types::Timestamp,
        stream_finish_time: prost_wkt_types::Timestamp,
        apply_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
    ) {
        // Emit time is when user was first able to interact with the diff viewer,
        // which is the latest of the stream finishing and the user clicking apply.
        let emit_time = if stream_finish_time.seconds > apply_time.seconds
            || (stream_finish_time.seconds == apply_time.seconds
                && stream_finish_time.nanos > apply_time.nanos)
        {
            stream_finish_time
        } else {
            apply_time
        };
        let emit: request_insight::SmartPasteClientTimeline =
            request_insight::SmartPasteClientTimeline {
                initial_request_time: Some(initial_request_time),
                stream_finish_time: Some(stream_finish_time),
                apply_time: Some(apply_time),
            };
        let emit_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(emit_time),
            event: Some(request_insight::request_event::Event::SmartPasteClientTimeline(emit)),
        };

        let resolve = request_insight::SmartPasteResolution {
            is_accepted_chunks,
            is_accept_all,
            is_reject_all,
        };
        let resolve_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(resolve_time),
            event: Some(request_insight::request_event::Event::SmartPasteResolution(
                resolve,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: original_request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![emit_event, resolve_event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_next_edit_resolution(
        &self,
        tenant_info: &TenantInfo,
        original_request_id: &RequestId,
        emit_time: prost_wkt_types::Timestamp,
        resolve_time: prost_wkt_types::Timestamp,
        is_accepted: bool,
    ) {
        let emit: request_insight::NextEditEmit = request_insight::NextEditEmit {};
        let emit_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(emit_time),
            event: Some(request_insight::request_event::Event::NextEditEmit(emit)),
        };

        let resolve = request_insight::NextEditResolution { is_accepted };

        let resolve_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(resolve_time),
            event: Some(request_insight::request_event::Event::NextEditResolution(
                resolve,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: original_request_id.to_string(),
            session_id: None,
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![emit_event, resolve_event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_embeddings_search(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        request: request_insight::EmbeddingsSearchRequest,
        response: request_insight::EmbeddingsSearchResponse,
    ) {
        // TODO: get_time() matches embeddings_search_host/server/embeddings_service.cc behavior
        // but eventually it would be better to actually pass request and response timestamps here
        let request_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(self.get_time()),
            event: Some(request_insight::request_event::Event::EmbeddingsSearchRequest(request)),
        };
        let response_event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(self.get_time()),
            event: Some(request_insight::request_event::Event::EmbeddingsSearchResponse(response)),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_context.request_id().to_string(),
            session_id: request_context.request_session_id().option_to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![request_event, response_event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_http_response(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        code: u16,
        error_message: Option<&str>,
    ) {
        let time = self.get_time();
        let api_http_response = request_insight::ApiHttpResponse {
            code: code.into(),
            error_message: error_message.unwrap_or("").to_string(),
        };
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::ApiHttpResponse(
                api_http_response,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_context.request_id().to_string(),
            session_id: request_context.request_session_id().option_to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_request_events(&self, request: UpdateRequestInfoRequest) {
        self.inner.update_request_info_internal(request).await
    }

    async fn record_session_events(&self, request: RecordSessionEventsRequest) {
        self.inner.record_session_events_internal(request).await
    }

    async fn record_full_export_user_events(&self, request: RecordFullExportUserEventsRequest) {
        self.inner
            .record_full_export_user_events_internal(request)
            .await
    }

    async fn record_request_metadata(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        metadata: request_insight::RequestMetadata,
    ) {
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::RequestMetadata(
                metadata,
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_context.request_id().to_string(),
            session_id: request_context.request_session_id().option_to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }

    async fn record_extension_error(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        original_request_id: Option<String>,
        message: String,
        stack_trace: String,
        diagnostics: HashMap<String, String>,
    ) {
        let time = self.get_time();
        let event = request_insight::RequestEvent {
            event_id: Some(Uuid::new_v4().to_string()),
            time: Some(time),
            event: Some(request_insight::request_event::Event::ExtensionError(
                request_insight::ExtensionError {
                    original_request_id,
                    message,
                    stack_trace,
                    diagnostics,
                },
            )),
        };

        let request = request_insight::UpdateRequestInfoRequest {
            request_id: request_context.request_id().to_string(),
            session_id: request_context.request_session_id().option_to_string(),
            tenant_info: to_tenant_info_proto(tenant_info),
            events: vec![event],
        };
        self.inner.update_request_info_internal(request).await
    }
}

impl RequestInsightPublisherImpl {
    fn get_time(&self) -> prost_wkt_types::Timestamp {
        let utc_system_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .expect("Failed to get time");

        let seconds = i64::try_from(utc_system_time.as_secs()).expect("Failed to convert time");
        let nanos = i32::try_from(utc_system_time.subsec_nanos()).expect("Failed to convert time");

        prost_wkt_types::Timestamp { seconds, nanos }
    }

    pub async fn new(config: RequestInsightPublisherConfig) -> Self {
        let publisher = create_publisher(config.project_id.clone(), config.topic_name.clone())
            .await
            .expect("Failed to create publisher");
        Self {
            inner: Arc::new(RequestInsightPublisherInternalImpl { publisher }),
        }
    }

    pub fn new_for_test(mock: Arc<RequestInsightPublisherInternalMockImpl>) -> Self {
        Self { inner: mock }
    }
}

lazy_static! {
    pub static ref PUBSUB_PUBLISH_COUNT: IntCounterVec = register_int_counter_vec!(
        Opts::new(
            "au_request_insight_pubsub_publish_count_total",
            "Number of publishes to the pubsub topic"
        ),
        &["message_type", "status"]
    )
    .expect("metric can be created");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_record_full_export_user_events() {
        // This is kind of trivial but it would have caught a stack overflow bug I wrote
        let mock = Arc::new(RequestInsightPublisherInternalMockImpl::default());
        let publisher = RequestInsightPublisherImpl::new_for_test(mock.clone());

        publisher
            .record_full_export_user_events(request_insight::RecordFullExportUserEventsRequest {
                session_id: Some("test-session".to_string()),
                user_id: Some("test-user".to_string()),
                extension_data: Some(request_insight::ExtensionData {
                    user_events: vec![request_insight::FullExportUserEvent {
                        time: Some(prost_wkt_types::Timestamp {
                            seconds: 100,
                            nanos: 200,
                        }),
                        file_path: Some("/test_path".to_string()),
                        event: Some(
                            request_insight::full_export_user_event::Event::CompletionRequestIdIssued(
                                request_insight::CompletionRequestIdIssuedEvent {
                                    request_id: "test-request".to_string(),
                                    file_path: None,
                                },
                            ),
                        ),
                    }],
                }),
                ..Default::default()
            })
            .await;

        assert!(mock
            .last_record_full_export_user_events_request
            .lock()
            .unwrap()
            .as_ref()
            .is_some());
        assert_eq!(
            mock.last_record_full_export_user_events_request
                .lock()
                .unwrap()
                .as_ref()
                .unwrap()
                .session_id,
            Some("test-session".to_string())
        );
    }

    #[tokio::test]
    async fn test_record_session_events() {
        let mock = Arc::new(RequestInsightPublisherInternalMockImpl::default());
        let publisher = RequestInsightPublisherImpl::new_for_test(mock.clone());

        publisher
            .record_session_events(request_insight::RecordSessionEventsRequest {
                session_id: "test-session".to_string(),
                tenant_info: Some(request_insight::TenantInfo {
                    tenant_id: "test-tenant".to_string(),
                    tenant_name: "test-name".to_string(),
                }),
                events: vec![request_insight::SessionEvent {
                    event_id: Some("test-event-id".to_string()),
                    time: Some(prost_wkt_types::Timestamp {
                        seconds: 100,
                        nanos: 200,
                    }),
                    event: Some(
                        request_insight::session_event::Event::OnboardingSessionEvent(
                            request_insight::OnboardingSessionEvent {
                                event_name: "test-event-name".to_string(),
                                user_agent: "test-user-agent".to_string(),
                            },
                        ),
                    ),
                }],
                opaque_user_id: None,
            })
            .await;

        assert!(mock
            .last_record_session_events_request
            .lock()
            .unwrap()
            .as_ref()
            .is_some());
    }
}
