// This file contains the schema for the BigQuery analytics dataset. The dataset is actually
// created by services/request_insight/analytics_dataset/deploy.jsonnet. This schema can also be
// passed into an emulator for testing.

// Staging/production callers should always provide a dataset name and piiPolicyTag
// generated by bigquery_lib.jsonnet. The defaults are here to facilitate easier testing.
// TODO(jacqueline): Find a way to feed in the dataset name during tests instead of making everyone
//                   use test_dataset.
function(dataset='test_dataset', piiPolicyTag=null)
  local bigqueryLib = import 'services/request_insight/lib/bigquery_lib.jsonnet';

  // The base schema common to all request events.
  local requestEventBaseSchema = [
    {
      name: 'request_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The request ID of the request',
    },
    {
      name: 'tenant',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The name of the tenant for the request',
    },
    {
      name: 'tenant_id',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'The ID of the tenant for the request',
    },
    {
      name: 'shard_namespace',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'The shard namespace of the tenant at the time of the request',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
    {
      name: 'sanitized_json',
      type: 'JSON',
      mode: 'REQUIRED',
      description: 'The raw JSON of the event, with sensitive data removed',
    },
    {
      name: 'session_id',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'The session ID of the request',
    },
  ];

  // The base schema common to all session events.
  local sessionEventBaseSchema = [
    {
      name: 'session_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The session ID of the event',
    },
    {
      name: 'tenant',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The name of the tenant for the event',
    },
    {
      name: 'tenant_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The ID of the tenant for the event',
    },
    {
      name: 'shard_namespace',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The shard namespace of the tenant at the time of the event',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
    {
      name: 'sanitized_json',
      type: 'JSON',
      mode: 'REQUIRED',
      description: 'The raw JSON of the event, with sensitive data removed',
    },
    {
      name: 'opaque_user_id',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'The opaque user ID of the user who performed the event. This ID can have different meanings, depending on the value of user_id_type.',
    },
    {
      name: 'user_id_type',
      type: 'STRING',
      mode: 'NULLABLE',
      description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
    },
  ];

  // The base schema common to all tenant events.
  local tenantEventBaseSchema = [
    {
      name: 'tenant',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The name of the tenant for the event',
    },
    {
      name: 'tenant_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The ID of the tenant for the event',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
    {
      name: 'sanitized_json',
      type: 'JSON',
      mode: 'REQUIRED',
      description: 'The raw JSON of the event, with sensitive data removed',
    },
  ];

  // The base schema common to all generic events.
  local genericEventBaseSchema = [
    {
      name: 'session_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The session ID of the event',
    },
    {
      name: 'event_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The ID of the event, set by the event publisher. Can be used for deduplication.',
    },
    {
      name: 'time',
      type: 'TIMESTAMP',
      mode: 'REQUIRED',
      description: 'The time of the event',
    },
    {
      name: 'sanitized_json',
      type: 'JSON',
      mode: 'REQUIRED',
      description: 'The raw JSON of the event, with sensitive data removed',
    },
  ];

  [
    bigqueryLib.tableDefinition(
      'request_metadata',
      'Metadata about a request. Intended to be useful for joining across tables.',
      [
        {
          name: 'request_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The request ID of the request',
        },
        {
          name: 'tenant',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The name of the tenant for the request',
        },
        {
          name: 'tenant_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the tenant for the request',
        },
        {
          name: 'shard_namespace',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The shard namespace of the tenant at the time of the request',
        },
        {
          name: 'time',
          type: 'TIMESTAMP',
          mode: 'REQUIRED',
          description: 'The server timestamp when this metadata was recorded. This is approximately when the request was first received by the backend.',
        },
        {
          // This is a string instead of an enum to avoid a database migration when we add new
          // request types.
          name: 'request_type',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The type of request ("COMPLETION", "EDIT", "CHAT").',
        },
        {
          name: 'session_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The session ID of the request',
        },
        // user_id is in the process of being deprecated, in favor of opaque_user_id.
        {
          name: 'user_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The user ID of the request',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'user_agent',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The user agent of the request',
        },
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the request. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        // Note(jacqueline): Eventually I'd like to remove emails from this table entirely and rely
        // on joins on the user table instead.
        {
          name: 'user_email',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The user email of the request, if any',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'source_ip',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The source IP of the request',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'completion_resolution',
      'Request Insight BigQuery table for completion resolution',
      requestEventBaseSchema + [
        {
          name: 'accepted',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the completion was accepted',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'tool_use_data',
      'Request Insight BigQuery table for tool use data',
      requestEventBaseSchema + [
        {
          name: 'terminal_command_prefix',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The terminal command prefix, if the tool is a terminal command and the command prefix is in the allowlist. This should only be the command prefix that is exactly in the allowlist, with none of the trailing arguments or flags.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'completion_request',
      'Request Insight BigQuery table for completion requests received by api-proxy',
      requestEventBaseSchema + [
        {
          name: 'user_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The user ID of the request',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'path_keywords',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Keywords from the path',
        },
        {
          name: 'path_extension',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The extension of the path (e.g., ".py"), if it matches an allowlisted set of extensions.',
        },
        {
          // This is actually unused, I mistakenly added this here instead of
          // chat_host_request :facepalm:. We can't remove columns by deleting
          // them, so comment it out for now. Maybe it will be useful
          // eventually!
          name: 'masked_external_source_ids',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Unused',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'completion_emit',
      'Request Insight BigQuery table for the CompletionEmit event',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'completion_host_request',
      'Request Insight BigQuery table for the CompletionHostRequest event',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'completion_host_response',
      'Request Insight BigQuery table for the CompletionHostResponse event',
      requestEventBaseSchema + [
        {
          name: 'character_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of characters in the completion.',
        },
        {
          name: 'line_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of lines in the completion (discounting empty lines).',
        },
        {
          name: 'unknown_blobs_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The length of unknown_blob_names in the response.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'retrieval_response',
      'Request Insight BigQuery table for the RetrievalResponse event',
      requestEventBaseSchema + [
        {
          name: 'retrieval_type',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The type of retrieval used ("DENSE", "SIGNATURE", etc)',
        },
        {
          name: 'retrieved_chunk_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of chunks retrieved.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'completion_feedback',
      'Request Insight BigQuery table for completion feedback received by api-proxy',
      requestEventBaseSchema + [
        {
          name: 'has_note',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the feedback contains a note',
        },
        {
          name: 'note',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Feedback note',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'chat_feedback',
      'Request Insight BigQuery table for chat feedback received by api-proxy',
      requestEventBaseSchema + [
        {
          name: 'has_note',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the feedback contains a note',
        },
        {
          name: 'note',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Feedback note',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'agent_feedback',
      'Request Insight BigQuery table for agent (chat mode) feedback received by api-proxy',
      requestEventBaseSchema + [
        {
          name: 'has_note',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the feedback contains a note',
        },
        {
          name: 'note',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Feedback note',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agent_feedback',
      'Request Insight BigQuery table for remote agent (chat mode) feedback received by api-proxy',
      requestEventBaseSchema + [
        {
          name: 'has_note',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the feedback contains a note',
        },
        {
          name: 'note',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Feedback note',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'next_edit_feedback',
      'Request Insight BigQuery table for next edit feedback received by api-proxy',
      requestEventBaseSchema + [
        {
          name: 'has_note',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the feedback contains a note',
        },
        {
          name: 'note',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'Feedback note',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'slackbot_feedback',
      'Request Insight BigQuery table for slackbot feedback received by api-proxy',
      requestEventBaseSchema + [
        {
          name: 'slack_response_timestamp',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The timestamp of the slackbot response the feedback is for. This is used along with the slack_channel_id to find the real request ID of the message the user is providing feedback on using the slackbot_response_lookup view',
        },
        {
          name: 'slack_channel_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The channel id of the slackbot response the feedback is for. This is used along with the slack_response_timestamp to find the real request ID of the message the user is providing feedback on using the slackbot_response_lookup view',
        },
        {
          name: 'note',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Feedback note',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'inference_host_response',
      'Request Insight BigQuery table for the InferenceHostResponse event',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'api_http_response',
      'Request Insight BigQuery table for the ApiHttpResponse event',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'embeddings_search_request',
      'Request Insight BigQuery table for the EmbeddingsSearchRequest event',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'embeddings_search_response',
      'Request Insight BigQuery table for the EmbeddingsSearchResponse event',
      requestEventBaseSchema + [
        {
          name: 'missing_blobs_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The length of missing_blob_names in the response.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'edit_host_request',
      'Request Insight BigQuery table for edit requests received by edit-host',
      requestEventBaseSchema + [
        {
          name: 'keywords',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Keywords from the code instruction',
        },
        {
          name: 'path_extension',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The extension of the path (e.g., ".py"), if it matches an allowlisted set of extensions.',
        },
        {
          name: 'categories',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Keyword categories from the code instruction',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'edit_host_response',
      'Request Insight BigQuery table for the EditHostResponse event',
      requestEventBaseSchema + [
        {
          name: 'character_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of characters in the completion.',
        },
        {
          name: 'line_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of lines in the completion (discounting empty lines).',
        },
        {
          name: 'unknown_blobs_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The length of unknown_blob_names in the response.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'edit_emit',
      'Request Insight BigQuery table for the EditEmit event',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'edit_resolution',
      'Request Insight BigQuery table for edit resolution',
      requestEventBaseSchema + [
        {
          name: 'accepted',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the edit was accepted',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'instruction_host_request',
      'Request Insight BigQuery table for instruction requests received by instruction-host',
      requestEventBaseSchema + [
        {
          name: 'path_extension',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The extension of the path (e.g., ".py"), if it matches an allowlisted set of extensions.',
        },
        {
          name: 'categories',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Keyword categories from the code instruction',
        },
        {
          name: 'code_block_character_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of characters in the code block.',
        },
        {
          name: 'code_block_line_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of lines in the code block (discounting empty lines).',
        },
        {
          name: 'target_file_path_extension',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The extension of the target file path (e.g., ".py"), if it matches an allowlisted set of extensions.',
        },
        {
          name: 'target_file_content_character_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of characters in the target file content.',
        },
        {
          name: 'target_file_content_line_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of lines in the target file content (discounting empty lines).',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'instruction_host_response',
      'Request Insight BigQuery table for the InstructionHostResponse event',
      requestEventBaseSchema + [
        {
          name: 'replace_text_character_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of characters in the replace_text response.',
        },
        {
          name: 'replace_text_line_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of lines in the replace_text response (discounting empty lines).',
        },
        {
          name: 'unknown_blobs_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of unknown blobs in the response.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'instruction_emit',
      'Request Insight BigQuery table for the InstructionEmit event',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'instruction_resolution',
      'Request Insight BigQuery table for instruction resolution',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'smart_paste_client_timeline',
      'Request Insight BigQuery table for client smart paste timeline',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'smart_paste_resolution',
      'Request Insight BigQuery table for smart paste resolution',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'extension_error',
      'Request Insight BigQuery table for the ExtensionError event',
      requestEventBaseSchema + [
        {
          name: 'sanitized_error_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'An error type extracted from field(s) of the event which could not be kept in sanitized_json',
        },
      ]
    ),
    bigqueryLib.tableDefinition(
      'chat_host_request',
      'Request Insight BigQuery table for chat requests received by chat-host',
      requestEventBaseSchema + [
        {
          name: 'message_keywords',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Keywords from the customer chat message',
        },
        {
          name: 'path_extension',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The extension of the path (e.g., ".py"), if it matches an allowlisted set of extensions.',
        },
        {
          name: 'selected_code_character_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of characters in the selected code.',
        },
        {
          name: 'selected_code_line_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of lines in the selected code (discounting empty lines).',
        },
        {
          name: 'message_categories',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Keyword categories from the customer chat message',
        },
        {
          name: 'masked_external_source_ids',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'The list of external IDs in the request, with any sensitive information masked out, for example repository names or bug titles.',
        },
        {
          name: 'image_node_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of image nodes in the request.',
        },
        {
          name: 'text_node_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of text nodes in the request.',
        },
        {
          name: 'tool_result_node_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of tool result nodes in the request.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'chat_host_response',
      'Request Insight BigQuery table for chat host responses',
      requestEventBaseSchema + [
        {
          name: 'character_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of characters in the chat response.',
        },
        {
          name: 'line_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of lines in the chat response (discounting empty lines).',
        },
        {
          name: 'unknown_blobs_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The length of unknown_blob_names in the response.',
        },
        {
          name: 'masked_incorporated_external_source_ids',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'The list of external IDs incorporated into the response, with any sensitive information masked out, for example repository names or bug titles.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'next_edit_host_request',
      'Request Insight BigQuery table for next-edit host requests',
      requestEventBaseSchema + [
        {
          name: 'token_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Token count for formatted request',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'next_edit_host_response',
      'Request Insight BigQuery table for next-edit host responses',
      requestEventBaseSchema + [
        {
          name: 'character_counts',
          type: 'INTEGER',
          mode: 'REPEATED',
          description: 'Number of characters in the edit suggestions in the response.',
        },
        {
          name: 'unknown_blobs_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The length of unknown_blob_names in the response.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'next_edit_session_event',
      'Request Insight BigQuery table for next-edit session events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'onboarding_session_event',
      'Request Insight BigQuery table for onboarding session events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'client_metric',
      'Request Insight BigQuery table for client metric session events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'extension_session_event',
      'Request Insight BigQuery table for extension session events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'agent_session_event',
      'Request Insight BigQuery table for agent session events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'agent_request_event',
      'Request Insight BigQuery table for agent request events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_agent_session_event',
      'Request Insight BigQuery table for remote agent session events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'feature_vector_report',
      'Request Insight BigQuery table for feature vector reports',
      sessionEventBaseSchema + [
        {
          name: 'feature_vector',
          type: 'JSON',
          mode: 'REQUIRED',
          description: 'The feature vector',
          // The goal of feature_vector is to identify a device
          // which might identify a person.
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'source_ip',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The source IP',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'prompt_cache_usage',
      'Request Insight BigQuery table for prompt cache usage events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'slackbot_request',
      'Request Insight BigQuery table for slackbot request events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'slackbot_response',
      'Request Insight BigQuery table for slackbot response events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'completion_post_process',
      'Request Insight BigQuery table for completion post process events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'parenthesis_truncation',
      'Request Insight BigQuery table for parenthesis truncation events',
      requestEventBaseSchema + [
        {
          name: 'original_length',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The length of the original text before any truncation was applied',
        },
        {
          name: 'truncated_length',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The length of the text after truncation (if applied)',
        },
        {
          name: 'path_extension',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The extension of the path (e.g., ".py"), if it matches an allowlisted set of extensions.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'share_save_chat_request',
      'Request Insight BigQuery table for save-chat request events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'share_save_chat_response',
      'Request Insight BigQuery table for save-chat response events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'share_get_chat_request',
      'Request Insight BigQuery table for get-chat request events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'share_get_chat_response',
      'Request Insight BigQuery table for get-chat response events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'slackbot_installation_event',
      'Request Insight BigQuery table for slackbot installation events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'github_app_installation_event',
      'Request Insight BigQuery table for github app installation events',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'add_user_to_tenant',
      'Request Insight BigQuery table for add user to tenant events',
      tenantEventBaseSchema + [
        {
          // This is a UUID, not an email.
          name: 'augment_user_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The id of the user that was added to the tenant',
        },
        {
          // This is what other tables like request_metadata call a user_id.
          // TODO(jacqueline): I think we should migrate other tables' user_id columns to be named
          // user_email instead, and start passing the actual UUID around as the id instead.
          name: 'user_email',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The email of the user that was added to the tenant.',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remove_user_from_tenant',
      'Request Insight BigQuery table for remove user from tenant events',
      tenantEventBaseSchema + [
        {
          name: 'augment_user_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The id of the user that was added to the tenant',
        },
        {
          name: 'user_email',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The email of the user that was added to the tenant.',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'invite_user_to_tenant',
      'Request Insight BigQuery table for invite user to tenant events',
      tenantEventBaseSchema + [
        {
          name: 'inviter_email',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The email of the user that invited the other user to the tenant.',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'invitee_email',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The email of the user that was invited to the tenant.',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'delete_invitation',
      'Request Insight BigQuery table for delete invitation events',
      tenantEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'glean_request',
      'Request Insight BigQuery table for Glean requests',
      requestEventBaseSchema + [
        {
          name: 'user_query_character_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of characters in the user query containing formatting conversation history and prompt.',
        },
        {
          name: 'user_query_line_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of lines in the user query containing formatting conversation history and prompt.',
        },
        {
          name: 'user_query_word_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'Number of words in the user query containing formatting conversation history and prompt.',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'update_subscription',
      'Request Insight BigQuery table for update subscription events',
      tenantEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'create_tenant_for_team',
      'Request Insight BigQuery table for tenant creation events',
      tenantEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'accept_invitation',
      'Request Insight BigQuery table for accept invitation events',
      tenantEventBaseSchema + [
        {
          name: 'invitee_email',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The email of the user that accepted the invitation.',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'decline_invitation',
      'Request Insight BigQuery table for decline invitation events',
      tenantEventBaseSchema + [
        {
          name: 'invitee_email',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The email of the user that declined the invitation.',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'glean_response',
      'Request Insight BigQuery table for Glean responses',
      requestEventBaseSchema + [
        {
          name: 'has_oauth_url',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the response contains an oauth url which indicates the user is not authenticated with Glean.',
        },
        {
          name: 'generated_search_query_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of search queries generated by the third party chat model.',
        },
        {
          name: 'generated_search_query_words',
          type: 'INTEGER',
          mode: 'REPEATED',
          description: 'Number of words in each of the generated search queries.',
        },
        {
          name: 'result_statistics',
          type: 'RECORD',
          mode: 'REPEATED',
          description: 'Statistics of each result.',
          fields: [
            {
              name: 'document_id',
              type: 'STRING',
              mode: 'REQUIRED',
              description: 'The id of the document',
            },
            {
              name: 'source',
              type: 'STRING',
              mode: 'REQUIRED',
              description: 'The name of the source',
            },
            {
              name: 'created_at',
              type: 'TIMESTAMP',
              mode: 'NULLABLE',
              description: 'The created at timestamp of the document',
            },
            {
              name: 'updated_at',
              type: 'TIMESTAMP',
              mode: 'NULLABLE',
              description: 'The updated at timestamp of the document',
            },
            {
              name: 'obfuscated_author_id',
              type: 'STRING',
              mode: 'NULLABLE',
              description: 'The obfuscated author id of the document',
            },
            {
              name: 'character_count',
              type: 'INTEGER',
              mode: 'REQUIRED',
              description: 'Total number of characters from this result',
            },
            {
              name: 'line_count',
              type: 'INTEGER',
              mode: 'REQUIRED',
              description: 'Total number of lines from this result',
            },
            {
              name: 'snippet_count',
              type: 'INTEGER',
              mode: 'REQUIRED',
              description: 'Total number of snippets from this result',
            },
            {
              name: 'nested_document_count',
              type: 'INTEGER',
              mode: 'REQUIRED',
              description: 'Total number of nested documents from this result',
            },
          ],
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'glean_oauth_url_request',
      'Request Insight BigQuery table for Glean OAuth URL requests',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'glean_oauth_url_response',
      'Request Insight BigQuery table for Glean OAuth URL responses',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'customer_ui_session_event',
      'Request Insight BigQuery table for customer UI session events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_tool_call_request',
      'Request Insight BigQuery table for remote tool call requests',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_tool_call_response',
      'Request Insight BigQuery table for remote tool call responses from client POV',
      requestEventBaseSchema + [
        {
          name: 'tool_response_size_bytes',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The size of the tool response in bytes',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'request_blocked',
      'Request Insight BigQuery table for request blocked events',
      requestEventBaseSchema + [
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the request. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        {
          name: 'user_email',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The user email of the request, if any',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'check_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of check that caused the request to be blocked',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'request_suspicious',
      'Request Insight BigQuery table for suspicious request events',
      requestEventBaseSchema + [
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the request. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        {
          name: 'user_email',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The user email of the request, if any',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'check_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of check that flagged the request as suspicious',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'token_exchange_error',
      'Request Insight BigQuery table for token exchange errors',
      requestEventBaseSchema + [
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the request. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        {
          name: 'user_email',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The user email of the request, if any',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'reason',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The reason for the token exchange error',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'daily_request_limit_exceeded',
      'Request Insight BigQuery table for daily request limit exceeded events',
      requestEventBaseSchema + [
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the request. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        {
          name: 'limit',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The configured daily request limit that was exceeded',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'find_missing',
      'Request Insight BigQuery table for FindMissing request events',
      requestEventBaseSchema + [
        {
          name: 'model_name',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The model name used in the FindMissing request',
        },
        {
          name: 'blob_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The number of blobs in the request',
        },
        {
          name: 'missing_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The number of missing blobs found',
        },
        {
          name: 'nonindexed_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The number of nonindexed blobs found',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'batch_upload',
      'Request Insight BigQuery table for BatchUpload request events',
      requestEventBaseSchema + [
        {
          name: 'blob_count',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The number of blobs in the upload request',
        },
        {
          name: 'total_size',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'The total size of all blobs in bytes',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'chat_user_message',
      'Request Insight BigQuery table for user messages in chat conversations',
      requestEventBaseSchema + [
        {
          name: 'chat_mode',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The chat mode used for this message (CHAT, AGENT, MEMORIES, etc.)',
        },
        {
          name: 'chat_history_length',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The length of the chat history at the time of the message',
        },
        {
          name: 'image_node_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Number of image nodes in the message',
        },
        {
          name: 'character_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Character count of the text content',
        },
        {
          name: 'line_count',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'Line count of the text content',
        },
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the user who sent the message',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, SLACK, or INTERNAL_IAP',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_create_request',
      'Request Insight BigQuery table for requests to the remote agents CreateAgent RPC',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_create_response',
      'Request Insight BigQuery table for responses from the remote agents CreateAgent RPC',
      requestEventBaseSchema + [
        {
          name: 'agent_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the created agent',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_chat_request',
      'Request Insight BigQuery table for requests to the remote agents Chat RPC',
      requestEventBaseSchema + [
        {
          name: 'agent_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the agent to send the chat request to',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_chat_response',
      'Request Insight BigQuery table for responses from the remote agents Chat RPC',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_interrupt_request',
      'Request Insight BigQuery table for requests to the remote agents Interrupt RPC',
      requestEventBaseSchema + [
        {
          name: 'agent_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the agent to interrupt',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_interrupt_response',
      'Request Insight BigQuery table for responses from the remote agents Interrupt RPC',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_delete_request',
      'Request Insight BigQuery table for requests to the remote agents Delete RPC',
      requestEventBaseSchema + [
        {
          name: 'agent_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the agent to delete',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_delete_response',
      'Request Insight BigQuery table for responses from the remote agents Delete RPC',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_add_ssh_key_request',
      'Request Insight BigQuery table for requests to the remote agents AddSSHKey RPC',
      requestEventBaseSchema + [
        {
          name: 'agent_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the agent to add the SSH key to',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_add_ssh_key_response',
      'Request Insight BigQuery table for responses from the remote agents AddSSHKey RPC',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_resume_request',
      'Request Insight BigQuery table for requests to the remote agents Resume RPC',
      requestEventBaseSchema + [
        {
          name: 'agent_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the agent to resume',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_resume_response',
      'Request Insight BigQuery table for responses from the remote agents Resume RPC',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_pause_request',
      'Request Insight BigQuery table for requests to the remote agents Pause RPC',
      requestEventBaseSchema + [
        {
          name: 'agent_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the agent to pause',
        },
      ],
    ),
    bigqueryLib.tableDefinition(
      'remote_agents_pause_response',
      'Request Insight BigQuery table for responses from the remote agents Pause RPC',
      requestEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'recaptcha',
      'Request Insight BigQuery table for reCAPTCHA events',
      genericEventBaseSchema + [
        {
          name: 'email',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The email of the user who triggered the reCAPTCHA',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'source_ip',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The source IP of the user who triggered the reCAPTCHA',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
      enableClustering=false,
    ),
    bigqueryLib.tableDefinition(
      'verisoul',
      'Request Insight BigQuery table for verisoul scores',
      genericEventBaseSchema + [
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the user who performed the event. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        {
          name: 'report',
          type: 'JSON',
          mode: 'REQUIRED',
          description: 'The report from verisoul',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
      // We cluster on tenant ID and there is no tenant ID in this schema
      enableClustering=false,
    ),
    bigqueryLib.tableDefinition(
      'verosint',
      'Request Insight BigQuery table for verosint scores',
      genericEventBaseSchema + [
        {
          name: 'opaque_user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The opaque user ID of the user who performed the event. This ID can have different meanings, depending on the value of user_id_type.',
        },
        {
          name: 'user_id_type',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The type of the opaque_user_id: AUGMENT, API_TOKEN, or SLACK',
        },
        {
          name: 'report',
          type: 'JSON',
          mode: 'REQUIRED',
          description: 'The report from verosint',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
      ],
      // We cluster on tenant ID and there is no tenant ID in this schema
      enableClustering=false,
    ),
    bigqueryLib.tableDefinition(
      'purchase_credits',
      'Request Insight BigQuery table for purchase credits events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'cancel_subscription',
      'Request Insight BigQuery table for cancel subscription events',
      sessionEventBaseSchema,
    ),
    bigqueryLib.tableDefinition(
      'unschedule_pending_subscription_cancellation',
      'Request Insight BigQuery table for unschedule pending subscription cancellation events',
      sessionEventBaseSchema,
    ),

    // Add tables above this point and views below.
    // NB: View queries cannot end in a semicolon, or they will cause bigquery-emulator to hang.
    //     https://github.com/goccy/bigquery-emulator/issues/300
    // A list of tenant ids for contractors, pentesters, or other tenants who we don't want to count
    // towards metrics about our customers.
    bigqueryLib.viewDefinition(
      'noncustomer_tenants',
      'List of non-customer tenants',
      |||
        SELECT *
        FROM UNNEST([
          STRUCT('pentest0' AS tenant, '4187b61482ca239ae58e6ab592f81401' AS tenant_id),
          STRUCT('pentest1' AS tenant, '4786613d86d0ad37e2339d0bb0c22770' AS tenant_id),
          STRUCT('aitutor-turing' AS tenant, '82951ccf8d0daaae79bda4489968b8fa' AS tenant_id),
          STRUCT('aitutor-pareto' AS tenant, '369110aa3f723c10f6822ebe8198530d' AS tenant_id),
          STRUCT('aitutor-mercor' AS tenant, 'd33d1ede5c91a1bc179f480caffb1470' AS tenant_id),
          STRUCT('atredis' AS tenant, '44a15159fd17d1360275780c7580a503' AS tenant_id),
          STRUCT('atredistemp' AS tenant, 'c87f158ae93967e4c908bbed44c21bd6' AS tenant_id),
          STRUCT('augmentdemo' AS tenant, '13bb4248bc34d6aeecd3108941b5bfe2' AS tenant_id)
        ])
      |||
    ),
    // View to filter out contractors and pentesters. This is useful for product dashboards, where
    // we don't care about these tenants.
    bigqueryLib.viewDefinition(
      'customers_completion_resolution',
      'Completion resolutions with contractors and pentesters filtered out',
      |||
        SELECT *
        FROM %(dataset)s.completion_resolution
        -- keep deleted customers in this list, unless we delete their data from request insight too
        WHERE tenant_id NOT IN (SELECT tenant_id FROM %(dataset)s.noncustomer_tenants)
      ||| % {
        dataset: dataset,
      }
    ),

    // View to filter out non-human requests (e.g., health checks and bots). This is useful for the
    // customer UI, where we only want to show information that originated from the customer.
    bigqueryLib.viewDefinition(
      'human_request_metadata',
      'Metadata with non-human requests filtered out',
      |||
        SELECT *
        FROM %s.request_metadata
        WHERE user_agent NOT LIKE 'Augment-EvalHarness%%'
        AND user_agent NOT LIKE 'AugmentHealthCheck%%'
        AND user_agent NOT LIKE 'api_proxy_client%%'
        AND user_agent NOT LIKE 'augment.info-chat%%'
      ||| % dataset
    ),
    // View to filter out non-customer tenants (e.g., pentesters) and non-human requests (e.g.,
    // health checks and bots). This is useful for product dashboards, where we don't care about
    // non-customer requests.
    bigqueryLib.viewDefinition(
      'customers_request_metadata',
      'Metadata with non-customer tenants and non-human requests filtered out',
      |||
        SELECT *
        FROM %(dataset)s.request_metadata
        WHERE user_agent NOT LIKE 'Augment-EvalHarness%%'
          AND user_agent NOT LIKE 'AugmentHealthCheck%%'
          AND user_agent NOT LIKE 'api_proxy_client%%'
          AND user_agent NOT LIKE 'augment.info-chat%%'
          AND tenant_id NOT IN (SELECT tenant_id FROM %(dataset)s.noncustomer_tenants)
      ||| % {
        dataset: dataset,
      }
    ),
    // View for looking up the request id given a slack message timestamp. Note that the timestamps
    // in this table don't have "." in them, because the timestamps from Slack urls don't.
    bigqueryLib.viewDefinition(
      'slackbot_response_lookup',
      'Table for looking up a request id given a slack message timestamp',
      |||
        SELECT
          request_id, tenant, tenant_id, shard_namespace, time,
          ARRAY(
            SELECT REPLACE(JSON_VALUE(events, '$.post_message.response_message_timestamp'), ".", "")
            FROM UNNEST(JSON_QUERY_ARRAY(sanitized_json, '$.response_events')) events
            WHERE JSON_QUERY(events, '$.post_message') IS NOT NULL
          ) AS slack_response_timestamps,
          JSON_VALUE(sanitized_json, '$.channel') AS channel
        FROM %s.slackbot_response
      ||| % dataset
    ),

    // Materialized views that make it a little easier to query the different types of Slackbot
    // events. These save a bit of annoying JSON parsing syntax.
    bigqueryLib.materializedViewDefinition(
      'slackbot_app_mention',
      'Slackbot request events when somebody calls the bot with "@Augment"',
      |||
        SELECT request_id, tenant, tenant_id, shard_namespace, time, sanitized_json, sanitized_json.slack_event.app_mention
        FROM %s.slackbot_request
        WHERE sanitized_json.slack_event.app_mention IS NOT NULL
      ||| % dataset,
      isIncremental=true
    ),
    bigqueryLib.materializedViewDefinition(
      'slackbot_message',
      'Slackbot request events when somebody DMs the bot',
      |||
        SELECT request_id, tenant, tenant_id, shard_namespace, time, sanitized_json, sanitized_json.slack_event.message
        FROM %s.slackbot_request
        WHERE sanitized_json.slack_event.message IS NOT NULL
      ||| % dataset,
      isIncremental=true
    ),
    bigqueryLib.materializedViewDefinition(
      'slackbot_reaction_added',
      'Slackbot request events when somebody reacts to a message',
      |||
        SELECT request_id, tenant, tenant_id, shard_namespace, time, sanitized_json, sanitized_json.slack_event.reaction_added
        FROM %s.slackbot_request
        WHERE sanitized_json.slack_event.reaction_added IS NOT NULL
      ||| % dataset,
      isIncremental=true
    ),
    bigqueryLib.materializedViewDefinition(
      'customer_ui_session_start',
      'Customer UI session start events',
      |||
        SELECT
          session_id, tenant, tenant_id, shard_namespace, time,
          JSON_VALUE(sanitized_json, '$.user_id') as user_id,
          JSON_VALUE(sanitized_json, '$.session_start') as session_start,
          sanitized_json
        FROM %s.customer_ui_session_event
        WHERE sanitized_json.session_start IS NOT NULL
      ||| % dataset,
      isIncremental=true
    ),
    bigqueryLib.materializedViewDefinition(
      'customer_ui_session_end',
      'Customer UI session end events',
      |||
        SELECT
          session_id, tenant, tenant_id, shard_namespace, time,
          JSON_VALUE(sanitized_json, '$.user_id') as user_id,
          JSON_VALUE(sanitized_json, '$.session_end') as session_end,
          sanitized_json
        FROM %s.customer_ui_session_event
        WHERE sanitized_json.session_end IS NOT NULL
      ||| % dataset,
      isIncremental=true
    ),

    // Materialized view for aggregating user activity by user, tenant, session and type.
    bigqueryLib.materializedViewDefinition(
      'request_aggregates_by_session',
      'Aggregated request counts by user, tenant, session and type for misuse monitoring',
      |||
        SELECT
          tenant_id,
          opaque_user_id,
          session_id,
          request_type,
          COUNT(request_id) as request_count,
          MIN(time) as first_request_time,
          MAX(time) as last_request_time
        FROM %s.request_metadata
        GROUP BY 1,2,3,4
      ||| % dataset,
      isIncremental=true,
      timePartitioned=false
    ),

    // Manually synced tables below this point. These tables are manually synced from their source
    // of truth, rather than a log of events. Note that these tables aren't partitioned or
    // clustered. Technically they could be, but it isn't clear what we would partition or cluster
    // by, and the size of these tables won't be a concern for the forseeable future.

    // This table corresponds to the Tenant proto in tenant_watcher.proto and should be kept in sync
    // with the TenantRow struct in services/request_insight/sync_job/sync.go.
    bigqueryLib.tableDefinition(
      'tenant',
      'Table of tenant information, synced hourly',
      [
        {
          name: 'id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The ID of the tenant',
        },
        {
          name: 'name',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The name of the tenant',
        },
        {
          name: 'shard_namespace',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The namespace of the shard the tenant belongs to',
        },
        {
          name: 'cloud',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The cloud of the tenant',
        },
        {
          name: 'tier',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The tier of the tenant (TENANT_TIER_UNKNOWN, ENTERPRISE, PROFESSIONAL, COMMUNITY)',
        },
        {
          name: 'other_namespace',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'A namespace that the tenant is allowed to operate in but is not preferred',
        },
        {
          name: 'encryption_key_name',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Name of the KMS key to use for tenant encryption, if any',
        },
        {
          name: 'encryption_key_ttl',
          type: 'INTEGER',
          mode: 'NULLABLE',
          description: 'TTL for the encryption key in seconds, if any',
        },
        {
          name: 'version',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The version of the tenant',
        },
        {
          name: 'deleted_at',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The time when the tenant was deleted, if relevant',
        },
        {
          name: 'support_access_control',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether support access control is enabled for the tenant',
        },
        {
          name: 'multi_tenant_allowed',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether multi-tenancy is allowed for the tenant',
        },
        {
          name: 'support_tenant',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether the tenant is a support tenant',
        },
        {
          name: 'default_support_tenant',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether the tenant is the default tenant in the support UI',
        },
        {
          name: 'slackbot_allowlist_channels',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'List of Slackbot channels that are allowed for the tenant',
        },
        {
          name: 'block_genie_request_access',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether Genie access requests are blocked for the tenant',
        },
        {
          name: 'ip_allowlist',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'IP allowlist for the tenant',
        },
        {
          name: 'is_self_serve_team',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether the tenant is a self-serve team',
        },
        {
          name: 'is_legacy_self_serve_team',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether the tenant is a legacy self-serve team',
        },
        {
          name: 'domain',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The domain whose users are routed to the tenant',
        },
        {
          name: 'username_domains',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Domains whose users are routed to the tenant',
        },
        {
          name: 'email_address_domains',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Email address domains whose users are routed to the tenant',
        },
        {
          name: 'allowed_identity_providers',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'Identity providers which are allowed to authenticate users',
        },
      ],
      enableClustering=false,
      enableTimePartitioning=false
    ),

    // This table corresponds to the User proto in auth_entities.proto and should be kept in sync
    // with the UserRow struct in services/request_insight/sync_job/sync.go.
    bigqueryLib.tableDefinition(
      'user',
      'Table of user information, synced hourly',
      [
        {
          name: 'id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The ID of the user',
        },
        {
          name: 'email',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The email of the user',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'tenant_ids',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'The IDs of the tenants the user belongs to',
        },
        {
          name: 'created_at',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The time when the user was created',
        },
        {
          name: 'in_usa',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The time when the user said they were in the USA, if relevant',
        },
        {
          name: 'stripe_customer_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Stripe customer ID of the user, if relevant',
        },
        {
          name: 'blocked',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether the user is blocked',
        },
        {
          name: 'idp_user_ids',
          type: 'STRING',
          mode: 'REPEATED',
          description: 'The IDP user ID(s) of the user',
          policyTags: if piiPolicyTag != null then {
            names: [
              piiPolicyTag,
            ],
          } else {},
        },
        {
          name: 'subscription_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Stripe subscription ID of the user, if relevant',
        },
        {
          name: 'orb_customer_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Orb customer ID of the user, if relevant',
        },
        {
          name: 'check_subscription_status',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether to check the user subscription status',
        },
        {
          name: 'billing_method',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The billing method used by the user (BILLING_METHOD_STRIPE, BILLING_METHOD_ORB, etc.)',
        },
        {
          name: 'orb_subscription_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Orb subscription ID of the user, if relevant',
        },
        {
          name: 'subscription_creation_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'ID used to help prevent duplicate subscription creations',
        },
        {
          name: 'suspensions',
          type: 'RECORD',
          mode: 'REPEATED',
          description: 'Active user suspensions',
          fields: [
            {
              name: 'suspension_id',
              type: 'STRING',
              mode: 'REQUIRED',
              description: 'Unique identifier for the suspension',
            },
            {
              name: 'created_time',
              type: 'TIMESTAMP',
              mode: 'REQUIRED',
              description: 'When the suspension was created',
            },
            {
              name: 'suspension_type',
              type: 'STRING',
              mode: 'REQUIRED',
              description: 'Type of suspension (USER_SUSPENSION_TYPE_UNKNOWN, USER_SUSPENSION_TYPE_API_ABUSE, USER_SUSPENSION_TYPE_FREE_TRIAL_ABUSE, USER_SUSPENSION_TYPE_COMMUNITY_ABUSE)',
            },
            {
              name: 'evidence',
              type: 'STRING',
              mode: 'NULLABLE',
              description: 'Evidence for the suspension',
            },
          ],
        },
        {
          name: 'tier_change',
          type: 'RECORD',
          mode: 'NULLABLE',
          description: 'Information about a pending tier change',
          fields: [
            {
              name: 'id',
              type: 'STRING',
              mode: 'NULLABLE',
              description: 'Unique identifier for the tier change',
            },
            {
              name: 'target_tier',
              type: 'STRING',
              mode: 'NULLABLE',
              description: 'The target tier for the change (UNKNOWN_TIER, COMMUNITY, PROFESSIONAL)',
            },
            {
              name: 'created_at',
              type: 'TIMESTAMP',
              mode: 'NULLABLE',
              description: 'When the tier change was created',
            },
            {
              name: 'updated_at',
              type: 'TIMESTAMP',
              mode: 'NULLABLE',
              description: 'When the tier change was last updated',
            },
          ],
        },
        {
          name: 'subscription_creation_info',
          type: 'RECORD',
          mode: 'NULLABLE',
          description: 'Information about subscription creation status',
          fields: [
            {
              name: 'id',
              type: 'STRING',
              mode: 'NULLABLE',
              description: 'Unique identifier for the subscription creation',
            },
            {
              name: 'status',
              type: 'STRING',
              mode: 'NULLABLE',
              description: 'The status of the subscription creation (SUCCESS, PENDING)',
            },
            {
              name: 'created_at',
              type: 'TIMESTAMP',
              mode: 'NULLABLE',
              description: 'When the subscription creation info was created',
            },
            {
              name: 'updated_at',
              type: 'TIMESTAMP',
              mode: 'NULLABLE',
              description: 'When the subscription creation info was last updated',
            },
          ],
        },
        {
          name: 'suspension_exempt',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'User is exempt from suspensions',
        },
      ],
      enableClustering=false,
      enableTimePartitioning=false
    ),

    // This table corresponds to the Subscription proto in auth_entities.proto and should be kept in sync
    // with the SubscriptionRow struct in services/request_insight/sync_job/sync.go.
    bigqueryLib.tableDefinition(
      'subscription',
      'Table of subscription information, synced hourly',
      [
        {
          name: 'subscription_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The ID of the subscription',
        },
        {
          name: 'stripe_customer_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The Stripe customer ID associated with the subscription',
        },
        {
          name: 'price_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The Stripe price ID of the subscription',
        },
        {
          name: 'status',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The status of the subscription (UNKNOWN, INCOMPLETE, INCOMPLETE_EXPIRED, TRIALING, ACTIVE, PAST_DUE, CANCELED, UNPAID, PAUSED)',
        },
        {
          name: 'seats',
          type: 'INTEGER',
          mode: 'REQUIRED',
          description: 'The number of seats in the subscription',
        },
        {
          name: 'start_date',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The start date of the subscription',
        },
        {
          name: 'trial_end',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The end date of the trial period, if relevant',
        },
        {
          name: 'end_date',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The end date of the subscription, if relevant',
        },
        {
          name: 'cancel_at_period_end',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the subscription will be canceled at the end of the current period',
        },
        {
          name: 'has_payment_method',
          type: 'BOOLEAN',
          mode: 'REQUIRED',
          description: 'Whether the customer has a payment method associated with the subscription',
        },
        {
          name: 'tenant_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the tenant that owns the subscription, if relevant',
        },
        {
          name: 'user_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the user that owns the subscription, if relevant',
        },
        {
          name: 'created_at',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The time when the subscription was created',
        },
        {
          name: 'updated_at',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'The time when the subscription was last updated',
        },
        {
          name: 'orb_customer_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Orb customer ID, not set for Stripe',
        },
        {
          name: 'orb_status',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Orb subscription status (ORB_STATUS_UNKNOWN, ORB_STATUS_UPCOMING, ORB_STATUS_ACTIVE, ORB_STATUS_ENDED), not set for Stripe',
        },
        {
          name: 'has_unpaid_invoice',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether the user has an unpaid invoice in Orb, not set for Stripe',
        },
        {
          name: 'external_plan_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'External plan id of the current plan of the Orb subscription, not set for Stripe',
        },
        {
          name: 'billing_method',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Billing method of this subscription (BILLING_METHOD_UNKNOWN, BILLING_METHOD_STRIPE, BILLING_METHOD_ORB, etc.)',
        },
        {
          name: 'seats_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Orb seats price ID',
        },
        {
          name: 'included_credits_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Orb included credits price ID',
        },
        {
          name: 'credits_per_month',
          type: 'FLOAT',
          mode: 'NULLABLE',
          description: 'Orb credits per month',
        },
        {
          name: 'usage_balance_depleted',
          type: 'BOOLEAN',
          mode: 'NULLABLE',
          description: 'Whether the user has used up all their credits for the month',
        },
      ],
      enableClustering=false,
      enableTimePartitioning=false
    ),

    // This table corresponds to the TenantSubscriptionMapping proto in auth_entities.proto and should be kept in sync
    // with the TenantSubscriptionMappingRow struct in services/request_insight/sync_job/sync.go.
    bigqueryLib.tableDefinition(
      'tenant_subscription_mapping',
      'Table of tenant subscription mapping information, synced hourly',
      [
        {
          name: 'tenant_id',
          type: 'STRING',
          mode: 'REQUIRED',
          description: 'The tenant ID',
        },
        {
          name: 'stripe_subscription_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Stripe subscription ID',
        },
        {
          name: 'stripe_customer_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Stripe customer ID associated with this tenant',
        },
        {
          name: 'orb_customer_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Orb customer ID associated with this tenant',
        },
        {
          name: 'orb_subscription_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The Orb subscription ID associated with this tenant',
        },
        {
          name: 'billing_method',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'Billing method used by this tenant (BILLING_METHOD_UNKNOWN, BILLING_METHOD_STRIPE, BILLING_METHOD_ORB, etc.)',
        },
        {
          name: 'created_at',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'When the mapping was created',
        },
        {
          name: 'plan_change_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The ID of the plan change if one is in progress',
        },
        {
          name: 'plan_change_target_orb_plan_id',
          type: 'STRING',
          mode: 'NULLABLE',
          description: 'The target Orb plan ID for the plan change',
        },
        {
          name: 'plan_change_created_at',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'When the plan change was created',
        },
        {
          name: 'plan_change_updated_at',
          type: 'TIMESTAMP',
          mode: 'NULLABLE',
          description: 'When the plan change was last updated',
        },
      ],
      enableClustering=false,
      enableTimePartitioning=false
    ),
  ]
