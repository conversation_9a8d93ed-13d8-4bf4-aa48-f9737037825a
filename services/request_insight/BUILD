load("@python_pip//:requirements.bzl", "requirement")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg", "kubecfg_library")
load("//tools/bzl:metadata.bzl", "metadata_test")
load("//tools/bzl:python.bzl", "py_binary", "py_grpc_library", "py_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

proto_library(
    name = "request_insight_proto",
    srcs = ["request_insight.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_proto",
        "//base/diff_utils:edit_events_proto",
        "//services/agents:agents_proto",
        "//services/auth/central/server:auth_entities_proto",
        "//services/autofix:autofix_proto",
        "//services/chat_host:chat_proto",
        "//services/completion_host:completion_proto",
        "//services/edit_host:edit_proto",
        "//services/integrations/github:github_event_proto",
        "//services/integrations/glean:glean_proto",
        "//services/integrations/slack_bot:slack_event_proto",
        "//services/next_edit_host:next_edit_proto",
        "//services/remote_agents:remote_agents_proto",
        "//services/share:share_proto",
        "//services/tenant_watcher:tenant_watcher_proto",
        "@googleapis//google/rpc:status_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "request_insight_py_proto",
    protos = [":request_insight_proto"],
    visibility = [
        # NOTE(arun): We make an exception for the request insight proto to be read
        # when creating datasets from request insight.
        "//base/datasets:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//services/agents:agents_py_proto",
        "//services/auth/central/server:auth_entities_py_proto",
        "//services/autofix:autofix_py_proto",
        "//services/chat_host:chat_proto_py_proto",
        "//services/completion_host:completion_proto_py_proto",
        "//services/edit_host:edit_proto_py_proto",
        "//services/integrations/github:github_event_py_proto",
        "//services/integrations/glean:glean_py_proto",
        "//services/integrations/slack_bot:slack_event_py_proto",
        "//services/next_edit_host:next_edit_proto_py_proto",
        "//services/remote_agents:remote_agents_py_proto",
        "//services/share:share_proto_py_proto",
        "//services/tenant_watcher:tenant_watcher_py_proto",
        "//third_party/proto:googleapis_status_py_proto",
    ],
)

go_grpc_library(
    name = "request_insight_go_proto",
    importpath = "github.com/augmentcode/augment/services/request_insight/proto",
    proto = ":request_insight_proto",
    visibility = [
        "//clients/beachhead/img:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        "//base/blob_names:blob_names_go_proto",
        "//base/diff_utils:edit_events_go_proto",
        "//services/agents:agents_go_proto",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/autofix:autofix_go_grpc",
        "//services/chat_host:chat_host_go_proto",
        "//services/completion_host:completion_host_go_proto",
        "//services/edit_host:edit_proto_go_proto",
        "//services/integrations/github:github_event_go_proto",
        "//services/integrations/glean:glean_go_proto",
        "//services/integrations/slack_bot:slack_event_go_proto",
        "//services/next_edit_host:next_edit_proto_go_proto",
        "//services/remote_agents:remote_agents_go_proto",
        "//services/share:share_go_proto",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "@org_golang_google_genproto_googleapis_rpc//status:go_default_library",
    ],
)

ts_proto_library(
    name = "request_insight_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":request_insight_proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/blob_names:blob_names_ts_proto",
        "//base/diff_utils:edit_events_ts_proto",
        "//services/agents:agents_ts_proto",
        "//services/auth/central/server:auth_entities_ts_proto",
        "//services/autofix:autofix_ts_proto",
        "//services/chat_host:chat_host_ts_proto",
        "//services/completion_host:completion_host_ts_proto",
        "//services/edit_host:edit_host_ts_proto",
        "//services/integrations/github:github_event_ts_proto",
        "//services/integrations/glean:glean_ts_proto",
        "//services/integrations/slack_bot:slack_event_ts_proto",
        "//services/next_edit_host:next_edit_host_ts_proto",
        "//services/remote_agents:remote_agents_ts_proto",
        "//services/share:share_ts_proto",
        "//services/tenant_watcher:tenant_watcher_ts_proto",
        "//third_party/proto:googleapis_status_ts_proto",
    ],
)

pytest_test(
    name = "request_insight_proto_test",
    srcs = ["request_insight_proto_test.py"],
    deps = [
        ":request_insight_py_proto",
    ],
)

metadata_test(
    name = "metadata_test",
    timeout = "long",
    src = "METADATA.jsonnet",
    deps = [
        ":core_kubecfg",
        ":external_service_accounts_kubecfg",
        ":monitoring_kubecfg",
        ":pubsub_kubecfg",
        "//deploy/tenants:namespaces",
    ],
)

kubecfg(
    name = "external_service_accounts_kubecfg",
    src = "external_service_accounts.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        ":external_service_accounts_lib",
        "//deploy/common:cloud_info",
        "//deploy/gcp:gcp-lib",
    ],
)

kubecfg_library(
    name = "external_service_accounts_lib",
    srcs = ["external_service_accounts_lib.jsonnet"],
    visibility = ["//services/request_insight:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//deploy/common:lib",
    ],
)

kubecfg(
    name = "core_kubecfg",
    src = "core_deploy.jsonnet",
    lint_allow_multiple_apps = True,
    visibility = [
        "//services/content_manager/test:__subpackages__",
        "//services/deploy:__subpackages__",
        "//services/inference_host/test:__subpackages__",
        "//services/memstore/test:__subpackages__",
        "//services/request_insight:__subpackages__",
        "//services/tenant_watcher/test:__subpackages__",
        "//services/token_exchange/test:__subpackages__",
    ],
    deps = [
        ":pubsub_lib",
    ],
)

# Used in metadata to deploy the RI pub/sub topic and config before RI clients.
kubecfg(
    name = "pubsub_kubecfg",
    src = "pubsub_deploy.jsonnet",
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        "//deploy/common:cloud_info",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

# Used in core_deploy.jsonnet to deploy the RI pub/sub topic and config alongside
# request-insight-core in dev deployments.
kubecfg_library(
    name = "pubsub_lib",
    srcs = ["pubsub_deploy.jsonnet"],
    visibility = ["//services/request_insight:__subpackages__"],
    deps = [
        "//deploy/common:cloud_info",
        "//services/request_insight/publisher:publisher_lib",
    ],
)

kubecfg(
    name = "monitoring_kubecfg",
    src = "monitoring.jsonnet",
    cluster_wide = True,
    deps = [
        "//deploy/gcp:monitoring-lib",
    ],
)
