"""Unit tests BigQueryPersistence."""

import base64
from datetime import datetime
from unittest.mock import MagicMock
from google.protobuf import timestamp_pb2
from google.rpc import status_pb2

import pytest
from prometheus_client import REGISTRY

from google.protobuf.timestamp_pb2 import Timestamp
import services.request_insight.request_insight_pb2 as request_insight_pb2
from base.blob_names import blob_names_pb2
from services.auth.central.server import auth_entities_pb2
from services.chat_host import chat_pb2
from services.completion_host import completion_pb2
from services.edit_host import edit_pb2
from services.next_edit_host import next_edit_pb2
from services.share import share_pb2
from services.request_insight.bigquery_exporter.bigquery_persistence import (
    BigQueryPersistence,
    _sanitize_agent_tracing_data,
    _sanitize_chat_user_message,
    _sanitize_customer_ui_session_end,
    _sanitize_customer_ui_session_start,
    _sanitize_flush_memories_data,
    _sanitize_initial_orientation_data,
    _sanitize_remember_tool_call_data,
    _sanitize_classify_and_distill_data,
    _sanitize_url,
)
from services.request_insight.bigquery_exporter.config import DataConfig
from services.integrations.slack_bot import slack_event_pb2
from services.integrations.glean import glean_pb2
from services.agents import agents_pb2
from services.tenant_watcher import tenant_watcher_pb2
from services.remote_agents import remote_agents_pb2


@pytest.fixture(scope="function")
def bigquery_persistence():
    data_config = DataConfig(
        path_keywords=["test"],
        instruction_keywords=[
            "comment",
            "document",
            "error",
            "explain",
            "fix",
            "imports",
            "improve",
            "log",
            "optimize",
            "refactor",
            "reformat",
            "simplify",
            "test",
        ],
        chat_keywords=[
            "comment",
            "document",
            "error",
            "explain",
            "fix",
            "imports",
            "improve",
            "log",
            "optimize",
            "refactor",
            "reformat",
            "simplify",
            "test",
        ],
        keyword_categories={
            "configure": [
                "config",
                "customize",
                "setup",
                "setting",
            ],
            "document": [
                "annotate",
                "annotation",
                "comment",
                "docs",
                "note",
                "outline",
            ],
            "explain": [
                "demo",
                "demonstrate",
                "describe",
                "detail",
                "discuss",
                "example",
                "how",
                "interpret",
                "learn",
                "practice",
                "summarize",
                "unpack",
                "why",
            ],
            "fix": [
                "bug",
                "clarify",
                "correct",
                "debug",
                "error",
                "exception",
                "invalid",
                "mend",
                "remedy",
                "repair",
                "resolve",
            ],
            "help": [
                "access",
                "aid",
                "advise",
                "answer",
                "assist",
                "coach",
                "criticize",
                "evaluate",
                "feedback",
                "guide",
                "problem",
                "suggest",
                "support",
                "teach",
                "troubleshoot",
            ],
            "improve": [
                "advance",
                "enhance",
                "enrich",
                "expand",
                "polish",
                "strengthen",
            ],
            "log": [
                "capture",
                "dump",
                "instrument",
                "report",
                "trace",
                "track",
            ],
            "performance": [
                "boost",
                "efficiency",
                "latency",
                "optimize",
                "responsive",
                "scale",
                "speed",
                "throughput",
            ],
            "refactor": [
                "deduplicate",
                "dedup",
                "encapsulate",
                "extract",
                "modular",
                "simplify",
                "modify",
            ],
            "secure": [
                "auth",
                "confidential",
                "encrypt",
                "harden",
                "privacy",
                "prevent",
                "protect",
                "security",
                "sensitive",
                "vulnerability",
            ],
            "test": [
                "validate",
                "verify",
            ],
            "write": [
                "generate",
                "include",
                "create",
                "add",
            ],
        },
        file_extensions=[".py", ".java"],
        terminal_command_prefixes=["git", "git push", "gt"],
    )
    bigquery_persistence = BigQueryPersistence(
        "test_dataset", "test_namespace", data_config
    )
    bigquery_persistence._bigquery_client = MagicMock()
    return bigquery_persistence


@pytest.fixture(scope="function")
def tenant_info():
    return request_insight_pb2.TenantInfo(tenant_name="test_tenant", tenant_id="123")


@pytest.fixture(scope="function")
def remote_agent_config() -> remote_agents_pb2.AgentConfig:
    return remote_agents_pb2.AgentConfig(
        workspace_setup=remote_agents_pb2.WorkspaceSetup(
            github_ref=remote_agents_pb2.GithubRef(
                url="https://github.com/test/repo",
                ref="main",
            ),
            patch="test-patch",
            setup_script="test-setup-script",
            token="test-token",
            api_url="test-api-url",
        ),
        starting_nodes=[
            chat_pb2.ChatRequestNode(
                id=1,
                type=chat_pb2.ChatRequestNodeType.TEXT,
                text_node=chat_pb2.ChatRequestText(content="test-content"),
            )
        ],
        user_guidelines="test-user-guidelines",
        workspace_guidelines="test-workspace-guidelines",
        agent_memories="test-agent-memories",
        model="test-model",
        is_setup_script_agent=False,
    )


@pytest.fixture(scope="function")
def remote_agent_ssh_config() -> remote_agents_pb2.AgentSSHConfig:
    return remote_agents_pb2.AgentSSHConfig(
        public_keys=["test-public-key", "test-public-key-2"],
        hostname="test-hostname",
        ssh_config_options=[
            remote_agents_pb2.SSHConfigOption(key="test-name", value="test-value"),
            remote_agents_pb2.SSHConfigOption(key="test-name-2", value="test-value-2"),
        ],
    )


@pytest.fixture(scope="function")
def remote_agent_agent(
    remote_agent_config, remote_agent_ssh_config
) -> remote_agents_pb2.Agent:
    return remote_agents_pb2.Agent(
        remote_agent_id="test-agent",
        status=remote_agents_pb2.AgentStatus.AGENT_STATUS_STARTING,
        config=remote_agent_config,
        created_at=timestamp_pb2.Timestamp(seconds=1234567890),
        updated_at=timestamp_pb2.Timestamp(seconds=1234567890),
        ssh_config=remote_agent_ssh_config,
    )


def test_request_metadata_row_email_user(bigquery_persistence, tenant_info):
    now = datetime.now()
    request_metadata = request_insight_pb2.RequestMetadata(
        request_type=request_insight_pb2.RequestType.COMPLETION,
        user_id="<EMAIL>",
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        user_email="<EMAIL>",
        session_id="test-session",
        user_agent="test-user-agent",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.request_metadata.CopyFrom(request_metadata)

    request_metadata_row = bigquery_persistence._request_metadata_row(
        "test-request", "test-session", tenant_info, event
    )

    assert request_metadata_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "request_type": "COMPLETION",
        "user_id": "<EMAIL>",
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "user_email": "<EMAIL>",
        "user_agent": "test-user-agent",
        "source_ip": "",
    }, "request_metadata_row does not match expected value"


def test_request_metadata_row_api_token_user(bigquery_persistence, tenant_info):
    now = datetime.now()
    request_metadata = request_insight_pb2.RequestMetadata(
        request_type=request_insight_pb2.RequestType.COMPLETION,
        user_id="test-user",
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.API_TOKEN,
        ),
        session_id="test-session",
        user_agent="test-user-agent",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.request_metadata.CopyFrom(request_metadata)

    request_metadata_row = bigquery_persistence._request_metadata_row(
        "test-request", "test-session", tenant_info, event
    )

    assert request_metadata_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "request_type": "COMPLETION",
        "user_id": "test-user",
        "opaque_user_id": "test-user",
        "user_id_type": "API_TOKEN",
        "user_agent": "test-user-agent",
        "source_ip": "",
    }, "request_metadata_row does not match expected value"


def test_edit_resolution_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    edit_resolution = request_insight_pb2.EditResolution(
        is_accepted=True,
        annotated_text="test-annotated-text",
        annotated_instruction="test-annotated-instruction",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.edit_resolution.CopyFrom(edit_resolution)

    edit_resolution_row = bigquery_persistence._edit_resolution_row(
        "test-request", "test-session", tenant_info, event
    )

    assert edit_resolution_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "is_accepted": True,
        },
        "accepted": True,
    }


def test_instruction_resolution_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    instruction_resolution = request_insight_pb2.InstructionResolution(
        is_accepted_chunks=[True, False],
        is_accept_all=True,
        is_reject_all=True,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.instruction_resolution.CopyFrom(instruction_resolution)

    instruction_resolution_row = bigquery_persistence._instruction_resolution_row(
        "test-request", "test-session", tenant_info, event
    )

    assert instruction_resolution_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "is_accepted_chunks": [True, False],
            "is_accept_all": True,
            "is_reject_all": True,
        },
    }


def test_smart_paste_resolution_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    smart_paste_resolution = request_insight_pb2.SmartPasteResolution(
        is_accepted_chunks=[True, False],
        is_accept_all=True,
        is_reject_all=True,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.smart_paste_resolution.CopyFrom(smart_paste_resolution)

    smart_paste_resolution_row = bigquery_persistence._smart_paste_resolution_row(
        "test-request", "test-session", tenant_info, event
    )

    assert smart_paste_resolution_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "is_accepted_chunks": [True, False],
            "is_accept_all": True,
            "is_reject_all": True,
        },
    }


def test_smart_paste_client_timeline_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    smart_paste_client_timeline = request_insight_pb2.SmartPasteClientTimeline(
        initial_request_time=timestamp_pb2.Timestamp(
            seconds=100,
            nanos=101,
        ),
        stream_finish_time=timestamp_pb2.Timestamp(
            seconds=200,
            nanos=201,
        ),
        apply_time=timestamp_pb2.Timestamp(
            seconds=300,
            nanos=301,
        ),
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.smart_paste_client_timeline.CopyFrom(smart_paste_client_timeline)

    smart_paste_client_timeline_row = (
        bigquery_persistence._smart_paste_client_timeline_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert smart_paste_client_timeline_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "sanitized_json": {
            "initial_request_time": "1970-01-01T00:01:40.000000101Z",
            "stream_finish_time": "1970-01-01T00:03:20.000000201Z",
            "apply_time": "1970-01-01T00:05:00.000000301Z",
        },
        "time": now,
    }


def test_completion_resolution_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    completion_resolution = request_insight_pb2.CompletionResolution(accepted_idx=1)
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_resolution.CopyFrom(completion_resolution)
    completion_resolution_row = bigquery_persistence._completion_resolution_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {"accepted_idx": 1}
    assert completion_resolution_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "accepted": True,
    }


@pytest.mark.parametrize(
    "user_filter_threshold",
    [None, 0.0, 0.5, 1.0],
)
def test_completion_request_row(
    bigquery_persistence, user_filter_threshold, tenant_info
):
    now = datetime.now()
    infer_request = request_insight_pb2.InferRequest(
        model="test-model",
        prompt="test-prompt",
        path="test-path.py",
        memories=["test-memory"],
        top_k=1,
        top_p=0.5,
        temperature=0.1,
        max_tokens=10,
        user_id="test-user",
        session_id="test-session",
        lang="test-lang",
        user_agent="test-user-agent",
        sequence_id=100,
        user_filter_threshold=user_filter_threshold,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.infer_request.CopyFrom(infer_request)
    completion_request_row = bigquery_persistence._completion_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "model": "test-model",
        "top_k": 1,
        "top_p": 0.5,
        "temperature": 0.1,
        "max_tokens": 10,
        "session_id": "test-session",
        "lang": "test-lang",
        "user_agent": "test-user-agent",
        "sequence_id": 100,
    }
    if user_filter_threshold is not None:
        expected_json["user_filter_threshold"] = user_filter_threshold
    assert completion_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "user_id": "test-user",
        "path_keywords": ["test"],
        "path_extension": ".py",
    }


def test_sanitize_chat_user_message():
    """Test that _sanitize_chat_user_message properly sanitizes sensitive fields."""
    # Create a ChatUserMessage with all fields populated
    chat_user_message = request_insight_pb2.ChatUserMessage(
        chat_mode=request_insight_pb2.ChatUserMessage.ChatMode.CHAT_MODE_AGENT,
        chat_history_length=5,
        image_node_count=2,
        character_count=100,
        line_count=10,
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user-id",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
    )

    # Sanitize the message
    sanitized = _sanitize_chat_user_message(chat_user_message)

    # Verify that non-sensitive fields are preserved
    assert (
        sanitized.chat_mode
        == request_insight_pb2.ChatUserMessage.ChatMode.CHAT_MODE_AGENT
    )
    assert sanitized.chat_history_length == 5
    assert sanitized.image_node_count == 2
    assert sanitized.character_count == 100
    assert sanitized.line_count == 10
    assert sanitized.opaque_user_id.user_id == "test-user-id"
    assert (
        sanitized.opaque_user_id.user_id_type
        == auth_entities_pb2.UserId.UserIdType.AUGMENT
    )


def test_path_keywords():
    data_config = DataConfig(
        path_keywords=["test"],
        instruction_keywords=[],
        chat_keywords=[],
        keyword_categories={},
        file_extensions=[],
        terminal_command_prefixes=[],
    )
    bigquery_persistence = BigQueryPersistence(
        "test_dataset", "test_tenant", data_config
    )
    assert bigquery_persistence._get_path_keywords("foo_test.py") == ["test"]
    assert bigquery_persistence._get_path_keywords("footestbar") == ["test"]
    assert bigquery_persistence._get_path_keywords("TEST") == ["test"]
    assert bigquery_persistence._get_path_keywords("foo.py") == []


def test_edit_emit_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    edit_emit = request_insight_pb2.EditEmit()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.edit_emit.CopyFrom(edit_emit)

    edit_emit_row = bigquery_persistence._edit_emit_row(
        "test-request", "test-session", tenant_info, event
    )
    assert edit_emit_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }


def test_instruction_emit_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    instruction_emit = request_insight_pb2.InstructionEmit()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.instruction_emit.CopyFrom(instruction_emit)

    instruction_emit_row = bigquery_persistence._instruction_emit_row(
        "test-request", "test-session", tenant_info, event
    )
    assert instruction_emit_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }


def test_completion_emit_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    completion_emit = request_insight_pb2.CompletionEmit()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_emit.CopyFrom(completion_emit)

    completion_emit_row = bigquery_persistence._completion_emit_row(
        "test-request", "test-session", tenant_info, event
    )
    assert completion_emit_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }


def test_edit_host_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    edit_host_request = request_insight_pb2.RIEditRequest()
    edit_host_request.request.CopyFrom(
        edit_pb2.EditRequest(
            model_name="test-model",
            selected_text="test-selected-text",
            instruction="refactor this test",
            prefix="test-prefix",
            suffix="test-suffix",
            path="test-path.py",
            blobs=blob_names_pb2.Blobs(
                baseline_checkpoint_id="test-checkpoint-id",
                added=[b"test-added-blob"],
                deleted=[b"test-deleted-blob"],
            ),
            lang="test-lang",
            position=edit_pb2.EditPosition(
                prefix_begin=1, blob_name="test-blob-name", suffix_end=3
            ),
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.edit_host_request.CopyFrom(edit_host_request)
    edit_host_request_row = bigquery_persistence._edit_host_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "request": {
            "model_name": "test-model",
            "blobs": {
                "baseline_checkpoint_id": "test-checkpoint-id",
                "added": [base64.b64encode(b"test-added-blob").decode()],
                "deleted": [base64.b64encode(b"test-deleted-blob").decode()],
            },
            "lang": "test-lang",
            "position": {
                "prefix_begin": 1,
                "blob_name": "test-blob-name",
                "suffix_end": 3,
            },
        }
    }
    assert edit_host_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "keywords": ["refactor", "test"],
        "categories": ["refactor", "test"],
        "path_extension": ".py",
    }


def test_instruction_host_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    instruction_host_request = request_insight_pb2.RIInstructionRequest(
        request=edit_pb2.InstructionRequest(
            model_name="test-model",
            lang="test-lang",
            blobs=blob_names_pb2.Blobs(
                baseline_checkpoint_id="test-checkpoint-id",
                added=[b"test-added-blob"],
                deleted=[b"test-deleted-blob"],
            ),
            position=edit_pb2.EditPosition(
                prefix_begin=1, blob_name="test-blob-name", suffix_end=3
            ),
            instruction="refactor this test",
            path="test-path.py",
            prefix="test-prefix",
            suffix="test-suffix",
            selected_text="test-selected-text",
            sequence_id=100,
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.instruction_host_request.CopyFrom(instruction_host_request)
    instruction_host_request_row = bigquery_persistence._instruction_host_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "request": {
            "model_name": "test-model",
            "blobs": {
                "baseline_checkpoint_id": "test-checkpoint-id",
                "added": [base64.b64encode(b"test-added-blob").decode()],
                "deleted": [base64.b64encode(b"test-deleted-blob").decode()],
            },
            "lang": "test-lang",
            "position": {
                "prefix_begin": 1,
                "blob_name": "test-blob-name",
                "suffix_end": 3,
            },
        }
    }
    assert instruction_host_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "categories": ["refactor", "test"],
        "path_extension": ".py",
    }


def test_instruction_keywords(bigquery_persistence):
    assert bigquery_persistence._get_instruction_keywords("REFACTOR") == ["refactor"]
    assert bigquery_persistence._get_instruction_keywords("refactor this test") == [
        "refactor",
        "test",
    ]
    assert bigquery_persistence._get_instruction_keywords("no keywords here") == []
    assert bigquery_persistence._get_instruction_keywords("OptimizerClass") == []
    assert bigquery_persistence._get_instruction_keywords("Fix it: refactor") == [
        "quickfix",
        "fix",
        "refactor",
    ]


def test_instruction_categories(bigquery_persistence):
    assert bigquery_persistence._get_instruction_categories("REFACTOR") == ["refactor"]
    assert bigquery_persistence._get_instruction_categories("refactor this test") == [
        "refactor",
        "test",
    ]
    assert bigquery_persistence._get_instruction_categories("no keywords here") == []
    assert bigquery_persistence._get_instruction_categories("OptimizerClass") == []
    assert bigquery_persistence._get_instruction_categories("Fix it: refactor") == [
        "quickfix",
        "fix",
        "refactor",
    ]
    assert bigquery_persistence._get_instruction_categories("Fix it: Answer") == [
        "quickfix",
        "fix",
        "help",
    ]
    assert bigquery_persistence._get_instruction_categories("answer") == ["help"]
    assert bigquery_persistence._get_instruction_categories(
        "test answer something test"
    ) == [
        "help",
        "test",
    ]
    assert bigquery_persistence._get_instruction_categories("demo example learn") == [
        "explain"
    ]
    assert bigquery_persistence._get_instruction_categories("demo example explain") == [
        "explain"
    ]
    assert bigquery_persistence._get_instruction_categories(
        "Deduplicate Verify Validate"
    ) == [
        "refactor",
        "test",
    ]
    assert bigquery_persistence._get_instruction_categories("CAPTURE") == [
        "log",
    ]


@pytest.mark.parametrize(
    "user_filter_threshold",
    [None, 0.0, 0.5, 1.0],
)
def test_completion_host_request_row(
    bigquery_persistence, tenant_info, user_filter_threshold
):
    now = datetime.now()
    completion_host_request = request_insight_pb2.CompletionHostRequest(
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1],
            offsets=[0],
            text="foo",
            log_probs=[0.0],
        ),
        seed=100,
        eos_token_id=[1],
        top_k=1,
        top_p=0.5,
        temperature=0.1,
        output_len=10,
        enable_path_prefix=True,
        enable_fill_in_the_middle=True,
        enable_bm25=True,
        enable_dense_retrieval=True,
        model="test-model",
        prefix="test-prefix",
        path="test-path",
        suffix="test-suffix",
        blob_names=["test-blob-name"],
        blobs=blob_names_pb2.Blobs(
            baseline_checkpoint_id="test-checkpoint-id",
            added=[b"test-added-blob"],
            deleted=[b"test-deleted-blob"],
        ),
        lang="test-lang",
        position=request_insight_pb2.CompletionHostRequestPosition(
            prefix_begin=1, cursor_position=2, suffix_end=3
        ),
        probe_only=True,
        user_filter_threshold=user_filter_threshold,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_host_request.CopyFrom(completion_host_request)

    completion_host_request_row = bigquery_persistence._completion_host_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "seed": "100",
        "eos_token_id": [1],
        "top_k": 1,
        "top_p": 0.5,
        "temperature": 0.1,
        "output_len": 10,
        "enable_path_prefix": True,
        "enable_fill_in_the_middle": True,
        "enable_bm25": True,
        "enable_dense_retrieval": True,
        "model": "test-model",
        "blob_names": ["test-blob-name"],
        "blobs": {
            "baseline_checkpoint_id": "test-checkpoint-id",
            "added": [base64.b64encode(b"test-added-blob").decode()],
            "deleted": [base64.b64encode(b"test-deleted-blob").decode()],
        },
        "lang": "test-lang",
        "position": {"prefix_begin": 1, "cursor_position": 2, "suffix_end": 3},
        "probe_only": True,
    }
    if user_filter_threshold is not None:
        expected_json["user_filter_threshold"] = user_filter_threshold

    assert completion_host_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_edit_host_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    edit_host_response = request_insight_pb2.RIEditResponse(
        response=edit_pb2.EditResponse(
            text="test-text",
            unknown_blob_names=["test-unknown-blob-name"],
            checkpoint_not_found=True,
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.edit_host_response.CopyFrom(edit_host_response)

    edit_host_response_row = bigquery_persistence._edit_host_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "response": {
            "checkpoint_not_found": True,
        }
    }
    assert edit_host_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "character_count": 9,
        "line_count": 1,
        "unknown_blobs_count": 1,
    }


def test_instruction_host_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    instruction_host_response = request_insight_pb2.RIInstructionResponse(
        response=edit_pb2.InstructionAggregateResponse(
            text="test-text",
            unknown_blob_names=["test-unknown-blob-name"],
            checkpoint_not_found=True,
            replace_text=[
                edit_pb2.ReplaceText(
                    old_text="test-old-text",
                    text="test-new-text",
                    start_line=1,
                    end_line=2,
                    sequence_id=0,
                )
            ],
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.instruction_host_response.CopyFrom(instruction_host_response)

    instruction_host_response_row = bigquery_persistence._instruction_host_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "response": {
            "checkpoint_not_found": True,
        }
    }
    assert instruction_host_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "replace_text_character_count": 13,
        "replace_text_line_count": 1,
        "unknown_blobs_count": 1,
    }


def test_completion_host_response_row(bigquery_persistence, tenant_info):
    # Test that we disregard empty lines.
    text = "foo\n \t \nbar"

    now = datetime.now()
    completion_host_response = request_insight_pb2.CompletionHostResponse(
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1],
            offsets=[0],
            text="foo",
            log_probs=[0.0],
        ),
        text=text,
        unknown_blob_names=["test-unknown-blob-name"],
        skipped_suffix="test-skipped-suffix",
        suffix_replacement_text="test-suffix-replacement-text",
        checkpoint_not_found=True,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_host_response.CopyFrom(completion_host_response)

    completion_host_response_row = bigquery_persistence._completion_host_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "checkpoint_not_found": True,
    }
    assert completion_host_response_row["sanitized_json"] == expected_json
    assert completion_host_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "character_count": len(text),
        "line_count": 2,
        "unknown_blobs_count": 1,
    }


def test_retrieval_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    retrieval_response = request_insight_pb2.RetrievalResponse(
        retrieval_type=request_insight_pb2.RetrievalType.DENSE,
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                text="test-retrieval-chunk-text",
                blob_name="test-retrieval-chunk-blob-name",
                chunk_index=1,
                char_offset=10,
            )
        ],
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.retrieval_response.CopyFrom(retrieval_response)

    retrieval_response_row = bigquery_persistence._retrieval_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "retrieval_type": "DENSE",
    }

    assert retrieval_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "retrieved_chunk_count": 1,
        "retrieval_type": "DENSE",
        "sanitized_json": expected_json,
    }


def test_completion_feedback_row_with_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    completion_feedback = request_insight_pb2.CompletionFeedback(
        rating=1,
        note="example user note",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_feedback.CopyFrom(completion_feedback)
    completion_request_row = bigquery_persistence._completion_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "POSITIVE",
    }
    assert completion_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": True,
        "note": "example user note",
    }


def test_completion_feedback_row_no_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    completion_feedback = request_insight_pb2.CompletionFeedback(
        rating=2,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_feedback.CopyFrom(completion_feedback)
    completion_request_row = bigquery_persistence._completion_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "NEGATIVE",
    }
    assert completion_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": False,
        "note": "",
    }


def test_chat_feedback_row_with_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    chat_feedback = request_insight_pb2.ChatFeedback(
        rating=1,
        note="example user note",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.chat_feedback.CopyFrom(chat_feedback)
    chat_request_row = bigquery_persistence._chat_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "POSITIVE",
    }
    assert chat_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": True,
        "note": "example user note",
    }


def test_chat_feedback_row_no_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    chat_feedback = request_insight_pb2.ChatFeedback(
        rating=2,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.chat_feedback.CopyFrom(chat_feedback)
    chat_request_row = bigquery_persistence._chat_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "NEGATIVE",
    }
    assert chat_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": False,
        "note": "",
    }


def test_agent_feedback_row_with_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    agent_feedback = request_insight_pb2.AgentFeedback(
        rating=1,
        note="example user note",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.agent_feedback.CopyFrom(agent_feedback)
    agent_request_row = bigquery_persistence._agent_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "POSITIVE",
    }
    assert agent_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": True,
        "note": "example user note",
    }


def test_agent_feedback_row_no_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    agent_feedback = request_insight_pb2.AgentFeedback(
        rating=2,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.agent_feedback.CopyFrom(agent_feedback)
    agent_request_row = bigquery_persistence._agent_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "NEGATIVE",
    }
    assert agent_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": False,
        "note": "",
    }


def test_next_edit_feedback_row_with_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    next_edit_feedback = request_insight_pb2.NextEditFeedback(
        rating=1,
        note="example user note",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.next_edit_feedback.CopyFrom(next_edit_feedback)
    next_edit_request_row = bigquery_persistence._next_edit_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "POSITIVE",
    }
    assert next_edit_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": True,
        "note": "example user note",
    }


def test_next_edit_feedback_row_no_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    next_edit_feedback = request_insight_pb2.NextEditFeedback(
        rating=2,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.next_edit_feedback.CopyFrom(next_edit_feedback)
    next_edit_request_row = bigquery_persistence._next_edit_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "NEGATIVE",
    }
    assert next_edit_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "has_note": False,
        "note": "",
    }


def test_inference_host_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    inference_host_response = request_insight_pb2.InferenceHostResponse(
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1],
            offsets=[0],
            text="foo",
            log_probs=[0.0],
        ),
        cum_log_probs=0.2,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.inference_host_response.CopyFrom(inference_host_response)

    inference_host_response_row = bigquery_persistence._inference_host_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {"cum_log_probs": 0.2}
    assert inference_host_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_api_http_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    api_http_response = request_insight_pb2.ApiHttpResponse(
        code=400,
        error_message="test-error-message",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.api_http_response.CopyFrom(api_http_response)

    api_http_response_row = bigquery_persistence._api_http_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {"code": 400, "error_message": "test-error-message"}
    assert api_http_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_embeddings_search_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    embeddings_search_request = request_insight_pb2.EmbeddingsSearchRequest(
        blob_names=["test-blob-name"],
        blobs=[
            blob_names_pb2.Blobs(
                baseline_checkpoint_id="test-checkpoint-id",
                added=[b"test-added-blob"],
                deleted=[b"test-deleted-blob"],
            )
        ],
        num_results=1,
        transformation_key="test-transformation-key",
        sub_key="test-sub-key",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.embeddings_search_request.CopyFrom(embeddings_search_request)

    embeddings_search_request_row = bigquery_persistence._embeddings_search_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "num_results": 1,
        "transformation_key": "test-transformation-key",
        "sub_key": "test-sub-key",
    }
    assert embeddings_search_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_embeddings_search_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    embeddings_search_response = request_insight_pb2.EmbeddingsSearchResponse(
        missing_blob_names=["test-blob-name"],
        results=[
            request_insight_pb2.EmbeddingsSearchResult(
                blob_name="test-blob-name", chunk_index=4, value=4.5
            )
        ],
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.embeddings_search_response.CopyFrom(embeddings_search_response)

    embeddings_search_response_row = (
        bigquery_persistence._embeddings_search_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    expected_json = {
        "results": [{"blob_name": "test-blob-name", "chunk_index": 4, "value": 4.5}],
    }
    assert embeddings_search_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "missing_blobs_count": 1,
    }


def test_extension_error_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    extension_error = request_insight_pb2.ExtensionError(
        original_request_id="test-original-request-id",
        message="my-fake-error-message",
        stack_trace="""a fake error occurred
        at foo:10
        at bar:20""",
        diagnostics={"file-name": "my-sensitive-file-name"},
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.extension_error.CopyFrom(extension_error)

    extension_error_row = bigquery_persistence._extension_error_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "original_request_id": "test-original-request-id",
    }
    assert extension_error_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_extension_error_row_chat_stream_failed(bigquery_persistence, tenant_info):
    now = datetime.now()
    extension_error = request_insight_pb2.ExtensionError(
        original_request_id="test-original-request-id",
        message="chat_stream_failed",
        stack_trace="""TypeError: terminated
        at Fetch.onAborted (node:internal/deps/undici/undici:11124:53)
        at Fetch.emit""",
        diagnostics={
            "message_timestamp_ms": "123456789",
            "extension_timestamp_ms": "123456789",
            "last_chunk_timestamp_ms": "123456789",
            "error_timestamp_ms": "123456789",
            "unexpected_key": "99",
            "chunks_received": "not a number",
        },
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.extension_error.CopyFrom(extension_error)

    extension_error_row = bigquery_persistence._extension_error_row(
        "test-request", "test-session", tenant_info, event
    )
    expected_json = {
        "original_request_id": "test-original-request-id",
        "message": "chat_stream_failed",
        "diagnostics": {
            "message_timestamp_ms": "123456789",
            "extension_timestamp_ms": "123456789",
            "last_chunk_timestamp_ms": "123456789",
            "error_timestamp_ms": "123456789",
        },
    }
    assert extension_error_row["sanitized_json"] == expected_json
    assert extension_error_row["sanitized_error_type"] == "TypeError: terminated"

    event.extension_error.message = "different error"
    extension_error_row = bigquery_persistence._extension_error_row(
        "test-request", "test-session", tenant_info, event
    )
    assert extension_error_row["sanitized_json"] == {
        "original_request_id": "test-original-request-id",
    }
    assert extension_error_row.get("sanitized_error_type") is None


def test_extension_error_row_webview_time_out(bigquery_persistence, tenant_info):
    now = datetime.now()
    extension_error = request_insight_pb2.ExtensionError(
        # these usually don't carry one
        original_request_id="",
        message="Request timed out: chat-user-message, id: 92f40709-fde5-44a5-be8d-f79d536aebed",
        stack_trace="other details",
        diagnostics={},
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.extension_error.CopyFrom(extension_error)

    extension_error_row = bigquery_persistence._extension_error_row(
        "test-request", "test-session", tenant_info, event
    )
    expected_json = {
        "original_request_id": "",
        "message": "Request timed out: chat-user-message, id: 92f40709-fde5-44a5-be8d-f79d536aebed",
    }
    assert extension_error_row["sanitized_json"] == expected_json
    assert extension_error_row.get("sanitized_error_type") is None


@pytest.mark.parametrize(
    "error",
    [
        "TypeError: terminated",
        "Error: fetch failed",
        "Error: HTTP error: 499",
        "Error: The operation was aborted due to timeout",
    ],
)
def test_extension_error_row_stack_sanitization(
    bigquery_persistence, tenant_info, error
):
    now = datetime.now()
    extension_error = request_insight_pb2.ExtensionError(
        original_request_id="",
        message="chat_stream_failed",
        stack_trace=error,
        diagnostics={},
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.extension_error.CopyFrom(extension_error)

    extension_error_row = bigquery_persistence._extension_error_row(
        "test-request", "test-session", tenant_info, event
    )
    assert extension_error_row["sanitized_error_type"] == error


def test_chat_host_request_row_with_image_nodes(bigquery_persistence, tenant_info):
    chat_host_request = request_insight_pb2.RIChatRequest(
        request_source="test-request-source",
        request=chat_pb2.ChatRequest(
            model_name="test-model",
            path="test-path.py",
            prefix="test-prefix",
            selected_code="test-selected-code",
            suffix="test-suffix",
            message="test message",
            chat_history=[
                chat_pb2.Exchange(
                    request_message="How you doing?",
                    response_text="Fine thanks",
                    request_id="test-request-id",
                )
            ],
            position=chat_pb2.ChatPosition(
                prefix_begin=1, suffix_end=2, blob_name="test-blob-name"
            ),
            sequence_id=1000,
            blobs=[
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="test-checkpoint-id",
                    added=[b"test-added-blob"],
                    deleted=[b"test-deleted-blob"],
                )
            ],
            lang="test-lang",
            external_source_ids=[
                "docset://test-docset",
                "github://test/mycoolsecretrepo",
                "github://test/mycoolsecretrepo2",
            ],
            user_guided_blobs=["test-user-guided-blob-name"],
            nodes=[
                chat_pb2.ChatRequestNode(
                    id=1,
                    type=chat_pb2.ChatRequestNodeType.TEXT,
                    text_node=chat_pb2.ChatRequestText(content="test text"),
                ),
                chat_pb2.ChatRequestNode(
                    id=2,
                    type=chat_pb2.ChatRequestNodeType.IMAGE,
                    image_node=chat_pb2.ChatRequestImage(image_data="base64data"),
                ),
            ],
            silent=True,
        ),
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                text="test-retrieval-chunk-text",
                path="test-path",
                char_offset=10,
                char_end=20,
                blob_name="test-retrieved-blob-name",
                chunk_index=1,
                origin="test-origin",
            )
        ],
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1],
            offsets=[2],
            log_probs=[0.0],
            text="test-text",
        ),
    )

    now = datetime.now()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.chat_host_request.CopyFrom(chat_host_request)

    chat_host_request_row = bigquery_persistence._chat_host_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "request_source": "test-request-source",
        "request": {
            "model_name": "test-model",
            "chat_history": [
                {
                    "request_id": "test-request-id",
                }
            ],
            "position": {
                "prefix_begin": 1,
                "suffix_end": 2,
                "blob_name": "test-blob-name",
            },
            "lang": "test-lang",
            "sequence_id": 1000,
            "blobs": [
                {
                    "baseline_checkpoint_id": "test-checkpoint-id",
                    "added": [base64.b64encode(b"test-added-blob").decode()],
                    "deleted": [base64.b64encode(b"test-deleted-blob").decode()],
                }
            ],
            "user_guided_blobs": ["test-user-guided-blob-name"],
            "silent": True,
        },
    }
    assert chat_host_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "masked_external_source_ids": ["***", "docset://test-docset"],
        "message_keywords": ["test"],
        "message_categories": ["test"],
        "path_extension": ".py",
        "selected_code_character_count": 18,
        "selected_code_line_count": 1,
        "image_node_count": 1,
        "text_node_count": 1,
        "tool_result_node_count": 0,
    }


def test_chat_host_request_row_with_no_image_nodes(bigquery_persistence, tenant_info):
    chat_host_request = request_insight_pb2.RIChatRequest(
        request_source="test-request-source",
        request=chat_pb2.ChatRequest(
            model_name="test-model",
            path="test-path.py",
            prefix="test-prefix",
            selected_code="test-selected-code",
            suffix="test-suffix",
            message="test message",
            chat_history=[
                chat_pb2.Exchange(
                    request_message="How you doing?",
                    response_text="Fine thanks",
                    request_id="test-request-id",
                )
            ],
            position=chat_pb2.ChatPosition(
                prefix_begin=1, suffix_end=2, blob_name="test-blob-name"
            ),
            sequence_id=1000,
            blobs=[
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="test-checkpoint-id",
                    added=[b"test-added-blob"],
                    deleted=[b"test-deleted-blob"],
                )
            ],
            lang="test-lang",
            external_source_ids=[
                "docset://test-docset",
                "github://test/mycoolsecretrepo",
                "github://test/mycoolsecretrepo2",
            ],
            user_guided_blobs=["test-user-guided-blob-name"],
            nodes=[
                chat_pb2.ChatRequestNode(
                    id=1,
                    type=chat_pb2.ChatRequestNodeType.TEXT,
                    text_node=chat_pb2.ChatRequestText(content="test text"),
                ),
                chat_pb2.ChatRequestNode(
                    id=2,
                    type=chat_pb2.ChatRequestNodeType.TOOL_RESULT,
                    tool_result_node=chat_pb2.ChatRequestToolResult(
                        tool_use_id="test-tool-id", content="test tool result"
                    ),
                ),
            ],
        ),
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                text="test-retrieval-chunk-text",
                path="test-path",
                char_offset=10,
                char_end=20,
                blob_name="test-retrieved-blob-name",
                chunk_index=1,
                origin="test-origin",
            )
        ],
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1],
            offsets=[2],
            log_probs=[0.0],
            text="test-text",
        ),
    )

    now = datetime.now()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.chat_host_request.CopyFrom(chat_host_request)

    chat_host_request_row = bigquery_persistence._chat_host_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "request_source": "test-request-source",
        "request": {
            "model_name": "test-model",
            "chat_history": [
                {
                    "request_id": "test-request-id",
                }
            ],
            "position": {
                "prefix_begin": 1,
                "suffix_end": 2,
                "blob_name": "test-blob-name",
            },
            "lang": "test-lang",
            "sequence_id": 1000,
            "blobs": [
                {
                    "baseline_checkpoint_id": "test-checkpoint-id",
                    "added": [base64.b64encode(b"test-added-blob").decode()],
                    "deleted": [base64.b64encode(b"test-deleted-blob").decode()],
                }
            ],
            "user_guided_blobs": ["test-user-guided-blob-name"],
        },
    }
    assert chat_host_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "masked_external_source_ids": ["***", "docset://test-docset"],
        "message_keywords": ["test"],
        "message_categories": ["test"],
        "path_extension": ".py",
        "selected_code_character_count": 18,
        "selected_code_line_count": 1,
        "image_node_count": 0,
        "text_node_count": 1,
        "tool_result_node_count": 1,
    }


def test_chat_host_request_row_with_no_nodes(bigquery_persistence, tenant_info):
    chat_host_request = request_insight_pb2.RIChatRequest(
        request_source="test-request-source",
        request=chat_pb2.ChatRequest(
            model_name="test-model",
            path="test-path.py",
            prefix="test-prefix",
            selected_code="test-selected-code",
            suffix="test-suffix",
            message="test message",
            chat_history=[
                chat_pb2.Exchange(
                    request_message="How you doing?",
                    response_text="Fine thanks",
                    request_id="test-request-id",
                )
            ],
            position=chat_pb2.ChatPosition(
                prefix_begin=1, suffix_end=2, blob_name="test-blob-name"
            ),
            sequence_id=1000,
            blobs=[
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="test-checkpoint-id",
                    added=[b"test-added-blob"],
                    deleted=[b"test-deleted-blob"],
                )
            ],
            lang="test-lang",
            external_source_ids=[
                "docset://test-docset",
                "github://test/mycoolsecretrepo",
                "github://test/mycoolsecretrepo2",
            ],
            user_guided_blobs=["test-user-guided-blob-name"],
            # No nodes field
        ),
        retrieved_chunks=[
            request_insight_pb2.RetrievalChunk(
                text="test-retrieval-chunk-text",
                path="test-path",
                char_offset=10,
                char_end=20,
                blob_name="test-retrieved-blob-name",
                chunk_index=1,
                origin="test-origin",
            )
        ],
        tokenization=request_insight_pb2.Tokenization(
            token_ids=[1],
            offsets=[2],
            log_probs=[0.0],
            text="test-text",
        ),
    )

    now = datetime.now()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.chat_host_request.CopyFrom(chat_host_request)

    chat_host_request_row = bigquery_persistence._chat_host_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "request_source": "test-request-source",
        "request": {
            "model_name": "test-model",
            "chat_history": [
                {
                    "request_id": "test-request-id",
                }
            ],
            "position": {
                "prefix_begin": 1,
                "suffix_end": 2,
                "blob_name": "test-blob-name",
            },
            "lang": "test-lang",
            "sequence_id": 1000,
            "blobs": [
                {
                    "baseline_checkpoint_id": "test-checkpoint-id",
                    "added": [base64.b64encode(b"test-added-blob").decode()],
                    "deleted": [base64.b64encode(b"test-deleted-blob").decode()],
                }
            ],
            "user_guided_blobs": ["test-user-guided-blob-name"],
        },
    }
    assert chat_host_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "masked_external_source_ids": ["***", "docset://test-docset"],
        "message_keywords": ["test"],
        "message_categories": ["test"],
        "path_extension": ".py",
        "selected_code_character_count": 18,
        "selected_code_line_count": 1,
        "image_node_count": 0,
        "text_node_count": 0,
        "tool_result_node_count": 0,
    }


def test_chat_host_request_row_with_context_code_exchange_request_id(
    bigquery_persistence, tenant_info
):
    # Expected strings which don't require sanitization are kept
    # Unexpected strings could be anything, so they should be removed
    cases = [
        ("new", "new"),
        (
            "123e4567-e89b-12d3-a456-426614174000",
            "123e4567-e89b-12d3-a456-426614174000",
        ),
        ("some other string", None),
    ]
    for input, expected in cases:
        chat_host_request = request_insight_pb2.RIChatRequest(
            request_source="test-request-source",
            request=chat_pb2.ChatRequest(
                context_code_exchange_request_id=input,
            ),
        )
        now = datetime.now()
        event = request_insight_pb2.RequestEvent()
        event.time.FromDatetime(now)
        event.chat_host_request.CopyFrom(chat_host_request)

        chat_host_request_row = bigquery_persistence._chat_host_request_row(
            "test-request", "test-session", tenant_info, event
        )
        assert (
            chat_host_request_row["sanitized_json"]["request"].get(
                "context_code_exchange_request_id"
            )
            == expected
        )


def test_chat_host_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    chat_host_response = request_insight_pb2.RIChatResponse(
        response=chat_pb2.ChatResponse(
            text="test response\nsecond line",
            unknown_blob_names=["test-unknown-blob-name"],
            checkpoint_not_found=True,
            incorporated_external_sources=[
                chat_pb2.IncorporatedExternalSource(
                    source_name="test-source-name",
                    source_id="docset://test-source",
                ),
                chat_pb2.IncorporatedExternalSource(
                    source_name="test-source-name",
                    source_id="docset://test-source",
                ),
            ],
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.chat_host_response.CopyFrom(chat_host_response)

    chat_host_response_row = bigquery_persistence._chat_host_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "response": {
            "checkpoint_not_found": True,
        }
    }
    assert chat_host_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "character_count": 25,
        "line_count": 2,
        "unknown_blobs_count": 1,
        "masked_incorporated_external_source_ids": ["docset://test-source"],
    }


def test_next_edit_host_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    next_edit_host_request = request_insight_pb2.RINextEditRequest(
        request=next_edit_pb2.NextEditRequest(
            model_name="test-model",
            sequence_id=1000,
            lang="test-lang",
            instruction="refactor this test",
            blobs=blob_names_pb2.Blobs(
                baseline_checkpoint_id="test-checkpoint-id",
                added=[b"test-added-blob"],
                deleted=[b"test-deleted-blob"],
            ),
            recent_changes=[
                completion_pb2.ReplacementText(
                    blob_name="test-blob-name",
                    path="test-path",
                    char_start=1,
                    char_end=2,
                    replacement_text="test-replacement-text",
                    present_in_blob=True,
                    expected_blob_name="test-expected-blob-name",
                )
            ],
            vcs_change=next_edit_pb2.VCSChange(
                working_directory_changes=[
                    next_edit_pb2.WorkingDirectoryChange(
                        before_path="test-before-path",
                        after_path="test-after-path",
                        change_type=next_edit_pb2.ChangeType.MODIFIED,
                        head_blob_name="test-head-blob-name",
                        indexed_blob_name="test-indexed-blob-name",
                        current_blob_name="test-current-blob-name",
                    )
                ]
            ),
            path="test-path.py",
            blob_name="test-blob-name",
            selection_begin_char=1,
            selection_end_char=2,
            prefix="test-prefix",
            selected_text="test-selected-text",
            suffix="test-suffix",
            diagnostics=[
                next_edit_pb2.Diagnostic(
                    location=next_edit_pb2.FileLocation(
                        path="test-path",
                        line_start=1,
                        line_end=2,
                    ),
                    message="test-message",
                    severity=next_edit_pb2.DiagnosticSeverity.ERROR,
                )
            ],
            mode=next_edit_pb2.NextEditMode.FOREGROUND,
            scope=next_edit_pb2.NextEditScope.FILE,
        ),
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.next_edit_host_request.CopyFrom(next_edit_host_request)

    next_edit_host_request_row = bigquery_persistence._next_edit_host_request_row(
        "test-request", "test-session", tenant_info, event
    )
    expected_json = {
        "request": {
            "model_name": "test-model",
            "sequence_id": 1000,
            "lang": "test-lang",
            "blobs": {
                "baseline_checkpoint_id": "test-checkpoint-id",
                "added": [base64.b64encode(b"test-added-blob").decode()],
                "deleted": [base64.b64encode(b"test-deleted-blob").decode()],
            },
            "vcs_change": {
                "working_directory_changes": [
                    {
                        "change_type": "MODIFIED",
                        "head_blob_name": "test-head-blob-name",
                        "indexed_blob_name": "test-indexed-blob-name",
                        "current_blob_name": "test-current-blob-name",
                    }
                ]
            },
            "recent_changes": [
                {
                    "blob_name": "test-blob-name",
                    "char_start": 1,
                    "char_end": 2,
                    "present_in_blob": True,
                    "expected_blob_name": "test-expected-blob-name",
                },
            ],
            "blob_name": "test-blob-name",
            "selection_begin_char": 1,
            "selection_end_char": 2,
            "mode": "FOREGROUND",
            "scope": "FILE",
        }
    }

    assert next_edit_host_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "token_count": 0,
    }


def test_next_edit_host_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    next_edit_host_response = request_insight_pb2.RINextEditResponse(
        retrieved_locations=[
            request_insight_pb2.RetrievalChunk(
                blob_name="test-blob-name",
                chunk_index=1,
                char_offset=2,
                char_end=3,
                score=0.1,
                path="test-path",
                origin="test-origin",
                text="test-text",
            )
        ],
        generation=[
            request_insight_pb2.RINextEditGeneration(
                generation_id="test-generation-id",
                retrieved_chunks=[
                    request_insight_pb2.RetrievalChunk(
                        blob_name="test-blob-name",
                        chunk_index=1,
                        char_offset=2,
                        char_end=3,
                        score=0.1,
                        path="test-path",
                        origin="test-origin",
                        text="test-text",
                    )
                ],
            )
        ],
        suggestions=[
            request_insight_pb2.RINextEditSuggestion(
                generation_id="test-generation-id",
                result=next_edit_pb2.NextEditResponse(
                    suggested_edit=next_edit_pb2.ScoredFileHunk(
                        path="test-path",
                        blob_name="test-blob-name",
                        char_start=1,
                        char_end=2,
                        existing_code="test-existing-code",
                        suggested_code="test-suggested-code",
                        localization_score=0.1,
                        editing_score=0.2,
                        truncation_char=0,
                        diff_spans=[
                            next_edit_pb2.DiffSpan(
                                original=next_edit_pb2.CharRange(
                                    start=1,
                                    stop=2,
                                ),
                                updated=next_edit_pb2.CharRange(
                                    start=1,
                                    stop=2,
                                ),
                            )
                        ],
                        change_description="test-change-description",
                    ),
                    unknown_blob_names=["test-unknown-blob-name"],
                    checkpoint_not_found=True,
                ),
            )
        ],
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.next_edit_host_response.CopyFrom(next_edit_host_response)

    next_edit_host_response_row = bigquery_persistence._next_edit_host_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "retrieved_locations": [
            {
                "blob_name": "test-blob-name",
                "chunk_index": 1,
                "char_offset": 2,
                "char_end": 3,
                "score": 0.1,
            }
        ],
        "generation": [
            {
                "generation_id": "test-generation-id",
                "retrieved_chunks": [
                    {
                        "blob_name": "test-blob-name",
                        "chunk_index": 1,
                        "char_offset": 2,
                        "char_end": 3,
                        "score": 0.1,
                    }
                ],
            }
        ],
        "suggestions": [
            {
                "generation_id": "test-generation-id",
                "result": {
                    "suggested_edit": {
                        "blob_name": "test-blob-name",
                        "char_start": 1,
                        "char_end": 2,
                        "localization_score": 0.1,
                        "editing_score": 0.2,
                        "truncation_char": 0,
                        "diff_spans": [
                            {
                                "original": {"start": 1, "stop": 2},
                                "updated": {"start": 1, "stop": 2},
                            }
                        ],
                    },
                    "checkpoint_not_found": True,
                },
            }
        ],
    }

    assert next_edit_host_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "character_counts": [19],
        "unknown_blobs_count": 1,
    }


def test_next_edit_host_empty_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    next_edit_host_response = request_insight_pb2.RINextEditResponse(
        retrieved_locations=[],
        generation=[],
        suggestions=[],
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.next_edit_host_response.CopyFrom(next_edit_host_response)

    next_edit_host_response_row = bigquery_persistence._next_edit_host_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {}

    assert next_edit_host_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "character_counts": [],
        "unknown_blobs_count": 0,
    }


def test_next_edit_session_event_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    next_edit_session_event = request_insight_pb2.NextEditSessionEvent(
        related_request_id="test-request-id",
        related_suggestion_id="test-suggestion-id",
        event_name="test-event-name",
        event_source="test-event-source",
        user_agent="test-user-agent",
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.next_edit_session_event.CopyFrom(next_edit_session_event)

    next_edit_session_event_row = bigquery_persistence._next_edit_session_event_row(
        "test-session", auth_entities_pb2.UserId(), tenant_info, event
    )

    expected_json = {
        "related_request_id": "test-request-id",
        "related_suggestion_id": "test-suggestion-id",
        "event_name": "test-event-name",
        "event_source": "test-event-source",
        "user_agent": "test-user-agent",
    }

    assert next_edit_session_event_row == {
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_onboarding_session_event_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    onboarding_session_event = request_insight_pb2.OnboardingSessionEvent(
        event_name="test-event-name",
        user_agent="test-user-agent",
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.onboarding_session_event.CopyFrom(onboarding_session_event)

    onboarding_session_event_row = bigquery_persistence._onboarding_session_event_row(
        "test-session", auth_entities_pb2.UserId(), tenant_info, event
    )

    expected_json = {
        "event_name": "test-event-name",
        "user_agent": "test-user-agent",
    }

    assert onboarding_session_event_row == {
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_client_metric_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    client_metric = request_insight_pb2.ClientMetric(
        event_name="test-event-name",
        user_agent="test-user-agent",
        client_metric="test-client-metric",
        value=100,
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.client_metric.CopyFrom(client_metric)

    client_metric_row = bigquery_persistence._client_metric_row(
        "test-session", auth_entities_pb2.UserId(), tenant_info, event
    )

    expected_json = {
        "event_name": "test-event-name",
        "user_agent": "test-user-agent",
        "client_metric": "test-client-metric",
        "value": "100",
    }

    assert client_metric_row == {
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_feature_vector_report_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    feature_vector_report = request_insight_pb2.FeatureVectorReport(
        user_agent="test-user-agent",
        source_ip="test-source-ip",
    )
    feature_vector_report.headers["accept"] = "test-accept-value"
    feature_vector_report.headers["test-header-name"] = "test-header-value"
    feature_vector_report.feature_vector[1] = "test-feature-value"
    feature_vector_report.feature_vector[2] = "test-feature-value-2"

    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.feature_vector_report.CopyFrom(feature_vector_report)

    feature_vector_report_row = bigquery_persistence._feature_vector_report_row(
        "test-session", auth_entities_pb2.UserId(), tenant_info, event
    )

    expected_json = {
        "user_agent": "test-user-agent",
        "headers": {
            "accept": "test-accept-value",
        },
    }

    assert feature_vector_report_row == {
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "feature_vector": {
            1: "test-feature-value",
            2: "test-feature-value-2",
        },
        "source_ip": "test-source-ip",
    }


def test_chat_keywords(bigquery_persistence):
    assert bigquery_persistence._get_chat_keywords("REFACTOR") == ["refactor"]
    assert bigquery_persistence._get_chat_keywords("refactor this test") == [
        "refactor",
        "test",
    ]
    assert bigquery_persistence._get_chat_keywords("no keywords here") == []
    assert bigquery_persistence._get_chat_keywords("OptimizerClass") == []


def test_chat_categories(bigquery_persistence):
    assert bigquery_persistence._get_chat_categories("REFACTOR") == ["refactor"]
    assert bigquery_persistence._get_chat_categories("refactor this test") == [
        "refactor",
        "test",
    ]
    assert bigquery_persistence._get_chat_categories("no keywords here") == []
    assert bigquery_persistence._get_chat_categories("OptimizerClass") == []
    assert bigquery_persistence._get_chat_categories("answer") == ["help"]
    assert bigquery_persistence._get_chat_categories("test answer something test") == [
        "help",
        "test",
    ]
    assert bigquery_persistence._get_chat_categories("demo example learn") == [
        "explain"
    ]
    assert bigquery_persistence._get_chat_categories("demo example explain") == [
        "explain"
    ]
    assert bigquery_persistence._get_chat_categories("Deduplicate Verify Validate") == [
        "refactor",
        "test",
    ]
    assert bigquery_persistence._get_chat_categories("CAPTURE") == [
        "log",
    ]


def test_slackbot_request_message(bigquery_persistence, tenant_info):
    now = datetime.now()
    slackbot_request = request_insight_pb2.RISlackbotRequest(
        slack_event=slack_event_pb2.SlackEvent(
            event_type="message",
            metadata=slack_event_pb2.EventMetadata(
                team_id="test-team-id",
                enterprise_id="test-enterprise-id",
                tenant_id="test-tenant-id",
                tenant_name="test-tenant-name",
                request_id="test-request-id",
            ),
            message=slack_event_pb2.MessageEvent(
                client_msg_id="test-client-msg-id",
                user="test-user",
                text="test-text",
                thread_timestamp="test-thread-timestamp",
                timestamp="test-timestamp",
                channel="test-channel",
                channel_type="test-channel-type",
                event_timestamp="test-event-timestamp",
                user_team="test-user-team",
                source_team="test-source-team",
                subtype="test-subtype",
            ),
        ),
        channel_type=request_insight_pb2.RISlackbotRequest.ChannelType.IM,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.slackbot_request.CopyFrom(slackbot_request)

    slackbot_request_row = bigquery_persistence._slackbot_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "slack_event": {
            "event_type": "message",
            "metadata": {
                "team_id": "test-team-id",
                "enterprise_id": "test-enterprise-id",
            },
            "message": {
                "client_msg_id": "test-client-msg-id",
                "user": "test-user",
                "thread_timestamp": "test-thread-timestamp",
                "timestamp": "test-timestamp",
                "channel": "test-channel",
                "channel_type": "test-channel-type",
                "event_timestamp": "test-event-timestamp",
                "user_team": "test-user-team",
                "source_team": "test-source-team",
                "subtype": "test-subtype",
            },
        },
        "channel_type": "IM",
    }
    assert slackbot_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_slackbot_request_app_mention(bigquery_persistence, tenant_info):
    now = datetime.now()
    slackbot_request = request_insight_pb2.RISlackbotRequest(
        slack_event=slack_event_pb2.SlackEvent(
            event_type="app_mention",
            metadata=slack_event_pb2.EventMetadata(
                team_id="test-team-id",
                enterprise_id="test-enterprise-id",
                tenant_id="test-tenant-id",
                tenant_name="test-tenant-name",
                request_id="test-request-id",
            ),
            app_mention=slack_event_pb2.AppMentionEvent(
                text="test-text",
                user="test-user",
                timestamp="test-timestamp",
                thread_timestamp="test-thread-timestamp",
                channel="test-channel",
                event_timestamp="test-event-timestamp",
                user_team="test-user-team",
                source_team="test-source-team",
            ),
        ),
        channel_type=request_insight_pb2.RISlackbotRequest.ChannelType.CHANNEL,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.slackbot_request.CopyFrom(slackbot_request)

    slackbot_request_row = bigquery_persistence._slackbot_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "slack_event": {
            "event_type": "app_mention",
            "metadata": {
                "team_id": "test-team-id",
                "enterprise_id": "test-enterprise-id",
            },
            "app_mention": {
                "user": "test-user",
                "timestamp": "test-timestamp",
                "thread_timestamp": "test-thread-timestamp",
                "channel": "test-channel",
                "event_timestamp": "test-event-timestamp",
                "user_team": "test-user-team",
                "source_team": "test-source-team",
            },
        },
        "channel_type": "CHANNEL",
    }
    assert slackbot_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_slackbot_request_reaction_added(bigquery_persistence, tenant_info):
    now = datetime.now()
    slackbot_request = request_insight_pb2.RISlackbotRequest(
        slack_event=slack_event_pb2.SlackEvent(
            event_type="reaction_added",
            metadata=slack_event_pb2.EventMetadata(
                team_id="test-team-id",
                enterprise_id="test-enterprise-id",
                tenant_id="test-tenant-id",
                tenant_name="test-tenant-name",
                request_id="test-request-id",
            ),
            reaction_added=slack_event_pb2.ReactionAddedEvent(
                user="test-user",
                item_user="test-item-user",
                event_timestamp="test-event-timestamp",
                item_type="test-item-type",
                item_channel="test-item-channel",
                reaction="test-reaction",
                item_timestamp="test-item-timestamp",
            ),
        ),
        channel_type=request_insight_pb2.RISlackbotRequest.ChannelType.CHANNEL,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.slackbot_request.CopyFrom(slackbot_request)

    slackbot_request_row = bigquery_persistence._slackbot_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "slack_event": {
            "event_type": "reaction_added",
            "metadata": {
                "team_id": "test-team-id",
                "enterprise_id": "test-enterprise-id",
            },
            "reaction_added": {
                "user": "test-user",
                "item_user": "test-item-user",
                "event_timestamp": "test-event-timestamp",
                "item_type": "test-item-type",
                "item_channel": "test-item-channel",
                "reaction": "test-reaction",
                "item_timestamp": "test-item-timestamp",
            },
        },
        "channel_type": "CHANNEL",
    }
    assert slackbot_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_slackbot_response(bigquery_persistence, tenant_info):
    now = datetime.now()
    post_message_event = request_insight_pb2.RISlackbotResponse.Event(
        post_message=request_insight_pb2.RISlackbotResponse.PostMessage(
            response_message_timestamp="post-timestamp", text="post-text"
        )
    )
    post_message_event.time.FromDatetime(now)
    update_message_event = request_insight_pb2.RISlackbotResponse.Event(
        update_message=request_insight_pb2.RISlackbotResponse.UpdateMessage(
            response_message_timestamp="update-timestamp",
            text="update-text",
        )
    )
    update_message_event.time.FromDatetime(now)
    slackbot_response = request_insight_pb2.RISlackbotResponse(
        channel="test-channel",
        thread_timestamp="test-thread-timestamp",
        request_message_timestamp="test-message-timestamp",
        response_events=[post_message_event, update_message_event],
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.slackbot_response.CopyFrom(slackbot_response)

    slackbot_response_row = bigquery_persistence._slackbot_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_timestamp = now.isoformat() + "Z"
    expected_json = {
        "channel": "test-channel",
        "thread_timestamp": "test-thread-timestamp",
        "request_message_timestamp": "test-message-timestamp",
        "response_events": [
            {
                "time": expected_timestamp,
                "post_message": {"response_message_timestamp": "post-timestamp"},
            },
            {
                "time": expected_timestamp,
                "update_message": {"response_message_timestamp": "update-timestamp"},
            },
        ],
    }
    assert (
        expected_json["response_events"][0]["time"]
        == slackbot_response_row["sanitized_json"]["response_events"][0]["time"]
    )
    assert slackbot_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_slackbot_feedback_row_with_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    slackbot_feedback = request_insight_pb2.SlackbotFeedback(
        slack_response_timestamp="test-timestamp",
        slack_channel_id="test-channel-id",
        rating=1,
        note="example user note",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.slackbot_feedback.CopyFrom(slackbot_feedback)
    slackbot_request_row = bigquery_persistence._slackbot_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "POSITIVE",
    }
    assert slackbot_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "slack_response_timestamp": "test-timestamp",
        "slack_channel_id": "test-channel-id",
        "note": "example user note",
    }


def test_slackbot_feedback_row_no_note(bigquery_persistence, tenant_info):
    now = datetime.now()
    slackbot_feedback = request_insight_pb2.SlackbotFeedback(
        slack_response_timestamp="test-timestamp",
        slack_channel_id="test-channel-id",
        rating=2,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.slackbot_feedback.CopyFrom(slackbot_feedback)
    slackbot_request_row = bigquery_persistence._slackbot_feedback_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "rating": "NEGATIVE",
    }
    assert slackbot_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "slack_response_timestamp": "test-timestamp",
        "slack_channel_id": "test-channel-id",
        "note": "",
    }


@pytest.mark.parametrize(
    "filter_reason, filter_name",
    [
        (
            request_insight_pb2.CompletionPostProcess.FilterReason.UNKNOWN_FILTER_REASON,
            "UNKNOWN_FILTER_REASON",
        ),
        (
            request_insight_pb2.CompletionPostProcess.FilterReason.NOT_FILTERED,
            "NOT_FILTERED",
        ),
        (
            request_insight_pb2.CompletionPostProcess.FilterReason.LOW_QUALITY,
            "LOW_QUALITY",
        ),
        (
            request_insight_pb2.CompletionPostProcess.FilterReason.DENY_LIST,
            "DENY_LIST",
        ),
    ],
)
def test_completion_post_process(
    bigquery_persistence, tenant_info, filter_reason, filter_name
):
    now = datetime.now()
    completion_post_process = request_insight_pb2.CompletionPostProcess(
        filter_score=0.4,
        applied_filter_threshold=0.8,
        filter_reason=filter_reason,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_post_process.CopyFrom(completion_post_process)

    completion_post_process_row = bigquery_persistence._completion_post_process_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "filter_score": 0.4,
        "applied_filter_threshold": 0.8,
        "filter_reason": filter_name,
    }
    assert completion_post_process_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_parenthesis_truncation(bigquery_persistence, tenant_info):
    now = datetime.now()
    parenthesis_truncation = request_insight_pb2.ParenthesisTruncation(
        original_text="abc(a,b)",
        truncated_text="abc(",
        was_truncated=True,
        could_have_truncated=True,
        path="test.py",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.parenthesis_truncation.CopyFrom(parenthesis_truncation)

    parenthesis_truncation_row = bigquery_persistence._parenthesis_truncation_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "was_truncated": True,
        "could_have_truncated": True,
    }
    assert parenthesis_truncation_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "original_length": 8,
        "truncated_length": 4,
        "path_extension": ".py",
    }


def test_write_request_event(bigquery_persistence):
    bigquery_persistence._bigquery_client = MagicMock()
    event_type_to_mock = dict()
    for table in bigquery_persistence._request_event_tables:
        mock = MagicMock()
        table.table = mock
        event_type_to_mock[table.event_type] = mock

    def get_prom_row_count(labels):
        # Default to 0 if no samples
        val = REGISTRY.get_sample_value(
            "au_bigquery_table_insert_row_count_total", labels
        )
        return val if val is not None else 0

    labels_success = {
        "table_name": str(event_type_to_mock["completion_resolution"]),
        "error": "",
    }
    labels_error = {
        "table_name": str(event_type_to_mock["completion_resolution"]),
        "error": "testFailure",
    }
    initial_success = get_prom_row_count(labels_success)
    initial_error = get_prom_row_count(labels_error)

    now = datetime.now()
    completion_resolution = request_insight_pb2.CompletionResolution(accepted_idx=1)
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.completion_resolution.CopyFrom(completion_resolution)

    request = request_insight_pb2.UpdateRequestInfoRequest()
    request.request_id = "test-request"
    request.events.append(event)
    pubsub_message = request_insight_pb2.RequestInsightMessage(
        update_request_info_request=request
    )

    bigquery_persistence.write([pubsub_message])
    assert bigquery_persistence._bigquery_client.insert_rows.call_count == 1
    assert (
        bigquery_persistence._bigquery_client.insert_rows.call_args_list[0][0][0]
        == event_type_to_mock["completion_resolution"]
    )

    assert get_prom_row_count(labels_success) - initial_success == 1
    assert get_prom_row_count(labels_error) - initial_error == 0

    # Test that metrics are valid if an insert fails
    fake_insert_error = [
        {
            "index": 0,
            "errors": [
                {
                    "reason": "testFailure",
                    "message": "unit test failure",
                    "debugInfo": "something",
                }
            ],
        }
    ]
    bigquery_persistence._bigquery_client.insert_rows = MagicMock(
        return_value=fake_insert_error
    )
    bigquery_persistence.write([pubsub_message])
    assert get_prom_row_count(labels_success) - initial_success == 1
    assert get_prom_row_count(labels_error) - initial_error == 1


def test_write_session_event(bigquery_persistence, tenant_info):
    bigquery_persistence._bigquery_client = MagicMock()
    event_type_to_mock = dict()
    for table in bigquery_persistence._session_event_tables:
        mock = MagicMock()
        table.table = mock
        event_type_to_mock[table.event_type] = mock

    def get_prom_row_count(labels):
        # Default to 0 if no samples
        val = REGISTRY.get_sample_value(
            "au_bigquery_table_insert_row_count_total", labels
        )
        return val if val is not None else 0

    labels_success = {
        "table_name": str(event_type_to_mock["next_edit_session_event"]),
        "error": "",
    }
    labels_error = {
        "table_name": str(event_type_to_mock["next_edit_session_event"]),
        "error": "testFailure",
    }
    initial_success = get_prom_row_count(labels_success)
    initial_error = get_prom_row_count(labels_error)

    # Test a session event.
    next_edit_session_event = request_insight_pb2.NextEditSessionEvent(
        related_request_id="test-request-id",
        related_suggestion_id="test-suggestion-id",
        event_name="test-event-name",
        event_source="test-event-source",
        user_agent="test-user-agent",
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(datetime.now())
    event.next_edit_session_event.CopyFrom(next_edit_session_event)

    request = request_insight_pb2.RecordSessionEventsRequest()
    request.session_id = "test-session"
    request.tenant_info.CopyFrom(tenant_info)
    request.events.append(event)
    pubsub_message = request_insight_pb2.RequestInsightMessage(
        record_session_events_request=request
    )

    bigquery_persistence.write([pubsub_message])
    assert bigquery_persistence._bigquery_client.insert_rows.call_count == 1
    assert (
        bigquery_persistence._bigquery_client.insert_rows.call_args_list[0][0][0]
        == event_type_to_mock["next_edit_session_event"]
    )
    assert get_prom_row_count(labels_success) - initial_success == 1
    assert get_prom_row_count(labels_error) - initial_error == 0

    # Test that metrics are valid if an insert fails
    fake_insert_error = [
        {
            "index": 0,
            "errors": [
                {
                    "reason": "testFailure",
                    "message": "unit test failure",
                    "debugInfo": "something",
                }
            ],
        }
    ]
    bigquery_persistence._bigquery_client.insert_rows = MagicMock(
        return_value=fake_insert_error
    )
    bigquery_persistence.write([pubsub_message])
    assert get_prom_row_count(labels_success) - initial_success == 1
    assert get_prom_row_count(labels_error) - initial_error == 1


def test_write_full_export_user_events(bigquery_persistence):
    """Test that we don't write old full-export user events to BigQuery."""
    bigquery_persistence._bigquery_client = MagicMock()
    event_type_to_mock = dict()
    for table in bigquery_persistence._request_event_tables:
        mock = MagicMock()
        table.table = mock
        event_type_to_mock[table.event_type] = mock

    event = request_insight_pb2.FullExportUserEvent()
    event.time.FromDatetime(datetime.now())
    event.file_path = "/test-path"
    event.completion_request_id_issued.CopyFrom(
        request_insight_pb2.CompletionRequestIdIssuedEvent(
            request_id="test-request",
        )
    )

    request = request_insight_pb2.RecordFullExportUserEventsRequest()
    request.session_id = "test-session"
    request.user_id = "test-user"
    request.extension_data.user_events.append(event)
    pubsub_message = request_insight_pb2.RequestInsightMessage(
        record_full_export_user_events_request=request
    )

    bigquery_persistence.write([pubsub_message])
    assert bigquery_persistence._bigquery_client.insert_rows.call_count == 0


def test_get_sanitized_path_extension(bigquery_persistence):
    assert bigquery_persistence._get_sanitized_path_extension("foo.py") == ".py"
    assert bigquery_persistence._get_sanitized_path_extension("bar/foo.JAVA") == ".java"
    assert bigquery_persistence._get_sanitized_path_extension("foo.bar.py") == ".py"
    assert bigquery_persistence._get_sanitized_path_extension("foo.sensitive") is None
    assert bigquery_persistence._get_sanitized_path_extension("foo") is None
    assert bigquery_persistence._get_sanitized_path_extension("") is None


def test_keyword_categories_duplication(bigquery_persistence):
    # verify there are no duplicate keywords in keyword categories, remembering to aggregate across keys and values
    all_keywords = list(bigquery_persistence._keyword_categories.keys()) + [
        keyword
        for category_keywords in bigquery_persistence._keyword_categories.values()
        for keyword in category_keywords
    ]
    for keyword in all_keywords:
        assert all_keywords.count(keyword) == 1


def test_share_save_chat_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    request = request_insight_pb2.RIShareSaveChatRequest(
        request=share_pb2.SaveChatConversationRequest(
            user="test-user",
            conversation_id="test-conversation-id",
            title="test-title",
            chat=[
                share_pb2.ChatExchange(
                    message="test-message",
                    response="test-response",
                    request_id="test-request-id",
                )
            ],
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.share_save_chat_request.CopyFrom(request)

    save_chat_request_row = bigquery_persistence._share_save_chat_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "request": {
            "conversation_id": "test-conversation-id",
            "title": "test-title",
            "chat": [
                {
                    "request_id": "test-request-id",
                }
            ],
        }
    }

    assert save_chat_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_share_save_chat_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    response = request_insight_pb2.RIShareSaveChatResponse(
        response=share_pb2.SaveChatConversationResponse(
            uuid="test-uuid",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.share_save_chat_response.CopyFrom(response)

    save_chat_response_row = bigquery_persistence._share_save_chat_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "response": {
            "uuid": "test-uuid",
        }
    }

    assert save_chat_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_share_get_chat_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    request = request_insight_pb2.RIShareGetChatRequest(
        request=share_pb2.GetChatConversationRequest(
            uuid="test-uuid",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.share_get_chat_request.CopyFrom(request)

    get_chat_request_row = bigquery_persistence._share_get_chat_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "request": {
            "uuid": "test-uuid",
        }
    }

    assert get_chat_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_share_get_chat_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    now_ts = Timestamp()
    now_ts.FromDatetime(now)
    response = request_insight_pb2.RIShareGetChatResponse(
        #   string uuid = 1;
        #   google.protobuf.Timestamp date = 2;
        #   string user = 3;
        #   string conversation_id = 4;
        #   repeated ChatExchange chat = 5;
        #   string title = 6;
        response=share_pb2.GetChatConversationResponse(
            uuid="test-uuid",
            date=now_ts,
            user="test-user",
            conversation_id="test-conversation-id",
            chat=[
                share_pb2.ChatExchange(
                    message="test-message",
                    response="test-response",
                    request_id="test-request-id",
                )
            ],
            title="test-title",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.share_get_chat_response.CopyFrom(response)

    get_chat_response_row = bigquery_persistence._share_get_chat_response_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "response": {
            "uuid": "test-uuid",
            "date": now_ts.ToJsonString(),
            "conversation_id": "test-conversation-id",
            "title": "test-title",
            "chat": [
                {
                    "request_id": "test-request-id",
                }
            ],
        }
    }

    assert get_chat_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }


def test_github_app_installation_event_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    github_installation_event = request_insight_pb2.RIGithubAppInstallationEvent(
        event_type=request_insight_pb2.InstallEventType.INSTALL,
        status=status_pb2.Status(code=200, message="Installation successful"),
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.github_app_installation_event.CopyFrom(github_installation_event)

    github_app_installation_event_row = (
        bigquery_persistence._github_app_installation_event_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert github_app_installation_event_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "event_type": "INSTALL",
            "status": {
                "code": 200
                # Note: 'message' is not included in sanitized output
            },
        },
    }


def test_slackbot_installation_event_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    slackbot_installation_event = request_insight_pb2.RISlackbotInstallationEvent(
        event_type=request_insight_pb2.InstallEventType.INSTALL,
        status=status_pb2.Status(code=200, message="Installation successful"),
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.slackbot_installation_event.CopyFrom(slackbot_installation_event)

    slackbot_installation_event_row = (
        bigquery_persistence._slackbot_installation_event_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert slackbot_installation_event_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "event_type": "INSTALL",
            "status": {
                "code": 200
                # Note: 'message' is not included in sanitized output
            },
        },
    }


def test_add_user_to_tenant_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    user = auth_entities_pb2.User(
        id="test-user-id",
        email="<EMAIL>",
        nonce=123,
        tenants=["test-tenant"],
    )
    user.created_at.FromDatetime(now)
    user.in_usa.FromDatetime(now)
    add_user_to_tenant = request_insight_pb2.AddUserToTenant(user=user)
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.add_user_to_tenant.CopyFrom(add_user_to_tenant)

    add_user_to_tenant_row = bigquery_persistence._add_user_to_tenant_row(
        tenant_info, event
    )

    assert add_user_to_tenant_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "user": {
                "id": "test-user-id",
                "tenants": ["test-tenant"],
                "created_at": now.isoformat() + "Z",
                "in_usa": now.isoformat() + "Z",
            }
        },
        "augment_user_id": "test-user-id",
        "user_email": "<EMAIL>",
    }


def test_remove_user_from_tenant_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    user = auth_entities_pb2.User(
        id="test-user-id",
        email="<EMAIL>",
        nonce=123,
        tenants=["test-tenant"],
    )
    user.created_at.FromDatetime(now)
    user.in_usa.FromDatetime(now)
    remove_user_from_tenant = request_insight_pb2.RemoveUserFromTenant(user=user)
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.remove_user_from_tenant.CopyFrom(remove_user_from_tenant)

    remove_user_from_tenant_row = bigquery_persistence._user_removed_from_tenant_row(
        tenant_info, event
    )

    assert remove_user_from_tenant_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "user": {
                "id": "test-user-id",
                "tenants": ["test-tenant"],
                "created_at": now.isoformat() + "Z",
                "in_usa": now.isoformat() + "Z",
            }
        },
        "augment_user_id": "test-user-id",
        "user_email": "<EMAIL>",
    }


def test_invite_user_to_tenant_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    invitation = auth_entities_pb2.TenantInvitation(
        id="test-invitation-id",
        created_at=Timestamp(),
        inviter_user_id="test-inviter-user-id",
        inviter_email="<EMAIL>",
        invitee_email="<EMAIL>",
        status=auth_entities_pb2.TenantInvitation.Status.PENDING,
    )
    invitation.created_at.FromDatetime(now)
    invite_user_to_tenant = request_insight_pb2.InviteUserToTenant(
        invitation=invitation
    )
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.invite_user_to_tenant.CopyFrom(invite_user_to_tenant)

    invite_user_to_tenant_row = bigquery_persistence._invite_user_to_tenant_row(
        tenant_info, event
    )

    assert invite_user_to_tenant_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "invitation": {
                "id": "test-invitation-id",
                "created_at": now.isoformat() + "Z",
                "inviter_user_id": "test-inviter-user-id",
            }
        },
        "inviter_email": "<EMAIL>",
        "invitee_email": "<EMAIL>",
    }


def test_delete_invitation_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    delete_invitation = request_insight_pb2.DeleteInvitation(
        invitation_id="test-invitation-id",
    )
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.delete_invitation.CopyFrom(delete_invitation)

    delete_invitation_row = bigquery_persistence._delete_invitation_row(
        tenant_info, event
    )

    assert delete_invitation_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "invitation_id": "test-invitation-id",
        },
    }


def test_update_subscription_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    update_subscription = request_insight_pb2.UpdateSubscription(
        subscription_id="test-subscription-id",
        seats=10,
    )
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.update_subscription.CopyFrom(update_subscription)

    update_subscription_row = bigquery_persistence._update_subscription_row(
        tenant_info, event
    )

    assert update_subscription_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "subscription_id": "test-subscription-id",
            "seats": 10,
        },
    }


def test_create_tenant_for_team_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    create_tenant_for_team = request_insight_pb2.CreateTenantForTeam(
        tenant=tenant_watcher_pb2.Tenant(
            id="test-tenant-id",
            name="test-tenant-name",
            shard_namespace="test-namespace",
            cloud="GCP_US_CENTRAL1_DEV",
            tier=tenant_watcher_pb2.TenantTier.PROFESSIONAL,
            config=tenant_watcher_pb2.Config(
                configs={"is_self_serve_team": "true"},
            ),
            version="test-version",
        ),
        admin_user_id="test-admin-user-id",
        subscription_id="test-subscription-id",
    )
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.create_tenant_for_team.CopyFrom(create_tenant_for_team)

    create_tenant_for_team_row = bigquery_persistence._create_tenant_for_team_row(
        tenant_info, event
    )

    assert create_tenant_for_team_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "tenant": {
                "id": "test-tenant-id",
                "name": "test-tenant-name",
                "shard_namespace": "test-namespace",
                "cloud": "GCP_US_CENTRAL1_DEV",
                "tier": "PROFESSIONAL",
                "config": {"configs": {"is_self_serve_team": "true"}},
            },
            "admin_user_id": "test-admin-user-id",
            "subscription_id": "test-subscription-id",
        },
    }


def test_accept_invitation_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    user = auth_entities_pb2.User(
        id="test-user-id",
        email="<EMAIL>",
        nonce=123,
        tenants=["test-tenant"],
    )
    user.created_at.FromDatetime(now)
    user.in_usa.FromDatetime(now)
    accept_invitation = request_insight_pb2.AcceptInvitation(
        invitation_id="test-invitation-id",
        user=user,
    )
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.accept_invitation.CopyFrom(accept_invitation)

    accept_invitation_row = bigquery_persistence._accept_invitation_row(
        tenant_info, event
    )

    assert accept_invitation_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "invitation_id": "test-invitation-id",
            "user": {
                "id": "test-user-id",
                "tenants": ["test-tenant"],
                "created_at": now.isoformat() + "Z",
                "in_usa": now.isoformat() + "Z",
            },
        },
        "invitee_email": "<EMAIL>",
    }


def test_decline_invitation_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    decline_invitation = request_insight_pb2.DeclineInvitation(
        invitation_id="test-invitation-id",
        invitee_email="<EMAIL>",
    )
    event = request_insight_pb2.TenantEvent()
    event.time.FromDatetime(now)
    event.decline_invitation.CopyFrom(decline_invitation)

    decline_invitation_row = bigquery_persistence._decline_invitation_row(
        tenant_info, event
    )

    assert decline_invitation_row == {
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "sanitized_json": {
            "invitation_id": "test-invitation-id",
        },
        "invitee_email": "<EMAIL>",
    }


def test_sanitize_glean_response_row(bigquery_persistence, tenant_info):
    """Tests that glean response rows are properly sanitized."""
    now = datetime.now()

    # Create a document with sensitive fields
    created_at = Timestamp()
    created_at.FromDatetime(datetime(2023, 7, 19, 16, 0, 0))
    document = glean_pb2.Document(
        document_id="test-doc",
        document_type=glean_pb2.DocumentType.CONVERSATION,
        data_source=glean_pb2.DataSource.SLACK,
        visibility=glean_pb2.Document.Visibility.PUBLIC_VISIBLE,
        title="Test Document",
        url="https://example.com",
        content="sensitive content",
        author=glean_pb2.Person(id="OBSCURED_AUTHOR", name="sensitive author name"),
        created_at=created_at,
        snippets=[
            glean_pb2.Document.Snippet(
                document_id="test-doc",
                text="sensitive snippet text",
                start_range=2,
                end_range=10,
            )
        ],
        children=[
            glean_pb2.Document(
                document_id="test-child-doc",
                document_type=glean_pb2.DocumentType.MESSAGE,
                data_source=glean_pb2.DataSource.SLACK,
                visibility=glean_pb2.Document.Visibility.PUBLIC_VISIBLE,
                content="sensitive child content",
                author=glean_pb2.Person(
                    id="OBSCURED_AUTHOR_2", name="another sensitive name"
                ),
                created_at=created_at,
            )
        ],
        data_source_name="SLACK",
    )

    # Create a glean response with the document
    glean_response = request_insight_pb2.RIGleanResponse(
        response=glean_pb2.SearchResponse(documents=[document]),
        generate_search_queries_request=request_insight_pb2.ThirdPartyModelRequest(
            prompt="test prompt",
            system_prompt="test system prompt",
            messages=[],
        ),
        generate_search_queries_response=["test query", "test query 2"],
    )

    # Create an event with the glean response
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.glean_response.CopyFrom(glean_response)

    # Get the sanitized row
    glean_response_row = bigquery_persistence._glean_response_row(
        "test-request", "test-session", tenant_info, event
    )

    # Verify the sanitized row
    expected_sanitized_json = {
        "documents": [
            {
                "document_id": "test-doc",
                "document_type": "CONVERSATION",
                "created_at": "2023-07-19T16:00:00Z",
                "updated_at": "2023-07-19T16:00:00Z",
                "data_source": "SLACK",
                "author": {
                    "id": "OBSCURED_AUTHOR",
                },
                "visibility": "PUBLIC_VISIBLE",
                "snippets": [
                    {"document_id": "test-doc", "start_range": 2, "end_range": 10}
                ],
                "children": [
                    {
                        "document_id": "test-child-doc",
                        "document_type": "MESSAGE",
                        "created_at": "2023-07-19T16:00:00Z",
                        "updated_at": "2023-07-19T16:00:00Z",
                        "data_source": "SLACK",
                        "author": {
                            "id": "OBSCURED_AUTHOR_2",
                        },
                        "visibility": "PUBLIC_VISIBLE",
                    }
                ],
                "data_source_name": "SLACK",
            }
        ]
    }

    assert glean_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "response": expected_sanitized_json,
        },
        "has_oauth_url": False,
        "generated_search_query_count": 2,
        "generated_search_query_words": [2, 3],
        "result_statistics": [
            {
                "document_id": "test-doc",
                "source": "SLACK",
                "created_at": datetime(2023, 7, 19, 16, 0),
                "updated_at": None,
                "obfuscated_author_id": "OBSCURED_AUTHOR",
                "nested_document_count": 2,
                "character_count": 40,
                "line_count": 2,
                "snippet_count": 1,
            },
        ],
    }


def test_sanitize_glean_request_row(bigquery_persistence, tenant_info):
    """Tests that glean request rows are properly sanitized."""
    now = datetime.now()

    # Create a glean request with search parameters
    glean_request = request_insight_pb2.RIGleanRequest(
        request=glean_pb2.SearchRequest(
            query="sensitive search query",
            include_private_docs=True,
            user_id="test-user",
        ),
        request_source="web",
        query_processor_config=request_insight_pb2.RIGleanRequest.RIGleanQueryProcessorConfig(
            gcp_project_id="test-project",
            gcp_region="us-central1",
            model_name="test-model",
            temperature=0.1,
            max_output_tokens=1024,
            max_results=5,
        ),
    )

    # Create an event with the glean request
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.glean_request.CopyFrom(glean_request)

    # Get the sanitized row
    glean_request_row = bigquery_persistence._glean_request_row(
        "test-request", "test-session", tenant_info, event
    )

    # Verify the sanitized row
    expected_sanitized_json = {
        "request": {
            "include_private_docs": True,
            "user_id": "test-user",
        },
        "request_source": "web",
        "query_processor_config": {
            "gcp_project_id": "test-project",
            "gcp_region": "us-central1",
            "model_name": "test-model",
            "temperature": 0.1,
            "max_output_tokens": 1024,
            "max_results": 5,
        },
    }

    assert glean_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_sanitized_json,
        "user_query_character_count": 22,
        "user_query_line_count": 1,
        "user_query_word_count": 3,
    }


def test_sanitize_url():
    """Test that _sanitize_url properly handles various URL formats."""

    # Test normal relative URL without sensitive parts.
    assert _sanitize_url("/dashboard/overview") == "/dashboard/overview"

    # Test absolute URL with query parameters and fragments that should be sanitized.
    complex_url = (
        "https://example.com/dashboard?user=sensitive&token=secret#private-section"
    )
    assert _sanitize_url(complex_url) == "https://example.com/dashboard"

    # Test empty URL.
    assert _sanitize_url("") == ""

    # Test invalid URL.
    assert _sanitize_url("not::/a valid url") == "/[INVALID_URL]"


def test_sanitize_customer_ui_session_start():
    """Tests CustomerUISessionEvent.SessionStart sanitization with redirect URLs."""
    # Test case 1: Session start with simple redirect URL
    session_start_simple = request_insight_pb2.CustomerUISessionEvent.SessionStart(
        redirect_url="/dashboard/overview"
    )
    sanitized_simple = _sanitize_customer_ui_session_start(session_start_simple)
    assert (
        sanitized_simple.redirect_url == "/dashboard/overview"
    ), "Simple redirect URL should remain unchanged"

    # Test case 2: Session start with sensitive redirect URL
    session_start_complex = request_insight_pb2.CustomerUISessionEvent.SessionStart(
        redirect_url="https://example.com/path?token=secret&user=sensitive#private"
    )
    sanitized_complex = _sanitize_customer_ui_session_start(session_start_complex)
    assert (
        sanitized_complex.redirect_url == "https://example.com/path"
    ), "Redirect URL should be sanitized to remove sensitive parameters"


def test_sanitize_customer_ui_session_end():
    session_end = request_insight_pb2.CustomerUISessionEvent.SessionEnd(
        reason=request_insight_pb2.CustomerUISessionEvent.SessionEnd.LOGOUT
    )
    sanitized = _sanitize_customer_ui_session_end(session_end)

    assert (
        sanitized.reason == request_insight_pb2.CustomerUISessionEvent.SessionEnd.LOGOUT
    ), "SessionEnd reason field should be preserved and match LOGOUT"
    assert (
        len(sanitized.ListFields()) == 1
    ), "Sanitized SessionEnd should only have the reason field"


def test_customer_ui_session_event_row_session_start(bigquery_persistence, tenant_info):
    now = datetime.now()
    customer_ui_session_event = request_insight_pb2.CustomerUISessionEvent(
        user_id="test-user-id",
        session_start=request_insight_pb2.CustomerUISessionEvent.SessionStart(
            redirect_url="/dashboard/overview"
        ),
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.customer_ui_session_event.CopyFrom(customer_ui_session_event)

    customer_ui_session_event_row = bigquery_persistence._customer_ui_session_event_row(
        "test-session", auth_entities_pb2.UserId(), tenant_info, event
    )

    expected_json = {
        "user_id": "test-user-id",
        "session_start": {"redirect_url": "/dashboard/overview"},
    }

    assert (
        customer_ui_session_event_row
        == {
            "session_id": "test-session",
            "tenant": "test_tenant",
            "tenant_id": "123",
            "shard_namespace": "test_namespace",
            "time": now,
            "sanitized_json": expected_json,
        }
    ), "Session start event row should match expected structure with empty session_start object"


def test_customer_ui_session_event_row_session_end(bigquery_persistence, tenant_info):
    now = datetime.now()
    customer_ui_session_event = request_insight_pb2.CustomerUISessionEvent(
        user_id="test-user-id",
        session_end=request_insight_pb2.CustomerUISessionEvent.SessionEnd(
            reason=request_insight_pb2.CustomerUISessionEvent.SessionEnd.LOGOUT
        ),
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.customer_ui_session_event.CopyFrom(customer_ui_session_event)

    customer_ui_session_event_row = bigquery_persistence._customer_ui_session_event_row(
        "test-session", auth_entities_pb2.UserId(), tenant_info, event
    )

    expected_json = {
        "user_id": "test-user-id",
        "session_end": {
            "reason": "LOGOUT",
        },
    }

    assert customer_ui_session_event_row == {
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Session end event row should match expected structure with LOGOUT reason"


def test_tool_use_data_row_without_mcp_tool(bigquery_persistence, tenant_info):
    now = datetime.now()
    tool_use_data = request_insight_pb2.ToolUseData(
        tool_name="test-tool-name",
        tool_use_id="test-tool-use-id",
        tool_output_is_error=True,
        tool_run_duration_ms=12345,
        is_mcp_tool=False,
        conversation_id="test-conversation-id",
        chat_history_length=10,
        tool_request_id="test-tool-request-id",
        tool_output_len=500,
        tool_input_len=200,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.tool_use_data.CopyFrom(tool_use_data)

    tool_use_data_row = bigquery_persistence._tool_use_data_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "tool_name": "test-tool-name",
        "tool_use_id": "test-tool-use-id",
        "tool_output_is_error": True,
        "tool_run_duration_ms": 12345,
        "is_mcp_tool": False,
        "conversation_id": "test-conversation-id",
        "chat_history_length": 10,
        "tool_request_id": "test-tool-request-id",
        "tool_output_len": 500,
        "tool_input_len": 200,
    }

    assert tool_use_data_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Tool use data row should match expected structure"


def test_tool_use_data_row_with_mcp_tool(bigquery_persistence, tenant_info):
    now = datetime.now()
    tool_use_data = request_insight_pb2.ToolUseData(
        is_mcp_tool=True,
        tool_name="test-tool-name",
        tool_use_id="test-tool-use-id",
        tool_output_is_error=True,
        tool_run_duration_ms=12345,
        conversation_id="test-conversation-id",
        chat_history_length=10,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.tool_use_data.CopyFrom(tool_use_data)

    tool_use_data_row = bigquery_persistence._tool_use_data_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "tool_name": "mcp_tool",
        "tool_use_id": "test-tool-use-id",
        "tool_output_is_error": True,
        "tool_run_duration_ms": 12345,
        "is_mcp_tool": True,
        "conversation_id": "test-conversation-id",
        "chat_history_length": 10,
        "tool_request_id": "",
        "tool_output_len": 0,
        "tool_input_len": 0,
    }

    assert tool_use_data_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Tool use data row should match expected structure"


def test_tool_use_data_row_with_launch_process_tool(bigquery_persistence, tenant_info):
    now = datetime.now()

    # First test an export of an allowed command prefix
    tool_input = '{"command": "git push my_cool_branch"}'
    tool_use_data = request_insight_pb2.ToolUseData(
        tool_name="launch-process",
        tool_use_id="test-tool-use-id",
        tool_output_is_error=True,
        tool_run_duration_ms=12345,
        tool_input=tool_input,
        conversation_id="test-conversation-id",
        chat_history_length=10,
        tool_input_len=len(tool_input),
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.tool_use_data.CopyFrom(tool_use_data)

    tool_use_data_row = bigquery_persistence._tool_use_data_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "tool_name": "launch-process",
        "tool_use_id": "test-tool-use-id",
        "tool_output_is_error": True,
        "tool_run_duration_ms": 12345,
        "is_mcp_tool": False,
        "conversation_id": "test-conversation-id",
        "chat_history_length": 10,
        "tool_request_id": "",
        "tool_output_len": 0,
        "tool_input_len": len(tool_input),
    }

    assert tool_use_data_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "terminal_command_prefix": "git push",
    }, "Tool use data row should match expected structure"

    # Now test a command that is not allowed
    tool_input = '{"command": "bazel test //..."}'
    tool_use_data = request_insight_pb2.ToolUseData(
        tool_name="launch-process",
        tool_use_id="test-tool-use-id",
        tool_output_is_error=True,
        tool_run_duration_ms=12345,
        tool_input=tool_input,
        conversation_id="test-conversation-id",
        chat_history_length=10,
        tool_input_len=len(tool_input),
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.tool_use_data.CopyFrom(tool_use_data)

    tool_use_data_row = bigquery_persistence._tool_use_data_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "tool_name": "launch-process",
        "tool_use_id": "test-tool-use-id",
        "tool_output_is_error": True,
        "tool_run_duration_ms": 12345,
        "is_mcp_tool": False,
        "conversation_id": "test-conversation-id",
        "chat_history_length": 10,
        "tool_request_id": "",
        "tool_output_len": 0,
        "tool_input_len": len(tool_input),
    }

    # Note that there is no terminal_command_prefix here
    assert tool_use_data_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Tool use data row should match expected structure"


def test_agent_session_event_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    agent_session_event = request_insight_pb2.AgentSessionEvent(
        event_name="test-event-name",
        user_agent="test-user-agent",
        conversation_id="test-conversation-id",
        agent_interruption_data=request_insight_pb2.AgentInterruptionData(
            request_id="test-request-id", curr_conversation_length=123
        ),
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.agent_session_event.CopyFrom(agent_session_event)

    agent_session_event_row = bigquery_persistence._agent_session_event_row(
        "test-session", auth_entities_pb2.UserId(), tenant_info, event
    )

    expected_json = {
        "event_name": "test-event-name",
        "user_agent": "test-user-agent",
        "conversation_id": "test-conversation-id",
        "agent_interruption_data": {
            "request_id": "test-request-id",
            "curr_conversation_length": 123,
        },
    }

    assert agent_session_event_row == {
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Agent session event row should match expected structure"


def test_remote_agent_session_event_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agent_session_event = request_insight_pb2.RemoteAgentSessionEvent(
        event_name="test-event-name",
        user_agent="test-user-agent",
        remote_agent_id="test-remote-agent-id",
        diff_panel_data=request_insight_pb2.DiffPanelData(
            loading_time_ms=123,
            applied=True,
        ),
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.remote_agent_session_event.CopyFrom(remote_agent_session_event)

    remote_agent_session_event_row = (
        bigquery_persistence._remote_agent_session_event_row(
            "test-session", auth_entities_pb2.UserId(), tenant_info, event
        )
    )

    expected_json = {
        "event_name": "test-event-name",
        "user_agent": "test-user-agent",
        "remote_agent_id": "test-remote-agent-id",
        "diff_panel_data": {
            "loading_time_ms": 123,
            "applied": True,
        },
    }

    assert remote_agent_session_event_row == {
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Remote agent session event row should match expected structure"


def test_sanitize_agent_tracing_data():
    # Create original data with mix of allowed and disallowed keys
    now = Timestamp()
    now.GetCurrentTime()
    original_data = request_insight_pb2.AgentTracingData()

    # Add test data for each collection type
    original_data.flags["allowed_key"].timestamp.CopyFrom(now)
    original_data.flags["allowed_key"].value = True
    original_data.flags["disallowed_key"].timestamp.CopyFrom(now)
    original_data.flags["disallowed_key"].value = False

    original_data.nums["allowed_key"].timestamp.CopyFrom(now)
    original_data.nums["allowed_key"].value = 42

    valid_uuid = "12345678-1234-5678-1234-************"
    original_data.request_ids["allowed_key"].timestamp.CopyFrom(now)
    original_data.request_ids["allowed_key"].value = valid_uuid
    original_data.request_ids["invalid_uuid"].timestamp.CopyFrom(now)
    original_data.request_ids["invalid_uuid"].value = "not-valid"

    # Define allowed keys and sanitize
    allowed_keys = ["allowed_key"]
    sanitized = request_insight_pb2.AgentTracingData()
    result = _sanitize_agent_tracing_data(sanitized, original_data, allowed_keys)

    # Verify filtering works correctly
    assert "allowed_key" in result.flags
    assert "disallowed_key" not in result.flags
    assert "allowed_key" in result.nums
    assert "allowed_key" in result.request_ids
    assert "invalid_uuid" not in result.request_ids


def test_sanitize_remember_tool_call_data():
    # Create original data with tracing data
    now = Timestamp()
    now.GetCurrentTime()
    original_data = request_insight_pb2.RememberToolCallData()
    original_data.caller = (
        request_insight_pb2.RememberToolCallData.RememberToolCaller.CLASSIFY_AND_DISTILL
    )
    original_data.is_complex_new_memory = True

    # Add an allowed key
    original_data.tracing_data.flags["compressionStarted"].timestamp.CopyFrom(now)
    original_data.tracing_data.flags["compressionStarted"].value = True

    # Call the function
    result = _sanitize_remember_tool_call_data(original_data)

    # Verify basic fields and allowed key preservation
    assert (
        result.caller
        == request_insight_pb2.RememberToolCallData.RememberToolCaller.CLASSIFY_AND_DISTILL
    )
    assert result.is_complex_new_memory is True
    assert "compressionStarted" in result.tracing_data.flags
    assert result.tracing_data.flags["compressionStarted"].value is True


def test_agent_request_event_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    agent_request_event = request_insight_pb2.AgentRequestEvent(
        event_name="test-event-name",
        user_agent="test-user-agent",
        conversation_id="test-conversation-id",
        chat_history_length=123,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.agent_request_event.CopyFrom(agent_request_event)

    agent_request_event_row = bigquery_persistence._agent_request_event_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {
        "event_name": "test-event-name",
        "user_agent": "test-user-agent",
        "conversation_id": "test-conversation-id",
        "chat_history_length": 123,
    }

    assert agent_request_event_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Agent request event row should match expected structure"


def test_sanitize_initial_orientation_data():
    now = Timestamp()
    now.GetCurrentTime()
    original_data = request_insight_pb2.InitialOrientationData()
    original_data.caller = (
        request_insight_pb2.InitialOrientationData.OrientationCaller.ONBOARDING
    )

    # Add allowed and disallowed keys
    original_data.tracing_data.flags["start"].timestamp.CopyFrom(now)
    original_data.tracing_data.flags["start"].value = True
    original_data.tracing_data.flags["disallowed_key"].timestamp.CopyFrom(now)
    original_data.tracing_data.flags["disallowed_key"].value = False

    result = _sanitize_initial_orientation_data(original_data)

    assert (
        result.caller
        == request_insight_pb2.InitialOrientationData.OrientationCaller.ONBOARDING
    )
    assert "start" in result.tracing_data.flags
    assert "disallowed_key" not in result.tracing_data.flags


def test_sanitize_classify_and_distill_data():
    now = Timestamp()
    now.GetCurrentTime()
    original_data = request_insight_pb2.ClassifyAndDistillData()

    original_data.tracing_data.flags["start"].timestamp.CopyFrom(now)
    original_data.tracing_data.flags["start"].value = True
    original_data.tracing_data.flags["disallowed_key"].timestamp.CopyFrom(now)
    original_data.tracing_data.flags["disallowed_key"].value = False

    result = _sanitize_classify_and_distill_data(original_data)

    assert "start" in result.tracing_data.flags
    assert "disallowed_key" not in result.tracing_data.flags


def test_sanitize_flush_memories_data():
    now = Timestamp()
    now.GetCurrentTime()
    original_data = request_insight_pb2.FlushMemoriesData()

    original_data.tracing_data.flags["start"].timestamp.CopyFrom(now)
    original_data.tracing_data.flags["start"].value = True
    original_data.tracing_data.flags["disallowed_key"].timestamp.CopyFrom(now)
    original_data.tracing_data.flags["disallowed_key"].value = False

    result = _sanitize_flush_memories_data(original_data)

    assert "start" in result.tracing_data.flags
    assert "disallowed_key" not in result.tracing_data.flags


def test_remote_tool_call_request_row_codebase_retrieval(
    bigquery_persistence, tenant_info
):
    now = datetime.now()
    remote_tool_call_request = request_insight_pb2.RIRemoteToolCallRequest(
        codebase_retrieval_request=agents_pb2.CodebaseRetrievalRequest(
            information_request="test information request", max_output_length=1000
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_tool_call_request.CopyFrom(remote_tool_call_request)

    request_row = bigquery_persistence._remote_tool_call_request_row(
        "test-request", "test-session", tenant_info, event
    )

    expected_json = {"codebase_retrieval_request": {"max_output_length": 1000}}

    assert (
        request_row
        == {
            "request_id": "test-request",
            "session_id": "test-session",
            "tenant": "test_tenant",
            "tenant_id": "123",
            "shard_namespace": "test_namespace",
            "time": now,
            "sanitized_json": expected_json,
        }
    ), "Remote tool call request row with codebase retrieval should match expected structure"


def test_remote_tool_call_request_row_edit_file(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_tool_call_request = request_insight_pb2.RIRemoteToolCallRequest(
        edit_file_request=agents_pb2.EditFileRequest(
            file_path="test/file/path.py",
            edit_summary="test edit summary",
            detailed_edit_description="test detailed edit description",
            file_contents="test file contents",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_tool_call_request.CopyFrom(remote_tool_call_request)

    request_row = bigquery_persistence._remote_tool_call_request_row(
        "test-request", "test-session", tenant_info, event
    )

    # The sanitized JSON should only contain an empty edit_file_request since all content is PII
    expected_json = {"edit_file_request": {}}

    assert request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
    }, "Remote tool call request row with edit file should match expected structure"


def test_remote_tool_call_request_row_run_remote_tool(
    bigquery_persistence, tenant_info
):
    now = datetime.now()
    remote_tool_call_request = request_insight_pb2.RIRemoteToolCallRequest(
        ri_run_remote_tool_request=request_insight_pb2.RIRunRemoteToolRequest(
            tool_name="test-tool-name",
            tool_input_json='{"param": "value"}',
            tool_id=agents_pb2.RemoteToolId.GITHUB_API,
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_tool_call_request.CopyFrom(remote_tool_call_request)

    request_row = bigquery_persistence._remote_tool_call_request_row(
        "test-request", "test-session", tenant_info, event
    )

    # The sanitized JSON should only contain tool_name and tool_id but NOT the actual tool_input_json
    expected_json = {
        "ri_run_remote_tool_request": {
            "tool_name": "test-tool-name",
            "tool_id": "GITHUB_API",
        }
    }

    assert (
        request_row
        == {
            "request_id": "test-request",
            "session_id": "test-session",
            "tenant": "test_tenant",
            "tenant_id": "123",
            "shard_namespace": "test_namespace",
            "time": now,
            "sanitized_json": expected_json,
        }
    ), "Remote tool call request row with run remote tool should match expected structure"


def test_remote_tool_call_response_row_codebase_retrieval(
    bigquery_persistence, tenant_info
):
    now = datetime.now()
    remote_tool_call_response = request_insight_pb2.RIRemoteToolCallResponse(
        codebase_retrieval_response=agents_pb2.CodebaseRetrievalResponse(
            formatted_retrieval="test formatted retrieval"
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_tool_call_response.CopyFrom(remote_tool_call_response)

    response_row = bigquery_persistence._remote_tool_call_response_row(
        "test-request", "test-session", tenant_info, event
    )

    # The sanitized JSON should only contain the empty codebase_retrieval_response
    # since the actual content is considered PII
    expected_json = {"codebase_retrieval_response": {}}

    assert (
        response_row
        == {
            "request_id": "test-request",
            "session_id": "test-session",
            "tenant": "test_tenant",
            "tenant_id": "123",
            "shard_namespace": "test_namespace",
            "time": now,
            "sanitized_json": expected_json,
            "tool_response_size_bytes": len("test formatted retrieval".encode("utf-8")),
        }
    ), "Remote tool call response row with codebase retrieval should match expected structure"


def test_remote_tool_call_response_row_edit_file(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_tool_call_response = request_insight_pb2.RIRemoteToolCallResponse(
        edit_file_response=agents_pb2.EditFileResponse(
            modified_file_contents="test modified file contents", is_error=True
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_tool_call_response.CopyFrom(remote_tool_call_response)

    response_row = bigquery_persistence._remote_tool_call_response_row(
        "test-request", "test-session", tenant_info, event
    )

    # The sanitized JSON should only contain is_error flag but not the actual file contents
    expected_json = {"edit_file_response": {"is_error": True}}

    assert response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": expected_json,
        "tool_response_size_bytes": len("test modified file contents".encode("utf-8")),
    }, "Remote tool call response row with edit file should match expected structure"


def test_remote_tool_call_response_row_run_remote_tool(
    bigquery_persistence, tenant_info
):
    now = datetime.now()
    remote_tool_call_response = request_insight_pb2.RIRemoteToolCallResponse(
        run_remote_tool_response=agents_pb2.RunRemoteToolResponse(
            tool_output="test tool output",
            tool_result_message="test result message",
            is_error=True,
            status=agents_pb2.RemoteToolResponseStatus.TOOL_EXECUTION_SUCCESS,
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_tool_call_response.CopyFrom(remote_tool_call_response)

    response_row = bigquery_persistence._remote_tool_call_response_row(
        "test-request", "test-session", tenant_info, event
    )

    # The sanitized JSON should only contain is_error and status flags but not the actual output
    expected_json = {
        "run_remote_tool_response": {
            "is_error": True,
            "status": "TOOL_EXECUTION_SUCCESS",
        }
    }

    assert (
        response_row
        == {
            "request_id": "test-request",
            "session_id": "test-session",
            "tenant": "test_tenant",
            "tenant_id": "123",
            "shard_namespace": "test_namespace",
            "time": now,
            "sanitized_json": expected_json,
            "tool_response_size_bytes": len("test tool output".encode("utf-8")),
        }
    ), "Remote tool call response row with run remote tool should match expected structure"


def test_request_blocked_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    request_blocked = request_insight_pb2.RequestBlocked(
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        user_email="<EMAIL>",
        check_type="test-check-type",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.request_blocked.CopyFrom(request_blocked)

    request_blocked_row = bigquery_persistence._request_blocked_row(
        "test-request", "test-session", tenant_info, event
    )

    assert request_blocked_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "user_email": "<EMAIL>",
        "check_type": "test-check-type",
        "sanitized_json": {},
    }, "Request blocked row should match expected structure"


def test_request_suspicious_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    request_suspicious = request_insight_pb2.RequestSuspicious(
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        user_email="<EMAIL>",
        check_type="test-check-type",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.request_suspicious.CopyFrom(request_suspicious)

    request_suspicious_row = bigquery_persistence._request_suspicious_row(
        "test-request", "test-session", tenant_info, event
    )

    assert request_suspicious_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "user_email": "<EMAIL>",
        "check_type": "test-check-type",
        "sanitized_json": {},
    }, "Request suspicious row should match expected structure"


def test_daily_request_limit_exceeded_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    limit_exceeded = request_insight_pb2.DailyRequestLimitExceeded(
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        limit=100,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.daily_request_limit_exceeded.CopyFrom(limit_exceeded)

    limit_exceeded_row = bigquery_persistence._daily_request_limit_exceeded_row(
        "test-request", "test-session", tenant_info, event
    )

    assert limit_exceeded_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "limit": 100,
        "sanitized_json": {},
    }, "Request suspicious row should match expected structure"


def test_prompt_cache_usage_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    prompt_cache_usage = request_insight_pb2.PromptCacheUsage(
        input_tokens=100,
        cache_read_input_tokens=50,
        cache_creation_input_tokens=50,
        model_caller="test-model-caller",
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.prompt_cache_usage.CopyFrom(prompt_cache_usage)

    prompt_cache_usage_row = bigquery_persistence._prompt_cache_usage_row(
        "test-request", "test-session", tenant_info, event
    )

    assert prompt_cache_usage_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "input_tokens": 100,
            "cache_read_input_tokens": 50,
            "cache_creation_input_tokens": 50,
            "model_caller": "test-model-caller",
        },
    }, "Prompt cache usage row should match expected structure"


def test_token_exchange_error_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    token_exchange_error = request_insight_pb2.TokenExchangeError(
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        user_email="<EMAIL>",
        reason=request_insight_pb2.TokenExchangeError.Reason.TENANT_NOT_IN_SHARD,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.token_exchange_error.CopyFrom(token_exchange_error)

    token_exchange_error_row = bigquery_persistence._token_exchange_error_row(
        "test-request", "test-session", tenant_info, event
    )

    assert token_exchange_error_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "user_email": "<EMAIL>",
        "reason": "TENANT_NOT_IN_SHARD",
        "sanitized_json": {},
    }, "Token exchange error row should match expected structure"


def test_find_missing_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    find_missing = request_insight_pb2.RIFindMissing(
        model_name="test-model",
        blob_count=10,
        missing_count=2,
        nonindexed_count=3,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.find_missing.CopyFrom(find_missing)

    find_missing_row = bigquery_persistence._find_missing_row(
        "test-request", "test-session", tenant_info, event
    )

    assert find_missing_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "model_name": "test-model",
        "blob_count": 10,
        "missing_count": 2,
        "nonindexed_count": 3,
        "sanitized_json": {},
    }, "Request find missing row should match expected structure"


def test_batch_upload_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    batch_upload = request_insight_pb2.RIBatchUpload(
        blob_count=10,
        total_size=1024,
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.batch_upload.CopyFrom(batch_upload)

    batch_upload_row = bigquery_persistence._batch_upload_row(
        "test-request", "test-session", tenant_info, event
    )

    assert batch_upload_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "blob_count": 10,
        "total_size": 1024,
        "sanitized_json": {},
    }, "Request batch upload row should match expected structure"


def test_remote_agents_create_request_row(
    bigquery_persistence, tenant_info, remote_agent_config
):
    now = datetime.now()
    remote_agents_create_request = request_insight_pb2.RemoteAgentsCreateRequest(
        request=remote_agents_pb2.CreateAgentRequest(
            config=remote_agent_config,
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_create_request.CopyFrom(remote_agents_create_request)
    remote_agents_create_request_row = (
        bigquery_persistence._remote_agents_create_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_create_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "request": {
                "config": {
                    "model": "test-model",
                    "is_setup_script_agent": False,
                    "workspace_setup": {
                        "github_ref": {
                            "ref": "main",
                        },
                    },
                },
            }
        },
    }, "Request remote agents create request row should match expected structure"

    # Try with a custom branch name
    remote_agents_create_request = request_insight_pb2.RemoteAgentsCreateRequest(
        request=remote_agents_pb2.CreateAgentRequest(
            config=remote_agent_config,
        )
    )
    remote_agents_create_request.request.config.workspace_setup.github_ref.ref = (
        "my_private_branch"
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_create_request.CopyFrom(remote_agents_create_request)
    remote_agents_create_request_row = (
        bigquery_persistence._remote_agents_create_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_create_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {
            "request": {
                "config": {
                    "model": "test-model",
                    "is_setup_script_agent": False,
                },
            }
        },
    }, "Request remote agents create request row should match expected structure"


def test_remote_agents_create_response_row(
    bigquery_persistence, tenant_info, remote_agent_agent
):
    now = datetime.now()
    remote_agents_create_response = request_insight_pb2.RemoteAgentsCreateResponse(
        response=remote_agents_pb2.CreateAgentResponse(
            agent=remote_agent_agent,
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_create_response.CopyFrom(remote_agents_create_response)
    remote_agents_create_response_row = (
        bigquery_persistence._remote_agents_create_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_create_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "agent_id": "test-agent",
        "sanitized_json": {
            "response": {
                "agent": {
                    "remote_agent_id": "test-agent",
                    "status": "AGENT_STATUS_STARTING",
                    "config": {"model": "test-model", "is_setup_script_agent": False},
                }
            }
        },
    }, "Request remote agents create response row should match expected structure"


def test_remote_agents_chat_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_chat_request = request_insight_pb2.RemoteAgentsChatRequest(
        request=remote_agents_pb2.ChatRequest(
            remote_agent_id="test-agent",
            request_details=remote_agents_pb2.ChatRequestDetails(
                request_nodes=[
                    chat_pb2.ChatRequestNode(
                        id=1,
                        type=chat_pb2.ChatRequestNodeType.TEXT,
                        text_node=chat_pb2.ChatRequestText(content="test-content"),
                    )
                ],
                user_guidelines="test-user-guidelines",
                workspace_guidelines="test-workspace-guidelines",
                agent_memories="test-agent-memories",
            ),
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_chat_request.CopyFrom(remote_agents_chat_request)
    remote_agents_chat_request_row = (
        bigquery_persistence._remote_agents_chat_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_chat_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "agent_id": "test-agent",
        "sanitized_json": {
            "request": {
                "remote_agent_id": "test-agent",
            },
        },
    }, "Request remote agents chat request row should match expected structure"


def test_remote_agents_chat_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_chat_response = request_insight_pb2.RemoteAgentsChatResponse()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_chat_response.CopyFrom(remote_agents_chat_response)
    remote_agents_chat_response_row = (
        bigquery_persistence._remote_agents_chat_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_chat_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }, "Request remote agents chat response row should match expected structure"


def test_remote_agents_interrupt_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_interrupt_request = request_insight_pb2.RemoteAgentsInterruptRequest(
        request=remote_agents_pb2.InterruptAgentRequest(
            remote_agent_id="test-agent",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_interrupt_request.CopyFrom(remote_agents_interrupt_request)
    remote_agents_interrupt_request_row = (
        bigquery_persistence._remote_agents_interrupt_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_interrupt_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "agent_id": "test-agent",
        "sanitized_json": {
            "request": {
                "remote_agent_id": "test-agent",
            },
        },
    }, "Request remote agents interrupt request row should match expected structure"


def test_remote_agents_interrupt_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_interrupt_response = (
        request_insight_pb2.RemoteAgentsInterruptResponse()
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_interrupt_response.CopyFrom(remote_agents_interrupt_response)
    remote_agents_interrupt_response_row = (
        bigquery_persistence._remote_agents_interrupt_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_interrupt_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }, "Request remote agents interrupt response row should match expected structure"


def test_remote_agents_delete_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_delete_request = request_insight_pb2.RemoteAgentsDeleteRequest(
        request=remote_agents_pb2.DeleteAgentRequest(
            remote_agent_id="test-agent",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_delete_request.CopyFrom(remote_agents_delete_request)
    remote_agents_delete_request_row = (
        bigquery_persistence._remote_agents_delete_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_delete_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "agent_id": "test-agent",
        "sanitized_json": {
            "request": {
                "remote_agent_id": "test-agent",
            },
        },
    }, "Request remote agents delete request row should match expected structure"


def test_remote_agents_delete_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_delete_response = request_insight_pb2.RemoteAgentsDeleteResponse()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_delete_response.CopyFrom(remote_agents_delete_response)
    remote_agents_delete_response_row = (
        bigquery_persistence._remote_agents_delete_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_delete_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }, "Request remote agents delete response row should match expected structure"


def test_remote_agents_add_ssh_key_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_add_ssh_key_request = (
        request_insight_pb2.RemoteAgentsAddSSHKeyRequest(
            request=remote_agents_pb2.AddSSHKeyRequest(
                remote_agent_id="test-agent",
                public_keys=["test-public-key"],
            )
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_add_ssh_key_request.CopyFrom(remote_agents_add_ssh_key_request)
    remote_agents_add_ssh_key_request_row = (
        bigquery_persistence._remote_agents_add_ssh_key_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_add_ssh_key_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "agent_id": "test-agent",
        "sanitized_json": {
            "request": {
                "remote_agent_id": "test-agent",
            },
        },
    }, "Request remote agents add ssh key request row should match expected structure"


def test_remote_agents_add_ssh_key_response_row(
    bigquery_persistence, tenant_info, remote_agent_ssh_config
):
    now = datetime.now()
    remote_agents_add_ssh_key_response = (
        request_insight_pb2.RemoteAgentsAddSSHKeyResponse(
            response=remote_agents_pb2.AddSSHKeyResponse(
                ssh_config=remote_agent_ssh_config,
            )
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_add_ssh_key_response.CopyFrom(
        remote_agents_add_ssh_key_response
    )
    remote_agents_add_ssh_key_response_row = (
        bigquery_persistence._remote_agents_add_ssh_key_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_add_ssh_key_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }, "Request remote agents add ssh key response row should match expected structure"


def test_remote_agents_pause_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_pause_request = request_insight_pb2.RemoteAgentsPauseRequest(
        request=remote_agents_pb2.PauseAgentRequest(
            remote_agent_id="test-agent",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_pause_request.CopyFrom(remote_agents_pause_request)
    remote_agents_pause_request_row = (
        bigquery_persistence._remote_agents_pause_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_pause_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "agent_id": "test-agent",
        "sanitized_json": {
            "request": {
                "remote_agent_id": "test-agent",
            },
        },
    }, "Request remote agents pause request row should match expected structure"


def test_remote_agents_pause_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_pause_response = request_insight_pb2.RemoteAgentsPauseResponse()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_pause_response.CopyFrom(remote_agents_pause_response)
    remote_agents_pause_response_row = (
        bigquery_persistence._remote_agents_pause_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_pause_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }, "Request remote agents pause response row should match expected structure"


def test_remote_agents_resume_request_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_resume_request = request_insight_pb2.RemoteAgentsResumeRequest(
        request=remote_agents_pb2.ResumeAgentRequest(
            remote_agent_id="test-agent",
        )
    )
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_resume_request.CopyFrom(remote_agents_resume_request)
    remote_agents_resume_request_row = (
        bigquery_persistence._remote_agents_resume_request_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_resume_request_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "agent_id": "test-agent",
        "sanitized_json": {
            "request": {
                "remote_agent_id": "test-agent",
            },
        },
    }, "Request remote agents resume request row should match expected structure"


def test_remote_agents_resume_response_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    remote_agents_resume_response = request_insight_pb2.RemoteAgentsResumeResponse()
    event = request_insight_pb2.RequestEvent()
    event.time.FromDatetime(now)
    event.remote_agents_resume_response.CopyFrom(remote_agents_resume_response)
    remote_agents_resume_response_row = (
        bigquery_persistence._remote_agents_resume_response_row(
            "test-request", "test-session", tenant_info, event
        )
    )

    assert remote_agents_resume_response_row == {
        "request_id": "test-request",
        "session_id": "test-session",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "shard_namespace": "test_namespace",
        "time": now,
        "sanitized_json": {},
    }, "Request remote agents resume response row should match expected structure"


def test_recaptcha_row(bigquery_persistence):
    now = datetime.now()
    recaptcha = request_insight_pb2.Recaptcha(
        email="<EMAIL>",
        assessment_name="test-assessment-name",
        assessment_reasons=["test-reason-1", "test-reason-2"],
        score=0.5,
        action="test-action",
        user_agent="test-user-agent",
        source_ip="test-ip-address",
    )
    event = request_insight_pb2.GenericEvent()
    event.time.FromDatetime(now)
    event.recaptcha.CopyFrom(recaptcha)

    recaptcha_row = bigquery_persistence._recaptcha_row(
        "test-session",
        event,
    )

    assert recaptcha_row == {
        "session_id": "test-session",
        "time": now,
        "event_id": event.event_id,
        "email": "<EMAIL>",
        "source_ip": "test-ip-address",
        "sanitized_json": {
            "assessment_name": "test-assessment-name",
            "assessment_reasons": ["test-reason-1", "test-reason-2"],
            "score": 0.5,
            "action": "test-action",
            "user_agent": "test-user-agent",
        },
    }, "Request recaptcha row should match expected structure"


def test_verisoul_row(bigquery_persistence):
    now = datetime.now()
    verisoul = request_insight_pb2.Verisoul(
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        report="{}",
    )
    event = request_insight_pb2.GenericEvent()
    event.time.FromDatetime(now)
    event.verisoul.CopyFrom(verisoul)
    event.event_id = "test-event-id"

    verisoul_row = bigquery_persistence._verisoul_row(
        "test-session",
        event,
    )

    assert verisoul_row == {
        "session_id": "test-session",
        "time": now,
        "event_id": "test-event-id",
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "report": {},
        "sanitized_json": {
            "opaque_user_id": {
                "user_id": "test-user",
                "user_id_type": "AUGMENT",
            },
        },
    }, "Request verisoul row should match expected structure"


def test_verosint_row(bigquery_persistence):
    now = datetime.now()
    verosint = request_insight_pb2.Verosint(
        opaque_user_id=auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        report="{}",
    )
    event = request_insight_pb2.GenericEvent()
    event.time.FromDatetime(now)
    event.verosint.CopyFrom(verosint)
    event.event_id = "test-event-id"

    verosint_row = bigquery_persistence._verosint_row(
        "test-session",
        event,
    )

    assert verosint_row == {
        "session_id": "test-session",
        "time": now,
        "event_id": "test-event-id",
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "report": {},
        "sanitized_json": {
            "opaque_user_id": {
                "user_id": "test-user",
                "user_id_type": "AUGMENT",
            },
        },
    }, "Request verosint row should match expected structure"


def test_purchase_credits_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    purchase_credits = request_insight_pb2.PurchaseCredits(
        orb_subscription_id="test-subscription-id",
        credits=100,
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.purchase_credits.CopyFrom(purchase_credits)

    purchase_credits_row = bigquery_persistence._purchase_credits_row(
        "test-session",
        auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        tenant_info,
        event,
    )
    assert purchase_credits_row == {
        "session_id": "test-session",
        "shard_namespace": "test_namespace",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "sanitized_json": {
            "orb_subscription_id": "test-subscription-id",
            "credits": 100,
        },
    }


def test_cancel_subscription_row(bigquery_persistence, tenant_info):
    now = datetime.now()
    cancel_subscription = request_insight_pb2.CancelSubscription(
        orb_subscription_id="test-subscription-id",
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.cancel_subscription.CopyFrom(cancel_subscription)

    cancel_subscription_row = bigquery_persistence._cancel_subscription_row(
        "test-session",
        auth_entities_pb2.UserId(
            user_id="test-user",
            user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
        ),
        tenant_info,
        event,
    )
    assert cancel_subscription_row == {
        "session_id": "test-session",
        "shard_namespace": "test_namespace",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "sanitized_json": {
            "orb_subscription_id": "test-subscription-id",
        },
    }


def test_unschedule_pending_subscription_cancellation_row(
    bigquery_persistence, tenant_info
):
    now = datetime.now()
    unschedule_pending_subscription_cancellation = (
        request_insight_pb2.UnschedulePendingSubscriptionCancellation(
            orb_subscription_id="test-subscription-id",
        )
    )
    event = request_insight_pb2.SessionEvent()
    event.time.FromDatetime(now)
    event.unschedule_pending_subscription_cancellation.CopyFrom(
        unschedule_pending_subscription_cancellation
    )

    unschedule_pending_subscription_cancellation_row = (
        bigquery_persistence._unschedule_pending_subscription_cancellation_row(
            "test-session",
            auth_entities_pb2.UserId(
                user_id="test-user",
                user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
            ),
            tenant_info,
            event,
        )
    )
    assert unschedule_pending_subscription_cancellation_row == {
        "session_id": "test-session",
        "shard_namespace": "test_namespace",
        "tenant": "test_tenant",
        "tenant_id": "123",
        "time": now,
        "opaque_user_id": "test-user",
        "user_id_type": "AUGMENT",
        "sanitized_json": {
            "orb_subscription_id": "test-subscription-id",
        },
    }
