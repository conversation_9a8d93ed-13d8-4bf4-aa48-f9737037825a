local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';

// The interval of this signal is fairly large, at once per 30min sync.
//
// The metrics are evaluated once every 10min.  We don't need to check
// too frequently due to the long interval for syncs, but we also do not
// want to further drag the latency longer significantly.
//
// Set the during here to 2 hours and 10m for alerts, which
// covers 5 consecutive syncs.  10m grace period is added to account for
// slight variation of time to schedule and run the sync jobs.
// <PERSON><PERSON>s check for persistent values across long periods to avoid false positives
// from temporary spikes. These can arise from migrating
// users to new tenants, which should generally wrap in a couple hours.
// Alerts will be sent to insights on call for such sustained jumps
function(cloud)
  local duplicateEmailsSpec = {
    displayName: 'Request Insight duplicate emails',
    conditionPrometheusQueryLanguage: {
      duration: '7800s',
      evaluationInterval: '600s',
      labels: { severity: 'error' },
      query: |||
        au_request_insight_duplicate_emails_count > 0
      |||,
    },
  };

  local multipleOrbSubscriptionsSpec = {
    displayName: 'Request Insight multiple Orb subscriptions',
    conditionPrometheusQueryLanguage: {
      duration: '7800s',
      evaluationInterval: '600s',
      labels: { severity: 'error' },
      query: |||
        au_request_insight_multiple_orb_subscriptions_count > 0
      |||,
    },
  };

  local userSubscriptionInconsistenciesSpec = {
    displayName: 'Request Insight user-subscription inconsistencies',
    conditionPrometheusQueryLanguage: {
      duration: '7800s',
      evaluationInterval: '600s',
      labels: { severity: 'error' },
      query: |||
        au_request_insight_user_subscription_inconsistencies_count > 0
      |||,
    },
  };

  local orphanedActiveOrbSubscriptionsSpec = {
    displayName: 'Request Insight orphaned active Orb subscriptions',
    conditionPrometheusQueryLanguage: {
      duration: '7800s',
      evaluationInterval: '600s',
      labels: { severity: 'error' },
      query: |||
        au_request_insight_orphaned_active_orb_subscriptions_count > 0
      |||,
    },
  };

  local professionalTenantsWithCommunitySubscriptionsSpec = {
    displayName: 'Request Insight professional tenants with community subscriptions',
    conditionPrometheusQueryLanguage: {
      duration: '7800s',
      evaluationInterval: '600s',
      labels: { severity: 'error' },
      query: |||
        au_request_insight_professional_tenants_with_community_subscriptions_count > 0
      |||,
    },
  };

  local communityTenantsWithProfessionalSubscriptionsSpec = {
    displayName: 'Request Insight community tenants with professional subscriptions',
    conditionPrometheusQueryLanguage: {
      duration: '7800s',
      evaluationInterval: '600s',
      labels: { severity: 'error' },
      query: |||
        au_request_insight_community_tenants_with_professional_subscriptions_count > 0
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      duplicateEmailsSpec,
      'request-insight-duplicate-emails',
      'Duplicate normalized emails detected in user table for more than 2 hours 10 minutes',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      multipleOrbSubscriptionsSpec,
      'request-insight-multiple-orb-subscriptions',
      'Users with multiple active Orb subscriptions detected for more than 2 hours 10 minutes',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      userSubscriptionInconsistenciesSpec,
      'request-insight-user-subscription-inconsistencies',
      'User-subscription inconsistencies detected for more than 2 hours 10 minutes',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      orphanedActiveOrbSubscriptionsSpec,
      'request-insight-orphaned-active-orb-subscriptions',
      'Orphaned active Orb subscriptions detected for more than 2 hours 10 minutes',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      professionalTenantsWithCommunitySubscriptionsSpec,
      'request-insight-professional-tenants-community-subscriptions',
      'Non-team users in professional tenants with community subscriptions detected for more than 2 hours 10 minutes',
      team='insights'
    ),
    monitoringLib.alertPolicy(
      cloud,
      communityTenantsWithProfessionalSubscriptionsSpec,
      'request-insight-community-tenants-professional-subscriptions',
      'Non-team users in community tenants with professional subscriptions detected for more than 2 hours 10 minutes',
      team='insights'
    ),
  ]
