package main

import (
	"context"
	"fmt"
	"time"

	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/integrations/orb"
	ripb "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Update Subscription Processor
type UpdateSubscriptionProcessor struct {
	daoFactory              *DAOFactory
	orbClient               orb.OrbClient
	auditLogger             *audit.AuditLogger
	orbConfig               *OrbConfig
	requestInsightPublisher ripublisher.RequestInsightPublisher
}

// NewUpdateSubscriptionProcessor creates a new UpdateSubscriptionProcessor
func NewUpdateSubscriptionProcessor(
	daoFactory *DAOFactory,
	orbClient orb.OrbClient,
	orbConfig *OrbConfig,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	auditLogger *audit.AuditLogger,
) (*UpdateSubscriptionProcessor, error) {
	return &UpdateSubscriptionProcessor{
		daoFactory:              daoFactory,
		orbClient:               orbClient,
		auditLogger:             auditLogger,
		orbConfig:               orbConfig,
		requestInsightPublisher: requestInsightPublisher,
	}, nil
}

func (p *UpdateSubscriptionProcessor) Process(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage) error {
	logger := log.Ctx(ctx).With().
		Str("subscription_id", msg.SubscriptionId).
		Int("num_seats", int(msg.NumSeats)).
		Str("subscription_change_id", msg.Id).
		Logger()

	logger.Info().Msg("Processing update subscription message")

	// Get the subscription from the database
	subscriptionDAO := p.daoFactory.GetSubscriptionDAO()
	var subscription *auth_entities.Subscription
	var err error
	maxAttempts := 5
	sleepDuration := time.Millisecond * 200
	// Since we publish to the pub/sub queue before writing to BigTable, it is possible that this will fail on the first try.
	// Retry a few times to avoid a long backoff while we wait for pub/sub to retry.
	for attempt := 0; attempt < maxAttempts; attempt++ {
		subscription, err = subscriptionDAO.Get(ctx, msg.SubscriptionId)
		if err == nil && subscription != nil && subscription.SubscriptionChangeId != nil {
			break
		}
		logger.Warn().Err(err).Msgf(
			"Failed to get subscription for update subscription verification, attempt %d/%d", attempt+1, maxAttempts)
		time.Sleep(sleepDuration)
	}
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get subscription for update subscription")
		return fmt.Errorf("failed to get subscription for update subscription: %w", err)
	} else if subscription == nil {
		logger.Error().Msg("Subscription not found for update subscription")
		return fmt.Errorf("subscription not found for update subscription")
	}

	// Ensure the subscription change ID in the subscription entity matches the message ID
	if subscription.SubscriptionChangeId == nil {
		// Check if the message has been in the queue for more than 5 minutes
		messageAge := time.Since(msg.PublishTime.AsTime())
		if messageAge > 5*time.Minute {
			logger.Info().Dur("message_age", messageAge).Msg("No pending subscription change, acknowledging message after 5 minute timeout")
			return nil // Return nil to ack the message
		}
		logger.Warn().Dur("message_age", messageAge).Msg("No pending subscription change, retrying")
		return fmt.Errorf("no pending subscription change, retrying")
	} else if *subscription.SubscriptionChangeId != msg.Id {
		logger.Info().Str("stored_subscription_change_id", *subscription.SubscriptionChangeId).Msg("Subscription change ID mismatch, dropping subscription creation message")
		return nil // Return nil to ack the message
	}

	// Get the user subscription info from Orb
	subscriptionInfo, err := p.orbClient.GetUserSubscription(ctx, msg.SubscriptionId, &orb.ItemIds{
		SeatsID:            p.orbConfig.SeatsItemID,
		IncludedMessagesID: p.orbConfig.IncludedMessagesItemID,
	})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get user subscription info from Orb")
		return fmt.Errorf("failed to get user subscription info: %w", err)
	}
	if subscriptionInfo.CurrentFixedQuantities == nil {
		logger.Error().Msg("Failed to get current fixed quantities")
		return fmt.Errorf("failed to get current fixed quantities")
	}

	currentSeats := subscriptionInfo.CurrentFixedQuantities.Seats
	var futureSeats int
	if subscriptionInfo.FutureFixedQuantities != nil {
		futureSeats = subscriptionInfo.FutureFixedQuantities.Seats
	} else {
		futureSeats = currentSeats
	}

	if int(msg.NumSeats) == futureSeats {
		logger.Info().Msg("No change in number of seats, skipping update")
		return nil
	}

	// We are checking against currentSeats only so we know whether we need to update
	// immediately or at the end of billing period
	isIncreasingSeats := int(msg.NumSeats) > currentSeats
	logger = logger.With().Str("plan_id", subscriptionInfo.ExternalPlanID).Logger()

	if subscriptionInfo.ExternalPlanID == p.orbConfig.getTrialPlan().ID {
		logger.Info().Msg("Updating seats for trial plan")
		err = p.updateTrialSeats(ctx, msg, subscriptionInfo, logger)
		if err != nil {
			return err
		}
	} else {
		logger.Info().Msg("Updating seats for nontrial plan")
		planInfo, err := p.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: p.orbConfig.IncludedMessagesItemID, SeatsID: p.orbConfig.SeatsItemID}, &msg.SubscriptionId, nil)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to get Orb plan info")
			return fmt.Errorf("failed to get Orb plan info: %w", err)
		}
		monthlyCredits := planInfo.MessagesPerSeat

		// Unschedule any future seats changes
		err = p.unscheduleFutureSeats(ctx, msg, subscriptionInfo, logger)
		if err != nil {
			return err
		}

		// Check if there's existing scheduled plan change
		scheduledTargetPlanID, err := p.orbClient.GetScheduledPlanChanges(ctx, subscriptionInfo.OrbSubscriptionID, subscriptionInfo.OrbCustomerID)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to get scheduled plan changes")
			return fmt.Errorf("failed to get scheduled plan changes: %w", err)
		}

		if isIncreasingSeats {
			// Update the number of seats
			err = p.updateCurrentMonthSeats(ctx, msg, subscriptionInfo, logger)
			if err != nil {
				return err
			}

			// Update the number of credits per month, for the current month. Only if we are increasing the number of seats.
			// We add pro-ration in manually based on the time left in the billing period.
			err = p.updateCurrentMonthCredits(ctx, msg, subscriptionInfo, monthlyCredits, currentSeats, logger)
			if err != nil {
				return err
			}

			if scheduledTargetPlanID == nil {
				// Update the number of credits per month, for future months
				err = p.updateFutureMonthCredits(ctx, msg, subscriptionInfo, monthlyCredits, logger)
				if err != nil {
					return err
				}
			} else {
				// Schedule a new plan change with updated credit and seat count
				err = p.updateFuturePlan(ctx, msg, subscriptionInfo, *scheduledTargetPlanID, logger)
				if err != nil {
					return err
				}
			}

		} else {
			if scheduledTargetPlanID == nil {
				// Update the number of seats
				err = p.updateFutureMonthSeats(ctx, msg, subscriptionInfo, logger)
				if err != nil {
					return err
				}
				// Update the number of credits per month, for future months
				err = p.updateFutureMonthCredits(ctx, msg, subscriptionInfo, monthlyCredits, logger)
				if err != nil {
					return err
				}
			} else {
				// Schedule a new plan change with updated credit and seat count
				err = p.updateFuturePlan(ctx, msg, subscriptionInfo, *scheduledTargetPlanID, logger)
				if err != nil {
					return err
				}
			}
		}
	}

	// Update subscription DAO to clear update ID and set number of seats
	_, err = subscriptionDAO.TryUpdate(ctx, msg.SubscriptionId, func(sub *auth_entities.Subscription) bool {
		sub.SubscriptionChangeId = nil
		sub.Seats = int32(msg.NumSeats) // TODO(sophie): think about how to make this work with webhook, leave it here for now
		return true
	}, DefaultRetry)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to clear subscription change info")
		return fmt.Errorf("failed to clear subscription change info: %w", err)
	}

	// Record this update in RI.
	event := ripublisher.NewTenantEvent()
	event.Event = &ripb.TenantEvent_UpdateSubscription{
		UpdateSubscription: &ripb.UpdateSubscription{
			SubscriptionId: msg.SubscriptionId,
			Seats:          msg.NumSeats,
		},
	}

	// Get the tenant ID from the subscription owner
	tenantOwner, ok := subscription.Owner.(*auth_entities.Subscription_TenantId)
	if !ok {
		log.Error().Msg("Subscription owner is not a tenant")
	} else {
		riErr := p.requestInsightPublisher.PublishTenantEvent(ctx, &ripb.TenantInfo{
			// Since this is a self serve tenant, we know that the owner is a tenant, and we know that the tenant name is the same as the tenant ID
			TenantId:   tenantOwner.TenantId,
			TenantName: tenantOwner.TenantId,
		}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish UpdateSubscription event")
		}
	}

	p.auditLogger.WriteAuditLog(
		"",
		"",
		"",
		fmt.Sprintf("Successfully updated subscription %s to %d seats", msg.SubscriptionId, msg.NumSeats),
	)

	logger.Info().Msg("Successfully updated subscription")

	return nil
}

// Update the number of seats when the user is on the trial plan
func (p *UpdateSubscriptionProcessor) updateTrialSeats(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, logger zerolog.Logger) error {
	// DO NOT change the number of credits on the trial plan. This only happens when users join the team.
	// Edit the current number of seats on the trial plan. Do this immediately regardless of increase/decrease,
	// as it has no impact on cost and there is no scheduled future plan
	seatsIdempotencyKey := fmt.Sprintf("%s-update-trial-seats", msg.Id)
	err := p.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
		OrbSubscriptionID: subscriptionInfo.OrbSubscriptionID,
		PriceOverride: orb.OrbPriceOverrides{
			PriceID:  subscriptionInfo.CurrentFixedQuantities.SeatsID,
			Quantity: float64(msg.NumSeats),
		},
		UpdateTimeType: orb.PlanChangeImmediate,
	}, &seatsIdempotencyKey)
	if err != nil {
		logger.Error().Err(err).
			Msg("Failed to update current trial plan seats")
		return fmt.Errorf("failed to update current trial plan seats: %w", err)
	}
	logger.Info().Msg("Successfully updated current trial plan seats")
	return nil
}

func (p *UpdateSubscriptionProcessor) unscheduleFutureSeats(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, logger zerolog.Logger) error {
	// If there is a scheduled change to seats, unschedule it
	// We want the immediate change to be the number of seats for the future, this future change is no longer valid
	unscheduleIdempotencyKey := fmt.Sprintf("%s-unschedule-future-seats", msg.Id)
	if subscriptionInfo.FutureFixedQuantities != nil && subscriptionInfo.FutureFixedQuantities.SeatsID != "" {
		logger.Info().Msg("Unscheduling future seats change")
		err := p.orbClient.UnscheduleFixedQuantity(ctx, subscriptionInfo.OrbSubscriptionID, subscriptionInfo.FutureFixedQuantities.SeatsID, &unscheduleIdempotencyKey)
		if err != nil {
			// Orb Client catches us from errors if there was no fixed quantity, so adjust to error here
			logger.Error().Err(err).Msg("Failed to unschedule future fixed quantity")
			return fmt.Errorf("failed to unschedule future fixed quantity: %w", err)
		}
	}
	return nil
}

func (p *UpdateSubscriptionProcessor) updateCurrentMonthSeats(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, logger zerolog.Logger) error {
	return p.updateSeats(ctx, msg, subscriptionInfo, orb.PlanChangeImmediate, logger)
}

func (p *UpdateSubscriptionProcessor) updateFutureMonthSeats(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, logger zerolog.Logger) error {
	return p.updateSeats(ctx, msg, subscriptionInfo, orb.PlanChangeEndOfTerm, logger)
}

func (p *UpdateSubscriptionProcessor) updateSeats(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, updateType orb.PlanChangeType, logger zerolog.Logger) error {
	seatsIdempotencyKey := fmt.Sprintf("%s-update-seats", msg.Id)
	err := p.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
		OrbSubscriptionID: subscriptionInfo.OrbSubscriptionID,
		PriceOverride: orb.OrbPriceOverrides{
			PriceID:  subscriptionInfo.CurrentFixedQuantities.SeatsID,
			Quantity: float64(msg.NumSeats),
		},
		UpdateTimeType: updateType,
	}, &seatsIdempotencyKey)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to update number of seats")
		return fmt.Errorf("failed to update number of seats for %s plan: %w", subscriptionInfo.ExternalPlanID, err)
	}
	logger.Info().Msg("Successfully updated number of seats")
	return nil
}

func (p *UpdateSubscriptionProcessor) updateCurrentMonthCredits(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, monthlyCredits float64, currentSeats int, logger zerolog.Logger) error {
	additionalSeats := int(msg.NumSeats) - currentSeats
	currentMonthlyCredits := subscriptionInfo.CurrentFixedQuantities.IncludedMessages
	proRatedCredits, err := CalculateProRatedCredits(
		float64(currentMonthlyCredits),                                         // current number of credits
		float64(additionalSeats)*monthlyCredits+float64(currentMonthlyCredits), // future possible number of credits: current plus the additional for the added seats
		subscriptionInfo.CurrentBillingPeriodStartDate,
		subscriptionInfo.CurrentBillingPeriodEndDate,
		msg.PublishTime.AsTime().UTC(), // use the message publish time, convert to UTC
	)
	if err != nil {
		return err
	}
	currentCreditsIdempotencyKey := fmt.Sprintf("%s-update-current-credits", msg.Id)
	err = p.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
		OrbSubscriptionID: subscriptionInfo.OrbSubscriptionID,
		PriceOverride: orb.OrbPriceOverrides{
			PriceID:  subscriptionInfo.CurrentFixedQuantities.IncludedMessagesID,
			Quantity: proRatedCredits,
		},
		UpdateTimeType: orb.PlanChangeImmediate,
	}, &currentCreditsIdempotencyKey)
	if err != nil {
		logger.Error().Err(err).Float64("pro_rated_credits", proRatedCredits).Msg("Failed to add pro-rated credits for current month")
		return fmt.Errorf("failed to purchase pro-rated credits for %s plan: %w", subscriptionInfo.ExternalPlanID, err)
	}
	logger.Info().Float64("pro_rated_credits", proRatedCredits).Msg("Successfully added pro-rated credits for current month")
	return nil
}

func (p *UpdateSubscriptionProcessor) updateFutureMonthCredits(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, monthlyCredits float64, logger zerolog.Logger) error {
	creditQuantity := float64(msg.NumSeats) * monthlyCredits
	futureCreditsIdempotencyKey := fmt.Sprintf("%s-update-future-credits", msg.Id)
	err := p.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
		OrbSubscriptionID: subscriptionInfo.OrbSubscriptionID,
		PriceOverride: orb.OrbPriceOverrides{
			PriceID:  subscriptionInfo.CurrentFixedQuantities.IncludedMessagesID,
			Quantity: creditQuantity,
		},
		UpdateTimeType: orb.PlanChangeEndOfTerm,
	}, &futureCreditsIdempotencyKey)
	if err != nil {
		logger.Error().Err(err).Float64("credit_quantity", creditQuantity).Msg("Failed to update future number of credits per month")
		return fmt.Errorf("failed to update future number of credits per month for %s plan: %w", subscriptionInfo.ExternalPlanID, err)
	}
	logger.Info().Float64("credit_quantity", creditQuantity).Msg("Successfully updated future number of credits per month")
	return nil
}

// updateFuturePlan updates future plan's number of seats and credits
func (p *UpdateSubscriptionProcessor) updateFuturePlan(ctx context.Context, msg *auth_internal.UpdateSubscriptionMessage, subscriptionInfo *orb.OrbSubscriptionInfo, targetPlanID string, logger zerolog.Logger) error {
	// Get target plan information to calculate correct credits per seat
	targetPlanInfo, err := p.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: p.orbConfig.IncludedMessagesItemID, SeatsID: p.orbConfig.SeatsItemID}, nil, &targetPlanID)
	if err != nil {
		logger.Error().Err(err).Str("target_plan_id", targetPlanID).Msg("Failed to get target plan information")
		return fmt.Errorf("failed to get target plan information for %s: %w", targetPlanID, err)
	}

	// Calculate credits for the new seat count on the target plan
	targetCredits := float64(msg.NumSeats) * targetPlanInfo.MessagesPerSeat

	// Create plan change with updated seat count and credits
	planChange := orb.OrbPlanChange{
		CustomerOrbID:         subscriptionInfo.OrbCustomerID,
		SubscriptionID:        subscriptionInfo.OrbSubscriptionID,
		NewPlanID:             targetPlanID,
		PlanChangeType:        orb.PlanChangeEndOfTerm, // Scheduled plan change
		BillingCycleAlignment: orb.BillingCycleAlignmentUnchanged,
		PriceOverrides: []orb.OrbPriceOverrides{
			{
				PriceID:  targetPlanInfo.SeatsPriceID,
				Quantity: float64(msg.NumSeats),
			},
			{
				PriceID:  targetPlanInfo.IncludedMessagesPriceID,
				Quantity: targetCredits,
			},
		},
	}
	// the suffix is not full function name since it will exceeds 64 characters of length limit on orb
	idempotencyKey := fmt.Sprintf("%s-update-future-plan", msg.Id)

	// Schedule a new plan change with updated seat count
	err = p.orbClient.SetCustomerPlanType(ctx, planChange, &idempotencyKey)
	if err != nil {
		logger.Error().
			Err(err).
			Str("target_plan_id", targetPlanID).
			Int32("num_seats", msg.NumSeats).
			Float64("target_credits", targetCredits).
			Msg("Failed to schedule plan change with updated credit and seats")
		return fmt.Errorf("failed to schedule plan change with updated credit and seats: %w", err)
	}

	logger.Info().
		Str("target_plan_id", targetPlanID).
		Int32("num_seats", msg.NumSeats).
		Float64("target_credits", targetCredits).
		Msg("Successfully updated future credits and seats")

	return nil
}
