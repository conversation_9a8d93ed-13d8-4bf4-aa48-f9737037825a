package main

import (
	"encoding/json"
	"fmt"
	"os"

	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
)

type GcpConfig struct {
	InstanceID string `json:"instance_id"`
	ProjectID  string `json:"project_id"`
	TableName  string `json:"table_name"`
	SetupTable bool   `json:"setup_table"`
}

type GrpcConfig struct {
	Ports                []int                   `json:"ports"`
	PrivatePort          int                     `json:"private_port"`
	Enabled              bool                    `json:"enabled"`
	MaxServerWorkers     int                     `json:"max_server_workers"`
	ServerMtls           *tlsconfig.ServerConfig `json:"server_mtls"`
	ClientMtls           *tlsconfig.ClientConfig `json:"client_mtls"`
	ShutdownGracePeriodS float64                 `json:"shutdown_grace_period_s"`
}

type TenantWatcherConfig struct {
	TenantWatcherEndpoint  string `json:"tenant_watcher_endpoint"`
	APIProxyHostnameDomain string `json:"api_proxy_hostname_domain"`
}

type AuthConfig struct {
	TokenExchangeEndpoint string `json:"token_exchange_endpoint"`
}

type RevokerConfig struct {
	SubscriptionName string `json:"subscription_name"`
}

type AsyncOpsConfig struct {
	TopicName                        string `json:"topic_name"`
	SubscriptionName                 string `json:"subscription_name"`
	DeadLetterSubscriptionName       string `json:"dead_letter_subscription_name"`
	MaxConcurrentReceivers           int    `json:"max_concurrent_receivers"`
	DeadLetterMaxConcurrentReceivers int    `json:"dead_letter_max_concurrent_receivers"`
}

type StripeEventProcessorConfig struct {
	SubscriptionName                 string `json:"subscription_name"`
	DeadLetterSubscriptionName       string `json:"dead_letter_subscription_name"`
	MaxConcurrentReceivers           int    `json:"max_concurrent_receivers"`
	DeadLetterMaxConcurrentReceivers int    `json:"dead_letter_max_concurrent_receivers"`
}

type BillingEventProcessorConfig struct {
	SubscriptionName                 string `json:"subscription_name"`
	DeadLetterSubscriptionName       string `json:"dead_letter_subscription_name"`
	MaxConcurrentReceivers           int    `json:"max_concurrent_receivers"`
	DeadLetterMaxConcurrentReceivers int    `json:"dead_letter_max_concurrent_receivers"`
}

type StripeConfig struct {
	Enabled       bool   `json:"enabled"`
	SecretKeyPath string `json:"secret_key_path"`
}

type PlanType string

const (
	// Community plan type
	PlanTypeCommunity PlanType = "community"
	// Paid plan in trial
	PlanTypePaidTrial PlanType = "trial"
	// Paid plan type
	PlanTypePaid PlanType = "paid"
)

type PlanFeatures struct {
	TrainingAllowed     bool `json:"training_allowed"`
	TeamsAllowed        bool `json:"teams_allowed"`
	MaxSeats            int  `json:"max_seats"`
	AddCreditsAvailable bool `json:"add_credits_available"`
	// PlanType is one of "community", "trial", "paid"
	// the plan type gives information around some behaviors of the plan, e.g.
	// the switching behavior. For example, it is not possible to switch into
	// a trial plan
	PlanType PlanType `json:"plan_type"`
}

type PlanConfig struct {
	ID        string       `json:"id"`         // maps to the external_plan_id in Orb
	Color     string       `json:"color"`      // color for display purposes only
	SortOrder int          `json:"sort_order"` // sorting order for displaying plans only
	Features  PlanFeatures `json:"features"`
}

func (orbConfig *OrbConfig) GetPlanFeatures(planID string) *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.ID == planID {
			return &plan
		}
	}
	return nil
}

// returns the community plan
//
// always exists
func (orbConfig *OrbConfig) getCommunityPlan() *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.Features.PlanType == PlanTypeCommunity {
			return &plan
		}
	}
	panic("Failed to find community plan")
}

// returns the trial plan
//
// always exists
func (orbConfig *OrbConfig) getTrialPlan() *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.Features.PlanType == PlanTypePaidTrial {
			return &plan
		}
	}
	panic("Failed to find trial plan")
}

// Find a plan by ID
// returns nil if not found
func (orbConfig *OrbConfig) findPlan(planID string) *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.ID == planID {
			return &plan
		}
	}
	return nil
}

type OrbConfig struct {
	Enabled                            bool         `json:"enabled"`
	ApiKeyPath                         string       `json:"api_key_path"`
	SeatsItemID                        string       `json:"seats_item_id"`
	IncludedMessagesItemID             string       `json:"included_messages_item_id"`
	PricingUnit                        string       `json:"pricing_unit"`
	CostPerMessage                     float64      `json:"cost_per_message"`
	Plans                              []PlanConfig `json:"plans"`
	MinAddonPurchase                   float64      `json:"min_addon_purchase"`
	MaxAddonPurchase                   float64      `json:"max_addon_purchase"`
	ProfessionalPlanID                 string       `json:"professional_plan_id"`
	StripeProfessionalPlanPricePerSeat float64      `json:"stripe_professional_plan_price_per_seat"`
	DeveloperPlanForStripeUsers        struct {
		VersionNumber           int64   `json:"version_number"`
		SeatsPriceID            string  `json:"seats_price_id"`
		IncludedMessagesPriceID string  `json:"included_messages_price_id"`
		MessagesPerSeat         float64 `json:"messages_per_seat"`
	} `json:"developer_plan_for_stripe_users"`
}

type Config struct {
	AuthConfig                        AuthConfig                  `json:"auth_config"`
	CodeTTLSeconds                    int                         `json:"code_ttl_seconds"`
	DynamicFeatureFlagsEndpoint       string                      `json:"dynamic_feature_flags_endpoint"`
	FeatureFlagsSdkKeyPath            string                      `json:"feature_flags_sdk_key_path"`
	RequestInsightPublisherConfigPath string                      `json:"request_insight_publisher_config_path"`
	GCP                               GcpConfig                   `json:"gcp"`
	GRPC                              GrpcConfig                  `json:"grpc"`
	TenantWatcher                     TenantWatcherConfig         `json:"tenant_watcher"`
	PrometheusBindAddress             string                      `json:"prometheus_bind_address"`
	RevokerConfig                     RevokerConfig               `json:"revoker"`
	AsyncOpsConfig                    AsyncOpsConfig              `json:"async_ops"`
	Stripe                            StripeConfig                `json:"stripe"`
	Orb                               OrbConfig                   `json:"orb"`
	StripeEventProcessorConfig        StripeEventProcessorConfig  `json:"stripe_event_processor"`
	BillingEventProcessorConfig       BillingEventProcessorConfig `json:"billing_event_processor"`
	CustomerIOAPIKeyPath              string                      `json:"customerio_api_key_path"`
	AuthCentralHostname               string                      `json:"auth_central_hostname"`
	DevServicerEnabled                bool                        `json:"dev_servicer_enabled"`

	// Kubernetes namespace where the Auth Central service is running
	Namespace string `json:"namespace"`
}

// LoadConfig reads and parses the configuration file
func LoadConfig(configFile string) (*Config, error) {
	if configFile == "" {
		return nil, fmt.Errorf("missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		return nil, fmt.Errorf("error opening config file: %w", err)
	}
	defer f.Close()

	var config Config
	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		return nil, fmt.Errorf("error decoding config file: %w", err)
	}

	return &config, nil
}
