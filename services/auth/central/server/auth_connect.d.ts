// @generated by protoc-gen-connect-es v1.4.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/auth/central/server/auth.proto (package auth, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { AddUserToTenantRequest, AddUserToTenantResponse, CancelSubscriptionRequest, CancelSubscriptionResponse, CreateTenantForTeamRequest, CreateTenantForTeamResponse, CreateUserSuspensionRequest, CreateUserSuspensionResponse, DeduplicateUsersByEmailRequest, DeduplicateUsersByEmailResponse, DeduplicateUsersByIdpUserIdRequest, DeduplicateUsersByIdpUserIdResponse, DeduplicateUserTenantListRequest, DeduplicateUserTenantListResponse, DeleteInvitationRequest, DeleteInvitationResponse, DeleteUserSuspensionsRequest, DeleteUserSuspensionsResponse, GetAllOrbPlansRequest, GetAllOrbPlansResponse, GetCreateTenantForTeamStatusRequest, GetCreateTenantForTeamStatusResponse, GetResolveInvitationsStatusRequest, GetResolveInvitationsStatusResponse, GetSubscriptionRequest, GetSubscriptionResponse, GetTenantInvitationsRequest, GetTenantInvitationsResponse, GetTenantPlanStatusRequest, GetTenantPlanStatusResponse, GetTokenInfoRequest, GetTokenInfoResponse, GetUserBillingInfoRequest, GetUserBillingInfoResponse, GetUserInvitationsRequest, GetUserInvitationsResponse, GetUserOnTenantRequest, GetUserOnTenantResponse, GetUserOrbCreditsInfoRequest, GetUserOrbCreditsInfoResponse, GetUserOrbInfoRequest, GetUserOrbInfoResponse, GetUserOrbPaymentInfoRequest, GetUserOrbPaymentInfoResponse, GetUserOrbPlanInfoRequest, GetUserOrbPlanInfoResponse, GetUserOrbSubscriptionInfoRequest, GetUserOrbSubscriptionInfoResponse, GetUserRequest, GetUserResponse, GetUsersRequest, GetUsersResponse, InviteUsersToTenantRequest, InviteUsersToTenantResponse, ListSubscriptionsRequest, ListSubscriptionsResponse, ListTenantSubscriptionMappingsRequest, ListTenantSubscriptionMappingsResponse, ListTenantUsersRequest, ListTenantUsersResponse, MigrateLegacySelfServeTeamsRequest, MigrateLegacySelfServeTeamsResponse, PurchaseCreditsRequest, PurchaseCreditsResponse, PutUserOnPlanRequest, PutUserOnPlanResponse, RemoveDeletedTenantsFromUsersRequest, RemoveDeletedTenantsFromUsersResponse, RemoveExtraSelfServeTenantsFromUsersRequest, RemoveExtraSelfServeTenantsFromUsersResponse, RemoveSelfServeAccountsForTeamRequest, RemoveSelfServeAccountsForTeamResponse, RemoveUserFromTenantRequest, RemoveUserFromTenantResponse, ResolveInvitationsRequest, ResolveInvitationsResponse, RevokeUserCookiesRequest, RevokeUserCookiesResponse, RevokeUserRequest, RevokeUserResponse, ScanLegacySelfServeTeamsRequest, ScanLegacySelfServeTeamsResponse, SetUsersBillingMethodToOrbRequest, SetUsersBillingMethodToOrbResponse, SuspensionCleanupRequest, SuspensionCleanupResponse, SyncAddressesRequest, SyncAddressesResponse, UnschedulePendingSubscriptionCancellationRequest, UnschedulePendingSubscriptionCancellationResponse, UnschedulePlanChangesRequest, UnschedulePlanChangesResponse, UpdateSubscriptionRequest, UpdateSubscriptionResponse, UpdateSuspensionExemptionRequest, UpdateSuspensionExemptionResponse, UpdateUserBillingInfoRequest, UpdateUserBillingInfoResponse, UpdateUserEmailRequest, UpdateUserEmailResponse, UpdateUserOnTenantRequest, UpdateUserOnTenantResponse } from "./auth_pb.js";
import { MethodKind } from "@bufbuild/protobuf";

/**
 * @generated from service auth.AuthService
 */
export declare const AuthService: {
  readonly typeName: "auth.AuthService",
  readonly methods: {
    /**
     * @generated from rpc auth.AuthService.AddUserToTenant
     */
    readonly addUserToTenant: {
      readonly name: "AddUserToTenant",
      readonly I: typeof AddUserToTenantRequest,
      readonly O: typeof AddUserToTenantResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.GetUserOnTenant
     */
    readonly getUserOnTenant: {
      readonly name: "GetUserOnTenant",
      readonly I: typeof GetUserOnTenantRequest,
      readonly O: typeof GetUserOnTenantResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.UpdateUserOnTenant
     */
    readonly updateUserOnTenant: {
      readonly name: "UpdateUserOnTenant",
      readonly I: typeof UpdateUserOnTenantRequest,
      readonly O: typeof UpdateUserOnTenantResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.RemoveUserFromTenant
     */
    readonly removeUserFromTenant: {
      readonly name: "RemoveUserFromTenant",
      readonly I: typeof RemoveUserFromTenantRequest,
      readonly O: typeof RemoveUserFromTenantResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.UpdateUserEmail
     */
    readonly updateUserEmail: {
      readonly name: "UpdateUserEmail",
      readonly I: typeof UpdateUserEmailRequest,
      readonly O: typeof UpdateUserEmailResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.ListTenantUsers
     */
    readonly listTenantUsers: {
      readonly name: "ListTenantUsers",
      readonly I: typeof ListTenantUsersRequest,
      readonly O: typeof ListTenantUsersResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.GetUser
     */
    readonly getUser: {
      readonly name: "GetUser",
      readonly I: typeof GetUserRequest,
      readonly O: typeof GetUserResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.GetUsers
     */
    readonly getUsers: {
      readonly name: "GetUsers",
      readonly I: typeof GetUsersRequest,
      readonly O: typeof GetUsersResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.GetTokenInfo
     */
    readonly getTokenInfo: {
      readonly name: "GetTokenInfo",
      readonly I: typeof GetTokenInfoRequest,
      readonly O: typeof GetTokenInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.RevokeUserCookies
     */
    readonly revokeUserCookies: {
      readonly name: "RevokeUserCookies",
      readonly I: typeof RevokeUserCookiesRequest,
      readonly O: typeof RevokeUserCookiesResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.RevokeUser
     */
    readonly revokeUser: {
      readonly name: "RevokeUser",
      readonly I: typeof RevokeUserRequest,
      readonly O: typeof RevokeUserResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.CreateUserSuspension
     */
    readonly createUserSuspension: {
      readonly name: "CreateUserSuspension",
      readonly I: typeof CreateUserSuspensionRequest,
      readonly O: typeof CreateUserSuspensionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.UpdateSuspensionExemption
     */
    readonly updateSuspensionExemption: {
      readonly name: "UpdateSuspensionExemption",
      readonly I: typeof UpdateSuspensionExemptionRequest,
      readonly O: typeof UpdateSuspensionExemptionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.DeleteUserSuspensions
     */
    readonly deleteUserSuspensions: {
      readonly name: "DeleteUserSuspensions",
      readonly I: typeof DeleteUserSuspensionsRequest,
      readonly O: typeof DeleteUserSuspensionsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.GetUserBillingInfo
     */
    readonly getUserBillingInfo: {
      readonly name: "GetUserBillingInfo",
      readonly I: typeof GetUserBillingInfoRequest,
      readonly O: typeof GetUserBillingInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.UpdateUserBillingInfo
     */
    readonly updateUserBillingInfo: {
      readonly name: "UpdateUserBillingInfo",
      readonly I: typeof UpdateUserBillingInfoRequest,
      readonly O: typeof UpdateUserBillingInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.RemoveSelfServeAccountsForTeam
     */
    readonly removeSelfServeAccountsForTeam: {
      readonly name: "RemoveSelfServeAccountsForTeam",
      readonly I: typeof RemoveSelfServeAccountsForTeamRequest,
      readonly O: typeof RemoveSelfServeAccountsForTeamResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.SuspensionCleanup
     */
    readonly suspensionCleanup: {
      readonly name: "SuspensionCleanup",
      readonly I: typeof SuspensionCleanupRequest,
      readonly O: typeof SuspensionCleanupResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.RemoveExtraSelfServeTenantsFromUsers
     */
    readonly removeExtraSelfServeTenantsFromUsers: {
      readonly name: "RemoveExtraSelfServeTenantsFromUsers",
      readonly I: typeof RemoveExtraSelfServeTenantsFromUsersRequest,
      readonly O: typeof RemoveExtraSelfServeTenantsFromUsersResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.RemoveDeletedTenantsFromUsers
     */
    readonly removeDeletedTenantsFromUsers: {
      readonly name: "RemoveDeletedTenantsFromUsers",
      readonly I: typeof RemoveDeletedTenantsFromUsersRequest,
      readonly O: typeof RemoveDeletedTenantsFromUsersResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.DeduplicateUsersByEmail
     */
    readonly deduplicateUsersByEmail: {
      readonly name: "DeduplicateUsersByEmail",
      readonly I: typeof DeduplicateUsersByEmailRequest,
      readonly O: typeof DeduplicateUsersByEmailResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.SetUsersBillingMethodToOrb
     */
    readonly setUsersBillingMethodToOrb: {
      readonly name: "SetUsersBillingMethodToOrb",
      readonly I: typeof SetUsersBillingMethodToOrbRequest,
      readonly O: typeof SetUsersBillingMethodToOrbResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.ScanLegacySelfServeTeams
     */
    readonly scanLegacySelfServeTeams: {
      readonly name: "ScanLegacySelfServeTeams",
      readonly I: typeof ScanLegacySelfServeTeamsRequest,
      readonly O: typeof ScanLegacySelfServeTeamsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.DeduplicateUsersByIdpUserId
     */
    readonly deduplicateUsersByIdpUserId: {
      readonly name: "DeduplicateUsersByIdpUserId",
      readonly I: typeof DeduplicateUsersByIdpUserIdRequest,
      readonly O: typeof DeduplicateUsersByIdpUserIdResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.DeduplicateUserTenantList
     */
    readonly deduplicateUserTenantList: {
      readonly name: "DeduplicateUserTenantList",
      readonly I: typeof DeduplicateUserTenantListRequest,
      readonly O: typeof DeduplicateUserTenantListResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.SyncAddresses
     */
    readonly syncAddresses: {
      readonly name: "SyncAddresses",
      readonly I: typeof SyncAddressesRequest,
      readonly O: typeof SyncAddressesResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.AuthService.MigrateLegacySelfServeTeams
     */
    readonly migrateLegacySelfServeTeams: {
      readonly name: "MigrateLegacySelfServeTeams",
      readonly I: typeof MigrateLegacySelfServeTeamsRequest,
      readonly O: typeof MigrateLegacySelfServeTeamsResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

/**
 * @generated from service auth.TeamManagementService
 */
export declare const TeamManagementService: {
  readonly typeName: "auth.TeamManagementService",
  readonly methods: {
    /**
     * @generated from rpc auth.TeamManagementService.CreateTenantForTeam
     */
    readonly createTenantForTeam: {
      readonly name: "CreateTenantForTeam",
      readonly I: typeof CreateTenantForTeamRequest,
      readonly O: typeof CreateTenantForTeamResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetCreateTenantForTeamStatus
     */
    readonly getCreateTenantForTeamStatus: {
      readonly name: "GetCreateTenantForTeamStatus",
      readonly I: typeof GetCreateTenantForTeamStatusRequest,
      readonly O: typeof GetCreateTenantForTeamStatusResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.InviteUsersToTenant
     */
    readonly inviteUsersToTenant: {
      readonly name: "InviteUsersToTenant",
      readonly I: typeof InviteUsersToTenantRequest,
      readonly O: typeof InviteUsersToTenantResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetTenantInvitations
     */
    readonly getTenantInvitations: {
      readonly name: "GetTenantInvitations",
      readonly I: typeof GetTenantInvitationsRequest,
      readonly O: typeof GetTenantInvitationsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetUserInvitations
     */
    readonly getUserInvitations: {
      readonly name: "GetUserInvitations",
      readonly I: typeof GetUserInvitationsRequest,
      readonly O: typeof GetUserInvitationsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.ResolveInvitations
     */
    readonly resolveInvitations: {
      readonly name: "ResolveInvitations",
      readonly I: typeof ResolveInvitationsRequest,
      readonly O: typeof ResolveInvitationsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetResolveInvitationsStatus
     */
    readonly getResolveInvitationsStatus: {
      readonly name: "GetResolveInvitationsStatus",
      readonly I: typeof GetResolveInvitationsStatusRequest,
      readonly O: typeof GetResolveInvitationsStatusResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.DeleteInvitation
     */
    readonly deleteInvitation: {
      readonly name: "DeleteInvitation",
      readonly I: typeof DeleteInvitationRequest,
      readonly O: typeof DeleteInvitationResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetSubscription
     */
    readonly getSubscription: {
      readonly name: "GetSubscription",
      readonly I: typeof GetSubscriptionRequest,
      readonly O: typeof GetSubscriptionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.ListSubscriptions
     */
    readonly listSubscriptions: {
      readonly name: "ListSubscriptions",
      readonly I: typeof ListSubscriptionsRequest,
      readonly O: typeof ListSubscriptionsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.ListTenantSubscriptionMappings
     */
    readonly listTenantSubscriptionMappings: {
      readonly name: "ListTenantSubscriptionMappings",
      readonly I: typeof ListTenantSubscriptionMappingsRequest,
      readonly O: typeof ListTenantSubscriptionMappingsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.UpdateSubscription
     */
    readonly updateSubscription: {
      readonly name: "UpdateSubscription",
      readonly I: typeof UpdateSubscriptionRequest,
      readonly O: typeof UpdateSubscriptionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.PurchaseCredits
     */
    readonly purchaseCredits: {
      readonly name: "PurchaseCredits",
      readonly I: typeof PurchaseCreditsRequest,
      readonly O: typeof PurchaseCreditsResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.CancelSubscription
     */
    readonly cancelSubscription: {
      readonly name: "CancelSubscription",
      readonly I: typeof CancelSubscriptionRequest,
      readonly O: typeof CancelSubscriptionResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetUserOrbInfo
     */
    readonly getUserOrbInfo: {
      readonly name: "GetUserOrbInfo",
      readonly I: typeof GetUserOrbInfoRequest,
      readonly O: typeof GetUserOrbInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetUserOrbPlanInfo
     */
    readonly getUserOrbPlanInfo: {
      readonly name: "GetUserOrbPlanInfo",
      readonly I: typeof GetUserOrbPlanInfoRequest,
      readonly O: typeof GetUserOrbPlanInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetAllOrbPlans
     */
    readonly getAllOrbPlans: {
      readonly name: "GetAllOrbPlans",
      readonly I: typeof GetAllOrbPlansRequest,
      readonly O: typeof GetAllOrbPlansResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.UnschedulePendingSubscriptionCancellation
     */
    readonly unschedulePendingSubscriptionCancellation: {
      readonly name: "UnschedulePendingSubscriptionCancellation",
      readonly I: typeof UnschedulePendingSubscriptionCancellationRequest,
      readonly O: typeof UnschedulePendingSubscriptionCancellationResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.UnschedulePlanChanges
     */
    readonly unschedulePlanChanges: {
      readonly name: "UnschedulePlanChanges",
      readonly I: typeof UnschedulePlanChangesRequest,
      readonly O: typeof UnschedulePlanChangesResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.PutUserOnPlan
     */
    readonly putUserOnPlan: {
      readonly name: "PutUserOnPlan",
      readonly I: typeof PutUserOnPlanRequest,
      readonly O: typeof PutUserOnPlanResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetUserOrbCreditsInfo
     */
    readonly getUserOrbCreditsInfo: {
      readonly name: "GetUserOrbCreditsInfo",
      readonly I: typeof GetUserOrbCreditsInfoRequest,
      readonly O: typeof GetUserOrbCreditsInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetUserOrbPaymentInfo
     */
    readonly getUserOrbPaymentInfo: {
      readonly name: "GetUserOrbPaymentInfo",
      readonly I: typeof GetUserOrbPaymentInfoRequest,
      readonly O: typeof GetUserOrbPaymentInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetUserOrbSubscriptionInfo
     */
    readonly getUserOrbSubscriptionInfo: {
      readonly name: "GetUserOrbSubscriptionInfo",
      readonly I: typeof GetUserOrbSubscriptionInfoRequest,
      readonly O: typeof GetUserOrbSubscriptionInfoResponse,
      readonly kind: MethodKind.Unary,
    },
    /**
     * @generated from rpc auth.TeamManagementService.GetTenantPlanStatus
     */
    readonly getTenantPlanStatus: {
      readonly name: "GetTenantPlanStatus",
      readonly I: typeof GetTenantPlanStatusRequest,
      readonly O: typeof GetTenantPlanStatusResponse,
      readonly kind: MethodKind.Unary,
    },
  }
};

