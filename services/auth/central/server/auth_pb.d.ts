// @generated by protoc-gen-es v1.10.0 with parameter "keep_empty_files=true,target=js+dts"
// @generated from file services/auth/central/server/auth.proto (package auth, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage, Timestamp } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";
import type { BillingMethod, CustomerUiRole, InvitationResolution, Subscription, TenantCreation, TenantInvitation, TenantSubscriptionMapping, User, UserSuspension, UserSuspensionType } from "./auth_entities_pb.js";

/**
 * @generated from message auth.GetUserRequest
 */
export declare class GetUserRequest extends Message<GetUserRequest> {
  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * @generated from field: optional string tenant_id = 3;
   */
  tenantId?: string;

  constructor(data?: PartialMessage<GetUserRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserRequest;

  static equals(a: GetUserRequest | PlainMessage<GetUserRequest> | undefined, b: GetUserRequest | PlainMessage<GetUserRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserResponse
 */
export declare class GetUserResponse extends Message<GetUserResponse> {
  /**
   * @generated from field: auth_entities.User user = 1;
   */
  user?: User;

  constructor(data?: PartialMessage<GetUserResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserResponse;

  static equals(a: GetUserResponse | PlainMessage<GetUserResponse> | undefined, b: GetUserResponse | PlainMessage<GetUserResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserBillingInfoRequest
 */
export declare class GetUserBillingInfoRequest extends Message<GetUserBillingInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  constructor(data?: PartialMessage<GetUserBillingInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserBillingInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserBillingInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserBillingInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserBillingInfoRequest;

  static equals(a: GetUserBillingInfoRequest | PlainMessage<GetUserBillingInfoRequest> | undefined, b: GetUserBillingInfoRequest | PlainMessage<GetUserBillingInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserBillingInfoResponse
 */
export declare class GetUserBillingInfoResponse extends Message<GetUserBillingInfoResponse> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: bool is_self_serve_team = 3;
   */
  isSelfServeTeam: boolean;

  /**
   * @generated from field: string orb_customer_id = 4;
   */
  orbCustomerId: string;

  constructor(data?: PartialMessage<GetUserBillingInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserBillingInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserBillingInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserBillingInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserBillingInfoResponse;

  static equals(a: GetUserBillingInfoResponse | PlainMessage<GetUserBillingInfoResponse> | undefined, b: GetUserBillingInfoResponse | PlainMessage<GetUserBillingInfoResponse> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateUserBillingInfoRequest
 */
export declare class UpdateUserBillingInfoRequest extends Message<UpdateUserBillingInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: optional auth_entities.BillingMethod billing_method = 3;
   */
  billingMethod?: BillingMethod;

  /**
   * @generated from field: optional string orb_customer_id = 4;
   */
  orbCustomerId?: string;

  /**
   * @generated from field: optional string orb_subscription_id = 5;
   */
  orbSubscriptionId?: string;

  /**
   * @generated from field: optional string stripe_customer_id = 6;
   */
  stripeCustomerId?: string;

  constructor(data?: PartialMessage<UpdateUserBillingInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateUserBillingInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserBillingInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserBillingInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserBillingInfoRequest;

  static equals(a: UpdateUserBillingInfoRequest | PlainMessage<UpdateUserBillingInfoRequest> | undefined, b: UpdateUserBillingInfoRequest | PlainMessage<UpdateUserBillingInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateUserBillingInfoResponse
 */
export declare class UpdateUserBillingInfoResponse extends Message<UpdateUserBillingInfoResponse> {
  constructor(data?: PartialMessage<UpdateUserBillingInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateUserBillingInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserBillingInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserBillingInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserBillingInfoResponse;

  static equals(a: UpdateUserBillingInfoResponse | PlainMessage<UpdateUserBillingInfoResponse> | undefined, b: UpdateUserBillingInfoResponse | PlainMessage<UpdateUserBillingInfoResponse> | undefined): boolean;
}

/**
 * @generated from message auth.AddUserToTenantRequest
 */
export declare class AddUserToTenantRequest extends Message<AddUserToTenantRequest> {
  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: string tenant_id = 3;
   */
  tenantId: string;

  constructor(data?: PartialMessage<AddUserToTenantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.AddUserToTenantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddUserToTenantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddUserToTenantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddUserToTenantRequest;

  static equals(a: AddUserToTenantRequest | PlainMessage<AddUserToTenantRequest> | undefined, b: AddUserToTenantRequest | PlainMessage<AddUserToTenantRequest> | undefined): boolean;
}

/**
 * @generated from message auth.AddUserToTenantResponse
 */
export declare class AddUserToTenantResponse extends Message<AddUserToTenantResponse> {
  /**
   * @generated from field: auth_entities.User user = 1;
   */
  user?: User;

  constructor(data?: PartialMessage<AddUserToTenantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.AddUserToTenantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddUserToTenantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddUserToTenantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddUserToTenantResponse;

  static equals(a: AddUserToTenantResponse | PlainMessage<AddUserToTenantResponse> | undefined, b: AddUserToTenantResponse | PlainMessage<AddUserToTenantResponse> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateUserOnTenantRequest
 */
export declare class UpdateUserOnTenantRequest extends Message<UpdateUserOnTenantRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * @generated from field: repeated auth_entities.CustomerUiRole customer_ui_roles = 3;
   */
  customerUiRoles: CustomerUiRole[];

  constructor(data?: PartialMessage<UpdateUserOnTenantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateUserOnTenantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserOnTenantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserOnTenantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserOnTenantRequest;

  static equals(a: UpdateUserOnTenantRequest | PlainMessage<UpdateUserOnTenantRequest> | undefined, b: UpdateUserOnTenantRequest | PlainMessage<UpdateUserOnTenantRequest> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateUserOnTenantResponse
 */
export declare class UpdateUserOnTenantResponse extends Message<UpdateUserOnTenantResponse> {
  /**
   * @generated from field: repeated auth_entities.CustomerUiRole customer_ui_roles = 1;
   */
  customerUiRoles: CustomerUiRole[];

  constructor(data?: PartialMessage<UpdateUserOnTenantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateUserOnTenantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserOnTenantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserOnTenantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserOnTenantResponse;

  static equals(a: UpdateUserOnTenantResponse | PlainMessage<UpdateUserOnTenantResponse> | undefined, b: UpdateUserOnTenantResponse | PlainMessage<UpdateUserOnTenantResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOnTenantRequest
 */
export declare class GetUserOnTenantRequest extends Message<GetUserOnTenantRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  constructor(data?: PartialMessage<GetUserOnTenantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOnTenantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOnTenantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOnTenantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOnTenantRequest;

  static equals(a: GetUserOnTenantRequest | PlainMessage<GetUserOnTenantRequest> | undefined, b: GetUserOnTenantRequest | PlainMessage<GetUserOnTenantRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOnTenantResponse
 */
export declare class GetUserOnTenantResponse extends Message<GetUserOnTenantResponse> {
  /**
   * @generated from field: repeated auth_entities.CustomerUiRole customer_ui_roles = 1;
   */
  customerUiRoles: CustomerUiRole[];

  constructor(data?: PartialMessage<GetUserOnTenantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOnTenantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOnTenantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOnTenantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOnTenantResponse;

  static equals(a: GetUserOnTenantResponse | PlainMessage<GetUserOnTenantResponse> | undefined, b: GetUserOnTenantResponse | PlainMessage<GetUserOnTenantResponse> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveUserFromTenantRequest
 */
export declare class RemoveUserFromTenantRequest extends Message<RemoveUserFromTenantRequest> {
  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 3;
   */
  tenantId: string;

  constructor(data?: PartialMessage<RemoveUserFromTenantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveUserFromTenantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveUserFromTenantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveUserFromTenantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveUserFromTenantRequest;

  static equals(a: RemoveUserFromTenantRequest | PlainMessage<RemoveUserFromTenantRequest> | undefined, b: RemoveUserFromTenantRequest | PlainMessage<RemoveUserFromTenantRequest> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveUserFromTenantResponse
 */
export declare class RemoveUserFromTenantResponse extends Message<RemoveUserFromTenantResponse> {
  constructor(data?: PartialMessage<RemoveUserFromTenantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveUserFromTenantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveUserFromTenantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveUserFromTenantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveUserFromTenantResponse;

  static equals(a: RemoveUserFromTenantResponse | PlainMessage<RemoveUserFromTenantResponse> | undefined, b: RemoveUserFromTenantResponse | PlainMessage<RemoveUserFromTenantResponse> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateUserEmailRequest
 */
export declare class UpdateUserEmailRequest extends Message<UpdateUserEmailRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string current_email = 2;
   */
  currentEmail: string;

  /**
   * @generated from field: string new_email = 3;
   */
  newEmail: string;

  constructor(data?: PartialMessage<UpdateUserEmailRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateUserEmailRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserEmailRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserEmailRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserEmailRequest;

  static equals(a: UpdateUserEmailRequest | PlainMessage<UpdateUserEmailRequest> | undefined, b: UpdateUserEmailRequest | PlainMessage<UpdateUserEmailRequest> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateUserEmailResponse
 */
export declare class UpdateUserEmailResponse extends Message<UpdateUserEmailResponse> {
  constructor(data?: PartialMessage<UpdateUserEmailResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateUserEmailResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserEmailResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserEmailResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserEmailResponse;

  static equals(a: UpdateUserEmailResponse | PlainMessage<UpdateUserEmailResponse> | undefined, b: UpdateUserEmailResponse | PlainMessage<UpdateUserEmailResponse> | undefined): boolean;
}

/**
 * @generated from message auth.ListTenantUsersRequest
 */
export declare class ListTenantUsersRequest extends Message<ListTenantUsersRequest> {
  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  constructor(data?: PartialMessage<ListTenantUsersRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ListTenantUsersRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTenantUsersRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTenantUsersRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTenantUsersRequest;

  static equals(a: ListTenantUsersRequest | PlainMessage<ListTenantUsersRequest> | undefined, b: ListTenantUsersRequest | PlainMessage<ListTenantUsersRequest> | undefined): boolean;
}

/**
 * @generated from message auth.ListTenantUsersResponse
 */
export declare class ListTenantUsersResponse extends Message<ListTenantUsersResponse> {
  /**
   * @generated from field: repeated auth_entities.User users = 1;
   */
  users: User[];

  constructor(data?: PartialMessage<ListTenantUsersResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ListTenantUsersResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTenantUsersResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTenantUsersResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTenantUsersResponse;

  static equals(a: ListTenantUsersResponse | PlainMessage<ListTenantUsersResponse> | undefined, b: ListTenantUsersResponse | PlainMessage<ListTenantUsersResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetTokenInfoRequest
 */
export declare class GetTokenInfoRequest extends Message<GetTokenInfoRequest> {
  /**
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * @generated from field: string requestor = 2;
   */
  requestor: string;

  constructor(data?: PartialMessage<GetTokenInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetTokenInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTokenInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTokenInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTokenInfoRequest;

  static equals(a: GetTokenInfoRequest | PlainMessage<GetTokenInfoRequest> | undefined, b: GetTokenInfoRequest | PlainMessage<GetTokenInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetTokenInfoResponse
 */
export declare class GetTokenInfoResponse extends Message<GetTokenInfoResponse> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: string tenant_name = 3;
   */
  tenantName: string;

  /**
   * @generated from field: string augment_user_id = 4;
   */
  augmentUserId: string;

  /**
   * @generated from field: string user_email = 5;
   */
  userEmail: string;

  /**
   * @generated from oneof auth.GetTokenInfoResponse.subscription
   */
  subscription: {
    /**
     * @generated from field: auth.EnterpriseSubscription enterprise = 6;
     */
    value: EnterpriseSubscription;
    case: "enterprise";
  } | {
    /**
     * @generated from field: auth.ActiveSubscription active_subscription = 7;
     */
    value: ActiveSubscription;
    case: "activeSubscription";
  } | {
    /**
     * @generated from field: auth.Trial trial = 8;
     */
    value: Trial;
    case: "trial";
  } | {
    /**
     * @generated from field: auth.InactiveSubscription inactive_subscription = 9;
     */
    value: InactiveSubscription;
    case: "inactiveSubscription";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: repeated auth_entities.UserSuspension suspensions = 10;
   */
  suspensions: UserSuspension[];

  constructor(data?: PartialMessage<GetTokenInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetTokenInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTokenInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTokenInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTokenInfoResponse;

  static equals(a: GetTokenInfoResponse | PlainMessage<GetTokenInfoResponse> | undefined, b: GetTokenInfoResponse | PlainMessage<GetTokenInfoResponse> | undefined): boolean;
}

/**
 * @generated from message auth.EnterpriseSubscription
 */
export declare class EnterpriseSubscription extends Message<EnterpriseSubscription> {
  constructor(data?: PartialMessage<EnterpriseSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.EnterpriseSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EnterpriseSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EnterpriseSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EnterpriseSubscription;

  static equals(a: EnterpriseSubscription | PlainMessage<EnterpriseSubscription> | undefined, b: EnterpriseSubscription | PlainMessage<EnterpriseSubscription> | undefined): boolean;
}

/**
 * @generated from message auth.ActiveSubscription
 */
export declare class ActiveSubscription extends Message<ActiveSubscription> {
  /**
   * @generated from field: google.protobuf.Timestamp end_date = 1;
   */
  endDate?: Timestamp;

  /**
   * @generated from field: bool usage_balance_depleted = 2;
   */
  usageBalanceDepleted: boolean;

  /**
   * @generated from field: auth_entities.BillingMethod billing_method = 3;
   */
  billingMethod: BillingMethod;

  constructor(data?: PartialMessage<ActiveSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ActiveSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ActiveSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ActiveSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ActiveSubscription;

  static equals(a: ActiveSubscription | PlainMessage<ActiveSubscription> | undefined, b: ActiveSubscription | PlainMessage<ActiveSubscription> | undefined): boolean;
}

/**
 * @generated from message auth.Trial
 */
export declare class Trial extends Message<Trial> {
  /**
   * @generated from field: google.protobuf.Timestamp trial_end = 1;
   */
  trialEnd?: Timestamp;

  /**
   * @generated from field: auth_entities.BillingMethod billing_method = 2;
   */
  billingMethod: BillingMethod;

  constructor(data?: PartialMessage<Trial>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.Trial";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Trial;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Trial;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Trial;

  static equals(a: Trial | PlainMessage<Trial> | undefined, b: Trial | PlainMessage<Trial> | undefined): boolean;
}

/**
 * @generated from message auth.InactiveSubscription
 */
export declare class InactiveSubscription extends Message<InactiveSubscription> {
  /**
   * @generated from field: auth_entities.BillingMethod billing_method = 1;
   */
  billingMethod: BillingMethod;

  constructor(data?: PartialMessage<InactiveSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.InactiveSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InactiveSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InactiveSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InactiveSubscription;

  static equals(a: InactiveSubscription | PlainMessage<InactiveSubscription> | undefined, b: InactiveSubscription | PlainMessage<InactiveSubscription> | undefined): boolean;
}

/**
 * @generated from message auth.RevokeUserCookiesRequest
 */
export declare class RevokeUserCookiesRequest extends Message<RevokeUserCookiesRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  constructor(data?: PartialMessage<RevokeUserCookiesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RevokeUserCookiesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RevokeUserCookiesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RevokeUserCookiesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RevokeUserCookiesRequest;

  static equals(a: RevokeUserCookiesRequest | PlainMessage<RevokeUserCookiesRequest> | undefined, b: RevokeUserCookiesRequest | PlainMessage<RevokeUserCookiesRequest> | undefined): boolean;
}

/**
 * @generated from message auth.RevokeUserCookiesResponse
 */
export declare class RevokeUserCookiesResponse extends Message<RevokeUserCookiesResponse> {
  constructor(data?: PartialMessage<RevokeUserCookiesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RevokeUserCookiesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RevokeUserCookiesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RevokeUserCookiesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RevokeUserCookiesResponse;

  static equals(a: RevokeUserCookiesResponse | PlainMessage<RevokeUserCookiesResponse> | undefined, b: RevokeUserCookiesResponse | PlainMessage<RevokeUserCookiesResponse> | undefined): boolean;
}

/**
 * @generated from message auth.RevokeUserRequest
 */
export declare class RevokeUserRequest extends Message<RevokeUserRequest> {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  constructor(data?: PartialMessage<RevokeUserRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RevokeUserRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RevokeUserRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RevokeUserRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RevokeUserRequest;

  static equals(a: RevokeUserRequest | PlainMessage<RevokeUserRequest> | undefined, b: RevokeUserRequest | PlainMessage<RevokeUserRequest> | undefined): boolean;
}

/**
 * @generated from message auth.RevokeUserResponse
 */
export declare class RevokeUserResponse extends Message<RevokeUserResponse> {
  /**
   * @generated from field: int32 tokens_deleted = 1;
   */
  tokensDeleted: number;

  constructor(data?: PartialMessage<RevokeUserResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RevokeUserResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RevokeUserResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RevokeUserResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RevokeUserResponse;

  static equals(a: RevokeUserResponse | PlainMessage<RevokeUserResponse> | undefined, b: RevokeUserResponse | PlainMessage<RevokeUserResponse> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveSelfServeAccountsForTeamRequest
 */
export declare class RemoveSelfServeAccountsForTeamRequest extends Message<RemoveSelfServeAccountsForTeamRequest> {
  /**
   * @generated from field: bool dry_run = 1;
   */
  dryRun: boolean;

  /**
   * @generated from field: optional string tenant_id = 2;
   */
  tenantId?: string;

  /**
   * @generated from field: repeated string tenant_ids_to_ignore = 3;
   */
  tenantIdsToIgnore: string[];

  constructor(data?: PartialMessage<RemoveSelfServeAccountsForTeamRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveSelfServeAccountsForTeamRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveSelfServeAccountsForTeamRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamRequest;

  static equals(a: RemoveSelfServeAccountsForTeamRequest | PlainMessage<RemoveSelfServeAccountsForTeamRequest> | undefined, b: RemoveSelfServeAccountsForTeamRequest | PlainMessage<RemoveSelfServeAccountsForTeamRequest> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveSelfServeAccountsForTeamResponse
 */
export declare class RemoveSelfServeAccountsForTeamResponse extends Message<RemoveSelfServeAccountsForTeamResponse> {
  /**
   * @generated from field: repeated auth.RemoveSelfServeAccountsForTeamResponse.UserRemovals removed_users = 1;
   */
  removedUsers: RemoveSelfServeAccountsForTeamResponse_UserRemovals[];

  /**
   * @generated from field: repeated string failed_users = 2;
   */
  failedUsers: string[];

  constructor(data?: PartialMessage<RemoveSelfServeAccountsForTeamResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveSelfServeAccountsForTeamResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveSelfServeAccountsForTeamResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamResponse;

  static equals(a: RemoveSelfServeAccountsForTeamResponse | PlainMessage<RemoveSelfServeAccountsForTeamResponse> | undefined, b: RemoveSelfServeAccountsForTeamResponse | PlainMessage<RemoveSelfServeAccountsForTeamResponse> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveSelfServeAccountsForTeamResponse.TenantInfo
 */
export declare class RemoveSelfServeAccountsForTeamResponse_TenantInfo extends Message<RemoveSelfServeAccountsForTeamResponse_TenantInfo> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string tenant_name = 2;
   */
  tenantName: string;

  constructor(data?: PartialMessage<RemoveSelfServeAccountsForTeamResponse_TenantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveSelfServeAccountsForTeamResponse.TenantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveSelfServeAccountsForTeamResponse_TenantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamResponse_TenantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamResponse_TenantInfo;

  static equals(a: RemoveSelfServeAccountsForTeamResponse_TenantInfo | PlainMessage<RemoveSelfServeAccountsForTeamResponse_TenantInfo> | undefined, b: RemoveSelfServeAccountsForTeamResponse_TenantInfo | PlainMessage<RemoveSelfServeAccountsForTeamResponse_TenantInfo> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveSelfServeAccountsForTeamResponse.UserRemovals
 */
export declare class RemoveSelfServeAccountsForTeamResponse_UserRemovals extends Message<RemoveSelfServeAccountsForTeamResponse_UserRemovals> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: repeated auth.RemoveSelfServeAccountsForTeamResponse.TenantInfo primary_tenants = 2;
   */
  primaryTenants: RemoveSelfServeAccountsForTeamResponse_TenantInfo[];

  /**
   * @generated from field: repeated auth.RemoveSelfServeAccountsForTeamResponse.TenantInfo removed_tenants = 3;
   */
  removedTenants: RemoveSelfServeAccountsForTeamResponse_TenantInfo[];

  constructor(data?: PartialMessage<RemoveSelfServeAccountsForTeamResponse_UserRemovals>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveSelfServeAccountsForTeamResponse.UserRemovals";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveSelfServeAccountsForTeamResponse_UserRemovals;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamResponse_UserRemovals;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveSelfServeAccountsForTeamResponse_UserRemovals;

  static equals(a: RemoveSelfServeAccountsForTeamResponse_UserRemovals | PlainMessage<RemoveSelfServeAccountsForTeamResponse_UserRemovals> | undefined, b: RemoveSelfServeAccountsForTeamResponse_UserRemovals | PlainMessage<RemoveSelfServeAccountsForTeamResponse_UserRemovals> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveExtraSelfServeTenantsFromUsersRequest
 */
export declare class RemoveExtraSelfServeTenantsFromUsersRequest extends Message<RemoveExtraSelfServeTenantsFromUsersRequest> {
  /**
   * @generated from field: bool make_changes = 1;
   */
  makeChanges: boolean;

  /**
   * @generated from field: repeated string namespace_ids = 2;
   */
  namespaceIds: string[];

  constructor(data?: PartialMessage<RemoveExtraSelfServeTenantsFromUsersRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveExtraSelfServeTenantsFromUsersRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveExtraSelfServeTenantsFromUsersRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersRequest;

  static equals(a: RemoveExtraSelfServeTenantsFromUsersRequest | PlainMessage<RemoveExtraSelfServeTenantsFromUsersRequest> | undefined, b: RemoveExtraSelfServeTenantsFromUsersRequest | PlainMessage<RemoveExtraSelfServeTenantsFromUsersRequest> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveExtraSelfServeTenantsFromUsersResponse
 */
export declare class RemoveExtraSelfServeTenantsFromUsersResponse extends Message<RemoveExtraSelfServeTenantsFromUsersResponse> {
  /**
   * @generated from field: repeated auth.RemoveExtraSelfServeTenantsFromUsersResponse.UserRemovals removed_users = 1;
   */
  removedUsers: RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals[];

  /**
   * @generated from field: repeated string failed_users = 2;
   */
  failedUsers: string[];

  constructor(data?: PartialMessage<RemoveExtraSelfServeTenantsFromUsersResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveExtraSelfServeTenantsFromUsersResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse;

  static equals(a: RemoveExtraSelfServeTenantsFromUsersResponse | PlainMessage<RemoveExtraSelfServeTenantsFromUsersResponse> | undefined, b: RemoveExtraSelfServeTenantsFromUsersResponse | PlainMessage<RemoveExtraSelfServeTenantsFromUsersResponse> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveExtraSelfServeTenantsFromUsersResponse.TenantInfo
 */
export declare class RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo extends Message<RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string tenant_name = 2;
   */
  tenantName: string;

  constructor(data?: PartialMessage<RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveExtraSelfServeTenantsFromUsersResponse.TenantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo;

  static equals(a: RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo | PlainMessage<RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo> | undefined, b: RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo | PlainMessage<RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveExtraSelfServeTenantsFromUsersResponse.UserRemovals
 */
export declare class RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals extends Message<RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: auth.RemoveExtraSelfServeTenantsFromUsersResponse.TenantInfo primary_tenant = 2;
   */
  primaryTenant?: RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo;

  /**
   * @generated from field: repeated auth.RemoveExtraSelfServeTenantsFromUsersResponse.TenantInfo removed_tenants = 3;
   */
  removedTenants: RemoveExtraSelfServeTenantsFromUsersResponse_TenantInfo[];

  constructor(data?: PartialMessage<RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveExtraSelfServeTenantsFromUsersResponse.UserRemovals";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals;

  static equals(a: RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals | PlainMessage<RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals> | undefined, b: RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals | PlainMessage<RemoveExtraSelfServeTenantsFromUsersResponse_UserRemovals> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveDeletedTenantsFromUsersRequest
 */
export declare class RemoveDeletedTenantsFromUsersRequest extends Message<RemoveDeletedTenantsFromUsersRequest> {
  /**
   * @generated from field: bool make_changes = 1;
   */
  makeChanges: boolean;

  /**
   * @generated from field: map<string, string> tenant_mappings_for_deleted_tenants = 2;
   */
  tenantMappingsForDeletedTenants: { [key: string]: string };

  constructor(data?: PartialMessage<RemoveDeletedTenantsFromUsersRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveDeletedTenantsFromUsersRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveDeletedTenantsFromUsersRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersRequest;

  static equals(a: RemoveDeletedTenantsFromUsersRequest | PlainMessage<RemoveDeletedTenantsFromUsersRequest> | undefined, b: RemoveDeletedTenantsFromUsersRequest | PlainMessage<RemoveDeletedTenantsFromUsersRequest> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveDeletedTenantsFromUsersResponse
 */
export declare class RemoveDeletedTenantsFromUsersResponse extends Message<RemoveDeletedTenantsFromUsersResponse> {
  /**
   * @generated from field: repeated auth.RemoveDeletedTenantsFromUsersResponse.UserRemovals removed_users = 1;
   */
  removedUsers: RemoveDeletedTenantsFromUsersResponse_UserRemovals[];

  /**
   * @generated from field: repeated string failed_users = 2;
   */
  failedUsers: string[];

  constructor(data?: PartialMessage<RemoveDeletedTenantsFromUsersResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveDeletedTenantsFromUsersResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveDeletedTenantsFromUsersResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersResponse;

  static equals(a: RemoveDeletedTenantsFromUsersResponse | PlainMessage<RemoveDeletedTenantsFromUsersResponse> | undefined, b: RemoveDeletedTenantsFromUsersResponse | PlainMessage<RemoveDeletedTenantsFromUsersResponse> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveDeletedTenantsFromUsersResponse.TenantInfo
 */
export declare class RemoveDeletedTenantsFromUsersResponse_TenantInfo extends Message<RemoveDeletedTenantsFromUsersResponse_TenantInfo> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string tenant_name = 2;
   */
  tenantName: string;

  constructor(data?: PartialMessage<RemoveDeletedTenantsFromUsersResponse_TenantInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveDeletedTenantsFromUsersResponse.TenantInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveDeletedTenantsFromUsersResponse_TenantInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersResponse_TenantInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersResponse_TenantInfo;

  static equals(a: RemoveDeletedTenantsFromUsersResponse_TenantInfo | PlainMessage<RemoveDeletedTenantsFromUsersResponse_TenantInfo> | undefined, b: RemoveDeletedTenantsFromUsersResponse_TenantInfo | PlainMessage<RemoveDeletedTenantsFromUsersResponse_TenantInfo> | undefined): boolean;
}

/**
 * @generated from message auth.RemoveDeletedTenantsFromUsersResponse.UserRemovals
 */
export declare class RemoveDeletedTenantsFromUsersResponse_UserRemovals extends Message<RemoveDeletedTenantsFromUsersResponse_UserRemovals> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: repeated auth.RemoveDeletedTenantsFromUsersResponse.TenantInfo removed_tenants = 2;
   */
  removedTenants: RemoveDeletedTenantsFromUsersResponse_TenantInfo[];

  constructor(data?: PartialMessage<RemoveDeletedTenantsFromUsersResponse_UserRemovals>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.RemoveDeletedTenantsFromUsersResponse.UserRemovals";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RemoveDeletedTenantsFromUsersResponse_UserRemovals;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersResponse_UserRemovals;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RemoveDeletedTenantsFromUsersResponse_UserRemovals;

  static equals(a: RemoveDeletedTenantsFromUsersResponse_UserRemovals | PlainMessage<RemoveDeletedTenantsFromUsersResponse_UserRemovals> | undefined, b: RemoveDeletedTenantsFromUsersResponse_UserRemovals | PlainMessage<RemoveDeletedTenantsFromUsersResponse_UserRemovals> | undefined): boolean;
}

/**
 * @generated from message auth.DeduplicateUsersByEmailRequest
 */
export declare class DeduplicateUsersByEmailRequest extends Message<DeduplicateUsersByEmailRequest> {
  /**
   * @generated from field: bool make_changes = 1;
   */
  makeChanges: boolean;

  constructor(data?: PartialMessage<DeduplicateUsersByEmailRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeduplicateUsersByEmailRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeduplicateUsersByEmailRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeduplicateUsersByEmailRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeduplicateUsersByEmailRequest;

  static equals(a: DeduplicateUsersByEmailRequest | PlainMessage<DeduplicateUsersByEmailRequest> | undefined, b: DeduplicateUsersByEmailRequest | PlainMessage<DeduplicateUsersByEmailRequest> | undefined): boolean;
}

/**
 * @generated from message auth.DeduplicateUsersByEmailResponse
 */
export declare class DeduplicateUsersByEmailResponse extends Message<DeduplicateUsersByEmailResponse> {
  /**
   * @generated from field: repeated auth.DeduplicateUsersByEmailResponse.UserMerge user_merges = 1;
   */
  userMerges: DeduplicateUsersByEmailResponse_UserMerge[];

  /**
   * @generated from field: repeated string failed_users = 2;
   */
  failedUsers: string[];

  constructor(data?: PartialMessage<DeduplicateUsersByEmailResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeduplicateUsersByEmailResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeduplicateUsersByEmailResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeduplicateUsersByEmailResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeduplicateUsersByEmailResponse;

  static equals(a: DeduplicateUsersByEmailResponse | PlainMessage<DeduplicateUsersByEmailResponse> | undefined, b: DeduplicateUsersByEmailResponse | PlainMessage<DeduplicateUsersByEmailResponse> | undefined): boolean;
}

/**
 * @generated from message auth.DeduplicateUsersByEmailResponse.UserMerge
 */
export declare class DeduplicateUsersByEmailResponse_UserMerge extends Message<DeduplicateUsersByEmailResponse_UserMerge> {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string primary_user_id = 2;
   */
  primaryUserId: string;

  /**
   * @generated from field: repeated string merged_user_ids = 3;
   */
  mergedUserIds: string[];

  /**
   * @generated from field: string subscription_source_user_id = 4;
   */
  subscriptionSourceUserId: string;

  /**
   * @generated from field: map<string, string> primary_user_tenant = 5;
   */
  primaryUserTenant: { [key: string]: string };

  /**
   * @generated from field: map<string, string> merged_user_tenants = 6;
   */
  mergedUserTenants: { [key: string]: string };

  /**
   * @generated from field: string preserved_orb_customer_id = 7;
   */
  preservedOrbCustomerId: string;

  /**
   * @generated from field: string preserved_orb_subscription_id = 8;
   */
  preservedOrbSubscriptionId: string;

  /**
   * @generated from field: string preserved_stripe_customer_id = 9;
   */
  preservedStripeCustomerId: string;

  /**
   * @generated from field: repeated string merged_user_subscription_ids = 10;
   */
  mergedUserSubscriptionIds: string[];

  /**
   * @generated from field: repeated string canceled_subscription_ids = 11;
   */
  canceledSubscriptionIds: string[];

  /**
   * @generated from field: repeated string idp_user_ids = 12;
   */
  idpUserIds: string[];

  constructor(data?: PartialMessage<DeduplicateUsersByEmailResponse_UserMerge>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeduplicateUsersByEmailResponse.UserMerge";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeduplicateUsersByEmailResponse_UserMerge;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeduplicateUsersByEmailResponse_UserMerge;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeduplicateUsersByEmailResponse_UserMerge;

  static equals(a: DeduplicateUsersByEmailResponse_UserMerge | PlainMessage<DeduplicateUsersByEmailResponse_UserMerge> | undefined, b: DeduplicateUsersByEmailResponse_UserMerge | PlainMessage<DeduplicateUsersByEmailResponse_UserMerge> | undefined): boolean;
}

/**
 * @generated from message auth.DeduplicateUsersByIdpUserIdRequest
 */
export declare class DeduplicateUsersByIdpUserIdRequest extends Message<DeduplicateUsersByIdpUserIdRequest> {
  /**
   * @generated from field: bool make_changes = 1;
   */
  makeChanges: boolean;

  constructor(data?: PartialMessage<DeduplicateUsersByIdpUserIdRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeduplicateUsersByIdpUserIdRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeduplicateUsersByIdpUserIdRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeduplicateUsersByIdpUserIdRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeduplicateUsersByIdpUserIdRequest;

  static equals(a: DeduplicateUsersByIdpUserIdRequest | PlainMessage<DeduplicateUsersByIdpUserIdRequest> | undefined, b: DeduplicateUsersByIdpUserIdRequest | PlainMessage<DeduplicateUsersByIdpUserIdRequest> | undefined): boolean;
}

/**
 * @generated from message auth.DeduplicateUsersByIdpUserIdResponse
 */
export declare class DeduplicateUsersByIdpUserIdResponse extends Message<DeduplicateUsersByIdpUserIdResponse> {
  /**
   * @generated from field: repeated auth.DeduplicateUsersByEmailResponse.UserMerge user_merges = 1;
   */
  userMerges: DeduplicateUsersByEmailResponse_UserMerge[];

  /**
   * @generated from field: repeated string failed_users = 2;
   */
  failedUsers: string[];

  constructor(data?: PartialMessage<DeduplicateUsersByIdpUserIdResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeduplicateUsersByIdpUserIdResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeduplicateUsersByIdpUserIdResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeduplicateUsersByIdpUserIdResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeduplicateUsersByIdpUserIdResponse;

  static equals(a: DeduplicateUsersByIdpUserIdResponse | PlainMessage<DeduplicateUsersByIdpUserIdResponse> | undefined, b: DeduplicateUsersByIdpUserIdResponse | PlainMessage<DeduplicateUsersByIdpUserIdResponse> | undefined): boolean;
}

/**
 * @generated from message auth.ScanLegacySelfServeTeamsRequest
 */
export declare class ScanLegacySelfServeTeamsRequest extends Message<ScanLegacySelfServeTeamsRequest> {
  constructor(data?: PartialMessage<ScanLegacySelfServeTeamsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ScanLegacySelfServeTeamsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ScanLegacySelfServeTeamsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ScanLegacySelfServeTeamsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ScanLegacySelfServeTeamsRequest;

  static equals(a: ScanLegacySelfServeTeamsRequest | PlainMessage<ScanLegacySelfServeTeamsRequest> | undefined, b: ScanLegacySelfServeTeamsRequest | PlainMessage<ScanLegacySelfServeTeamsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.ScanLegacySelfServeTeamsResponse
 */
export declare class ScanLegacySelfServeTeamsResponse extends Message<ScanLegacySelfServeTeamsResponse> {
  /**
   * @generated from field: repeated auth.ScanLegacySelfServeTeamsResponse.TeamSubscription team_subscriptions = 1;
   */
  teamSubscriptions: ScanLegacySelfServeTeamsResponse_TeamSubscription[];

  /**
   * @generated from field: repeated string failed_tenants = 2;
   */
  failedTenants: string[];

  constructor(data?: PartialMessage<ScanLegacySelfServeTeamsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ScanLegacySelfServeTeamsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ScanLegacySelfServeTeamsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ScanLegacySelfServeTeamsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ScanLegacySelfServeTeamsResponse;

  static equals(a: ScanLegacySelfServeTeamsResponse | PlainMessage<ScanLegacySelfServeTeamsResponse> | undefined, b: ScanLegacySelfServeTeamsResponse | PlainMessage<ScanLegacySelfServeTeamsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.ScanLegacySelfServeTeamsResponse.TeamSubscription
 */
export declare class ScanLegacySelfServeTeamsResponse_TeamSubscription extends Message<ScanLegacySelfServeTeamsResponse_TeamSubscription> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string tenant_name = 2;
   */
  tenantName: string;

  /**
   * @generated from field: int32 user_count = 3;
   */
  userCount: number;

  /**
   * @generated from field: string admin_user_id = 4;
   */
  adminUserId: string;

  /**
   * @generated from field: string stripe_customer_id = 5;
   */
  stripeCustomerId: string;

  /**
   * @generated from field: string stripe_subscription_id = 6;
   */
  stripeSubscriptionId: string;

  /**
   * @generated from field: string stripe_subscription_status = 7;
   */
  stripeSubscriptionStatus: string;

  /**
   * @generated from field: string stripe_subscription_price_id = 8;
   */
  stripeSubscriptionPriceId: string;

  /**
   * @generated from field: string stripe_subscription_price_key = 9;
   */
  stripeSubscriptionPriceKey: string;

  /**
   * @generated from field: int64 stripe_subscription_quantity = 10;
   */
  stripeSubscriptionQuantity: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp stripe_subscription_current_period_start = 11;
   */
  stripeSubscriptionCurrentPeriodStart?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp stripe_subscription_current_period_end = 12;
   */
  stripeSubscriptionCurrentPeriodEnd?: Timestamp;

  /**
   * @generated from field: string find_stripe_subscription_error = 13;
   */
  findStripeSubscriptionError: string;

  constructor(data?: PartialMessage<ScanLegacySelfServeTeamsResponse_TeamSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ScanLegacySelfServeTeamsResponse.TeamSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ScanLegacySelfServeTeamsResponse_TeamSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ScanLegacySelfServeTeamsResponse_TeamSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ScanLegacySelfServeTeamsResponse_TeamSubscription;

  static equals(a: ScanLegacySelfServeTeamsResponse_TeamSubscription | PlainMessage<ScanLegacySelfServeTeamsResponse_TeamSubscription> | undefined, b: ScanLegacySelfServeTeamsResponse_TeamSubscription | PlainMessage<ScanLegacySelfServeTeamsResponse_TeamSubscription> | undefined): boolean;
}

/**
 * @generated from message auth.DeduplicateUserTenantListRequest
 */
export declare class DeduplicateUserTenantListRequest extends Message<DeduplicateUserTenantListRequest> {
  /**
   * @generated from field: bool make_changes = 1;
   */
  makeChanges: boolean;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  constructor(data?: PartialMessage<DeduplicateUserTenantListRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeduplicateUserTenantListRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeduplicateUserTenantListRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeduplicateUserTenantListRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeduplicateUserTenantListRequest;

  static equals(a: DeduplicateUserTenantListRequest | PlainMessage<DeduplicateUserTenantListRequest> | undefined, b: DeduplicateUserTenantListRequest | PlainMessage<DeduplicateUserTenantListRequest> | undefined): boolean;
}

/**
 * @generated from message auth.DeduplicateUserTenantListResponse
 */
export declare class DeduplicateUserTenantListResponse extends Message<DeduplicateUserTenantListResponse> {
  /**
   * @generated from field: repeated string previous_tenants = 1;
   */
  previousTenants: string[];

  /**
   * @generated from field: string new_tenant = 2;
   */
  newTenant: string;

  constructor(data?: PartialMessage<DeduplicateUserTenantListResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeduplicateUserTenantListResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeduplicateUserTenantListResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeduplicateUserTenantListResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeduplicateUserTenantListResponse;

  static equals(a: DeduplicateUserTenantListResponse | PlainMessage<DeduplicateUserTenantListResponse> | undefined, b: DeduplicateUserTenantListResponse | PlainMessage<DeduplicateUserTenantListResponse> | undefined): boolean;
}

/**
 * @generated from message auth.SyncAddressesRequest
 */
export declare class SyncAddressesRequest extends Message<SyncAddressesRequest> {
  /**
   * @generated from field: bool make_changes = 1;
   */
  makeChanges: boolean;

  /**
   * @generated from field: repeated string ids_to_sync = 2;
   */
  idsToSync: string[];

  constructor(data?: PartialMessage<SyncAddressesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.SyncAddressesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SyncAddressesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SyncAddressesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SyncAddressesRequest;

  static equals(a: SyncAddressesRequest | PlainMessage<SyncAddressesRequest> | undefined, b: SyncAddressesRequest | PlainMessage<SyncAddressesRequest> | undefined): boolean;
}

/**
 * @generated from message auth.SyncAddressesResponse
 */
export declare class SyncAddressesResponse extends Message<SyncAddressesResponse> {
  /**
   * @generated from field: repeated string users_updated = 1;
   */
  usersUpdated: string[];

  /**
   * @generated from field: repeated string users_failed = 2;
   */
  usersFailed: string[];

  /**
   * @generated from field: repeated string users_not_attempted = 3;
   */
  usersNotAttempted: string[];

  /**
   * @generated from field: repeated string users_multiple_payment_methods = 4;
   */
  usersMultiplePaymentMethods: string[];

  constructor(data?: PartialMessage<SyncAddressesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.SyncAddressesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SyncAddressesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SyncAddressesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SyncAddressesResponse;

  static equals(a: SyncAddressesResponse | PlainMessage<SyncAddressesResponse> | undefined, b: SyncAddressesResponse | PlainMessage<SyncAddressesResponse> | undefined): boolean;
}

/**
 * @generated from message auth.MigrateLegacySelfServeTeamsRequest
 */
export declare class MigrateLegacySelfServeTeamsRequest extends Message<MigrateLegacySelfServeTeamsRequest> {
  /**
   * @generated from field: repeated auth.MigrateLegacySelfServeTeamsRequest.LegacyTeamInfo teams = 1;
   */
  teams: MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo[];

  constructor(data?: PartialMessage<MigrateLegacySelfServeTeamsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.MigrateLegacySelfServeTeamsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MigrateLegacySelfServeTeamsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsRequest;

  static equals(a: MigrateLegacySelfServeTeamsRequest | PlainMessage<MigrateLegacySelfServeTeamsRequest> | undefined, b: MigrateLegacySelfServeTeamsRequest | PlainMessage<MigrateLegacySelfServeTeamsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.MigrateLegacySelfServeTeamsRequest.LegacyTeamInfo
 */
export declare class MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo extends Message<MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string tenant_name = 2;
   */
  tenantName: string;

  /**
   * @generated from field: string admin_user_id = 3;
   */
  adminUserId: string;

  /**
   * @generated from field: string stripe_customer_id = 4;
   */
  stripeCustomerId: string;

  /**
   * @generated from field: string stripe_subscription_id = 5;
   */
  stripeSubscriptionId: string;

  /**
   * @generated from field: google.protobuf.Timestamp stripe_current_period_start = 6;
   */
  stripeCurrentPeriodStart?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp stripe_current_period_end = 7;
   */
  stripeCurrentPeriodEnd?: Timestamp;

  /**
   * @generated from field: string orb_external_plan_id = 8;
   */
  orbExternalPlanId: string;

  /**
   * @generated from field: int64 orb_plan_version_number = 9;
   */
  orbPlanVersionNumber: bigint;

  /**
   * @generated from field: int32 seats = 10;
   */
  seats: number;

  /**
   * @generated from field: bool apply_free_seat_adjustment = 11;
   */
  applyFreeSeatAdjustment: boolean;

  /**
   * @generated from field: string seats_price_id = 12;
   */
  seatsPriceId: string;

  /**
   * @generated from field: string messages_price_id = 13;
   */
  messagesPriceId: string;

  /**
   * @generated from field: float price_per_seat = 14;
   */
  pricePerSeat: number;

  /**
   * @generated from field: float messages_per_seat = 15;
   */
  messagesPerSeat: number;

  constructor(data?: PartialMessage<MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.MigrateLegacySelfServeTeamsRequest.LegacyTeamInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo;

  static equals(a: MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo | PlainMessage<MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo> | undefined, b: MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo | PlainMessage<MigrateLegacySelfServeTeamsRequest_LegacyTeamInfo> | undefined): boolean;
}

/**
 * @generated from message auth.MigrateLegacySelfServeTeamsResponse
 */
export declare class MigrateLegacySelfServeTeamsResponse extends Message<MigrateLegacySelfServeTeamsResponse> {
  /**
   * @generated from field: repeated auth.MigrateLegacySelfServeTeamsResponse.MigrationResult results = 1;
   */
  results: MigrateLegacySelfServeTeamsResponse_MigrationResult[];

  /**
   * @generated from field: int32 successful_migrations = 2;
   */
  successfulMigrations: number;

  /**
   * @generated from field: int32 failed_migrations = 3;
   */
  failedMigrations: number;

  constructor(data?: PartialMessage<MigrateLegacySelfServeTeamsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.MigrateLegacySelfServeTeamsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MigrateLegacySelfServeTeamsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsResponse;

  static equals(a: MigrateLegacySelfServeTeamsResponse | PlainMessage<MigrateLegacySelfServeTeamsResponse> | undefined, b: MigrateLegacySelfServeTeamsResponse | PlainMessage<MigrateLegacySelfServeTeamsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.MigrateLegacySelfServeTeamsResponse.MigrationResult
 */
export declare class MigrateLegacySelfServeTeamsResponse_MigrationResult extends Message<MigrateLegacySelfServeTeamsResponse_MigrationResult> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: bool success = 2;
   */
  success: boolean;

  /**
   * @generated from field: string error_message = 3;
   */
  errorMessage: string;

  constructor(data?: PartialMessage<MigrateLegacySelfServeTeamsResponse_MigrationResult>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.MigrateLegacySelfServeTeamsResponse.MigrationResult";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MigrateLegacySelfServeTeamsResponse_MigrationResult;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsResponse_MigrationResult;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MigrateLegacySelfServeTeamsResponse_MigrationResult;

  static equals(a: MigrateLegacySelfServeTeamsResponse_MigrationResult | PlainMessage<MigrateLegacySelfServeTeamsResponse_MigrationResult> | undefined, b: MigrateLegacySelfServeTeamsResponse_MigrationResult | PlainMessage<MigrateLegacySelfServeTeamsResponse_MigrationResult> | undefined): boolean;
}

/**
 * @generated from message auth.CreateTenantForTeamRequest
 */
export declare class CreateTenantForTeamRequest extends Message<CreateTenantForTeamRequest> {
  /**
   * @generated from field: string admin_user_id = 1;
   */
  adminUserId: string;

  constructor(data?: PartialMessage<CreateTenantForTeamRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.CreateTenantForTeamRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTenantForTeamRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTenantForTeamRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTenantForTeamRequest;

  static equals(a: CreateTenantForTeamRequest | PlainMessage<CreateTenantForTeamRequest> | undefined, b: CreateTenantForTeamRequest | PlainMessage<CreateTenantForTeamRequest> | undefined): boolean;
}

/**
 * @generated from message auth.CreateTenantForTeamResponse
 */
export declare class CreateTenantForTeamResponse extends Message<CreateTenantForTeamResponse> {
  /**
   * @generated from field: string tenant_creation_id = 1;
   */
  tenantCreationId: string;

  constructor(data?: PartialMessage<CreateTenantForTeamResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.CreateTenantForTeamResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTenantForTeamResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTenantForTeamResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTenantForTeamResponse;

  static equals(a: CreateTenantForTeamResponse | PlainMessage<CreateTenantForTeamResponse> | undefined, b: CreateTenantForTeamResponse | PlainMessage<CreateTenantForTeamResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetCreateTenantForTeamStatusRequest
 */
export declare class GetCreateTenantForTeamStatusRequest extends Message<GetCreateTenantForTeamStatusRequest> {
  /**
   * @generated from field: string tenant_creation_id = 1;
   */
  tenantCreationId: string;

  constructor(data?: PartialMessage<GetCreateTenantForTeamStatusRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetCreateTenantForTeamStatusRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCreateTenantForTeamStatusRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCreateTenantForTeamStatusRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCreateTenantForTeamStatusRequest;

  static equals(a: GetCreateTenantForTeamStatusRequest | PlainMessage<GetCreateTenantForTeamStatusRequest> | undefined, b: GetCreateTenantForTeamStatusRequest | PlainMessage<GetCreateTenantForTeamStatusRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetCreateTenantForTeamStatusResponse
 */
export declare class GetCreateTenantForTeamStatusResponse extends Message<GetCreateTenantForTeamStatusResponse> {
  /**
   * @generated from field: auth_entities.TenantCreation tenant_creation = 1;
   */
  tenantCreation?: TenantCreation;

  constructor(data?: PartialMessage<GetCreateTenantForTeamStatusResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetCreateTenantForTeamStatusResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetCreateTenantForTeamStatusResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetCreateTenantForTeamStatusResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetCreateTenantForTeamStatusResponse;

  static equals(a: GetCreateTenantForTeamStatusResponse | PlainMessage<GetCreateTenantForTeamStatusResponse> | undefined, b: GetCreateTenantForTeamStatusResponse | PlainMessage<GetCreateTenantForTeamStatusResponse> | undefined): boolean;
}

/**
 * @generated from message auth.InviteUsersToTenantRequest
 */
export declare class InviteUsersToTenantRequest extends Message<InviteUsersToTenantRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: repeated string invitee_emails = 2;
   */
  inviteeEmails: string[];

  constructor(data?: PartialMessage<InviteUsersToTenantRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.InviteUsersToTenantRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InviteUsersToTenantRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InviteUsersToTenantRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InviteUsersToTenantRequest;

  static equals(a: InviteUsersToTenantRequest | PlainMessage<InviteUsersToTenantRequest> | undefined, b: InviteUsersToTenantRequest | PlainMessage<InviteUsersToTenantRequest> | undefined): boolean;
}

/**
 * @generated from message auth.InviteUsersToTenantResponse
 */
export declare class InviteUsersToTenantResponse extends Message<InviteUsersToTenantResponse> {
  /**
   * @generated from field: repeated auth.InviteUsersToTenantResponse.InvitationCreationStatus invitation_statuses = 1;
   */
  invitationStatuses: InviteUsersToTenantResponse_InvitationCreationStatus[];

  constructor(data?: PartialMessage<InviteUsersToTenantResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.InviteUsersToTenantResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InviteUsersToTenantResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InviteUsersToTenantResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InviteUsersToTenantResponse;

  static equals(a: InviteUsersToTenantResponse | PlainMessage<InviteUsersToTenantResponse> | undefined, b: InviteUsersToTenantResponse | PlainMessage<InviteUsersToTenantResponse> | undefined): boolean;
}

/**
 * @generated from message auth.InviteUsersToTenantResponse.InvitationCreationStatus
 */
export declare class InviteUsersToTenantResponse_InvitationCreationStatus extends Message<InviteUsersToTenantResponse_InvitationCreationStatus> {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: auth.InviteUsersToTenantResponse.InvitationCreationStatus.Status status = 2;
   */
  status: InviteUsersToTenantResponse_InvitationCreationStatus_Status;

  /**
   * @generated from field: string invitation_id = 3;
   */
  invitationId: string;

  constructor(data?: PartialMessage<InviteUsersToTenantResponse_InvitationCreationStatus>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.InviteUsersToTenantResponse.InvitationCreationStatus";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InviteUsersToTenantResponse_InvitationCreationStatus;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InviteUsersToTenantResponse_InvitationCreationStatus;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InviteUsersToTenantResponse_InvitationCreationStatus;

  static equals(a: InviteUsersToTenantResponse_InvitationCreationStatus | PlainMessage<InviteUsersToTenantResponse_InvitationCreationStatus> | undefined, b: InviteUsersToTenantResponse_InvitationCreationStatus | PlainMessage<InviteUsersToTenantResponse_InvitationCreationStatus> | undefined): boolean;
}

/**
 * @generated from enum auth.InviteUsersToTenantResponse.InvitationCreationStatus.Status
 */
export declare enum InviteUsersToTenantResponse_InvitationCreationStatus_Status {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: SUCCESS = 1;
   */
  SUCCESS = 1,

  /**
   * @generated from enum value: ERROR = 2;
   */
  ERROR = 2,
}

/**
 * @generated from message auth.GetTenantInvitationsRequest
 */
export declare class GetTenantInvitationsRequest extends Message<GetTenantInvitationsRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  constructor(data?: PartialMessage<GetTenantInvitationsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetTenantInvitationsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTenantInvitationsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTenantInvitationsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTenantInvitationsRequest;

  static equals(a: GetTenantInvitationsRequest | PlainMessage<GetTenantInvitationsRequest> | undefined, b: GetTenantInvitationsRequest | PlainMessage<GetTenantInvitationsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetTenantInvitationsResponse
 */
export declare class GetTenantInvitationsResponse extends Message<GetTenantInvitationsResponse> {
  /**
   * @generated from field: repeated auth_entities.TenantInvitation invitations = 1;
   */
  invitations: TenantInvitation[];

  constructor(data?: PartialMessage<GetTenantInvitationsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetTenantInvitationsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTenantInvitationsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTenantInvitationsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTenantInvitationsResponse;

  static equals(a: GetTenantInvitationsResponse | PlainMessage<GetTenantInvitationsResponse> | undefined, b: GetTenantInvitationsResponse | PlainMessage<GetTenantInvitationsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserInvitationsRequest
 */
export declare class GetUserInvitationsRequest extends Message<GetUserInvitationsRequest> {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  constructor(data?: PartialMessage<GetUserInvitationsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserInvitationsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserInvitationsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserInvitationsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserInvitationsRequest;

  static equals(a: GetUserInvitationsRequest | PlainMessage<GetUserInvitationsRequest> | undefined, b: GetUserInvitationsRequest | PlainMessage<GetUserInvitationsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserInvitationsResponse
 */
export declare class GetUserInvitationsResponse extends Message<GetUserInvitationsResponse> {
  /**
   * @generated from field: repeated auth_entities.TenantInvitation invitations = 1;
   */
  invitations: TenantInvitation[];

  constructor(data?: PartialMessage<GetUserInvitationsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserInvitationsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserInvitationsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserInvitationsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserInvitationsResponse;

  static equals(a: GetUserInvitationsResponse | PlainMessage<GetUserInvitationsResponse> | undefined, b: GetUserInvitationsResponse | PlainMessage<GetUserInvitationsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.ResolveInvitationsRequest
 */
export declare class ResolveInvitationsRequest extends Message<ResolveInvitationsRequest> {
  /**
   * @generated from field: optional string accept_invitation_id = 1;
   */
  acceptInvitationId?: string;

  /**
   * @generated from field: repeated string decline_invitation_ids = 2;
   */
  declineInvitationIds: string[];

  constructor(data?: PartialMessage<ResolveInvitationsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ResolveInvitationsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResolveInvitationsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResolveInvitationsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResolveInvitationsRequest;

  static equals(a: ResolveInvitationsRequest | PlainMessage<ResolveInvitationsRequest> | undefined, b: ResolveInvitationsRequest | PlainMessage<ResolveInvitationsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.ResolveInvitationsResponse
 */
export declare class ResolveInvitationsResponse extends Message<ResolveInvitationsResponse> {
  /**
   * @generated from field: string invitation_resolution_id = 1;
   */
  invitationResolutionId: string;

  constructor(data?: PartialMessage<ResolveInvitationsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ResolveInvitationsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResolveInvitationsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResolveInvitationsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResolveInvitationsResponse;

  static equals(a: ResolveInvitationsResponse | PlainMessage<ResolveInvitationsResponse> | undefined, b: ResolveInvitationsResponse | PlainMessage<ResolveInvitationsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetResolveInvitationsStatusRequest
 */
export declare class GetResolveInvitationsStatusRequest extends Message<GetResolveInvitationsStatusRequest> {
  /**
   * @generated from field: string invitation_resolution_id = 1;
   */
  invitationResolutionId: string;

  constructor(data?: PartialMessage<GetResolveInvitationsStatusRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetResolveInvitationsStatusRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetResolveInvitationsStatusRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetResolveInvitationsStatusRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetResolveInvitationsStatusRequest;

  static equals(a: GetResolveInvitationsStatusRequest | PlainMessage<GetResolveInvitationsStatusRequest> | undefined, b: GetResolveInvitationsStatusRequest | PlainMessage<GetResolveInvitationsStatusRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetResolveInvitationsStatusResponse
 */
export declare class GetResolveInvitationsStatusResponse extends Message<GetResolveInvitationsStatusResponse> {
  /**
   * @generated from field: auth_entities.InvitationResolution invitation_resolution = 1;
   */
  invitationResolution?: InvitationResolution;

  constructor(data?: PartialMessage<GetResolveInvitationsStatusResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetResolveInvitationsStatusResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetResolveInvitationsStatusResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetResolveInvitationsStatusResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetResolveInvitationsStatusResponse;

  static equals(a: GetResolveInvitationsStatusResponse | PlainMessage<GetResolveInvitationsStatusResponse> | undefined, b: GetResolveInvitationsStatusResponse | PlainMessage<GetResolveInvitationsStatusResponse> | undefined): boolean;
}

/**
 * @generated from message auth.DeleteInvitationRequest
 */
export declare class DeleteInvitationRequest extends Message<DeleteInvitationRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  /**
   * @generated from field: string invitation_id = 2;
   */
  invitationId: string;

  constructor(data?: PartialMessage<DeleteInvitationRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeleteInvitationRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteInvitationRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteInvitationRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteInvitationRequest;

  static equals(a: DeleteInvitationRequest | PlainMessage<DeleteInvitationRequest> | undefined, b: DeleteInvitationRequest | PlainMessage<DeleteInvitationRequest> | undefined): boolean;
}

/**
 * @generated from message auth.DeleteInvitationResponse
 */
export declare class DeleteInvitationResponse extends Message<DeleteInvitationResponse> {
  constructor(data?: PartialMessage<DeleteInvitationResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeleteInvitationResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteInvitationResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteInvitationResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteInvitationResponse;

  static equals(a: DeleteInvitationResponse | PlainMessage<DeleteInvitationResponse> | undefined, b: DeleteInvitationResponse | PlainMessage<DeleteInvitationResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetSubscriptionRequest
 */
export declare class GetSubscriptionRequest extends Message<GetSubscriptionRequest> {
  /**
   * @generated from oneof auth.GetSubscriptionRequest.lookup_id
   */
  lookupId: {
    /**
     * @generated from field: string subscription_id = 1;
     */
    value: string;
    case: "subscriptionId";
  } | {
    /**
     * @generated from field: string tenant_id = 2;
     */
    value: string;
    case: "tenantId";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<GetSubscriptionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetSubscriptionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSubscriptionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSubscriptionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSubscriptionRequest;

  static equals(a: GetSubscriptionRequest | PlainMessage<GetSubscriptionRequest> | undefined, b: GetSubscriptionRequest | PlainMessage<GetSubscriptionRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetSubscriptionResponse
 */
export declare class GetSubscriptionResponse extends Message<GetSubscriptionResponse> {
  /**
   * @generated from field: auth_entities.Subscription subscription = 1;
   */
  subscription?: Subscription;

  constructor(data?: PartialMessage<GetSubscriptionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetSubscriptionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSubscriptionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSubscriptionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSubscriptionResponse;

  static equals(a: GetSubscriptionResponse | PlainMessage<GetSubscriptionResponse> | undefined, b: GetSubscriptionResponse | PlainMessage<GetSubscriptionResponse> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateSubscriptionRequest
 */
export declare class UpdateSubscriptionRequest extends Message<UpdateSubscriptionRequest> {
  /**
   * @generated from field: string subscription_id = 1;
   */
  subscriptionId: string;

  /**
   * @generated from field: int32 seats = 2;
   */
  seats: number;

  constructor(data?: PartialMessage<UpdateSubscriptionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateSubscriptionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSubscriptionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSubscriptionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSubscriptionRequest;

  static equals(a: UpdateSubscriptionRequest | PlainMessage<UpdateSubscriptionRequest> | undefined, b: UpdateSubscriptionRequest | PlainMessage<UpdateSubscriptionRequest> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateSubscriptionResponse
 */
export declare class UpdateSubscriptionResponse extends Message<UpdateSubscriptionResponse> {
  constructor(data?: PartialMessage<UpdateSubscriptionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateSubscriptionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSubscriptionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSubscriptionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSubscriptionResponse;

  static equals(a: UpdateSubscriptionResponse | PlainMessage<UpdateSubscriptionResponse> | undefined, b: UpdateSubscriptionResponse | PlainMessage<UpdateSubscriptionResponse> | undefined): boolean;
}

/**
 * @generated from message auth.CreateUserSuspensionRequest
 */
export declare class CreateUserSuspensionRequest extends Message<CreateUserSuspensionRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: auth_entities.UserSuspensionType suspension_type = 3;
   */
  suspensionType: UserSuspensionType;

  /**
   * @generated from field: string evidence = 4;
   */
  evidence: string;

  constructor(data?: PartialMessage<CreateUserSuspensionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.CreateUserSuspensionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateUserSuspensionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateUserSuspensionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateUserSuspensionRequest;

  static equals(a: CreateUserSuspensionRequest | PlainMessage<CreateUserSuspensionRequest> | undefined, b: CreateUserSuspensionRequest | PlainMessage<CreateUserSuspensionRequest> | undefined): boolean;
}

/**
 * @generated from message auth.CreateUserSuspensionResponse
 */
export declare class CreateUserSuspensionResponse extends Message<CreateUserSuspensionResponse> {
  /**
   * @generated from field: string suspension_id = 1;
   */
  suspensionId: string;

  /**
   * @generated from field: int32 tokens_deleted = 2;
   */
  tokensDeleted: number;

  constructor(data?: PartialMessage<CreateUserSuspensionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.CreateUserSuspensionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateUserSuspensionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateUserSuspensionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateUserSuspensionResponse;

  static equals(a: CreateUserSuspensionResponse | PlainMessage<CreateUserSuspensionResponse> | undefined, b: CreateUserSuspensionResponse | PlainMessage<CreateUserSuspensionResponse> | undefined): boolean;
}

/**
 * @generated from message auth.DeleteUserSuspensionsRequest
 */
export declare class DeleteUserSuspensionsRequest extends Message<DeleteUserSuspensionsRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: repeated string suspension_ids = 3;
   */
  suspensionIds: string[];

  constructor(data?: PartialMessage<DeleteUserSuspensionsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeleteUserSuspensionsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUserSuspensionsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUserSuspensionsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUserSuspensionsRequest;

  static equals(a: DeleteUserSuspensionsRequest | PlainMessage<DeleteUserSuspensionsRequest> | undefined, b: DeleteUserSuspensionsRequest | PlainMessage<DeleteUserSuspensionsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.DeleteUserSuspensionsResponse
 */
export declare class DeleteUserSuspensionsResponse extends Message<DeleteUserSuspensionsResponse> {
  /**
   * @generated from field: int32 suspensions_deleted = 1;
   */
  suspensionsDeleted: number;

  constructor(data?: PartialMessage<DeleteUserSuspensionsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.DeleteUserSuspensionsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUserSuspensionsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUserSuspensionsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUserSuspensionsResponse;

  static equals(a: DeleteUserSuspensionsResponse | PlainMessage<DeleteUserSuspensionsResponse> | undefined, b: DeleteUserSuspensionsResponse | PlainMessage<DeleteUserSuspensionsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateSuspensionExemptionRequest
 */
export declare class UpdateSuspensionExemptionRequest extends Message<UpdateSuspensionExemptionRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: bool exempt = 3;
   */
  exempt: boolean;

  constructor(data?: PartialMessage<UpdateSuspensionExemptionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateSuspensionExemptionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSuspensionExemptionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSuspensionExemptionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSuspensionExemptionRequest;

  static equals(a: UpdateSuspensionExemptionRequest | PlainMessage<UpdateSuspensionExemptionRequest> | undefined, b: UpdateSuspensionExemptionRequest | PlainMessage<UpdateSuspensionExemptionRequest> | undefined): boolean;
}

/**
 * @generated from message auth.UpdateSuspensionExemptionResponse
 */
export declare class UpdateSuspensionExemptionResponse extends Message<UpdateSuspensionExemptionResponse> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: bool exempt = 3;
   */
  exempt: boolean;

  constructor(data?: PartialMessage<UpdateSuspensionExemptionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UpdateSuspensionExemptionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateSuspensionExemptionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateSuspensionExemptionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateSuspensionExemptionResponse;

  static equals(a: UpdateSuspensionExemptionResponse | PlainMessage<UpdateSuspensionExemptionResponse> | undefined, b: UpdateSuspensionExemptionResponse | PlainMessage<UpdateSuspensionExemptionResponse> | undefined): boolean;
}

/**
 * @generated from message auth.PurchaseCreditsRequest
 */
export declare class PurchaseCreditsRequest extends Message<PurchaseCreditsRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  /**
   * @generated from field: float credits = 3;
   */
  credits: number;

  /**
   * @generated from field: optional string idempotency_key = 4;
   */
  idempotencyKey?: string;

  constructor(data?: PartialMessage<PurchaseCreditsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.PurchaseCreditsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PurchaseCreditsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PurchaseCreditsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PurchaseCreditsRequest;

  static equals(a: PurchaseCreditsRequest | PlainMessage<PurchaseCreditsRequest> | undefined, b: PurchaseCreditsRequest | PlainMessage<PurchaseCreditsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.PurchaseCreditsResponse
 */
export declare class PurchaseCreditsResponse extends Message<PurchaseCreditsResponse> {
  constructor(data?: PartialMessage<PurchaseCreditsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.PurchaseCreditsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PurchaseCreditsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PurchaseCreditsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PurchaseCreditsResponse;

  static equals(a: PurchaseCreditsResponse | PlainMessage<PurchaseCreditsResponse> | undefined, b: PurchaseCreditsResponse | PlainMessage<PurchaseCreditsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.CancelSubscriptionRequest
 */
export declare class CancelSubscriptionRequest extends Message<CancelSubscriptionRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string tenant_id = 2;
   */
  tenantId: string;

  constructor(data?: PartialMessage<CancelSubscriptionRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.CancelSubscriptionRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CancelSubscriptionRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CancelSubscriptionRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CancelSubscriptionRequest;

  static equals(a: CancelSubscriptionRequest | PlainMessage<CancelSubscriptionRequest> | undefined, b: CancelSubscriptionRequest | PlainMessage<CancelSubscriptionRequest> | undefined): boolean;
}

/**
 * @generated from message auth.CancelSubscriptionResponse
 */
export declare class CancelSubscriptionResponse extends Message<CancelSubscriptionResponse> {
  constructor(data?: PartialMessage<CancelSubscriptionResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.CancelSubscriptionResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CancelSubscriptionResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CancelSubscriptionResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CancelSubscriptionResponse;

  static equals(a: CancelSubscriptionResponse | PlainMessage<CancelSubscriptionResponse> | undefined, b: CancelSubscriptionResponse | PlainMessage<CancelSubscriptionResponse> | undefined): boolean;
}

/**
 * @generated from message auth.ListSubscriptionsRequest
 */
export declare class ListSubscriptionsRequest extends Message<ListSubscriptionsRequest> {
  /**
   * @generated from field: uint32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  constructor(data?: PartialMessage<ListSubscriptionsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ListSubscriptionsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSubscriptionsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSubscriptionsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSubscriptionsRequest;

  static equals(a: ListSubscriptionsRequest | PlainMessage<ListSubscriptionsRequest> | undefined, b: ListSubscriptionsRequest | PlainMessage<ListSubscriptionsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.ListSubscriptionsResponse
 */
export declare class ListSubscriptionsResponse extends Message<ListSubscriptionsResponse> {
  /**
   * @generated from field: repeated auth_entities.Subscription subscriptions = 1;
   */
  subscriptions: Subscription[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  constructor(data?: PartialMessage<ListSubscriptionsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ListSubscriptionsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListSubscriptionsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListSubscriptionsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListSubscriptionsResponse;

  static equals(a: ListSubscriptionsResponse | PlainMessage<ListSubscriptionsResponse> | undefined, b: ListSubscriptionsResponse | PlainMessage<ListSubscriptionsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.ListTenantSubscriptionMappingsRequest
 */
export declare class ListTenantSubscriptionMappingsRequest extends Message<ListTenantSubscriptionMappingsRequest> {
  /**
   * @generated from field: uint32 page_size = 1;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  constructor(data?: PartialMessage<ListTenantSubscriptionMappingsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ListTenantSubscriptionMappingsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTenantSubscriptionMappingsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTenantSubscriptionMappingsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTenantSubscriptionMappingsRequest;

  static equals(a: ListTenantSubscriptionMappingsRequest | PlainMessage<ListTenantSubscriptionMappingsRequest> | undefined, b: ListTenantSubscriptionMappingsRequest | PlainMessage<ListTenantSubscriptionMappingsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.ListTenantSubscriptionMappingsResponse
 */
export declare class ListTenantSubscriptionMappingsResponse extends Message<ListTenantSubscriptionMappingsResponse> {
  /**
   * @generated from field: repeated auth_entities.TenantSubscriptionMapping tenant_subscription_mappings = 1;
   */
  tenantSubscriptionMappings: TenantSubscriptionMapping[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  constructor(data?: PartialMessage<ListTenantSubscriptionMappingsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.ListTenantSubscriptionMappingsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTenantSubscriptionMappingsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTenantSubscriptionMappingsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTenantSubscriptionMappingsResponse;

  static equals(a: ListTenantSubscriptionMappingsResponse | PlainMessage<ListTenantSubscriptionMappingsResponse> | undefined, b: ListTenantSubscriptionMappingsResponse | PlainMessage<ListTenantSubscriptionMappingsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbInfoRequest
 */
export declare class GetUserOrbInfoRequest extends Message<GetUserOrbInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  constructor(data?: PartialMessage<GetUserOrbInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbInfoRequest;

  static equals(a: GetUserOrbInfoRequest | PlainMessage<GetUserOrbInfoRequest> | undefined, b: GetUserOrbInfoRequest | PlainMessage<GetUserOrbInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.FailedPayment
 */
export declare class FailedPayment extends Message<FailedPayment> {
  /**
   * @generated from field: string amount = 1;
   */
  amount: string;

  /**
   * @generated from field: string date = 2;
   */
  date: string;

  constructor(data?: PartialMessage<FailedPayment>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.FailedPayment";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): FailedPayment;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): FailedPayment;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): FailedPayment;

  static equals(a: FailedPayment | PlainMessage<FailedPayment> | undefined, b: FailedPayment | PlainMessage<FailedPayment> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbInfoResponse
 */
export declare class GetUserOrbInfoResponse extends Message<GetUserOrbInfoResponse> {
  /**
   * @generated from field: string orb_customer_id = 1;
   */
  orbCustomerId: string;

  /**
   * @generated from field: string orb_subscription_id = 2;
   */
  orbSubscriptionId: string;

  /**
   * @generated from field: string portal_url = 3;
   */
  portalUrl: string;

  /**
   * @generated from field: string external_plan_id = 4;
   */
  externalPlanId: string;

  /**
   * @generated from field: string billing_period_end = 5;
   */
  billingPeriodEnd: string;

  /**
   * @generated from field: bool has_payment_method = 6;
   */
  hasPaymentMethod: boolean;

  /**
   * @generated from field: int32 usage_units_available = 7;
   */
  usageUnitsAvailable: number;

  /**
   * @generated from field: int32 usage_units_used_this_billing_cycle = 8;
   */
  usageUnitsUsedThisBillingCycle: number;

  /**
   * @generated from field: int32 usage_units_pending = 9;
   */
  usageUnitsPending: number;

  /**
   * @generated from field: int32 usage_units_renewing_each_billing_cycle = 10;
   */
  usageUnitsRenewingEachBillingCycle: number;

  /**
   * @generated from field: int32 number_of_seats_this_billing_cycle = 11;
   */
  numberOfSeatsThisBillingCycle: number;

  /**
   * @generated from field: int32 number_of_seats_next_billing_cycle = 12;
   */
  numberOfSeatsNextBillingCycle: number;

  /**
   * @generated from field: optional string trial_period_end = 13;
   */
  trialPeriodEnd?: string;

  /**
   * @generated from field: optional string subscription_end_date = 14;
   */
  subscriptionEndDate?: string;

  /**
   * @generated from field: string next_billing_cycle_amount = 15;
   */
  nextBillingCycleAmount: string;

  /**
   * @generated from field: string usage_unit_name = 16;
   */
  usageUnitName: string;

  /**
   * @generated from field: optional auth.FailedPayment failed_payment = 17;
   */
  failedPayment?: FailedPayment;

  /**
   * @generated from field: auth.GetUserOrbInfoResponse.SubscriptionStatus subscription_status = 18;
   */
  subscriptionStatus: GetUserOrbInfoResponse_SubscriptionStatus;

  constructor(data?: PartialMessage<GetUserOrbInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbInfoResponse;

  static equals(a: GetUserOrbInfoResponse | PlainMessage<GetUserOrbInfoResponse> | undefined, b: GetUserOrbInfoResponse | PlainMessage<GetUserOrbInfoResponse> | undefined): boolean;
}

/**
 * @generated from enum auth.GetUserOrbInfoResponse.SubscriptionStatus
 */
export declare enum GetUserOrbInfoResponse_SubscriptionStatus {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: UPCOMING = 1;
   */
  UPCOMING = 1,

  /**
   * @generated from enum value: ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * @generated from enum value: ENDED = 3;
   */
  ENDED = 3,
}

/**
 * @generated from message auth.GetUserOrbPlanInfoRequest
 */
export declare class GetUserOrbPlanInfoRequest extends Message<GetUserOrbPlanInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  constructor(data?: PartialMessage<GetUserOrbPlanInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbPlanInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbPlanInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbPlanInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbPlanInfoRequest;

  static equals(a: GetUserOrbPlanInfoRequest | PlainMessage<GetUserOrbPlanInfoRequest> | undefined, b: GetUserOrbPlanInfoRequest | PlainMessage<GetUserOrbPlanInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.OrbPlanInfo
 */
export declare class OrbPlanInfo extends Message<OrbPlanInfo> {
  /**
   * @generated from field: string external_plan_id = 1;
   */
  externalPlanId: string;

  /**
   * @generated from field: string formatted_plan_name = 2;
   */
  formattedPlanName: string;

  /**
   * @generated from field: string price_per_seat = 3;
   */
  pricePerSeat: string;

  /**
   * @generated from field: string additional_usage_unit_cost = 4;
   */
  additionalUsageUnitCost: string;

  /**
   * @generated from field: bool add_usage_available = 5;
   */
  addUsageAvailable: boolean;

  /**
   * @generated from field: float usage_units_per_seat = 6;
   */
  usageUnitsPerSeat: number;

  /**
   * @generated from field: bool training_allowed = 7;
   */
  trainingAllowed: boolean;

  /**
   * @generated from field: bool teams_allowed = 8;
   */
  teamsAllowed: boolean;

  /**
   * @generated from field: int32 max_num_seats = 9;
   */
  maxNumSeats: number;

  /**
   * @generated from field: string usage_unit_name = 10;
   */
  usageUnitName: string;

  constructor(data?: PartialMessage<OrbPlanInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.OrbPlanInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrbPlanInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrbPlanInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrbPlanInfo;

  static equals(a: OrbPlanInfo | PlainMessage<OrbPlanInfo> | undefined, b: OrbPlanInfo | PlainMessage<OrbPlanInfo> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbPlanInfoResponse
 */
export declare class GetUserOrbPlanInfoResponse extends Message<GetUserOrbPlanInfoResponse> {
  /**
   * @generated from field: auth.OrbPlanInfo orb_plan_info = 1;
   */
  orbPlanInfo?: OrbPlanInfo;

  constructor(data?: PartialMessage<GetUserOrbPlanInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbPlanInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbPlanInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbPlanInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbPlanInfoResponse;

  static equals(a: GetUserOrbPlanInfoResponse | PlainMessage<GetUserOrbPlanInfoResponse> | undefined, b: GetUserOrbPlanInfoResponse | PlainMessage<GetUserOrbPlanInfoResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetAllOrbPlansRequest
 */
export declare class GetAllOrbPlansRequest extends Message<GetAllOrbPlansRequest> {
  constructor(data?: PartialMessage<GetAllOrbPlansRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetAllOrbPlansRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllOrbPlansRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllOrbPlansRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllOrbPlansRequest;

  static equals(a: GetAllOrbPlansRequest | PlainMessage<GetAllOrbPlansRequest> | undefined, b: GetAllOrbPlansRequest | PlainMessage<GetAllOrbPlansRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetAllOrbPlansResponse
 */
export declare class GetAllOrbPlansResponse extends Message<GetAllOrbPlansResponse> {
  /**
   * @generated from field: repeated auth.OrbPlanInfo orb_plans = 1;
   */
  orbPlans: OrbPlanInfo[];

  constructor(data?: PartialMessage<GetAllOrbPlansResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetAllOrbPlansResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAllOrbPlansResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAllOrbPlansResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAllOrbPlansResponse;

  static equals(a: GetAllOrbPlansResponse | PlainMessage<GetAllOrbPlansResponse> | undefined, b: GetAllOrbPlansResponse | PlainMessage<GetAllOrbPlansResponse> | undefined): boolean;
}

/**
 * @generated from message auth.UnschedulePendingSubscriptionCancellationRequest
 */
export declare class UnschedulePendingSubscriptionCancellationRequest extends Message<UnschedulePendingSubscriptionCancellationRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  constructor(data?: PartialMessage<UnschedulePendingSubscriptionCancellationRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UnschedulePendingSubscriptionCancellationRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnschedulePendingSubscriptionCancellationRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnschedulePendingSubscriptionCancellationRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnschedulePendingSubscriptionCancellationRequest;

  static equals(a: UnschedulePendingSubscriptionCancellationRequest | PlainMessage<UnschedulePendingSubscriptionCancellationRequest> | undefined, b: UnschedulePendingSubscriptionCancellationRequest | PlainMessage<UnschedulePendingSubscriptionCancellationRequest> | undefined): boolean;
}

/**
 * @generated from message auth.UnschedulePendingSubscriptionCancellationResponse
 */
export declare class UnschedulePendingSubscriptionCancellationResponse extends Message<UnschedulePendingSubscriptionCancellationResponse> {
  constructor(data?: PartialMessage<UnschedulePendingSubscriptionCancellationResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UnschedulePendingSubscriptionCancellationResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnschedulePendingSubscriptionCancellationResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnschedulePendingSubscriptionCancellationResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnschedulePendingSubscriptionCancellationResponse;

  static equals(a: UnschedulePendingSubscriptionCancellationResponse | PlainMessage<UnschedulePendingSubscriptionCancellationResponse> | undefined, b: UnschedulePendingSubscriptionCancellationResponse | PlainMessage<UnschedulePendingSubscriptionCancellationResponse> | undefined): boolean;
}

/**
 * @generated from message auth.UnschedulePlanChangesRequest
 */
export declare class UnschedulePlanChangesRequest extends Message<UnschedulePlanChangesRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  constructor(data?: PartialMessage<UnschedulePlanChangesRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UnschedulePlanChangesRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnschedulePlanChangesRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnschedulePlanChangesRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnschedulePlanChangesRequest;

  static equals(a: UnschedulePlanChangesRequest | PlainMessage<UnschedulePlanChangesRequest> | undefined, b: UnschedulePlanChangesRequest | PlainMessage<UnschedulePlanChangesRequest> | undefined): boolean;
}

/**
 * @generated from message auth.UnschedulePlanChangesResponse
 */
export declare class UnschedulePlanChangesResponse extends Message<UnschedulePlanChangesResponse> {
  constructor(data?: PartialMessage<UnschedulePlanChangesResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.UnschedulePlanChangesResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnschedulePlanChangesResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnschedulePlanChangesResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnschedulePlanChangesResponse;

  static equals(a: UnschedulePlanChangesResponse | PlainMessage<UnschedulePlanChangesResponse> | undefined, b: UnschedulePlanChangesResponse | PlainMessage<UnschedulePlanChangesResponse> | undefined): boolean;
}

/**
 * @generated from message auth.PutUserOnPlanRequest
 */
export declare class PutUserOnPlanRequest extends Message<PutUserOnPlanRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: auth.PutUserOnPlanRequest.Plan plan = 2;
   */
  plan: PutUserOnPlanRequest_Plan;

  /**
   * @generated from field: string plan_id = 3;
   */
  planId: string;

  constructor(data?: PartialMessage<PutUserOnPlanRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.PutUserOnPlanRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PutUserOnPlanRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PutUserOnPlanRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PutUserOnPlanRequest;

  static equals(a: PutUserOnPlanRequest | PlainMessage<PutUserOnPlanRequest> | undefined, b: PutUserOnPlanRequest | PlainMessage<PutUserOnPlanRequest> | undefined): boolean;
}

/**
 * @generated from enum auth.PutUserOnPlanRequest.Plan
 */
export declare enum PutUserOnPlanRequest_Plan {
  /**
   * @generated from enum value: UNKNOWN_PLAN = 0;
   */
  UNKNOWN_PLAN = 0,

  /**
   * @generated from enum value: COMMUNITY = 1;
   */
  COMMUNITY = 1,

  /**
   * @generated from enum value: DEVELOPER = 2;
   */
  DEVELOPER = 2,
}

/**
 * @generated from message auth.PutUserOnPlanResponse
 */
export declare class PutUserOnPlanResponse extends Message<PutUserOnPlanResponse> {
  constructor(data?: PartialMessage<PutUserOnPlanResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.PutUserOnPlanResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PutUserOnPlanResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PutUserOnPlanResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PutUserOnPlanResponse;

  static equals(a: PutUserOnPlanResponse | PlainMessage<PutUserOnPlanResponse> | undefined, b: PutUserOnPlanResponse | PlainMessage<PutUserOnPlanResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUsersRequest
 */
export declare class GetUsersRequest extends Message<GetUsersRequest> {
  /**
   * @generated from field: string search_string = 1;
   */
  searchString: string;

  /**
   * @generated from field: uint32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken: string;

  constructor(data?: PartialMessage<GetUsersRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUsersRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUsersRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUsersRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUsersRequest;

  static equals(a: GetUsersRequest | PlainMessage<GetUsersRequest> | undefined, b: GetUsersRequest | PlainMessage<GetUsersRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetUsersResponse
 */
export declare class GetUsersResponse extends Message<GetUsersResponse> {
  /**
   * @generated from field: repeated auth_entities.User users = 1;
   */
  users: User[];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  constructor(data?: PartialMessage<GetUsersResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUsersResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUsersResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUsersResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUsersResponse;

  static equals(a: GetUsersResponse | PlainMessage<GetUsersResponse> | undefined, b: GetUsersResponse | PlainMessage<GetUsersResponse> | undefined): boolean;
}

/**
 * @generated from message auth.MigrateUserBlocksToSuspensionsRequest
 */
export declare class MigrateUserBlocksToSuspensionsRequest extends Message<MigrateUserBlocksToSuspensionsRequest> {
  constructor(data?: PartialMessage<MigrateUserBlocksToSuspensionsRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.MigrateUserBlocksToSuspensionsRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MigrateUserBlocksToSuspensionsRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MigrateUserBlocksToSuspensionsRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MigrateUserBlocksToSuspensionsRequest;

  static equals(a: MigrateUserBlocksToSuspensionsRequest | PlainMessage<MigrateUserBlocksToSuspensionsRequest> | undefined, b: MigrateUserBlocksToSuspensionsRequest | PlainMessage<MigrateUserBlocksToSuspensionsRequest> | undefined): boolean;
}

/**
 * @generated from message auth.MigrateUserBlocksToSuspensionsResponse
 */
export declare class MigrateUserBlocksToSuspensionsResponse extends Message<MigrateUserBlocksToSuspensionsResponse> {
  /**
   * @generated from field: int32 users_migrated = 1;
   */
  usersMigrated: number;

  /**
   * @generated from field: int32 users_failed = 2;
   */
  usersFailed: number;

  constructor(data?: PartialMessage<MigrateUserBlocksToSuspensionsResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.MigrateUserBlocksToSuspensionsResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): MigrateUserBlocksToSuspensionsResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): MigrateUserBlocksToSuspensionsResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): MigrateUserBlocksToSuspensionsResponse;

  static equals(a: MigrateUserBlocksToSuspensionsResponse | PlainMessage<MigrateUserBlocksToSuspensionsResponse> | undefined, b: MigrateUserBlocksToSuspensionsResponse | PlainMessage<MigrateUserBlocksToSuspensionsResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbCreditsInfoRequest
 */
export declare class GetUserOrbCreditsInfoRequest extends Message<GetUserOrbCreditsInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  constructor(data?: PartialMessage<GetUserOrbCreditsInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbCreditsInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbCreditsInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbCreditsInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbCreditsInfoRequest;

  static equals(a: GetUserOrbCreditsInfoRequest | PlainMessage<GetUserOrbCreditsInfoRequest> | undefined, b: GetUserOrbCreditsInfoRequest | PlainMessage<GetUserOrbCreditsInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbCreditsInfoResponse
 */
export declare class GetUserOrbCreditsInfoResponse extends Message<GetUserOrbCreditsInfoResponse> {
  /**
   * @generated from field: int32 usage_units_available = 1;
   */
  usageUnitsAvailable: number;

  /**
   * @generated from field: int32 usage_units_used_this_billing_cycle = 2;
   */
  usageUnitsUsedThisBillingCycle: number;

  /**
   * @generated from field: int32 usage_units_pending = 3;
   */
  usageUnitsPending: number;

  constructor(data?: PartialMessage<GetUserOrbCreditsInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbCreditsInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbCreditsInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbCreditsInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbCreditsInfoResponse;

  static equals(a: GetUserOrbCreditsInfoResponse | PlainMessage<GetUserOrbCreditsInfoResponse> | undefined, b: GetUserOrbCreditsInfoResponse | PlainMessage<GetUserOrbCreditsInfoResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbPaymentInfoRequest
 */
export declare class GetUserOrbPaymentInfoRequest extends Message<GetUserOrbPaymentInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  constructor(data?: PartialMessage<GetUserOrbPaymentInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbPaymentInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbPaymentInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbPaymentInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbPaymentInfoRequest;

  static equals(a: GetUserOrbPaymentInfoRequest | PlainMessage<GetUserOrbPaymentInfoRequest> | undefined, b: GetUserOrbPaymentInfoRequest | PlainMessage<GetUserOrbPaymentInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbPaymentInfoResponse
 */
export declare class GetUserOrbPaymentInfoResponse extends Message<GetUserOrbPaymentInfoResponse> {
  /**
   * @generated from field: bool has_payment_method = 1;
   */
  hasPaymentMethod: boolean;

  /**
   * @generated from field: optional auth.FailedPayment failed_payment = 2;
   */
  failedPayment?: FailedPayment;

  constructor(data?: PartialMessage<GetUserOrbPaymentInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbPaymentInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbPaymentInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbPaymentInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbPaymentInfoResponse;

  static equals(a: GetUserOrbPaymentInfoResponse | PlainMessage<GetUserOrbPaymentInfoResponse> | undefined, b: GetUserOrbPaymentInfoResponse | PlainMessage<GetUserOrbPaymentInfoResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbSubscriptionInfoRequest
 */
export declare class GetUserOrbSubscriptionInfoRequest extends Message<GetUserOrbSubscriptionInfoRequest> {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  constructor(data?: PartialMessage<GetUserOrbSubscriptionInfoRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbSubscriptionInfoRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbSubscriptionInfoRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbSubscriptionInfoRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbSubscriptionInfoRequest;

  static equals(a: GetUserOrbSubscriptionInfoRequest | PlainMessage<GetUserOrbSubscriptionInfoRequest> | undefined, b: GetUserOrbSubscriptionInfoRequest | PlainMessage<GetUserOrbSubscriptionInfoRequest> | undefined): boolean;
}

/**
 * @generated from message auth.OrbSubscriptionInfo
 */
export declare class OrbSubscriptionInfo extends Message<OrbSubscriptionInfo> {
  /**
   * @generated from field: string orb_customer_id = 1;
   */
  orbCustomerId: string;

  /**
   * @generated from field: string orb_subscription_id = 2;
   */
  orbSubscriptionId: string;

  /**
   * @generated from field: string portal_url = 3;
   */
  portalUrl: string;

  /**
   * @generated from field: string external_plan_id = 4;
   */
  externalPlanId: string;

  /**
   * @generated from field: auth.OrbSubscriptionInfo.SubscriptionStatus subscription_status = 5;
   */
  subscriptionStatus: OrbSubscriptionInfo_SubscriptionStatus;

  /**
   * @generated from field: int32 usage_units_renewing_each_billing_cycle = 6;
   */
  usageUnitsRenewingEachBillingCycle: number;

  /**
   * @generated from field: int32 usage_units_included_this_billing_cycle = 7;
   */
  usageUnitsIncludedThisBillingCycle: number;

  /**
   * @generated from field: int32 seats = 8;
   */
  seats: number;

  /**
   * @generated from field: string monthly_total_cost = 9;
   */
  monthlyTotalCost: string;

  /**
   * @generated from field: string next_billing_cycle_amount = 10;
   */
  nextBillingCycleAmount: string;

  /**
   * @generated from field: string billing_period_end_date_iso = 11;
   */
  billingPeriodEndDateIso: string;

  /**
   * @generated from field: optional string trial_period_end_date_iso = 12;
   */
  trialPeriodEndDateIso?: string;

  /**
   * @generated from field: optional string subscription_end_date_iso = 13;
   */
  subscriptionEndDateIso?: string;

  /**
   * @generated from field: bool subscription_end_at_billing_cycle_end = 14;
   */
  subscriptionEndAtBillingCycleEnd: boolean;

  /**
   * @generated from field: optional string scheduled_target_plan_id = 15;
   */
  scheduledTargetPlanId?: string;

  /**
   * @generated from field: bool cancelled_due_to_payment_failure = 16;
   */
  cancelledDueToPaymentFailure: boolean;

  constructor(data?: PartialMessage<OrbSubscriptionInfo>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.OrbSubscriptionInfo";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrbSubscriptionInfo;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrbSubscriptionInfo;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrbSubscriptionInfo;

  static equals(a: OrbSubscriptionInfo | PlainMessage<OrbSubscriptionInfo> | undefined, b: OrbSubscriptionInfo | PlainMessage<OrbSubscriptionInfo> | undefined): boolean;
}

/**
 * @generated from enum auth.OrbSubscriptionInfo.SubscriptionStatus
 */
export declare enum OrbSubscriptionInfo_SubscriptionStatus {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: UPCOMING = 1;
   */
  UPCOMING = 1,

  /**
   * @generated from enum value: ACTIVE = 2;
   */
  ACTIVE = 2,

  /**
   * @generated from enum value: ENDED = 3;
   */
  ENDED = 3,
}

/**
 * @generated from message auth.PendingOrbSubscription
 */
export declare class PendingOrbSubscription extends Message<PendingOrbSubscription> {
  constructor(data?: PartialMessage<PendingOrbSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.PendingOrbSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PendingOrbSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PendingOrbSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PendingOrbSubscription;

  static equals(a: PendingOrbSubscription | PlainMessage<PendingOrbSubscription> | undefined, b: PendingOrbSubscription | PlainMessage<PendingOrbSubscription> | undefined): boolean;
}

/**
 * @generated from message auth.NoSubscription
 */
export declare class NoSubscription extends Message<NoSubscription> {
  constructor(data?: PartialMessage<NoSubscription>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.NoSubscription";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NoSubscription;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NoSubscription;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NoSubscription;

  static equals(a: NoSubscription | PlainMessage<NoSubscription> | undefined, b: NoSubscription | PlainMessage<NoSubscription> | undefined): boolean;
}

/**
 * @generated from message auth.GetUserOrbSubscriptionInfoResponse
 */
export declare class GetUserOrbSubscriptionInfoResponse extends Message<GetUserOrbSubscriptionInfoResponse> {
  /**
   * @generated from oneof auth.GetUserOrbSubscriptionInfoResponse.orb_subscription_info
   */
  orbSubscriptionInfo: {
    /**
     * @generated from field: auth.OrbSubscriptionInfo subscription = 1;
     */
    value: OrbSubscriptionInfo;
    case: "subscription";
  } | {
    /**
     * @generated from field: auth.PendingOrbSubscription pending_subscription = 2;
     */
    value: PendingOrbSubscription;
    case: "pendingSubscription";
  } | {
    /**
     * @generated from field: auth.NoSubscription nonexistent_subscription = 3;
     */
    value: NoSubscription;
    case: "nonexistentSubscription";
  } | { case: undefined; value?: undefined };

  constructor(data?: PartialMessage<GetUserOrbSubscriptionInfoResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetUserOrbSubscriptionInfoResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserOrbSubscriptionInfoResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserOrbSubscriptionInfoResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserOrbSubscriptionInfoResponse;

  static equals(a: GetUserOrbSubscriptionInfoResponse | PlainMessage<GetUserOrbSubscriptionInfoResponse> | undefined, b: GetUserOrbSubscriptionInfoResponse | PlainMessage<GetUserOrbSubscriptionInfoResponse> | undefined): boolean;
}

/**
 * @generated from message auth.SuspensionCleanupRequest
 */
export declare class SuspensionCleanupRequest extends Message<SuspensionCleanupRequest> {
  /**
   * @generated from field: repeated auth_entities.UserSuspensionType remove_suspension_types = 1;
   */
  removeSuspensionTypes: UserSuspensionType[];

  /**
   * @generated from field: repeated auth_entities.UserSuspensionType dedup_suspension_types = 2;
   */
  dedupSuspensionTypes: UserSuspensionType[];

  constructor(data?: PartialMessage<SuspensionCleanupRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.SuspensionCleanupRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SuspensionCleanupRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SuspensionCleanupRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SuspensionCleanupRequest;

  static equals(a: SuspensionCleanupRequest | PlainMessage<SuspensionCleanupRequest> | undefined, b: SuspensionCleanupRequest | PlainMessage<SuspensionCleanupRequest> | undefined): boolean;
}

/**
 * @generated from message auth.SuspensionCleanupResponse
 */
export declare class SuspensionCleanupResponse extends Message<SuspensionCleanupResponse> {
  /**
   * @generated from field: int32 suspensions_removed = 1;
   */
  suspensionsRemoved: number;

  /**
   * @generated from field: int32 suspensions_deduped = 2;
   */
  suspensionsDeduped: number;

  /**
   * @generated from field: int32 updates_failed = 3;
   */
  updatesFailed: number;

  constructor(data?: PartialMessage<SuspensionCleanupResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.SuspensionCleanupResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SuspensionCleanupResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SuspensionCleanupResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SuspensionCleanupResponse;

  static equals(a: SuspensionCleanupResponse | PlainMessage<SuspensionCleanupResponse> | undefined, b: SuspensionCleanupResponse | PlainMessage<SuspensionCleanupResponse> | undefined): boolean;
}

/**
 * @generated from message auth.GetTenantPlanStatusRequest
 */
export declare class GetTenantPlanStatusRequest extends Message<GetTenantPlanStatusRequest> {
  /**
   * @generated from field: string tenant_id = 1;
   */
  tenantId: string;

  constructor(data?: PartialMessage<GetTenantPlanStatusRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetTenantPlanStatusRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTenantPlanStatusRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTenantPlanStatusRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTenantPlanStatusRequest;

  static equals(a: GetTenantPlanStatusRequest | PlainMessage<GetTenantPlanStatusRequest> | undefined, b: GetTenantPlanStatusRequest | PlainMessage<GetTenantPlanStatusRequest> | undefined): boolean;
}

/**
 * @generated from message auth.GetTenantPlanStatusResponse
 */
export declare class GetTenantPlanStatusResponse extends Message<GetTenantPlanStatusResponse> {
  /**
   * @generated from field: bool is_pending = 1;
   */
  isPending: boolean;

  /**
   * @generated from field: optional string pending_target_plan_id = 2;
   */
  pendingTargetPlanId?: string;

  /**
   * @generated from field: optional google.protobuf.Timestamp plan_change_created_at = 3;
   */
  planChangeCreatedAt?: Timestamp;

  constructor(data?: PartialMessage<GetTenantPlanStatusResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.GetTenantPlanStatusResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTenantPlanStatusResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTenantPlanStatusResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTenantPlanStatusResponse;

  static equals(a: GetTenantPlanStatusResponse | PlainMessage<GetTenantPlanStatusResponse> | undefined, b: GetTenantPlanStatusResponse | PlainMessage<GetTenantPlanStatusResponse> | undefined): boolean;
}

/**
 * @generated from message auth.SetUsersBillingMethodToOrbRequest
 */
export declare class SetUsersBillingMethodToOrbRequest extends Message<SetUsersBillingMethodToOrbRequest> {
  /**
   * @generated from field: bool make_changes = 1;
   */
  makeChanges: boolean;

  constructor(data?: PartialMessage<SetUsersBillingMethodToOrbRequest>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.SetUsersBillingMethodToOrbRequest";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetUsersBillingMethodToOrbRequest;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetUsersBillingMethodToOrbRequest;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetUsersBillingMethodToOrbRequest;

  static equals(a: SetUsersBillingMethodToOrbRequest | PlainMessage<SetUsersBillingMethodToOrbRequest> | undefined, b: SetUsersBillingMethodToOrbRequest | PlainMessage<SetUsersBillingMethodToOrbRequest> | undefined): boolean;
}

/**
 * @generated from message auth.SetUsersBillingMethodToOrbResponse
 */
export declare class SetUsersBillingMethodToOrbResponse extends Message<SetUsersBillingMethodToOrbResponse> {
  /**
   * @generated from field: repeated string updated_user_ids = 1;
   */
  updatedUserIds: string[];

  /**
   * @generated from field: repeated string failed_user_ids = 2;
   */
  failedUserIds: string[];

  constructor(data?: PartialMessage<SetUsersBillingMethodToOrbResponse>);

  static readonly runtime: typeof proto3;
  static readonly typeName = "auth.SetUsersBillingMethodToOrbResponse";
  static readonly fields: FieldList;

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetUsersBillingMethodToOrbResponse;

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetUsersBillingMethodToOrbResponse;

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetUsersBillingMethodToOrbResponse;

  static equals(a: SetUsersBillingMethodToOrbResponse | PlainMessage<SetUsersBillingMethodToOrbResponse> | undefined, b: SetUsersBillingMethodToOrbResponse | PlainMessage<SetUsersBillingMethodToOrbResponse> | undefined): boolean;
}

