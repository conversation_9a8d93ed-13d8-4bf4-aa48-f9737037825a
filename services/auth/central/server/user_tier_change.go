package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// TestPanicPoints defines panic points that can be enabled for testing
var UserTierChangePanicPoints = struct {
	MoveUserToTenantError string
}{
	MoveUserToTenantError: "move-user-to-tenant-error",
}

var (
	UserTierChangeProcessDeadLetterQueue = featureflags.NewBoolFlag("auth_central_async_ops_process_dead_letter_queue", false)
	UserTierChangeEnabled                = featureflags.NewBoolFlag("auth_central_user_tier_change", true)
)

// UserTierChangeProcessor is responsible for processing user tier change messages from the async-ops pubsub topic
type UserTierChangeProcessor struct {
	daoFactory  *DAOFactory
	tierManager *UserTierManager
	auditLogger *audit.AuditLogger
	tenantMap   *TenantMap
}

func NewUserTierChangeProcessor(
	config *Config,
	daoFactory *DAOFactory,
	featureFlagHandle featureflags.FeatureFlagHandle,
	auditLogger *audit.AuditLogger,
	tenantMap *TenantMap,
) (*UserTierChangeProcessor, error) {
	tierManager, err := NewUserTierManager(daoFactory, featureFlagHandle, &config.Orb, auditLogger, tenantMap)
	if err != nil {
		return nil, fmt.Errorf("failed to create user tier manager: %w", err)
	}

	return &UserTierChangeProcessor{
		daoFactory:  daoFactory,
		tierManager: tierManager,
		auditLogger: auditLogger,
		tenantMap:   tenantMap,
	}, nil
}

type UserTierManager struct {
	daoFactory        *DAOFactory
	featureFlagHandle featureflags.FeatureFlagHandle
	orbConfig         *OrbConfig
	orbClient         orb.OrbClient
	auditLogger       *audit.AuditLogger
	tenantMap         *TenantMap
}

func NewUserTierManager(
	daoFactory *DAOFactory,
	featureFlagHandle featureflags.FeatureFlagHandle,
	orbConfig *OrbConfig,
	auditLogger *audit.AuditLogger,
	tenantMap *TenantMap,
) (*UserTierManager, error) {
	if daoFactory == nil {
		return nil, fmt.Errorf("daoFactory cannot be nil")
	}
	if featureFlagHandle == nil {
		return nil, fmt.Errorf("featureFlagHandle cannot be nil")
	}

	// Initialize Orb client if enabled
	var orbClient orb.OrbClient
	if orbConfig != nil && orbConfig.Enabled {
		// Read the Orb API key from the specified file path
		key, err := os.ReadFile(orbConfig.ApiKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Orb secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			orbClient = orb.NewOrbClient(cleanKey, featureFlagHandle)
			log.Info().Msg("Orb client initialized successfully")
		}
	}

	return &UserTierManager{
		daoFactory:        daoFactory,
		featureFlagHandle: featureFlagHandle,
		orbConfig:         orbConfig,
		orbClient:         orbClient,
		auditLogger:       auditLogger,
		tenantMap:         tenantMap,
	}, nil
}

func checkDeadLetterFlag(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := UserTierChangeProcessDeadLetterQueue.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading dead letter queue feature flag")
		return false
	}
	return val
}

func checkUserTierChangeEnabled(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := UserTierChangeEnabled.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading user tier change feature flag")
		return false
	}
	return val
}

func (w *UserTierChangeProcessor) Process(ctx context.Context, msg *auth_internal.UserTierChangeMessage) error {
	if !checkUserTierChangeEnabled(w.tierManager.featureFlagHandle) {
		log.Info().Msg("User tier change is disabled, skipping message")
		return fmt.Errorf("user tier change is disabled, nack so we can recover these")
	}

	log.Info().Str("user_id", msg.User.Id).Str("tier_change_id", msg.TierChangeId).Msg("Processing tier change message")
	userDAO := w.daoFactory.GetUserDAO()

	// Verify the tier change ID matches what's in the user object. It is likely that this will fail
	// on the first try since we publish to the pub/sub queue before writing this record to BigTable.
	// Retry a few times to avoid a long backoff while we wait for pub/sub to retry.
	var user *auth_entities.User
	var err error
	maxAttempts := 5
	sleepDuration := time.Millisecond * 200
	for attempt := 0; attempt < maxAttempts; attempt++ {
		user, err = userDAO.Get(ctx, msg.User.Id)
		if err == nil && user != nil && user.TierChange != nil {
			break
		}
		log.Warn().Err(err).Msgf(
			"Failed to get user for tier change verification, attempt %d/%d", attempt+1, maxAttempts)
		time.Sleep(sleepDuration)
	}
	if err != nil {
		log.Error().Err(err).Str("user_id", msg.User.Id).Msg("Failed to fetch user for tier change verification")
		return fmt.Errorf("failed to fetch user for tier change verification: user_id=%s: %w", msg.User.Id, err)
	}

	if user == nil {
		log.Error().Str("user_id", msg.User.Id).Msg("User not found for tier change")
		return fmt.Errorf("user not found for tier change: user_id=%s", msg.User.Id)
	}

	messageAge := time.Since(msg.PublishTime.AsTime())

	// Check if the tier change ID matches
	if user.TierChange == nil {
		// Check if the message has been in the queue for more than 5 minutes
		if messageAge > 5*time.Minute {
			log.Info().
				Str("user_id", msg.User.Id).
				Str("message_tier_change_id", msg.TierChangeId).
				Dur("message_age", messageAge).
				Msg("Nil tier change on user, acknowledging message after 5 minute timeout")
			return nil // Return nil to ack the message
		}

		log.Warn().Str("user_id", msg.User.Id).Str("tier_change_id", msg.TierChangeId).Dur("message_age", messageAge).Msg("Nil tier change on user, retrying")
		return fmt.Errorf("Nil tier change on user, retrying: user_id=%s, message_tier_change_id=%s, message_age=%v",
			msg.User.Id, msg.TierChangeId, messageAge)
	}

	if user.TierChange.Id != msg.TierChangeId {
		log.Info().
			Str("user_id", msg.User.Id).
			Str("message_tier_change_id", msg.TierChangeId).
			Str("user_tier_change_id", user.TierChange.Id).
			Dur("message_age", messageAge).
			Msg("Tier change ID mismatch, acknowledging message after 5 minute timeout")
		return nil // Return nil to ack the message
	}

	log.Info().
		Str("user_id", msg.User.Id).
		Str("tier_change_id", msg.TierChangeId).
		Str("old_tenant", msg.CurrentTenant.Name).
		Str("new_tenant", msg.NewTenant.Name).
		Str("new_tier", msg.NewTier.String()).
		Msg("Executing tier change")

	// Execute the tier change
	err = w.tierManager.executeTierChange(ctx, msg)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_id", msg.User.Id).
			Str("tier_change_id", msg.TierChangeId).
			Str("old_tenant", msg.CurrentTenant.Name).
			Str("new_tenant", msg.NewTenant.Name).
			Str("new_tier", msg.NewTier.String()).
			Msg("Failed to process user tier change")
		return fmt.Errorf("failed to process user tier change: user_id=%s, tier_change_id=%s, old_tenant=%s, new_tenant=%s, tier=%s: %w",
			msg.User.Id, msg.TierChangeId, msg.CurrentTenant.Name, msg.NewTenant.Name, msg.NewTier.String(), err)
	}

	// Clear the tier change info from the user after successful processing
	updateUser := func(u *auth_entities.User) bool {
		u.TierChange = nil
		return true
	}

	_, err = userDAO.TryUpdate(ctx, user.Id, updateUser, DefaultRetry)
	if err != nil {
		log.Error().Err(err).Str("user_id", user.Id).Str("tier_change_id", msg.TierChangeId).Msg("Failed to clear tier change info after successful processing")
		// Return error to nack the message so we can retry clearing the tier change ID
		return fmt.Errorf("failed to clear tier change info after successful processing: user_id=%s, tier_change_id=%s: %w",
			user.Id, msg.TierChangeId, err)
	}

	log.Info().
		Str("user_id", msg.User.Id).
		Str("tier_change_id", msg.TierChangeId).
		Str("old_tenant", msg.CurrentTenant.Name).
		Str("new_tenant", msg.NewTenant.Name).
		Str("new_tier", msg.NewTier.String()).
		Str("new_plan_id", msg.NewPlanId).
		Msg("Tier change completed successfully")

	w.auditLogger.WriteAuditLog(
		msg.User.Id,
		"",
		msg.CurrentTenant.Name,
		fmt.Sprintf("User %s tier change completed successfully", msg.User.Id),
	)

	return nil
}

// Core tier change logic
// Initial Notion Doc: https://www.notion.so/User-Plan-Change-Proposal-1adbba10175a80b7a651c757d294f251
func (tm *UserTierManager) executeTierChange(
	ctx context.Context,
	msg *auth_internal.UserTierChangeMessage,
) error {
	// Step 1: Handle subscription changes first
	if err := tm.processOrbSubscription(ctx, msg); err != nil {
		log.Error().Err(err).Str("user_id", msg.User.Id).Msg("subscription update failed")
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	// We can skip the rest of the steps if the tenant is not changing
	if msg.CurrentTenant.Id == msg.NewTenant.Id {
		return nil
	}

	// Step 2: Move the user to the new tenant - this handles updating the user's tenants list, mappings, and invalidating tokens + cookies
	if err := tm.tenantMap.MoveUserToTenant(ctx, msg.User.Id, msg.NewTenant.Id); err != nil {
		log.Error().Err(err).Str("user_id", msg.User.Id).Msg("Failed to move user to new tenant")
		return fmt.Errorf("failed to move user to new tenant: %w", err)
	}

	test_utils.CheckPanic(UserTierChangePanicPoints.MoveUserToTenantError)

	return nil
}

// Allows the user to switch from community -> developer and developer -> community plans. This is triggered from CustomerUI.
// If the user switches community -> developer, we do not give them a free trial.
// If a user requests to switch to a plan they are already on, do not change anything.
func (tm *UserTierManager) processOrbSubscription(ctx context.Context, msg *auth_internal.UserTierChangeMessage) error {
	log.Info().Msg("Processing Orb subscription change")

	// Check if orbClient is nil
	if tm.orbClient == nil {
		return status.Error(codes.Internal, "Orb client is nil")
	}

	// TODO: remove this once we have removed the plan enum.
	// Need this to fix the race of people who were published before PlanId was set in the RPC.
	if msg.NewPlanId == "" {
		switch msg.NewTier {
		case auth_entities.UserTier_COMMUNITY:
			msg.NewPlanId = "orb_community_plan"
		case auth_entities.UserTier_PROFESSIONAL:
			msg.NewPlanId = "orb_developer_plan"
		default:
			return status.Error(codes.InvalidArgument, "Plan ID is required")
		}
	}

	// NewPlanId is required
	if msg.NewPlanId == "" {
		return status.Error(codes.InvalidArgument, "New plan ID is required")
	}

	tierTo := msg.NewTier
	planIDStr := msg.NewPlanId
	orbSubscriptionId := msg.User.OrbSubscriptionId
	orbCustomerId := msg.User.OrbCustomerId

	// Check if the user has a valid subscription
	var currentSubscription *orb.OrbSubscriptionInfo
	var err error

	if orbSubscriptionId != "" {
		// Try to get the current subscription
		currentSubscription, err = tm.orbClient.GetUserSubscription(ctx, orbSubscriptionId, nil)
		if err != nil {
			log.Warn().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to get user's current subscription, may need to create a new one")
			// Continue execution - we'll create a new subscription below if needed
		}
	}

	// Determine which plan to use based on the target tier
	targetPlan := tm.orbConfig.findPlan(planIDStr)
	if targetPlan == nil {
		log.Error().Str("user_id", msg.User.Id).Str("subscription_id", orbSubscriptionId).Msgf(
			"Failed to find plan for tier %s, plan %s", tierTo.String(), planIDStr)
		return status.Error(codes.Internal, fmt.Sprintf("Failed to find plan"))
	}
	if targetPlan.Features.PlanType == PlanTypePaidTrial {
		log.Error().Str("user_id", msg.User.Id).Str("subscription_id", orbSubscriptionId).Msgf(
			"Cannot change to a trial plan")
		return status.Error(codes.FailedPrecondition, fmt.Sprintf("Cannot change to a trial plan"))
	}

	// If no subscription exists or it's not active, create a new one
	if currentSubscription == nil || currentSubscription.OrbStatus != "active" {
		log.Info().Str("user_id", msg.User.Id).Str("orb_customer_id", orbCustomerId).Msg("No active subscription found, creating a new one")
		planId := targetPlan.ID

		// Create a new subscription
		orbSubscription := orb.OrbSubscription{
			CustomerOrbID:  orbCustomerId,
			ExternalPlanID: planId,
		}

		// Use the tier change ID as the idempotency key.
		idempotencyKey := msg.TierChangeId
		newSubscriptionId, err := tm.orbClient.CreateSubscription(ctx, orbSubscription, &idempotencyKey)
		if err != nil {
			return status.Error(codes.Internal, fmt.Sprintf("Failed to create new Orb subscription: %v", err))
		}

		// Update the subscription ID for further operations
		orbSubscriptionId = newSubscriptionId

		// Update the user with the new subscription ID
		userDAO := tm.daoFactory.GetUserDAO()
		_, err = userDAO.TryUpdate(ctx, msg.User.Id, func(u *auth_entities.User) bool {
			u.OrbSubscriptionId = newSubscriptionId
			return true
		}, DefaultRetry)
		if err != nil {
			return status.Error(codes.Internal, fmt.Sprintf("Failed to update user with new subscription ID: %v", err))
		}

		log.Info().Str("user_id", msg.User.Id).Str("subscription_id", newSubscriptionId).Msg("Created new subscription")
		tm.auditLogger.WriteAuditLog(
			msg.User.Id,
			"",
			msg.CurrentTenant.Name,
			fmt.Sprintf("Created new subscription %s for user tier change", newSubscriptionId),
		)
		return nil
	}

	// If the current subscription type is the same as the tier we're trying to change to, don't change it
	currentPlanID := currentSubscription.ExternalPlanID
	currentPlan := tm.orbConfig.findPlan(currentPlanID)
	if currentPlan == nil {
		return status.Error(codes.Internal, fmt.Sprintf("Failed to find plan for current subscription ID %s", currentPlanID))
	}
	if currentPlan.ID == targetPlan.ID {
		log.Info().Str("user_id", msg.User.Id).Str("subscription_id", orbSubscriptionId).Msg(
			"User already has the correct plan, no change needed")
		return nil
	}

	// If the current subscription is set to cancel at the end of the term, unschedule it
	if !currentSubscription.EndDate.IsZero() {
		unscheduleCancelIdempotencyKey := fmt.Sprintf("%s-unschedule-cancel", msg.TierChangeId)
		log.Info().Str("user_id", msg.User.Id).Str("subscription_id", orbSubscriptionId).Msg("Unscheduling subscription cancellation")
		err := tm.orbClient.UnschedulePendingSubscriptionCancellation(ctx, orbSubscriptionId, &unscheduleCancelIdempotencyKey)
		if err != nil {
			return status.Error(codes.Internal, fmt.Sprintf("Failed to unschedule subscription cancellation: %v", err))
		}
	}

	// Determine plan change type and create plan change request
	var planChange orb.OrbPlanChange
	var isUpgrade bool
	var needsProrate bool

	if targetPlan.Features.PlanType == PlanTypeCommunity {
		log.Info().
			Str("user_id", msg.User.Id).
			Str("current_plan_id", currentPlan.ID).
			Str("target_plan_id", targetPlan.ID).
			Msg("Changing to community plan - immediate change")

		// Put them on the community plan - always immediate since we need to change tier
		planChange = orb.OrbPlanChange{
			CustomerOrbID:         orbCustomerId,
			SubscriptionID:        orbSubscriptionId,
			NewPlanID:             targetPlan.ID,
			PlanChangeType:        orb.PlanChangeImmediate,
			BillingCycleAlignment: orb.BillingCycleAlignmentPlanChangeDate,
		}
		isUpgrade = false
		needsProrate = false
	} else {
		// We are going to one of plans on Professional tier
		log.Info().
			Str("user_id", msg.User.Id).
			Str("current_plan_id", currentPlan.ID).
			Str("target_plan_id", targetPlan.ID).
			Msgf("Changing to %s plan", targetPlan.ID)

		// If the current plan is a trial or community, we change immediately and change the plan dates
		if currentPlan.Features.PlanType == PlanTypePaidTrial || currentPlan.Features.PlanType == PlanTypeCommunity {
			planChange = orb.OrbPlanChange{
				CustomerOrbID:         orbCustomerId,
				SubscriptionID:        orbSubscriptionId,
				NewPlanID:             targetPlan.ID,
				PlanChangeType:        orb.PlanChangeImmediate,
				BillingCycleAlignment: orb.BillingCycleAlignmentPlanChangeDate,
			}
			isUpgrade = true
			needsProrate = false
		} else {
			// If the current plan is not a trial, it depends whether we are upgrading or downgrading.
			// For upgrade (ex. developer -> max) we will pro-rate and the plan change is immediate.

			// Get plan information for both current and target plans to compare
			// Use subscription ID to get the actual plan configuration (including any grandfathered pricing)
			currentPlanInfo, err := tm.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: tm.orbConfig.SeatsItemID, IncludedMessagesID: tm.orbConfig.IncludedMessagesItemID}, &orbSubscriptionId, nil)
			if err != nil {
				log.Error().Err(err).Str("orb_subscription_id", orbSubscriptionId).Msg("Failed to get current plan information")
				return err
			}

			targetPlanInfo, err := tm.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: tm.orbConfig.SeatsItemID, IncludedMessagesID: tm.orbConfig.IncludedMessagesItemID}, nil, &targetPlan.ID)
			if err != nil {
				log.Error().Err(err).Str("target_plan_id", targetPlan.ID).Msg("Failed to get target plan information")
				return err
			}

			// Determine if this is an upgrade or downgrade to decide on timing
			isUpgrade, err = IsPlanUpgrade(ctx, currentPlanInfo, targetPlanInfo)
			if err != nil {
				log.Error().
					Err(err).
					Str("user_id", msg.User.Id).
					Str("orb_subscription_id", orbSubscriptionId).
					Str("target_plan_id", targetPlan.ID).
					Msg("Failed to determine if plan change is an upgrade or downgrade")
				return err
			}
			needsProrate = isUpgrade

			// Choose plan change type based on upgrade/downgrade
			var planChangeType orb.PlanChangeType
			if isUpgrade {
				planChangeType = orb.PlanChangeImmediate // Upgrades happen immediately
			} else {
				planChangeType = orb.PlanChangeEndOfTerm // Other downgrades happen at end of billing period
			}

			planChange = orb.OrbPlanChange{
				CustomerOrbID:         orbCustomerId,
				SubscriptionID:        orbSubscriptionId,
				NewPlanID:             targetPlan.ID,
				PlanChangeType:        planChangeType,
				BillingCycleAlignment: orb.BillingCycleAlignmentUnchanged,
			}

			if needsProrate {
				// Override the number of credits per month to be a pro-rated number for the current month
				// Calculate pro-rated credits
				proRatedCredits, err := CalculateProRatedCredits(
					currentPlanInfo.MessagesPerSeat,
					targetPlanInfo.MessagesPerSeat,
					currentSubscription.CurrentBillingPeriodStartDate,
					currentSubscription.CurrentBillingPeriodEndDate,
					msg.PublishTime.AsTime(),
				)
				if err != nil {
					return err
				}
				planChange.PriceOverrides = []orb.OrbPriceOverrides{
					{
						PriceID:  targetPlanInfo.IncludedMessagesPriceID,
						Quantity: proRatedCredits,
					},
				}
				log.Info().Float64("pro_rated_credits", proRatedCredits).Str("subscription_id", orbSubscriptionId).Msg("Successfully calculated pro-rated credits for plan upgrade")
			}
		}
	}

	// Use the tier change ID as the idempotency key
	idempotencyKey := msg.TierChangeId
	err = tm.orbClient.SetCustomerPlanType(ctx, planChange, &idempotencyKey)
	if err != nil {
		return status.Error(codes.Internal, fmt.Sprintf("Failed to update Orb subscription: %v", err))
	}

	// If we gave pro-rated credits for the current month, update future months to the full amount
	if needsProrate {
		futurePlanInfo, err := tm.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: tm.orbConfig.IncludedMessagesItemID}, nil, &targetPlan.ID)
		if err != nil {
			log.Error().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to get plan information for pro-rating credits")
			return err
		}
		creditQuantity := futurePlanInfo.MessagesPerSeat
		priceID := futurePlanInfo.IncludedMessagesPriceID
		nextMonthCreditsIdempotencyKey := fmt.Sprintf("%s-next-month-credits", msg.TierChangeId)
		err = tm.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
			OrbSubscriptionID: orbSubscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  priceID,
				Quantity: creditQuantity,
			},
			UpdateTimeType: orb.PlanChangeEndOfTerm,
		}, &nextMonthCreditsIdempotencyKey)
		if err != nil {
			log.Error().Err(err).Float64("credit_quantity", creditQuantity).Str("orb_subscription_id", orbSubscriptionId).Msg("Failed to update future number of credits per month")
			return fmt.Errorf("failed to update future number of credits per month: %w", err)
		} else {
			log.Info().Float64("credit_quantity", creditQuantity).Str("orb_subscription_id", orbSubscriptionId).Msg("Successfully updated future number of credits per month")
		}
	}

	return nil
}
