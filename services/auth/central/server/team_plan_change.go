package main

import (
	"context"
	"fmt"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var TeamManagementEnabled = featureflags.NewBoolFlag("auth_central_team_management_enabled", false)

const (
	PanicPointTeamPlanChangeGetSubscriptionMappingFailed = "TeamPlanChange.GetSubscriptionMappingFailed"
	PanicPointTeamPlanChangeUpdateOrbSubscriptionFailed  = "TeamPlanChange.UpdateOrbSubscriptionFailed"
	PanicPointTeamPlanChangeOrbClientSetPlanFailed       = "TeamPlanChange.OrbClientSetPlanFailed"
)

// TeamPlanChangeProcessor is responsible for processing team plan change messages from the async-ops pubsub topic
type TeamPlanChangeProcessor struct {
	daoFactory        *DAOFactory
	orbClient         orb.OrbClient
	orbConfig         *OrbConfig
	auditLogger       *audit.AuditLogger
	featureFlagHandle featureflags.FeatureFlagHandle
	tenantMap         *TenantMap
}

func NewTeamPlanChangeProcessor(
	daoFactory *DAOFactory,
	orbClient orb.OrbClient,
	orbConfig *OrbConfig,
	featureFlagHandle featureflags.FeatureFlagHandle,
	auditLogger *audit.AuditLogger,
	tenantMap *TenantMap,
) (*TeamPlanChangeProcessor, error) {
	if daoFactory == nil {
		return nil, fmt.Errorf("daoFactory cannot be nil")
	}
	if orbClient == nil {
		return nil, fmt.Errorf("orbClient cannot be nil")
	}
	if orbConfig == nil {
		return nil, fmt.Errorf("orbConfig cannot be nil")
	}
	if featureFlagHandle == nil {
		return nil, fmt.Errorf("featureFlagHandle cannot be nil")
	}
	if auditLogger == nil {
		return nil, fmt.Errorf("auditLogger cannot be nil")
	}
	if tenantMap == nil {
		return nil, fmt.Errorf("tenantMap cannot be nil")
	}

	return &TeamPlanChangeProcessor{
		daoFactory:        daoFactory,
		orbClient:         orbClient,
		orbConfig:         orbConfig,
		featureFlagHandle: featureFlagHandle,
		auditLogger:       auditLogger,
		tenantMap:         tenantMap,
	}, nil
}

func checkTeamManagementEnabled(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := TeamManagementEnabled.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading team plan change feature flag")
		return false
	}
	return val
}

func (p *TeamPlanChangeProcessor) Process(ctx context.Context, msg *auth_internal.TeamPlanChangeMessage) error {
	if !checkTeamManagementEnabled(p.featureFlagHandle) {
		log.Info().Msg("Team management is disabled, skipping message")
		return fmt.Errorf("team management is disabled, nack so we can recover these")
	}

	log.Info().
		Str("team_tenant_id", msg.TeamTenantId).
		Str("plan_change_id", msg.PlanChangeId).
		Msg("Processing team plan change message")

	// Step 1: Verify the plan change ID matches what's in the tenant subscription mapping
	// It is likely that this will fail on the first try since we publish to the pub/sub
	// queue before writing this record to BigTable.
	// Retry a few times to avoid a long backoff while we wait for pub/sub to retry.
	tenantSubscriptionMappingDAO := p.daoFactory.GetTenantSubscriptionMappingDAO()
	var subscriptionMapping *auth_entities.TenantSubscriptionMapping
	var err error
	maxAttempts := 5
	sleepDuration := time.Millisecond * 200

	for attempt := 0; attempt < maxAttempts; attempt++ {
		subscriptionMapping, err = tenantSubscriptionMappingDAO.Get(ctx, msg.TeamTenantId)
		if err == nil && subscriptionMapping != nil && subscriptionMapping.PlanChange != nil {
			break
		}
		log.Warn().Err(err).Msgf(
			"Failed to get tenant subscription mapping for plan change verification, attempt %d/%d", attempt+1, maxAttempts)
		time.Sleep(sleepDuration)
	}
	test_utils.CheckPanic(PanicPointTeamPlanChangeGetSubscriptionMappingFailed)
	if err != nil {
		log.Error().
			Err(err).
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Failed to get tenant subscription mapping")
		return fmt.Errorf("failed to get tenant subscription mapping: team_tenant_id=%s: %w", msg.TeamTenantId, err)
	}

	if subscriptionMapping == nil {
		log.Error().
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Tenant subscription mapping not found")
		return fmt.Errorf("tenant subscription mapping not found: team_tenant_id=%s", msg.TeamTenantId)
	}

	messageAge := time.Since(msg.PublishTime.AsTime())

	// Check if the plan change ID matches
	if subscriptionMapping.PlanChange == nil {
		// Check if the message has been in the queue for more than 5 minutes
		if messageAge > 5*time.Minute {
			log.Info().
				Str("team_tenant_id", msg.TeamTenantId).
				Str("message_plan_change_id", msg.PlanChangeId).
				Dur("message_age", messageAge).
				Msg("Nil plan change on tenant, acknowledging message after 5 minute timeout")
			return nil // Return nil to ack the message
		}

		log.Warn().
			Str("team_tenant_id", msg.TeamTenantId).
			Str("plan_change_id", msg.PlanChangeId).
			Dur("message_age", messageAge).
			Msg("Nil plan change on tenant, retrying")
		return fmt.Errorf("nil plan change on tenant, retrying: team_tenant_id=%s, message_plan_change_id=%s, message_age=%v",
			msg.TeamTenantId, msg.PlanChangeId, messageAge)
	}

	if subscriptionMapping.PlanChange.Id != msg.PlanChangeId {
		log.Info().
			Str("team_tenant_id", msg.TeamTenantId).
			Str("message_plan_change_id", msg.PlanChangeId).
			Str("tenant_plan_change_id", subscriptionMapping.PlanChange.Id).
			Dur("message_age", messageAge).
			Msg("Plan change ID mismatch, acknowledging message")
		return nil // Return nil to ack the message
	}

	// Step 2: Execute the plan change
	if err := p.executePlanChange(ctx, msg, subscriptionMapping); err != nil {
		log.Error().
			Err(err).
			Str("team_tenant_id", msg.TeamTenantId).
			Str("plan_change_id", msg.PlanChangeId).
			Msg("Failed to process team plan change")
		return fmt.Errorf("failed to process team plan change: team_tenant_id=%s, plan_change_id=%s, %w",
			msg.TeamTenantId, msg.PlanChangeId, err)
	}

	// Step 3: Clear the plan change info from the tenant subscription mapping
	// Fetch the latest mapping since we may have updated it in the previous step
	subscriptionMapping, err = tenantSubscriptionMappingDAO.Get(ctx, msg.TeamTenantId)
	if err != nil {
		log.Error().
			Err(err).
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Failed to fetch tenant subscription mapping after successful processing")
		return fmt.Errorf("failed to fetch tenant subscription mapping after successful processing: team_tenant_id=%s: %w",
			msg.TeamTenantId, err)
	}
	if subscriptionMapping == nil {
		log.Error().
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Tenant subscription mapping not found after successful processing")
		return fmt.Errorf("tenant subscription mapping not found after successful processing: team_tenant_id=%s", msg.TeamTenantId)
	}
	updatedMapping := *subscriptionMapping
	updatedMapping.PlanChange = nil // Clear the plan change info
	_, err = tenantSubscriptionMappingDAO.Update(ctx, &updatedMapping)
	if err != nil {
		log.Error().
			Err(err).
			Str("team_tenant_id", msg.TeamTenantId).
			Str("plan_change_id", msg.PlanChangeId).
			Msg("Failed to clear plan change info after successful processing")
		// Return error to nack the message so we can retry clearing the plan change ID
		return fmt.Errorf("failed to clear plan change info after successful processing: team_tenant_id=%s, plan_change_id=%s: %w",
			msg.TeamTenantId, msg.PlanChangeId, err)
	}

	log.Info().
		Str("team_tenant_id", msg.TeamTenantId).
		Str("plan_change_id", msg.PlanChangeId).
		Msg("Team plan change completed successfully")

	p.auditLogger.WriteAuditLog(
		msg.InitiatedByUserId,
		"",
		msg.TeamTenantId,
		fmt.Sprintf("Team %s plan change completed successfully", msg.TeamTenantId),
	)

	return nil
}

func (p *TeamPlanChangeProcessor) executePlanChange(
	ctx context.Context,
	msg *auth_internal.TeamPlanChangeMessage,
	subscriptionMapping *auth_entities.TenantSubscriptionMapping,
) error {
	// Check if the target plan is the community plan
	communityPlan := p.orbConfig.getCommunityPlan()
	if msg.TargetOrbPlanId == communityPlan.ID {
		log.Error().
			Str("team_tenant_id", msg.TeamTenantId).
			Str("target_plan_id", msg.TargetOrbPlanId).
			Msg("Teams cannot be put on the community plan")
		return fmt.Errorf("teams cannot be put on the community plan")
	}

	// Update the Orb subscription
	test_utils.CheckPanic(PanicPointTeamPlanChangeUpdateOrbSubscriptionFailed)
	if err := p.updateOrbSubscription(ctx, msg, subscriptionMapping); err != nil {
		log.Error().
			Err(err).
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Failed to update Orb subscription")
		return fmt.Errorf("failed to update Orb subscription: %w", err)
	}

	return nil
}

func (p *TeamPlanChangeProcessor) updateOrbSubscription(
	ctx context.Context,
	msg *auth_internal.TeamPlanChangeMessage,
	subscriptionMapping *auth_entities.TenantSubscriptionMapping,
) error {
	// Get the subscription to update in Orb
	orbSubscriptionId := subscriptionMapping.OrbSubscriptionId
	orbCustomerId := subscriptionMapping.OrbCustomerId

	// Creating a team requires the user to have an existing subscription and be transferred to the team; otherwise, the process fails.
	// Therefore, we assume a subscription exists regardless of its status. If no subscription is found throw an error, as this means team creation has failed.
	var currentSubscription *orb.OrbSubscriptionInfo
	var err error
	currentSubscription, err = p.orbClient.GetUserSubscription(ctx, orbSubscriptionId, &orb.ItemIds{
		SeatsID: p.orbConfig.SeatsItemID,
	})
	if err != nil {
		log.Error().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to get user's current subscription")
		return fmt.Errorf("failed to get user's current subscription: %w", err)
	}
	if currentSubscription == nil {
		log.Error().
			Str("subscription_id", orbSubscriptionId).
			Msg("Subscription not found for team plan change")
		return fmt.Errorf("subscription not found for team plan change: %s", orbSubscriptionId)
	}

	// Get target plan details to find MessagesPerSeat for the new plan
	targetPlanDetails, err := p.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: p.orbConfig.SeatsItemID, IncludedMessagesID: p.orbConfig.IncludedMessagesItemID}, nil, &msg.TargetOrbPlanId)
	if err != nil {
		log.Error().
			Err(err).
			Str("target_orb_plan_id", msg.TargetOrbPlanId).
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Failed to get target plan details from Orb")
		return fmt.Errorf("failed to get target plan details from Orb for plan %s: %w", msg.TargetOrbPlanId, err)
	}
	if targetPlanDetails == nil {
		log.Error().
			Str("target_orb_plan_id", msg.TargetOrbPlanId).
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Target plan details not found in Orb")
		return fmt.Errorf("target plan details not found in Orb for plan %s", msg.TargetOrbPlanId)
	}

	// Get current and future number of seats from the existing subscription
	// Future number of seats is next month's number of seats
	currentNumberOfSeats := currentSubscription.CurrentFixedQuantities.Seats
	futureNumberOfSeats := currentNumberOfSeats
	if currentSubscription.FutureFixedQuantities != nil && currentSubscription.FutureFixedQuantities.Seats > 0 {
		futureNumberOfSeats = currentSubscription.FutureFixedQuantities.Seats
	}
	seatsForPlanChange := currentNumberOfSeats

	// If the subscription is not active, we create a new subscription
	if currentSubscription.OrbStatus != "active" {
		log.Info().Str("tenant_id", msg.TeamTenantId).Str("orb_customer_id", orbCustomerId).Msg("Subscription found but not active, creating a new one")

		// Get the number of seats from the people on the team
		members, err := GetActiveTeamMemberCount(ctx, p.daoFactory, p.tenantMap, msg.TeamTenantId)
		if err != nil {
			return fmt.Errorf("failed to get active team member count: %w", err)
		}
		invitations, err := GetInvitationsForTenant(ctx, p.daoFactory, msg.TeamTenantId, auth_entities.TenantInvitation_PENDING)
		if err != nil {
			return fmt.Errorf("failed to get pending invitations: %w", err)
		}
		numSeats := members + len(invitations)

		// Create a new subscription
		orbSubscription := orb.OrbSubscription{
			CustomerOrbID:  orbCustomerId,
			ExternalPlanID: msg.TargetOrbPlanId,
			PriceOverrides: []orb.OrbPriceOverrides{
				{
					PriceID:  targetPlanDetails.SeatsPriceID,
					Quantity: float64(numSeats),
				},
				{
					PriceID:  targetPlanDetails.IncludedMessagesPriceID,
					Quantity: float64(numSeats) * targetPlanDetails.MessagesPerSeat,
				},
			},
		}

		// Use the plan change ID as the idempotency key
		idempotencyKey := msg.PlanChangeId
		// A user can still be under a tier change when creating a team.
		newSubscriptionId, err := p.orbClient.CreateSubscription(ctx, orbSubscription, &idempotencyKey)
		if err != nil {
			return status.Error(codes.Internal, fmt.Sprintf("Failed to create new Orb subscription: %v", err))
		}

		// Update the subscription ID for further operations
		orbSubscriptionId = newSubscriptionId

		// Update the tenantSubscriptionMapping with the new subscription ID
		tenantSubscriptionMappingDAO := p.daoFactory.GetTenantSubscriptionMappingDAO()
		_, err = tenantSubscriptionMappingDAO.TryUpdate(ctx, msg.TeamTenantId, func(mapping *auth_entities.TenantSubscriptionMapping) bool {
			mapping.OrbSubscriptionId = newSubscriptionId
			return true
		}, DefaultRetry)
		if err != nil {
			return status.Error(codes.Internal, fmt.Sprintf("Failed to update tenant subscription mapping with new subscription ID: %v", err))
		}

		log.Info().Str("tenant_id", msg.TeamTenantId).Str("subscription_id", newSubscriptionId).Msg("Created new subscription for team plan change")
		p.auditLogger.WriteAuditLog(
			msg.InitiatedByUserId,
			"",
			msg.TeamTenantId,
			fmt.Sprintf("Created new subscription %s for team plan change", newSubscriptionId),
		)
		return nil
	}

	// Get current plan details to compare with target plan
	// Use subscription ID to get the actual plan configuration (including any grandfathered pricing)
	currentPlanDetails, err := p.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: p.orbConfig.SeatsItemID, IncludedMessagesID: p.orbConfig.IncludedMessagesItemID}, &orbSubscriptionId, nil)
	if err != nil {
		log.Error().
			Err(err).
			Str("current_orb_subscription_id", orbSubscriptionId).
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Failed to get current plan details from Orb")
		return fmt.Errorf("failed to get current plan details from Orb for subscription %s: %w", orbSubscriptionId, err)
	}
	if currentPlanDetails == nil {
		log.Error().
			Str("current_orb_subscription_id", orbSubscriptionId).
			Str("team_tenant_id", msg.TeamTenantId).
			Msg("Current plan details not found in Orb")
		return fmt.Errorf("current plan details not found in Orb for subscription %s", orbSubscriptionId)
	}

	// Determine whether this is an upgrade or downgrade. We’ll always prorate for upgrades within teams on the current plans.
	isUpgrade, err := IsPlanUpgrade(ctx, currentPlanDetails, targetPlanDetails)
	if err != nil {
		log.Error().
			Err(err).
			Str("team_tenant_id", msg.TeamTenantId).
			Str("current_orb_subscription_id", orbSubscriptionId).
			Str("target_plan_id", msg.TargetOrbPlanId).
			Msg("Failed to determine if plan change is an upgrade or downgrade")
		return fmt.Errorf("failed to determine if plan change is an upgrade or downgrade: %w", err)
	}

	// Choose plan change type based on upgrade/downgrade
	var planChangeType orb.PlanChangeType
	if isUpgrade {
		planChangeType = orb.PlanChangeImmediate // Upgrades happen immediately
	} else {
		planChangeType = orb.PlanChangeEndOfTerm // Other downgrades happen at end of billing period
		seatsForPlanChange = futureNumberOfSeats // Since it happens at the end of the billing period, use the future number of seats
	}

	// Calculate the desired total credits per month based on the new plan's messages per seat and seats to use in plan change
	desiredCreditsPerMonth := float64(targetPlanDetails.MessagesPerSeat) * float64(seatsForPlanChange)

	log.Info().
		Str("team_tenant_id", msg.TeamTenantId).
		Str("subscription_id", orbSubscriptionId).
		Str("target_orb_plan_id", msg.TargetOrbPlanId).
		Int("current_number_of_seats", int(currentNumberOfSeats)).
		Float64("target_plan_messages_per_seat", targetPlanDetails.MessagesPerSeat).
		Float64("calculated_desired_credits_per_month", desiredCreditsPerMonth).
		Msg("Calculated price override values for plan change")

	// Determine billing cycle alignment and pro-rating behavior based on current plan type
	// If leaving trial plan, use plan change date and don't pro-rate; otherwise use unchanged and pro-rate for upgrades
	var billingCycleAlignment orb.BillingCycleAlignment
	var needsProrate bool
	trialPlan := p.orbConfig.getTrialPlan()
	if currentPlanDetails.ExternalPlanID == trialPlan.ID {
		billingCycleAlignment = orb.BillingCycleAlignmentPlanChangeDate
		needsProrate = false // Don't pro-rate when leaving trial plans
		log.Info().
			Str("current_plan_id", currentPlanDetails.ExternalPlanID).
			Str("target_plan_id", msg.TargetOrbPlanId).
			Msg("Leaving trial plan - using plan change date for billing cycle alignment and no pro-rating")
	} else {
		billingCycleAlignment = orb.BillingCycleAlignmentUnchanged
		needsProrate = isUpgrade // Pro-rate for paid-to-paid upgrades
	}

	// Create the plan change request
	planChange := orb.OrbPlanChange{
		CustomerOrbID:  orbCustomerId,
		SubscriptionID: orbSubscriptionId,
		NewPlanID:      msg.TargetOrbPlanId,
		PriceOverrides: []orb.OrbPriceOverrides{
			{
				PriceID:  targetPlanDetails.SeatsPriceID, // Price ID for seats
				Quantity: float64(seatsForPlanChange),    // Current/future depends on if you are going up/down
			},
			{
				PriceID:  targetPlanDetails.IncludedMessagesPriceID, // Price ID for included credits/messages
				Quantity: desiredCreditsPerMonth,                    // New calculated credits per month
			},
		},
		PlanChangeType:        planChangeType,
		BillingCycleAlignment: billingCycleAlignment,
	}

	if needsProrate {
		// For upgrades: Override the number of credits per month to be a pro-rated number for the current month
		// Calculate pro-rated credits
		proRatedCredits, err := CalculateProRatedCredits(
			currentPlanDetails.MessagesPerSeat,
			targetPlanDetails.MessagesPerSeat,
			currentSubscription.CurrentBillingPeriodStartDate,
			currentSubscription.CurrentBillingPeriodEndDate,
			msg.PublishTime.AsTime(),
		)
		if err != nil {
			return err
		}
		planChange.PriceOverrides = []orb.OrbPriceOverrides{
			{
				PriceID:  targetPlanDetails.SeatsPriceID, // Price ID for seats
				Quantity: float64(currentNumberOfSeats),  // Current number of seats if you are pro-rating, happens immediately
			},
			{
				PriceID:  targetPlanDetails.IncludedMessagesPriceID,
				Quantity: proRatedCredits * float64(currentNumberOfSeats),
			},
		}
		log.Info().Float64("pro_rated_credits", proRatedCredits).Str("subscription_id", orbSubscriptionId).Msg("Successfully calculated pro-rated credits for plan upgrade")
	}

	// Use the plan change ID as the idempotency key
	idempotencyKey := msg.PlanChangeId
	test_utils.CheckPanic(PanicPointTeamPlanChangeOrbClientSetPlanFailed)
	err = p.orbClient.SetCustomerPlanType(ctx, planChange, &idempotencyKey)
	if err != nil {
		return status.Error(codes.Internal, fmt.Sprintf("Failed to update Orb subscription: %v", err))
	}

	// If we gave pro-rated credits for the current month, update future months to the full amount
	if needsProrate {
		// Reuse the targetPlanDetails we already have instead of making another API call
		// Since this is for next month, use the future number of seats
		creditQuantity := targetPlanDetails.MessagesPerSeat * float64(futureNumberOfSeats)
		priceID := targetPlanDetails.IncludedMessagesPriceID
		nextMonthCreditsIdempotencyKey := fmt.Sprintf("%s-next-month-credits", msg.PlanChangeId)
		err = p.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
			OrbSubscriptionID: orbSubscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  priceID,
				Quantity: creditQuantity,
			},
			UpdateTimeType: orb.PlanChangeEndOfTerm,
		}, &nextMonthCreditsIdempotencyKey)
		if err != nil {
			log.Error().Err(err).Float64("credit_quantity", creditQuantity).Str("orb_subscription_id", orbSubscriptionId).Msg("Failed to update future number of credits per month")
			return fmt.Errorf("failed to update future number of credits per month: %w", err)
		} else {
			log.Info().Float64("credit_quantity", creditQuantity).Str("orb_subscription_id", orbSubscriptionId).Msg("Successfully updated future number of credits per month")
		}

		// Update the number of seats at the end of the month to the future number of seats, if the future number of seats is different than current
		if futureNumberOfSeats != currentNumberOfSeats {
			seatsIdempotencyKey := fmt.Sprintf("%s-update-future-seats", msg.PlanChangeId)
			err = p.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
				OrbSubscriptionID: orbSubscriptionId,
				PriceOverride: orb.OrbPriceOverrides{
					PriceID:  targetPlanDetails.SeatsPriceID,
					Quantity: float64(futureNumberOfSeats),
				},
				UpdateTimeType: orb.PlanChangeEndOfTerm,
			}, &seatsIdempotencyKey)
			if err != nil {
				log.Error().Err(err).Int("future_number_of_seats", futureNumberOfSeats).Str("orb_subscription_id", orbSubscriptionId).Msg("Failed to update future number of seats")
				return fmt.Errorf("failed to update future number of seats: %w", err)
			} else {
				log.Info().Int("future_number_of_seats", futureNumberOfSeats).Str("orb_subscription_id", orbSubscriptionId).Msg("Successfully updated future number of seats")
			}
		}
	}

	log.Info().
		Str("team_tenant_id", msg.TeamTenantId).
		Str("subscription_id", orbSubscriptionId).
		Str("new_plan_id", msg.TargetOrbPlanId).
		Msg("Successfully updated Orb subscription")

	return nil
}
