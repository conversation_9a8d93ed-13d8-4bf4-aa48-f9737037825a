package main

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/integrations/orb"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/rs/zerolog/log"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	noChangeSubscriptionId = "no-change-id"
	subscriptionId         = "test-subscription-id"
	subscriptionChangeId   = "test-change-id"
	trialPlanId            = "orb_trial_plan"
	seatPriceId            = "seat-price-id"
	creditPriceId          = "credit-price-id"
	messagesPerSeat        = 100
)

func newTestUpdateSubscriptionProcessor(t *testing.T, useDefaultOrbClient bool) (*UpdateSubscriptionProcessor, func()) {
	bigtableFixture := NewBigtableFixture(t)
	daoFactoryFixture := NewDAOFactoryFixture(bigtableFixture)

	subscriptionDAO := daoFactoryFixture.DAOFactory.GetSubscriptionDAO()
	noChangeIdSubscription := &auth_entities.Subscription{
		SubscriptionId:       noChangeSubscriptionId,
		Seats:                10,
		SubscriptionChangeId: nil,
		Owner:                &auth_entities.Subscription_TenantId{TenantId: "test-tenant-id"},
	}
	_, err := subscriptionDAO.Create(context.Background(), noChangeIdSubscription)
	require.NoError(t, err)

	changeId := subscriptionChangeId
	sub := &auth_entities.Subscription{
		SubscriptionId:       subscriptionId,
		Seats:                10,
		SubscriptionChangeId: &changeId,
		Owner:                &auth_entities.Subscription_TenantId{TenantId: "test-tenant-id"},
	}
	_, err = subscriptionDAO.Create(context.Background(), sub)
	require.NoError(t, err)

	mockOrbClient := orb.NewMockOrbClient()
	if useDefaultOrbClient {
		mockOrbClient.On("GetUserSubscription", mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID:             subscriptionId,
			ExternalPlanID:                "orb-developer-plan",
			CurrentBillingPeriodStartDate: time.Now().Add(-15 * 24 * time.Hour), // 15 days before now
			CurrentBillingPeriodEndDate:   time.Now().Add(15 * 24 * time.Hour),  // 15 days from now
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:              10,
				SeatsID:            seatPriceId,
				IncludedMessages:   10 * messagesPerSeat,
				IncludedMessagesID: creditPriceId,
			},
		}, nil)
	}
	mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

	mockOrbClient.On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbPlanInfo{
		MessagesPerSeat:         messagesPerSeat,
		SeatsPriceID:            seatPriceId,
		IncludedMessagesPriceID: creditPriceId,
	}, nil)

	mockOrbClient.On("UnscheduleFixedQuantity", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

	// Mock GetScheduledPlanChanges to return nil (no scheduled changes) by default
	mockOrbClient.On("GetScheduledPlanChanges", mock.Anything, mock.Anything, mock.Anything).Return((*string)(nil), nil)

	// Mock SetCustomerPlanType for the plan change
	mockOrbClient.On("SetCustomerPlanType", mock.Anything, mock.Anything, mock.Anything).Return(nil)

	// Create a new update subscription processor
	updateProcessor := &UpdateSubscriptionProcessor{
		daoFactory: daoFactoryFixture.DAOFactory,
		orbClient:  mockOrbClient,
		orbConfig: &OrbConfig{
			Enabled:                true,
			IncludedMessagesItemID: "test-messages-item-id",
			SeatsItemID:            "test-seats-item-id",
			Plans: []PlanConfig{
				{
					ID: "orb_trial_plan",
					Features: PlanFeatures{
						TrainingAllowed:     false,
						TeamsAllowed:        true,
						MaxSeats:            5,
						AddCreditsAvailable: false,
						PlanType:            PlanTypePaidTrial,
					},
				},
				{
					ID: "orb_community_plan",
					Features: PlanFeatures{
						TrainingAllowed:     true,
						TeamsAllowed:        false,
						MaxSeats:            1,
						AddCreditsAvailable: true,
						PlanType:            PlanTypeCommunity,
					},
				},
				{
					ID: "orb_developer_plan",
					Features: PlanFeatures{
						TrainingAllowed:     false,
						TeamsAllowed:        true,
						MaxSeats:            100,
						AddCreditsAvailable: true,
						PlanType:            PlanTypePaid,
					},
				},
			},
		},
		auditLogger:             audit.NewDefaultAuditLogger(),
		requestInsightPublisher: ripublisher.NewRequestInsightPublisherMock(),
	}
	cleanup := func() {
		bigtableFixture.Cleanup()
		daoFactoryFixture.Cleanup()
	}
	return updateProcessor, cleanup
}

func TestSeatsProcessorValidation(t *testing.T) {
	t.Run("No subscription found", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, true)
		defer cleanup()

		// Try to update a subscription with a nonexistent subscription ID
		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             "any-id",
			SubscriptionId: "invalid-id",
			NumSeats:       10,
		})
		require.Error(t, err)
	})

	t.Run("No subscription change id and message has been >5 minutes", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, true)
		defer cleanup()

		// Try to update a subscription with a nonexistent subscription ID
		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             "any-id",
			SubscriptionId: noChangeSubscriptionId,
			NumSeats:       10,
			PublishTime:    timestamppb.New(time.Now().Add(-10 * time.Minute)),
		})
		require.NoError(t, err) // no error, we ack the message as it has been >5 minutes
	})

	t.Run("No subscription change id and message has not been >5 minutes", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, true)
		defer cleanup()

		// Try to update a subscription with a nonexistent subscription ID
		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             "any-id",
			SubscriptionId: noChangeSubscriptionId,
			NumSeats:       10,
			PublishTime:    timestamppb.Now(),
		})
		require.Error(t, err) // error as we want to try again later
	})

	t.Run("Subscription change id does not match message id", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, true)
		defer cleanup()

		// Try to update a subscription with a nonexistent subscription ID
		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             "mismatch-id",
			SubscriptionId: subscriptionId,
			NumSeats:       10,
		})
		require.NoError(t, err) // No error, drop the message. Subscription change id does not match message id.
	})

	t.Run("No change in number of seats", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Mock GetSubscription from Orb
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, subscriptionId, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			ExternalPlanID:    "plan-id",
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 10,
			},
		}, nil)

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       10,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err) // No error, drop the message. Updating to the same number of seats as we already have.
	})

	t.Run("No change in number of seats with future seats", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Mock GetSubscription from Orb
		// 15 seats now, going down to 10 at end of month, so changing to 10 makes no difference.
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, subscriptionId, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			ExternalPlanID:    "plan-id",
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:   15,
				SeatsID: seatPriceId,
			},
			FutureFixedQuantities: &orb.FixedQuantities{
				Seats:   10,
				SeatsID: seatPriceId,
			},
		}, nil)

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       10,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err) // No error, drop the message. Updating to the same number of seats as we already have.
	})
}

func TestUpdateTrialSeats(t *testing.T) {
	t.Run("Increase trial seats", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Mock GetSubscription from Orb
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, subscriptionId, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			ExternalPlanID:    trialPlanId,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:              10,
				SeatsID:            seatPriceId,
				IncludedMessages:   6000,
				IncludedMessagesID: creditPriceId,
			},
		}, nil)
		updateProcessor.orbClient.(*orb.MockOrbClient).On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       15,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 1)
		expectedIdempotencyKey := fmt.Sprintf("%s-update-trial-seats", subscriptionChangeId)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  seatPriceId,
				Quantity: 15,
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == expectedIdempotencyKey // assert we use idempotency key
		}))
	})

	t.Run("Decrease trial seats", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Mock GetSubscription from Orb
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, subscriptionId, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			ExternalPlanID:    trialPlanId,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:              10,
				SeatsID:            seatPriceId,
				IncludedMessages:   6000,
				IncludedMessagesID: creditPriceId,
			},
		}, nil)
		updateProcessor.orbClient.(*orb.MockOrbClient).On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       5,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 1)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  seatPriceId,
				Quantity: 5,
			},
			UpdateTimeType: orb.PlanChangeImmediate, // ensure that decrease is still immediate on trial
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-trial-seats", subscriptionChangeId)
			// assert we use idempotency key
		}))
	})
}

func TestIncreaseSeats(t *testing.T) {
	t.Run("User is on professional plan and increasing number of seats", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, true)
		defer cleanup()

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       15,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)

		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 3)
		// Assert we update to 15 seats immediately
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  seatPriceId,
				Quantity: 15,
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-seats", subscriptionChangeId) // assert we use idempotency key
		}))

		// Assert we update to 1500 credits at end of term (next month)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  creditPriceId,
				Quantity: 15 * messagesPerSeat,
			},
			UpdateTimeType: orb.PlanChangeEndOfTerm,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-future-credits", subscriptionChangeId) // assert we use idempotency key
		}))

		// Assert that we update to a pro-rated number of credits immediately
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  creditPriceId,
				Quantity: 10*messagesPerSeat + 5*messagesPerSeat/2, // check that we pro-rate correctly for exactly halfway through month, increase of 5 seats
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-current-credits", subscriptionChangeId) // assert we use idempotency key
		}))
	})

	t.Run("User is on professional plan and increasing number of seats, but there is a scheduled change to decrease", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Mock GetSubscription from Orb
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			ExternalPlanID:    "plan-id",
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:              10,
				SeatsID:            seatPriceId,
				IncludedMessages:   10 * messagesPerSeat,
				IncludedMessagesID: creditPriceId,
			},
			FutureFixedQuantities: &orb.FixedQuantities{
				Seats:              5,
				SeatsID:            seatPriceId,
				IncludedMessages:   5 * messagesPerSeat,
				IncludedMessagesID: creditPriceId,
			},
			CurrentBillingPeriodStartDate: time.Now().In(time.UTC).Add(-15 * 24 * time.Hour), // 15 days before now
			CurrentBillingPeriodEndDate:   time.Now().In(time.UTC).Add(15 * 24 * time.Hour),  // 15 days from now
		}, nil)

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       15,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)

		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 3)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UnscheduleFixedQuantity", 1) // assert we unschedule the future change

		// Assert we update to 15 seats immediately
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  seatPriceId,
				Quantity: 15,
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-seats", subscriptionChangeId)
		}))

		// Assert we update to 1500 credits at end of term (next month)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  creditPriceId,
				Quantity: 15 * messagesPerSeat,
			},
			UpdateTimeType: orb.PlanChangeEndOfTerm,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-future-credits", subscriptionChangeId)
		}))

		// Assert that we update to a pro-rated number of credits immediately
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  creditPriceId,
				Quantity: 10*messagesPerSeat + 5*messagesPerSeat/2, // Increase of 5 seats (10 seats now, will go to 5 at EOM)
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-current-credits", subscriptionChangeId)
		}))
	})

	t.Run("User is on a professional plan, scheduled to decrease, bring back to current number of seats", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Mock GetSubscription from Orb
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, mock.Anything, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			ExternalPlanID:    "plan-id",
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:              10,
				SeatsID:            seatPriceId,
				IncludedMessages:   10 * messagesPerSeat,
				IncludedMessagesID: creditPriceId,
			},
			FutureFixedQuantities: &orb.FixedQuantities{
				Seats:              5,
				SeatsID:            seatPriceId,
				IncludedMessages:   5 * messagesPerSeat,
				IncludedMessagesID: creditPriceId,
			},
			CurrentBillingPeriodStartDate: time.Now().Add(-15 * 24 * time.Hour), // 15 days before now
			CurrentBillingPeriodEndDate:   time.Now().Add(15 * 24 * time.Hour),  // 15 days from now
		}, nil)

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       10,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)

		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 2)     // assert we do not add anything for current month, only 2 calls
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UnscheduleFixedQuantity", 1) // assert we unschedule the future change
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UnscheduleFixedQuantity", mock.Anything, subscriptionId, seatPriceId, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-unschedule-future-seats", subscriptionChangeId)
		}))
	})
}

func TestDecreaseSeats(t *testing.T) {
	t.Run("User is on professional plan and decreasing number of seats", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, true)
		defer cleanup()

		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       5,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)

		// Assert only two calls (nothing for current month)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 2)

		// Assert we update to 5 seats at end of term (next month)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  seatPriceId,
				Quantity: 5,
			},
			UpdateTimeType: orb.PlanChangeEndOfTerm,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-seats", subscriptionChangeId)
		}))

		// Assert we update to 500 credits at end of term (next month)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  creditPriceId,
				Quantity: 5 * messagesPerSeat,
			},
			UpdateTimeType: orb.PlanChangeEndOfTerm,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-future-credits", subscriptionChangeId)
		}))
	})
}

func TestScheduledPlanChangeWithSeatUpdates(t *testing.T) {
	const (
		currentPlanId   = "orb-pro-plan"
		targetPlanId    = "orb-developer-plan"
		currentSeats    = 10
		targetSeats     = 5
		proPricePerSeat = 150
		devPricePerSeat = 100
	)

	t.Run("Scheduled plan change with seat increase", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Clear the default mock for GetScheduledPlanChanges that returns nil
		updateProcessor.orbClient.(*orb.MockOrbClient).ExpectedCalls = nil

		// Mock GetUserSubscription to return current subscription with scheduled downgrade
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, subscriptionId, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			OrbCustomerID:     "test-customer-id",
			ExternalPlanID:    currentPlanId,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:              currentSeats,
				SeatsID:            seatPriceId,
				IncludedMessages:   currentSeats * proPricePerSeat,
				IncludedMessagesID: creditPriceId,
			},
			CurrentBillingPeriodStartDate: time.Now().Add(-15 * 24 * time.Hour), // 15 days before now
			CurrentBillingPeriodEndDate:   time.Now().Add(15 * 24 * time.Hour),  // 15 days from now
		}, nil)

		// Mock GetScheduledPlanChanges to return scheduled downgrade
		scheduledPlanId := targetPlanId
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, subscriptionId, "test-customer-id").Return(&scheduledPlanId, nil)

		// Mock GetPlanInformation for current plan
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.MatchedBy(func(planId *string) bool {
			return planId == nil
		})).Return(&orb.OrbPlanInfo{
			MessagesPerSeat:         proPricePerSeat,
			SeatsPriceID:            seatPriceId,
			IncludedMessagesPriceID: creditPriceId,
		}, nil)

		// Mock GetPlanInformation for target plan
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.MatchedBy(func(planId *string) bool {
			return planId != nil && *planId == targetPlanId
		})).Return(&orb.OrbPlanInfo{
			MessagesPerSeat:         devPricePerSeat,
			SeatsPriceID:            seatPriceId,
			IncludedMessagesPriceID: creditPriceId,
		}, nil)

		// Mock UpdateFixedQuantity and SetCustomerPlanType
		updateProcessor.orbClient.(*orb.MockOrbClient).On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
		updateProcessor.orbClient.(*orb.MockOrbClient).On("SetCustomerPlanType", mock.Anything, mock.Anything, mock.Anything).Return(nil)
		updateProcessor.orbClient.(*orb.MockOrbClient).On("UnscheduleFixedQuantity", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		// Process seat increase from 10 to 15
		newSeats := int32(15)
		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       newSeats,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)

		// Verify immediate seat increase
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  seatPriceId,
				Quantity: float64(newSeats),
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-seats", subscriptionChangeId)
		}))

		// Verify immediate prorated credit increase
		// The proration calculation uses the current plan's credits per seat: 10*150 + 5*150/2 = 1500 + 375 = 1875
		expectedCurrentCredits := float64(currentSeats*proPricePerSeat + (int(newSeats)-currentSeats)*proPricePerSeat/2)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  creditPriceId,
				Quantity: expectedCurrentCredits,
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-current-credits", subscriptionChangeId)
		}))

		// Verify scheduled plan change with updated seat count
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "SetCustomerPlanType", mock.Anything, mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
			return planChange.NewPlanID == targetPlanId &&
				planChange.PlanChangeType == orb.PlanChangeEndOfTerm &&
				len(planChange.PriceOverrides) == 2 &&
				planChange.PriceOverrides[0].PriceID == seatPriceId &&
				planChange.PriceOverrides[0].Quantity == float64(newSeats) &&
				planChange.PriceOverrides[1].PriceID == creditPriceId &&
				planChange.PriceOverrides[1].Quantity == float64(newSeats)*devPricePerSeat &&
				planChange.BillingCycleAlignment == orb.BillingCycleAlignmentUnchanged
		}), mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-future-plan", subscriptionChangeId)
		}))
	})

	t.Run("Scheduled plan change with seat decrease", func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()

		// Clear the default mock for GetScheduledPlanChanges that returns nil
		updateProcessor.orbClient.(*orb.MockOrbClient).ExpectedCalls = nil

		// Mock GetUserSubscription to return current subscription with scheduled downgrade
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetUserSubscription", mock.Anything, subscriptionId, mock.Anything).Return(&orb.OrbSubscriptionInfo{
			OrbSubscriptionID: subscriptionId,
			OrbCustomerID:     "test-customer-id",
			ExternalPlanID:    currentPlanId,
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats:              currentSeats,
				SeatsID:            seatPriceId,
				IncludedMessages:   currentSeats * proPricePerSeat,
				IncludedMessagesID: creditPriceId,
			},
			CurrentBillingPeriodStartDate: time.Now().Add(-15 * 24 * time.Hour), // 15 days before now
			CurrentBillingPeriodEndDate:   time.Now().Add(15 * 24 * time.Hour),  // 15 days from now
		}, nil)

		// Mock GetScheduledPlanChanges to return scheduled downgrade
		scheduledPlanId := targetPlanId
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetScheduledPlanChanges", mock.Anything, subscriptionId, "test-customer-id").Return(&scheduledPlanId, nil)

		// Mock GetPlanInformation for current plan
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.MatchedBy(func(planId *string) bool {
			return planId == nil
		})).Return(&orb.OrbPlanInfo{
			MessagesPerSeat:         proPricePerSeat,
			SeatsPriceID:            seatPriceId,
			IncludedMessagesPriceID: creditPriceId,
		}, nil)

		// Mock GetPlanInformation for target plan
		updateProcessor.orbClient.(*orb.MockOrbClient).On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.MatchedBy(func(planId *string) bool {
			return planId != nil && *planId == targetPlanId
		})).Return(&orb.OrbPlanInfo{
			MessagesPerSeat:         devPricePerSeat,
			SeatsPriceID:            seatPriceId,
			IncludedMessagesPriceID: creditPriceId,
		}, nil)

		// Mock UpdateFixedQuantity and SetCustomerPlanType
		updateProcessor.orbClient.(*orb.MockOrbClient).On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
		updateProcessor.orbClient.(*orb.MockOrbClient).On("SetCustomerPlanType", mock.Anything, mock.Anything, mock.Anything).Return(nil)
		updateProcessor.orbClient.(*orb.MockOrbClient).On("UnscheduleFixedQuantity", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)

		// Process seat decrease from 10 to 7
		newSeats := int32(7)
		err := updateProcessor.Process(context.Background(), &auth_internal.UpdateSubscriptionMessage{
			Id:             subscriptionChangeId,
			SubscriptionId: subscriptionId,
			NumSeats:       newSeats,
			PublishTime:    timestamppb.Now(),
		})
		require.NoError(t, err)

		// Verify no immediate seat change (seat decreases are scheduled)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNotCalled(t, "UpdateFixedQuantity", mock.Anything, mock.MatchedBy(func(update orb.OrbQuantityUpdate) bool {
			return update.PriceOverride.PriceID == seatPriceId && update.UpdateTimeType == orb.PlanChangeImmediate
		}), mock.Anything)

		// Verify no immediate credit change for seat decrease
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNotCalled(t, "UpdateFixedQuantity", mock.Anything, mock.MatchedBy(func(update orb.OrbQuantityUpdate) bool {
			return update.PriceOverride.PriceID == creditPriceId && update.UpdateTimeType == orb.PlanChangeImmediate
		}), mock.Anything)

		// Verify scheduled plan change with updated seat count (7 seats on target plan)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "SetCustomerPlanType", mock.Anything, mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
			return planChange.NewPlanID == targetPlanId &&
				planChange.PlanChangeType == orb.PlanChangeEndOfTerm &&
				len(planChange.PriceOverrides) == 2 &&
				planChange.PriceOverrides[0].PriceID == seatPriceId &&
				planChange.PriceOverrides[0].Quantity == float64(newSeats) &&
				planChange.PriceOverrides[1].PriceID == creditPriceId &&
				planChange.PriceOverrides[1].Quantity == float64(newSeats)*devPricePerSeat &&
				planChange.BillingCycleAlignment == orb.BillingCycleAlignmentUnchanged
		}), mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-future-plan", subscriptionChangeId)
		}))
	})
}

// Helper function to test updating the current month's credits and assert we pro-rate properly
func runUpdateCurrentMonthCreditsTest(t *testing.T, testName string, billingPeriodStart, billingPeriodEnd time.Time, expectedQuantity int64) {
	t.Run(testName, func(t *testing.T) {
		updateProcessor, cleanup := newTestUpdateSubscriptionProcessor(t, false)
		defer cleanup()
		logger := log.Logger

		// Mock UpdateFixedQuantity from Orb
		updateProcessor.orbClient.(*orb.MockOrbClient).On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

		err := updateProcessor.updateCurrentMonthCredits(
			context.Background(),
			&auth_internal.UpdateSubscriptionMessage{
				Id:             subscriptionChangeId,
				SubscriptionId: subscriptionId,
				NumSeats:       15,
				PublishTime:    timestamppb.Now(),
			},
			&orb.OrbSubscriptionInfo{
				OrbSubscriptionID: subscriptionId,
				ExternalPlanID:    "plan-id",
				CurrentFixedQuantities: &orb.FixedQuantities{
					Seats:              10,
					SeatsID:            seatPriceId,
					IncludedMessages:   10 * messagesPerSeat,
					IncludedMessagesID: creditPriceId,
				},
				CurrentBillingPeriodStartDate: billingPeriodStart,
				CurrentBillingPeriodEndDate:   billingPeriodEnd,
			},
			messagesPerSeat,
			10,
			logger,
		)
		require.NoError(t, err)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertNumberOfCalls(t, "UpdateFixedQuantity", 1)
		updateProcessor.orbClient.(*orb.MockOrbClient).AssertCalled(t, "UpdateFixedQuantity", mock.Anything, orb.OrbQuantityUpdate{
			OrbSubscriptionID: subscriptionId,
			PriceOverride: orb.OrbPriceOverrides{
				PriceID:  creditPriceId,
				Quantity: float64(expectedQuantity), // Comment: comment
			},
			UpdateTimeType: orb.PlanChangeImmediate,
		}, mock.MatchedBy(func(idempotencyKey *string) bool {
			return idempotencyKey != nil && *idempotencyKey == fmt.Sprintf("%s-update-current-credits", subscriptionChangeId)
		}))
	})
}

func TestUpdateCurrentMonthCredits(t *testing.T) {
	now := time.Now()

	// Go from 10 seats to 15 seats at different points in the month, 100 credits per seat

	runUpdateCurrentMonthCreditsTest(t,
		"Beginning of the month",
		now,
		now.Add(30*24*time.Hour),
		1500, // Get the full amount as we are at the beginning of the month
	)

	runUpdateCurrentMonthCreditsTest(t,
		"Middle of the month",
		now.Add(-15*24*time.Hour),
		now.Add(15*24*time.Hour),
		1250, // Get 1/2 of the amount as we are 1/2 of the way through the month"
	)

	runUpdateCurrentMonthCreditsTest(t,
		"End of the month",
		now.Add(-30*24*time.Hour),
		now,
		1000, // Don't get any additional pro-rated credits, we are at end of month
	)
}
