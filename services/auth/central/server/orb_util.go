package main

import (
	"context"
	"time"

	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// checkUserTierChangeInProgress is an internal helper to check tier change status
// and return a gRPC error if a change is in progress.
func checkUserTierChangeInProgress(ctx context.Context, userID string, userDAO *UserDAO) error {
	if userID == "" {
		return status.Error(codes.InvalidArgument, "user ID is required to check tier change status")
	}

	user, err := userDAO.Get(ctx, userID) // d.Get already handles d.rowKey(userID)
	if err != nil {
		log.Error().Err(err).Str("user_id", userID).Msg("Failed to get user")
		return status.Error(codes.Internal, "Failed to get user")
	}

	if user == nil {
		log.Warn().Str("user_id", userID).Msg("User not found")
		return status.Error(codes.NotFound, "User not found")
	}

	if user.TierChange != nil {
		log.Warn().
			Str("user_id", userID).
			Str("tier_change_id", user.TierChange.Id).
			Msg("User has a tier change in progress, aborting Orb operation")
		return status.Errorf(codes.FailedPrecondition,
			"User has a tier change in progress (ID: %s), operation aborted",
			user.TierChange.Id)
	}

	return nil
}

// SafeCreateCustomer creates a new customer in Orb after checking for tier changes.
// It requires a userID to check the tier change status.
func SafeCreateCustomer(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string, // Explicitly require userID for the check
	customer orb.OrbCustomer,
	usingStripe bool,
	idempotencyKey *string,
) (string, error) {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		// Log already happened in checkUserTierChangeInProgress if it was a tier change conflict
		return "", err
	}
	return orbClient.CreateCustomer(ctx, customer, usingStripe, idempotencyKey)
}

// SafeCreateSubscription creates a new subscription in Orb after checking for tier changes.
func SafeCreateSubscription(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	subscription orb.OrbSubscription,
	idempotencyKey *string,
) (string, error) {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return "", err
	}
	return orbClient.CreateSubscription(ctx, subscription, idempotencyKey)
}

// SafeAddAlertsForCustomer adds alerts for a customer in Orb after checking for tier changes.
func SafeAddAlertsForCustomer(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	customerOrbID string,
	currency string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.AddAlertsForCustomer(ctx, customerOrbID, currency)
}

// SafeAddCreditBalanceDepletedAlert adds a credit balance depleted alert in Orb after checking for tier changes.
func SafeAddCreditBalanceDepletedAlert(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	customerOrbID string,
	currency string,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.AddCreditBalanceDepletedAlert(ctx, customerOrbID, currency, idempotencyKey)
}

// SafeAddCreditBalanceRecoveredAlert adds a credit balance recovered alert in Orb after checking for tier changes.
func SafeAddCreditBalanceRecoveredAlert(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	customerOrbID string,
	currency string,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.AddCreditBalanceRecoveredAlert(ctx, customerOrbID, currency, idempotencyKey)
}

// SafeSetCustomerPlanType sets a customer's plan type in Orb after checking for tier changes.
func SafeSetCustomerPlanType(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	planChange orb.OrbPlanChange,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.SetCustomerPlanType(ctx, planChange, idempotencyKey)
}

// SafeIngestEvents ingests events in Orb after checking for tier changes.
func SafeIngestEvents(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	events []*orb.OrbEvent,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.IngestEvents(ctx, events)
}

// SafePurchaseCredits purchases credits in Orb after checking for tier changes.
func SafePurchaseCredits(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	creditPurchase orb.OrbCreditPurchase,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.PurchaseCredits(ctx, creditPurchase, idempotencyKey)
}

// SafeCancelOrbSubscription cancels an Orb subscription after checking for tier changes.
func SafeCancelOrbSubscription(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	orbSubscriptionId string,
	cancelTime orb.PlanChangeType,
	cancelDate *time.Time,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.CancelOrbSubscription(ctx, orbSubscriptionId, cancelTime, cancelDate, idempotencyKey)
}

// SafeUpdateFixedQuantity updates a fixed quantity in Orb after checking for tier changes.
func SafeUpdateFixedQuantity(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	quantityUpdate orb.OrbQuantityUpdate,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.UpdateFixedQuantity(ctx, quantityUpdate, idempotencyKey)
}

// SafeUnschedulePendingSubscriptionCancellation unschedules a pending subscription cancellation in Orb after checking for tier changes.
func SafeUnschedulePendingSubscriptionCancellation(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	orbSubscriptionId string,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.UnschedulePendingSubscriptionCancellation(ctx, orbSubscriptionId, idempotencyKey)
}

// SafeUnscheduleFixedQuantity unschedules a fixed quantity change in Orb after checking for tier changes.
func SafeUnscheduleFixedQuantity(
	ctx context.Context,
	orbClient orb.OrbClient,
	userDAO *UserDAO,
	userID string,
	orbSubscriptionID string,
	priceID string,
	idempotencyKey *string,
) error {
	if err := checkUserTierChangeInProgress(ctx, userID, userDAO); err != nil {
		return err
	}
	return orbClient.UnscheduleFixedQuantity(ctx, orbSubscriptionID, priceID, idempotencyKey)
}
