package main

import (
	"context"
	"fmt"
	"testing"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	tw_client "github.com/augmentcode/augment/services/tenant_watcher/client"
	tw_proto "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/google/uuid"

	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// createTestOrbConfig creates a test OrbConfig with the necessary plans for testing
func createTestOrbConfig() *OrbConfig {
	return &OrbConfig{
		Enabled:                true,
		SeatsItemID:            "test-seats-item-id",
		IncludedMessagesItemID: "test-messages-item-id",
		Plans: []PlanConfig{
			{
				ID:        "orb_community_plan",
				SortOrder: 1,
				Features: PlanFeatures{
					PlanType:            PlanTypeCommunity,
					TrainingAllowed:     true,
					TeamsAllowed:        false,
					MaxSeats:            1,
					AddCreditsAvailable: true,
				},
			},
			{
				ID:        "orb_trial_plan",
				SortOrder: 2,
				Features: PlanFeatures{
					PlanType:            PlanTypePaidTrial,
					TrainingAllowed:     false,
					TeamsAllowed:        true,
					MaxSeats:            5,
					AddCreditsAvailable: false,
				},
			},
			{
				ID:        "orb_developer_plan",
				SortOrder: 3,
				Features: PlanFeatures{
					PlanType:            PlanTypePaid,
					TrainingAllowed:     false,
					TeamsAllowed:        true,
					MaxSeats:            100,
					AddCreditsAvailable: true,
				},
			},
			{
				ID:        "orb_pro_plan",
				SortOrder: 4,
				Features: PlanFeatures{
					PlanType:            PlanTypePaid,
					TrainingAllowed:     false,
					TeamsAllowed:        true,
					MaxSeats:            100,
					AddCreditsAvailable: true,
				},
			},
			{
				ID:        "orb_max_plan",
				SortOrder: 5,
				Features: PlanFeatures{
					PlanType:            PlanTypePaid,
					TrainingAllowed:     false,
					TeamsAllowed:        true,
					MaxSeats:            100,
					AddCreditsAvailable: true,
				},
			},
		},
	}
}

// setupTeamPlanChangeTest creates the common test setup for team tier change tests
func setupTeamPlanChangeTest(t *testing.T) (
	context.Context,
	*DAOFactory,
	*orb.MockOrbClient,
	*MockAsyncOpsPublisher,
	func(),
) {
	// Create context
	ctx, cancel := context.WithCancel(context.Background())

	// Set up bigtable
	bigtableFixture := NewBigtableFixture(t)
	cleanup := func() {
		cancel()
		bigtableFixture.Cleanup()
	}

	// Create DAO factory
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	// Create mock Orb client
	mockOrbClient := orb.NewMockOrbClient()

	// Create mock async ops publisher
	mockAsyncOpsPublisher := NewMockAsyncOpsPublisher()

	return ctx, daoFactory, mockOrbClient, mockAsyncOpsPublisher, cleanup
}

// createTestTeamPlanChangeMessage creates a standard test message for team plan change tests
func createTestTeamPlanChangeMessage(teamTenantID string, targetOrbPlanID string, planChangeID string, publishTime time.Time, initiatedByUserID string) *auth_internal.TeamPlanChangeMessage {
	return &auth_internal.TeamPlanChangeMessage{
		TeamTenantId:      teamTenantID,
		TargetOrbPlanId:   targetOrbPlanID,
		PlanChangeId:      planChangeID,
		PublishTime:       timestamppb.New(publishTime),
		InitiatedByUserId: initiatedByUserID,
	}
}

// setupTenantSubscriptionMapping creates a tenant subscription mapping for testing
func setupTenantSubscriptionMapping(
	t *testing.T,
	ctx context.Context,
	daoFactory *DAOFactory,
	teamTenantID string,
	withPlanChange bool,
	planChangeID string,
	targetOrbPlanID string,
) *auth_entities.TenantSubscriptionMapping {
	tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()

	mapping := &auth_entities.TenantSubscriptionMapping{
		TenantId:          teamTenantID,
		StripeCustomerId:  "cus_test",
		OrbCustomerId:     "orb-customer-id",
		OrbSubscriptionId: "orb_sub_1",
		CreatedAt:         timestamppb.Now(),
		BillingMethod:     auth_entities.BillingMethod_BILLING_METHOD_ORB,
	}

	if withPlanChange {
		mapping.PlanChange = &auth_entities.TenantSubscriptionMapping_PlanChangeInfo{
			Id:              planChangeID,
			TargetOrbPlanId: targetOrbPlanID,
			CreatedAt:       timestamppb.Now(),
			UpdatedAt:       timestamppb.Now(),
		}
	}

	createdMapping, err := tenantSubscriptionMappingDAO.Create(ctx, mapping)
	require.NoError(t, err, "Failed to create tenant subscription mapping")

	return createdMapping
}

// setupTestSubscription creates a subscription for testing
func setupTestSubscription(
	t *testing.T,
	ctx context.Context,
	daoFactory *DAOFactory,
	subscriptionID string,
) {
	subscriptionDAO := daoFactory.GetSubscriptionDAO()
	subscription := &auth_entities.Subscription{
		SubscriptionId: subscriptionID,
		CreatedAt:      timestamppb.Now(),
		Status:         auth_entities.Subscription_ACTIVE,
	}

	_, err := subscriptionDAO.Create(ctx, subscription)
	require.NoError(t, err, "Failed to create subscription")
}

// Setup the UserTenantMapping and TenantInvitation for a team
func updateSeatsAndInvitations(
	t *testing.T,
	ctx context.Context,
	daoFactory *DAOFactory,
	teamTenantName string,
	teamTenantID string,
	numMembers int,
	numInvitations int,
) {
	// Create user tenant mappings
	// Create 3 team members in the tenant, including the admin user
	userTenantMappingDAO := daoFactory.GetUserTenantMappingDAO(teamTenantName)

	// First, add the admin user with admin role
	adminMapping := &auth_entities.UserTenantMapping{
		Tenant:          teamTenantName,
		UserId:          "admin-user-id",
		CustomerUiRoles: []auth_entities.CustomerUiRole{auth_entities.CustomerUiRole_ADMIN},
	}
	_, err := userTenantMappingDAO.Create(context.Background(), adminMapping)
	require.NoError(t, err)

	for i := 0; i < numMembers-1; i++ {
		userID := fmt.Sprintf("user-%d", i)
		mapping := &auth_entities.UserTenantMapping{
			Tenant: teamTenantName,
			UserId: userID,
		}
		_, err := userTenantMappingDAO.Create(context.Background(), mapping)
		require.NoError(t, err)
	}

	// Create 2 pending invitations for the tenant
	invitationDAO := daoFactory.GetTenantInvitationDAO(teamTenantID)
	for i := 0; i < numInvitations; i++ {
		invitationID := uuid.New().String()
		invitation := &auth_entities.TenantInvitation{
			Id:           invitationID,
			TenantId:     teamTenantID,
			InviteeEmail: fmt.Sprintf("<EMAIL>", i),
			Status:       auth_entities.TenantInvitation_PENDING,
			CreatedAt:    timestamppb.Now(),
		}
		_, err := invitationDAO.Create(context.Background(), invitation)
		require.NoError(t, err)
	}
}

// TestTeamPlanChangeProcessor tests the TeamPlanChangeProcessor
func TestTeamPlanChangeProcessor(t *testing.T) {
	t.Run("FeatureFlagDisabled", func(t *testing.T) {
		ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)
		defer cleanup()

		// Create feature flag handle with team tier change disabled
		featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
		featureFlagHandle.Set("auth_central_team_management_enabled", false)

		// Create processor
		auditLogger := audit.NewDefaultAuditLogger()
		processor, err := NewTeamPlanChangeProcessor(
			daoFactory,
			mockOrbClient,
			createTestOrbConfig(),
			featureFlagHandle,
			auditLogger,
			NewTenantMap(
				daoFactory,
				&tw_client.MockTenantWatcherClient{},
				"us-central.api.augmentcode.com",
				featureFlagHandle,
				NewMockAsyncOpsPublisher(),
				auditLogger,
			),
		)
		require.NoError(t, err)

		// Create test message
		msg := createTestTeamPlanChangeMessage("team-tenant-id", "orb-professional-plan", "test-plan-change-id", time.Now(), "admin-user-id")

		// Process message - should return error because feature flag is disabled
		err = processor.Process(ctx, msg)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "team management is disabled")
	})

	t.Run("NilPlanChangeAndRecentMessage", func(t *testing.T) {
		ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)
		defer cleanup()

		// Create feature flag handle with team plan change enabled
		featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
		featureFlagHandle.Set("auth_central_team_management_enabled", true)

		// Create processor
		auditLogger := audit.NewDefaultAuditLogger()
		processor, err := NewTeamPlanChangeProcessor(
			daoFactory,
			mockOrbClient,
			createTestOrbConfig(),
			featureFlagHandle,
			auditLogger,
			NewTenantMap(
				daoFactory,
				&tw_client.MockTenantWatcherClient{},
				"us-central.api.augmentcode.com",
				featureFlagHandle,
				NewMockAsyncOpsPublisher(),
				auditLogger,
			),
		)
		require.NoError(t, err)

		// Create tenant subscription mapping without plan change
		teamTenantID := "team-tenant-id"
		planChangeID := "test-plan-change-id"
		// For this test, the mapping has NO PlanChange info.
		setupTenantSubscriptionMapping(t, ctx, daoFactory, teamTenantID, false, "", "")

		// Create a recent test message
		msg := createTestTeamPlanChangeMessage(teamTenantID, "orb-target-plan", planChangeID, time.Now(), "admin-user-id")

		// Process message - should return error because plan change is nil on mapping and message is recent
		err = processor.Process(ctx, msg)
		assert.Error(t, err)
		expectedErrorSubString := fmt.Sprintf("nil plan change on tenant, retrying: team_tenant_id=%s, message_plan_change_id=%s", teamTenantID, planChangeID)
		assert.Contains(t, err.Error(), expectedErrorSubString)
	})

	t.Run("NilPlanChangeAndOldMessage", func(t *testing.T) {
		ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)
		defer cleanup()

		featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
		featureFlagHandle.Set("auth_central_team_management_enabled", true)
		auditLogger := audit.NewDefaultAuditLogger()

		processor, err := NewTeamPlanChangeProcessor(
			daoFactory,
			mockOrbClient,
			createTestOrbConfig(),
			featureFlagHandle,
			auditLogger,
			NewTenantMap(
				daoFactory,
				&tw_client.MockTenantWatcherClient{},
				"us-central.api.augmentcode.com",
				featureFlagHandle,
				NewMockAsyncOpsPublisher(),
				auditLogger,
			),
		)
		require.NoError(t, err)

		teamTenantID := "team-tenant-id"
		planChangeID := "test-plan-change-id"
		// For this test, the mapping has NO PlanChange info.
		setupTenantSubscriptionMapping(t, ctx, daoFactory, teamTenantID, false, "", "")

		// Create a message that's older than 5 minutes
		oldMsg := createTestTeamPlanChangeMessage(teamTenantID, "orb-target-plan", planChangeID, time.Now().Add(-6*time.Minute), "admin-user-id")

		// Process old message - should be acknowledged (return nil) because PlanChange is nil on mapping and message is old
		err = processor.Process(ctx, oldMsg)
		assert.NoError(t, err)
	})

	t.Run("PlanChangeIDMismatch", func(t *testing.T) {
		ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)
		defer cleanup()

		featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
		featureFlagHandle.Set("auth_central_team_management_enabled", true)
		auditLogger := audit.NewDefaultAuditLogger()

		processor, err := NewTeamPlanChangeProcessor(
			daoFactory,
			mockOrbClient,
			createTestOrbConfig(),
			featureFlagHandle,
			auditLogger,
			NewTenantMap(
				daoFactory,
				&tw_client.MockTenantWatcherClient{},
				"us-central.api.augmentcode.com",
				featureFlagHandle,
				NewMockAsyncOpsPublisher(),
				auditLogger,
			),
		)
		require.NoError(t, err)

		teamTenantID := "team-tenant-id"
		dbPlanChangeID := "db-plan-change-id"
		msgPlanChangeID := "msg-plan-change-id"
		// Create tenant subscription mapping with a specific plan change ID
		setupTenantSubscriptionMapping(t, ctx, daoFactory, teamTenantID, true, dbPlanChangeID, "orb-plan-A")

		// Create test message with a different plan change ID
		msg := createTestTeamPlanChangeMessage(teamTenantID, "orb-plan-B", msgPlanChangeID, time.Now(), "admin-user-id")

		// Process message - should be acknowledged (return nil) because IDs don't match
		err = processor.Process(ctx, msg)
		assert.NoError(t, err)
	})

	t.Run("Ended subscription", func(t *testing.T) {
		testCases := []struct {
			numInvitations int
			numMembers     int
		}{
			{
				// 5 team members
				numInvitations: 0,
				numMembers:     5,
			}, {
				// Members and invitations
				numInvitations: 2,
				numMembers:     3,
			}, {
				// Only admin
				numInvitations: 0,
				numMembers:     1,
			},
		}
		for _, tc := range testCases {
			t.Run(fmt.Sprintf("numInvitations=%d,numMembers=%d", tc.numInvitations, tc.numMembers), func(t *testing.T) {
				ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)
				defer cleanup()

				teamTenantName := "team-tenant-name"
				teamTenantID := "team-tenant-id"
				planChangeID := "test-plan-change-id"
				targetOrbPlanID := "orb-target-plan"
				orbSubscriptionID := "orb_sub_1"

				featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
				featureFlagHandle.Set("auth_central_team_management_enabled", true)

				selfServeTeamTenant := &tw_proto.Tenant{
					Id:             teamTenantID,
					Name:           teamTenantName,
					Tier:           tw_proto.TenantTier_PROFESSIONAL,
					ShardNamespace: "test-namespace-self-serve-team",
					Cloud:          "CLOUD_PROD",
					Config: &tw_proto.Config{
						Configs: map[string]string{
							"is_self_serve_team": "true",
						},
					},
				}

				mockTenantWatcherClient := &tw_client.MockTenantWatcherClient{
					Tenants: []*tw_proto.Tenant{
						selfServeTeamTenant,
					},
				}

				auditLogger := audit.NewDefaultAuditLogger()
				processor, err := NewTeamPlanChangeProcessor(
					daoFactory,
					mockOrbClient,
					createTestOrbConfig(),
					featureFlagHandle,
					auditLogger,
					NewTenantMap(
						daoFactory,
						mockTenantWatcherClient,
						"us-central.api.augmentcode.com",
						featureFlagHandle,
						NewMockAsyncOpsPublisher(),
						auditLogger,
					),
				)
				require.NoError(t, err)

				// Create tenant subscription mapping with a specific plan change ID
				setupTenantSubscriptionMapping(t, ctx, daoFactory, teamTenantID, true, planChangeID, targetOrbPlanID)

				// Create subscription
				setupTestSubscription(t, ctx, daoFactory, orbSubscriptionID)

				// Set up users and invitations properly
				updateSeatsAndInvitations(t, ctx, daoFactory, teamTenantName, teamTenantID, tc.numMembers, tc.numInvitations)

				// Mock Orb API calls
				mockOrbClient.On("GetUserSubscription",
					mock.Anything,
					orbSubscriptionID,
					mock.Anything,
				).Return(&orb.OrbSubscriptionInfo{
					OrbStatus: "ended",
					CurrentFixedQuantities: &orb.FixedQuantities{
						Seats: 0, // Current seats is 0 because ended
					},
				}, nil).Once()

				// maybe change to a specific plan or something
				mockOrbClient.On("GetPlanInformation",
					mock.Anything,
					orb.ItemIds{SeatsID: "test-seats-item-id", IncludedMessagesID: "test-messages-item-id"},
					(*string)(nil),
					mock.MatchedBy(func(s *string) bool { return s != nil && *s == targetOrbPlanID }),
				).Return(&orb.OrbPlanInfo{
					ExternalPlanID:          targetOrbPlanID,
					Name:                    "Test Target Plan",
					PricePerSeat:            "200.00",
					SeatsPriceID:            "seats-price-id",
					MessagesPerSeat:         1000,
					IncludedMessagesPriceID: "included-messages-price-id",
				}, nil).Once()

				mockOrbClient.On("CreateSubscription", mock.Anything, mock.Anything, mock.AnythingOfType("*string")).Return("new-orb-subscription-id", nil).Once()

				// Create test message
				msg := createTestTeamPlanChangeMessage(teamTenantID, targetOrbPlanID, planChangeID, time.Now(), "admin-user-id")

				// Process message - should succeed
				err = processor.Process(ctx, msg)
				assert.NoError(t, err)

				// Verify the tenant subscription mapping's has the new subscription
				tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
				updatedMapping, err := tenantSubscriptionMappingDAO.Get(ctx, teamTenantID)
				require.NoError(t, err)
				assert.Equal(t, "new-orb-subscription-id", updatedMapping.OrbSubscriptionId, "Subscription ID should be updated after successful processing")

				// Verify the mock calls
				mockOrbClient.AssertCalled(t, "CreateSubscription", mock.Anything, mock.MatchedBy(func(subscription orb.OrbSubscription) bool {
					return subscription.ExternalPlanID == targetOrbPlanID &&
						subscription.CustomerOrbID == "orb-customer-id" &&
						len(subscription.PriceOverrides) == 2 &&
						subscription.PriceOverrides[0].PriceID == "seats-price-id" &&
						subscription.PriceOverrides[0].Quantity == float64(tc.numMembers+tc.numInvitations) &&
						subscription.PriceOverrides[1].PriceID == "included-messages-price-id" &&
						subscription.PriceOverrides[1].Quantity == float64(tc.numMembers+tc.numInvitations)*1000
				}), mock.AnythingOfType("*string"))
			})
		}
	})

	// Test cases for plan changes between all plan types (trial and paid plans)
	t.Run("SuccessfulPlanChange", func(t *testing.T) {
		testCases := []struct {
			name                string
			currentPlanID       string
			targetPlanID        string
			isUpgrade           bool
			currentPricePerSeat string
			targetPricePerSeat  string
		}{
			// Trial to Paid plan transitions (always upgrades)
			{
				name:                "Trial to Developer - Upgrade",
				currentPlanID:       "orb_trial_plan",
				targetPlanID:        "orb_developer_plan",
				isUpgrade:           true,
				currentPricePerSeat: "75.00",
				targetPricePerSeat:  "100.00",
			},
			{
				name:                "Trial to Pro - Upgrade",
				currentPlanID:       "orb_trial_plan",
				targetPlanID:        "orb_pro_plan",
				isUpgrade:           true,
				currentPricePerSeat: "75.00",
				targetPricePerSeat:  "200.00",
			},
			{
				name:                "Trial to Max - Upgrade",
				currentPlanID:       "orb_trial_plan",
				targetPlanID:        "orb_max_plan",
				isUpgrade:           true,
				currentPricePerSeat: "75.00",
				targetPricePerSeat:  "300.00",
			},
			// Paid-to-Paid plan transitions
			{
				name:                "Developer to Pro - Upgrade",
				currentPlanID:       "orb_developer_plan",
				targetPlanID:        "orb_pro_plan",
				isUpgrade:           true,
				currentPricePerSeat: "100.00",
				targetPricePerSeat:  "200.00",
			},
			{
				name:                "Developer to Max - Upgrade",
				currentPlanID:       "orb_developer_plan",
				targetPlanID:        "orb_max_plan",
				isUpgrade:           true,
				currentPricePerSeat: "100.00",
				targetPricePerSeat:  "300.00",
			},
			{
				name:                "Pro to Developer - Downgrade",
				currentPlanID:       "orb_pro_plan",
				targetPlanID:        "orb_developer_plan",
				isUpgrade:           false,
				currentPricePerSeat: "200.00",
				targetPricePerSeat:  "100.00",
			},
			{
				name:                "Pro to Max - Upgrade",
				currentPlanID:       "orb_pro_plan",
				targetPlanID:        "orb_max_plan",
				isUpgrade:           true,
				currentPricePerSeat: "200.00",
				targetPricePerSeat:  "300.00",
			},
			{
				name:                "Max to Developer - Downgrade",
				currentPlanID:       "orb_max_plan",
				targetPlanID:        "orb_developer_plan",
				isUpgrade:           false,
				currentPricePerSeat: "300.00",
				targetPricePerSeat:  "100.00",
			},
			{
				name:                "Max to Pro - Downgrade",
				currentPlanID:       "orb_max_plan",
				targetPlanID:        "orb_pro_plan",
				isUpgrade:           false,
				currentPricePerSeat: "300.00",
				targetPricePerSeat:  "200.00",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)
				defer cleanup()

				featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
				featureFlagHandle.Set("auth_central_team_management_enabled", true)
				auditLogger := audit.NewDefaultAuditLogger()

				processor, err := NewTeamPlanChangeProcessor(
					daoFactory,
					mockOrbClient,
					createTestOrbConfig(),
					featureFlagHandle,
					auditLogger,
					NewTenantMap(
						daoFactory,
						&tw_client.MockTenantWatcherClient{},
						"us-central.api.augmentcode.com",
						featureFlagHandle,
						NewMockAsyncOpsPublisher(),
						auditLogger,
					),
				)
				require.NoError(t, err)

				teamTenantID := fmt.Sprintf("team-tenant-id-%s", tc.name)
				planChangeID := fmt.Sprintf("plan-change-id-%s", tc.name)
				// Create tenant subscription mapping with matching plan change ID and target orb plan ID
				mapping := setupTenantSubscriptionMapping(t, ctx, daoFactory, teamTenantID, true, planChangeID, tc.targetPlanID)

				// Create subscription
				setupTestSubscription(t, ctx, daoFactory, mapping.OrbSubscriptionId)

				// Setup mock Orb client responses
				// Mock for GetUserSubscription (called to check subscription status) - only uses SeatsID
				mockOrbClient.On("GetUserSubscription",
					mock.Anything,             // Context
					mapping.OrbSubscriptionId, // Subscription ID
					&orb.ItemIds{SeatsID: "test-seats-item-id"},
				).Return(&orb.OrbSubscriptionInfo{
					OrbStatus:      "active",
					ExternalPlanID: tc.currentPlanID,
					CurrentFixedQuantities: &orb.FixedQuantities{
						Seats: 2,
					},
				}, nil).Once()

				// Mock for GetPlanInformation for current plan (using subscription ID to get grandfathered pricing)
				mockOrbClient.On("GetPlanInformation",
					mock.Anything,
					orb.ItemIds{SeatsID: "test-seats-item-id", IncludedMessagesID: "test-messages-item-id"},
					mock.MatchedBy(func(s *string) bool { return s != nil && *s == mapping.OrbSubscriptionId }),
					(*string)(nil),
				).Return(&orb.OrbPlanInfo{
					ExternalPlanID: tc.currentPlanID,
					Name:           fmt.Sprintf("Test %s Plan", tc.currentPlanID),
					PricePerSeat:   tc.currentPricePerSeat,
					SeatsPriceID:   "seats-price-id",
				}, nil)

				// Mock for GetPlanInformation for target plan
				mockOrbClient.On("GetPlanInformation",
					mock.Anything,
					orb.ItemIds{SeatsID: "test-seats-item-id", IncludedMessagesID: "test-messages-item-id"},
					(*string)(nil),
					mock.MatchedBy(func(s *string) bool { return s != nil && *s == tc.targetPlanID }),
				).Return(&orb.OrbPlanInfo{
					ExternalPlanID: tc.targetPlanID,
					Name:           fmt.Sprintf("Test %s Plan", tc.targetPlanID),
					PricePerSeat:   tc.targetPricePerSeat,
					SeatsPriceID:   "seats-price-id",
				}, nil)

				// Mock for SetCustomerPlanType (called after GetPlanInformation)
				mockOrbClient.On("SetCustomerPlanType", mock.Anything, mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
					// Verify the plan change type based on upgrade/downgrade
					expectedPlanChangeType := orb.PlanChangeEndOfTerm // Default for downgrades
					if tc.isUpgrade {
						expectedPlanChangeType = orb.PlanChangeImmediate // Upgrades are immediate
					}

					// Verify billing cycle alignment based on current plan type
					expectedBillingAlignment := orb.BillingCycleAlignmentUnchanged // Default for paid-to-paid
					if tc.currentPlanID == "orb_trial_plan" {
						expectedBillingAlignment = orb.BillingCycleAlignmentPlanChangeDate // Trial transitions use plan change date
					}

					return planChange.CustomerOrbID == mapping.OrbCustomerId &&
						planChange.SubscriptionID == mapping.OrbSubscriptionId &&
						planChange.NewPlanID == tc.targetPlanID &&
						planChange.PlanChangeType == expectedPlanChangeType &&
						planChange.BillingCycleAlignment == expectedBillingAlignment
				}), mock.AnythingOfType("*string")).Return(nil)

				// Mock for UpdateFixedQuantity (called for upgrades to update future credits)
				if tc.isUpgrade {
					mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
				}

				// Create test message
				msg := createTestTeamPlanChangeMessage(teamTenantID, tc.targetPlanID, planChangeID, time.Now(), "admin-user-id")

				// Process message - should succeed
				err = processor.Process(ctx, msg)
				assert.NoError(t, err)

				// Verify the tenant subscription mapping's plan change was cleared
				tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()
				updatedMapping, err := tenantSubscriptionMappingDAO.Get(ctx, teamTenantID)
				require.NoError(t, err)
				assert.Nil(t, updatedMapping.PlanChange, "Plan change should be cleared after successful processing")

				// Verify the mock calls
				mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything, mock.Anything, mock.AnythingOfType("*string"))

				// Verify pro-rating behavior based on plan type
				if tc.currentPlanID == "orb_trial_plan" {
					// Trial transitions should NOT be pro-rated
					mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 0)
				} else if tc.isUpgrade {
					// Paid-to-paid upgrades should be pro-rated
					mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 1)
				} else {
					// Paid-to-paid downgrades should not be pro-rated
					mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 0)
				}
			})
		}
	})
}

// TestTeamPlanChangeProcessor_PanicRecovery tests that the processor can recover and succeed
// after a transient panic condition is resolved.
func TestTeamPlanChangeProcessor_PanicRecovery(t *testing.T) {
	panicPointsToTest := []string{
		PanicPointTeamPlanChangeGetSubscriptionMappingFailed,
		PanicPointTeamPlanChangeUpdateOrbSubscriptionFailed,
		PanicPointTeamPlanChangeOrbClientSetPlanFailed,
	}

	for _, panicPoint := range panicPointsToTest {
		currentPanicPoint := panicPoint

		t.Run(fmt.Sprintf("RecoverAfterPanic_%s", currentPanicPoint), func(t *testing.T) {
			// Create a new context, DAO factory, and mocks for each test
			ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)
			defer cleanup()

			// Create feature flag handle and enable relevant flags
			featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
			featureFlagHandle.Set("auth_central_team_management_enabled", true)

			// Create unique IDs for this test case
			teamTenantID := "team-tenant-id-panic-recovery-" + currentPanicPoint
			planChangeID := "plan-change-panic-recovery-" + currentPanicPoint
			targetOrbPlanID := "orb_pro_plan"
			initiatedByUserID := "admin-user-panic-recovery"

			// Set up the tenant subscription mapping and subscription
			mapping := setupTenantSubscriptionMapping(t, ctx, daoFactory, teamTenantID, true, planChangeID, targetOrbPlanID)
			setupTestSubscription(t, ctx, daoFactory, mapping.OrbSubscriptionId)

			// Create the message
			msg := createTestTeamPlanChangeMessage(teamTenantID, targetOrbPlanID, planChangeID, time.Now(), initiatedByUserID)

			// First, test that the panic occurs
			test_utils.EnablePanicPoint(currentPanicPoint)

			// Set up expectations for the panic mock client
			if currentPanicPoint == PanicPointTeamPlanChangeOrbClientSetPlanFailed {
				mockOrbClient.On("GetUserSubscription", mock.Anything, mapping.OrbSubscriptionId, &orb.ItemIds{SeatsID: "test-seats-item-id"}).
					Return(&orb.OrbSubscriptionInfo{
						OrbStatus:      "active",
						ExternalPlanID: "orb_developer_plan", // Current plan is developer
						CurrentFixedQuantities: &orb.FixedQuantities{
							Seats: 2,
						},
					}, nil).Once()

				// First run (with panic) - current plan (using subscription ID to get grandfathered pricing)
				mockOrbClient.On("GetPlanInformation",
					mock.Anything,
					orb.ItemIds{SeatsID: "test-seats-item-id", IncludedMessagesID: "test-messages-item-id"},
					mock.MatchedBy(func(s *string) bool { return s != nil && *s == mapping.OrbSubscriptionId }),
					(*string)(nil),
				).Return(&orb.OrbPlanInfo{
					ExternalPlanID: "orb_developer_plan",
					Name:           "Test Developer Plan",
					PricePerSeat:   "100.00",
					SeatsPriceID:   "seats-price-id",
				}, nil).Once()

				// First run (with panic) - target plan
				mockOrbClient.On("GetPlanInformation",
					mock.Anything,
					orb.ItemIds{SeatsID: "test-seats-item-id", IncludedMessagesID: "test-messages-item-id"},
					(*string)(nil),
					mock.MatchedBy(func(s *string) bool { return s != nil && *s == targetOrbPlanID }),
				).Return(&orb.OrbPlanInfo{
					ExternalPlanID: targetOrbPlanID,
					Name:           "Test Pro Plan",
					PricePerSeat:   "200.00",
					SeatsPriceID:   "seats-price-id",
				}, nil).Once()

				// First run (with panic) - UpdateFixedQuantity (called for upgrades to update future credits)
				mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			}

			// Create a processor with the panic mock
			auditLogger := audit.NewDefaultAuditLogger()
			processor, err := NewTeamPlanChangeProcessor(
				daoFactory,
				mockOrbClient,
				createTestOrbConfig(),
				featureFlagHandle,
				auditLogger,
				NewTenantMap(
					daoFactory,
					&tw_client.MockTenantWatcherClient{},
					"us-central.api.augmentcode.com",
					featureFlagHandle,
					NewMockAsyncOpsPublisher(),
					auditLogger,
				),
			)
			require.NoError(t, err)

			assert.PanicsWithValue(t,
				fmt.Sprintf("Test panic triggered: %s", currentPanicPoint),
				func() {
					_ = processor.Process(ctx, msg)
				},
				"Process should panic when %s is enabled", currentPanicPoint)

			// Disable the panic point for the second attempt
			test_utils.DisablePanicPoint(currentPanicPoint)

			// Set up mocks for the successful retry
			mockOrbClient.On("GetUserSubscription", mock.Anything, mapping.OrbSubscriptionId, &orb.ItemIds{SeatsID: "test-seats-item-id"}).
				Return(&orb.OrbSubscriptionInfo{
					OrbStatus:      "active",
					ExternalPlanID: "orb_developer_plan", // Current plan is developer
					CurrentFixedQuantities: &orb.FixedQuantities{
						Seats: 2,
					},
				}, nil).Once()

			// Second run (successful) - current plan (using subscription ID to get grandfathered pricing)
			mockOrbClient.On("GetPlanInformation",
				mock.Anything,
				orb.ItemIds{SeatsID: "test-seats-item-id", IncludedMessagesID: "test-messages-item-id"},
				mock.MatchedBy(func(s *string) bool { return s != nil && *s == mapping.OrbSubscriptionId }),
				(*string)(nil),
			).Return(&orb.OrbPlanInfo{
				ExternalPlanID: "orb_developer_plan",
				Name:           "Test Developer Plan",
				PricePerSeat:   "100.00",
				SeatsPriceID:   "seats-price-id",
			}, nil).Once()

			// Second run (successful) - target plan
			mockOrbClient.On("GetPlanInformation",
				mock.Anything,
				orb.ItemIds{SeatsID: "test-seats-item-id", IncludedMessagesID: "test-messages-item-id"},
				(*string)(nil),
				mock.MatchedBy(func(s *string) bool { return s != nil && *s == targetOrbPlanID }),
			).Return(&orb.OrbPlanInfo{
				ExternalPlanID: targetOrbPlanID,
				Name:           "Test Pro Plan",
				PricePerSeat:   "200.00",
				SeatsPriceID:   "seats-price-id",
			}, nil).Once()

			mockOrbClient.On("SetCustomerPlanType",
				mock.Anything,
				mock.MatchedBy(func(pc orb.OrbPlanChange) bool {
					return pc.CustomerOrbID == mapping.OrbCustomerId &&
						pc.SubscriptionID == mapping.OrbSubscriptionId &&
						pc.NewPlanID == targetOrbPlanID
				}),
				mock.AnythingOfType("*string"),
			).Return(nil).Once()

			// Mock for UpdateFixedQuantity (called for upgrades to update future credits)
			mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

			// Process the message again - should succeed this time
			err = processor.Process(ctx, msg)
			require.NoError(t, err, "Process should succeed after disabling panic point %s", currentPanicPoint)

			// Verify the tenant subscription mapping's plan change was cleared
			updatedMapping, getErr := daoFactory.GetTenantSubscriptionMappingDAO().Get(ctx, teamTenantID)
			require.NoError(t, getErr)
			assert.Nil(t, updatedMapping.PlanChange, "PlanChange should be cleared after successful processing post-panic")

			// Verify all mock expectations were met
			mockOrbClient.AssertExpectations(t)
		})
	}
}

func SetupSeatsTest(t *testing.T, targetOrbPlanID string) (func(), *TeamPlanChangeProcessor, *auth_internal.TeamPlanChangeMessage, *orb.MockOrbClient) {
	ctx, daoFactory, mockOrbClient, _, cleanup := setupTeamPlanChangeTest(t)

	// Create feature flag handle and enable relevant flags
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
	featureFlagHandle.Set("auth_central_team_management_enabled", true)

	// Create unique IDs for this test case
	teamTenantID := "team-tenant-id"
	planChangeID := "plan-change-id"
	initiatedByUserID := "admin-user-panic-recovery"

	// Set up the tenant subscription mapping and subscription
	mapping := setupTenantSubscriptionMapping(t, ctx, daoFactory, teamTenantID, true, planChangeID, targetOrbPlanID)
	setupTestSubscription(t, ctx, daoFactory, mapping.OrbSubscriptionId)

	// Create the message
	msg := createTestTeamPlanChangeMessage(teamTenantID, targetOrbPlanID, planChangeID, time.Now(), initiatedByUserID)

	// Set up the mock Orb client properly
	mockOrbClient.On("GetUserSubscription", mock.Anything, mapping.OrbSubscriptionId, &orb.ItemIds{SeatsID: "test-seats-item-id"}).
		Return(&orb.OrbSubscriptionInfo{
			OrbStatus:      "active",
			ExternalPlanID: "orb_pro_plan", // Current plan is pro
			// Currently have 4 seats, scheduled to go down to 2 at end of month
			CurrentFixedQuantities: &orb.FixedQuantities{
				Seats: 4,
			},
			FutureFixedQuantities: &orb.FixedQuantities{
				Seats: 2,
			},
		}, nil)

	// Mock for GetPlanInformation with dynamic responses based on plan ID
	// First call - target plan
	if targetOrbPlanID == "orb_max_plan" {
		mockOrbClient.On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(&orb.OrbPlanInfo{
				ExternalPlanID:          "orb_max_plan",
				Name:                    "Test Max Plan",
				PricePerSeat:            "300.00",
				SeatsPriceID:            "seats-price-id",
				IncludedMessagesPriceID: "included-messages-price-id",
				MessagesPerSeat:         4500,
			}, nil).Once()
	} else if targetOrbPlanID == "orb_developer_plan" {
		mockOrbClient.On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
			Return(&orb.OrbPlanInfo{
				ExternalPlanID:          "orb_developer_plan",
				Name:                    "Test Developer Plan",
				PricePerSeat:            "100.00",
				SeatsPriceID:            "seats-price-id",
				IncludedMessagesPriceID: "included-messages-price-id",
				MessagesPerSeat:         500,
			}, nil).Once()
	}

	// Second call - return current plan info (orb_pro_plan)
	mockOrbClient.On("GetPlanInformation", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(&orb.OrbPlanInfo{
			ExternalPlanID:          "orb_pro_plan",
			Name:                    "Test Pro Plan",
			PricePerSeat:            "200.00",
			SeatsPriceID:            "seats-price-id",
			IncludedMessagesPriceID: "included-messages-price-id",
			MessagesPerSeat:         1000,
		}, nil).Once()

	mockOrbClient.On("SetCustomerPlanType", mock.Anything, mock.Anything, mock.AnythingOfType("*string")).Return(nil)
	mockOrbClient.On("UpdateFixedQuantity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

	// Create a processor with the mock
	auditLogger := audit.NewDefaultAuditLogger()
	processor, err := NewTeamPlanChangeProcessor(
		daoFactory,
		mockOrbClient,
		createTestOrbConfig(),
		featureFlagHandle,
		auditLogger,
		NewTenantMap(
			daoFactory,
			&tw_client.MockTenantWatcherClient{},
			"us-central.api.augmentcode.com",
			featureFlagHandle,
			NewMockAsyncOpsPublisher(),
			auditLogger,
		),
	)
	require.NoError(t, err)

	return cleanup, processor, msg, mockOrbClient
}

func TestTeamPlanChangeProcessor_SeatsInteraction(t *testing.T) {
	t.Run("Upgrade with seats scheduled to go down", func(t *testing.T) {
		cleanup, processor, msg, mockOrbClient := SetupSeatsTest(t, "orb_max_plan")
		defer cleanup()

		// Process the message
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Verify the mock calls
		mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 1)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 2) // Set future credits and seats based on future seat
		mockOrbClient.AssertCalled(t, "UpdateFixedQuantity", mock.Anything, mock.MatchedBy(func(update orb.OrbQuantityUpdate) bool {
			return update.OrbSubscriptionID == "orb_sub_1" &&
				update.PriceOverride.PriceID == "included-messages-price-id" &&
				update.PriceOverride.Quantity == 4500*2 && // 4500 messages per seat, 2 seats
				update.UpdateTimeType == orb.PlanChangeEndOfTerm
		}), mock.Anything)
		mockOrbClient.AssertCalled(t, "UpdateFixedQuantity", mock.Anything, mock.MatchedBy(func(update orb.OrbQuantityUpdate) bool {
			return update.OrbSubscriptionID == "orb_sub_1" &&
				update.PriceOverride.PriceID == "seats-price-id" &&
				update.PriceOverride.Quantity == 2 && // 2 seats
				update.UpdateTimeType == orb.PlanChangeEndOfTerm
		}), mock.Anything)
		mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything, mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
			return planChange.NewPlanID == "orb_max_plan" &&
				planChange.PlanChangeType == orb.PlanChangeImmediate &&
				planChange.BillingCycleAlignment == orb.BillingCycleAlignmentUnchanged
		}), mock.Anything)
	})

	t.Run("Downgrade with seats scheduled to go down", func(t *testing.T) {
		cleanup, processor, msg, mockOrbClient := SetupSeatsTest(t, "orb_developer_plan")
		defer cleanup()

		// Process the message
		err := processor.Process(context.Background(), msg)
		require.NoError(t, err)

		// Verify the mock calls
		mockOrbClient.AssertNumberOfCalls(t, "SetCustomerPlanType", 1)
		mockOrbClient.AssertNumberOfCalls(t, "UpdateFixedQuantity", 0) // Do not do any future updates
		mockOrbClient.AssertCalled(t, "SetCustomerPlanType", mock.Anything, mock.MatchedBy(func(planChange orb.OrbPlanChange) bool {
			return planChange.NewPlanID == "orb_developer_plan" &&
				planChange.PlanChangeType == orb.PlanChangeEndOfTerm && // happens at end of term
				planChange.BillingCycleAlignment == orb.BillingCycleAlignmentUnchanged &&
				len(planChange.PriceOverrides) == 2 &&
				planChange.PriceOverrides[0].PriceID == "seats-price-id" &&
				planChange.PriceOverrides[0].Quantity == 2 && // 2 seats
				planChange.PriceOverrides[1].PriceID == "included-messages-price-id" &&
				planChange.PriceOverrides[1].Quantity == 500*2 // 500 messages per seat, 2 seats
		}), mock.Anything)
	})
}
