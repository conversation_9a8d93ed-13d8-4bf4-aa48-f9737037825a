"""Global authorization service."""

import argparse
import base64
import copy
import functools
import json
import logging
import os
import pathlib
import pydantic
import random
import requests
import re
import secrets
import sys
import threading
from datetime import datetime, timezone
from enum import Enum
from hashlib import sha256
from pathlib import Path
from typing import Any, Protocol, Sequence
from urllib.parse import urlencode, urlparse, parse_qsl

import flask
import grpc
import prometheus_client
import segment.analytics as analytics
from authlib.integrations.flask_client import <PERSON>Auth, OAuthError
from google.cloud import recaptchaenterprise_v1
from google.cloud.recaptchaenterprise_v1 import Assessment
from gunicorn.app.base import BaseApplication
from prometheus_flask_exporter.multiprocess import GunicornPrometheusMetrics
from pydantic import SecretStr

import base.feature_flags
import base.tracing
from services.auth.central.server import auth_entities_pb2
import services.lib.grpc.tls_config.tls_config as tls_config
import services.request_insight.publisher.request_insight_publisher as request_insight_publisher
import services.tenant_watcher.tenant_watcher_pb2 as tenant_watcher_pb2
from base.logging.audit import audit
from base.logging.struct_logging import setup_struct_logging
from services.auth.central.server import (
    auth_pb2,
    front_end_token_service_pb2,
    front_end_token_service_pb2_grpc,
    invitation_service,
)
from services.auth.central.server.auth_entities_pb2 import (
    User,
    UserSuspensionType,
)

from services.auth.central.server.config import Config
from services.auth.central.server.tenant_map import TenantDetails, TenantMap
from services.request_insight import request_insight_pb2
from services.tenant_watcher.client.client import TenantsClient
from services.tenant_watcher.client.tenant_cache import KILL_PID_ON_EXIT_ENV_VAR_NAME

_response_counter = prometheus_client.Counter(
    "au_auth_central_response",
    "Counter of auth central responses",
    ["method", "status"],
)

_signup_callbacks_counter = prometheus_client.Counter(
    "au_auth_central_signup_callback",
    "Counter of auth central signup callbacks",
    ["signup_type", "status"],
)

_login_redirect_uri_counter = prometheus_client.Counter(
    "au_auth_central_login_redirect_uri",
    "Counter of auth central login redirect uris",
    ["uri"],
)

_invitation_counter = prometheus_client.Counter(
    "au_auth_central_invitation",
    "Counter of auth central invitation operations",
    ["operation", "status"],
)

_x_forwarded_for_fail_counter = prometheus_client.Counter(
    "au_auth_central_x_forwarded_for_fails",
    "Counter of auth central x-forwarded-for failures",
)

_captcha_histogram = prometheus_client.Histogram(
    "au_auth_central_captcha_score",
    "Histogram of captcha scores",
    ["operation", "status"],
    buckets=[0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
)

_errors_counter = prometheus_client.Counter(
    "au_auth_central_errors",
    "Counter of auth central errors",
    ["error_type", "detail"],
)

_ops_counter = prometheus_client.Counter(
    "au_auth_central_ops",
    "Counter of auth central operations",
    ["operation", "status"],
)

_SIGNUP_TENANT = base.feature_flags.StringFlag("auth_central_signup_tenant", "")
_INDIVIDUAL_TENANT = base.feature_flags.StringFlag("auth_central_individual_tenant", "")

_SIGNUP_DONE_REDIRECT = base.feature_flags.StringFlag(
    "auth_central_signup_done_redirect", "/echo"
)
_INDIVIDUAL_SIGNUP_DONE_REDIRECT = base.feature_flags.StringFlag(
    "auth_central_individual_redirect", "/echo"
)
_LOGIN_INVITATIONS_ENABLED = base.feature_flags.BoolFlag(
    "auth_central_login_invitations_enabled", True
)

_RECAPTCHA_THRESHOLD = base.feature_flags.FloatFlag(
    "auth_central_recaptcha_threshold", 0.0
)

_ALLOW_SIMILAR_SIGNUPS = base.feature_flags.BoolFlag(
    "auth_central_allow_similar_signups", False
)

_SIMILAR_SIGNUPS_WHITELIST_DOMAINS = base.feature_flags.StringFlag(
    "auth_central_similar_signups_whitelist_domains", ""
)

_VERISOUL_ENABLED = base.feature_flags.BoolFlag("auth_central_verisoul_enabled", False)


class SignupStatus(Enum):
    """Possible return values of do_signup."""

    SIGNUP_LIMIT_REACHED = "signup-limit-reached"
    ALREADY_SIGNED_UP = "already-signed-up"
    IN_ANOTHER_TENANT = "in-another-tenant"
    YOUR_ORG_HAS_AUGMENT = "your-org-has-augment"
    INTERNAL_ERROR = "internal-error"
    INVALID_EMAIL = "invalid-email"


def _token_hash(token: str) -> str:
    return sha256(token.encode("utf-8")).hexdigest()


def _base64_url_encode(data: bytes) -> str:
    """Base64-URL-encode the given data."""
    return base64.urlsafe_b64encode(data).decode("utf-8").replace("=", "")


def _generate_code_challenge(code_verifier: str) -> str:
    return _base64_url_encode(sha256(code_verifier.encode("utf-8")).digest())


def get_candidate_signup_tenant_list(config: Config, is_individual: bool):
    if is_individual:
        tenant_names_str = _INDIVIDUAL_TENANT.get(
            base.feature_flags.get_global_context()
        )
        backup_tenant = config.individual_tenant
    else:
        tenant_names_str = _SIGNUP_TENANT.get(base.feature_flags.get_global_context())
        backup_tenant = config.signup_tenant

    # Handle empty string case
    if not tenant_names_str:
        if backup_tenant is None:
            return []
        return [backup_tenant]

    # Split and filter out empty strings
    tenant_names = [name for name in tenant_names_str.split(",") if name]

    if not tenant_names:
        if backup_tenant is None:
            return []
        return [backup_tenant]

    return tenant_names


def get_signup_tenant(tenant_names: list[str], idp_user_id: str):
    # HACK
    #
    # This is a hack to deterministically select the tenant the user will be placed in
    # to ensure we don't accidently put the user in multiple tenants if this code executes
    # multiple times concurrently.
    #
    # Some might consider this to be "clever" but this is what Google's Engineering book
    # says about "clever" solutions:
    #
    # > We’ve taken to saying, “It’s programming if 'clever' is a compliment,
    # > but it’s software engineering if 'clever' is an accusation.”
    #
    # Create a hash of the idp_user_id so we have a nice distribution of tenants
    # and use it to select a tenant
    hash_value = int(sha256(idp_user_id.encode("utf-8")).hexdigest(), 16)
    tenant_index = hash_value % len(tenant_names)
    tenant_name = tenant_names[tenant_index]
    logging.info(
        "Deterministic signup tenant: %s. Chosen from list: %s based on idp_user_id.",
        tenant_name,
        tenant_names,
    )

    return tenant_name


def check_if_uri_differs_only_by_scheme(
    redirect_uris: Sequence[str], uri: str
) -> str | None:
    """Check if the uri differs only by scheme from any of the redirect uris.

    Returns the scheme name if it does, None otherwise.
    """
    split_uri = uri.split("://", 1)
    if len(split_uri) != 2:
        return None

    for redirect_uri in redirect_uris:
        if redirect_uri.endswith("://" + split_uri[1]):
            return split_uri[0]
    return None


def _find_arg_safe(args: list[tuple[str, str]], arg_name: str):
    """
    Return the argument only if it exists once in the list of arguments.

    Returns None if the argument is not found.

    Raises RuntimeError if the argument is found multiple times.
    """
    found = [arg[1] for arg in args if arg[0] == arg_name]
    if len(found) == 1:
        return found[0]
    elif len(found) > 1:
        raise RuntimeError("Multiple values for {arg_name}")
    else:
        return None


def setup_segment(config: Config):
    """Initialize Segment analytics with configuration."""
    if config.segment and config.segment.enabled:
        analytics.write_key = config.segment.write_key
        analytics.host = config.segment.host


def identify_user(user_id: str, traits: dict, anonymous_id: str | None = None):
    """Identify user in Segment with their traits."""
    if not analytics.write_key:
        return

    try:
        # Remove None values
        traits = {k: v for k, v in traits.items() if v is not None}

        if anonymous_id:
            try:
                # https://segment.com/docs/connections/sources/catalog/libraries/server/python/#alias
                analytics.alias(anonymous_id, user_id)
            except Exception as e:
                logging.exception("Failed to alias user in Segment: %s", e)
        else:
            logging.info(
                "No anonymous ID provided for user %s - skipping alias", user_id
            )

        # Then identify the user with their traits
        analytics.identify(user_id, anonymous_id=anonymous_id, traits=traits)
    except Exception as e:
        logging.exception("Failed to identify user in Segment: %s", e)


def track_event(user_id: str, event_name: str, anonymous_id: str | None = None):
    """Track authentication events in Segment."""
    if not analytics.write_key:
        return

    try:
        analytics.track(user_id, event_name, anonymous_id=anonymous_id)
    except Exception as e:
        logging.exception("Failed to track auth event in Segment: %s", e)


def parse_x_forwarded_for(header: str) -> str:
    """Parse X-Forwarded-For header and return the penultimate IP address.

    The penultimate IP address is the IP address of the client that made the
    request. The last IP address is the IP address of the load balancer.
    Verified this in my dev deploy.

    Other IP addresses are not to be trusted.

    If we add or change proxies in the future, we may need to update this logic.
    """
    ips = header.split(",")
    if len(ips) < 2:
        logging.error(
            "X-Forwarded-For header has fewer than 2 IP addresses: %s", header
        )
        _x_forwarded_for_fail_counter.inc()
        return ""
    return ips[-2].strip()


class CaptchaChecker(Protocol):
    def allow(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        threshold: float,
        user_email: str | None = None,
    ) -> bool: ...

    def site_key(self) -> str | None: ...


class RecaptchaChecker(CaptchaChecker):
    def __init__(
        self,
        project_id: str,
        site_key: str,
        ri_publisher: request_insight_publisher.RequestInsightPublisher | None = None,
    ):
        self._project_id = project_id
        self._site_key = site_key
        self._ri_publisher = ri_publisher

    def create_assessment(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        user_email: str | None = None,
    ) -> float | None:
        """Create an assessment to analyze the risk of a UI action.
        Args:
            project_id: GCloud Project ID
            token: The token obtained from the client on passing the recaptchaSiteKey.
            recaptcha_action: Action name corresponding to the token.
            user_ip_address: IP address of the user sending a request.
            user_agent: User agent is included in the HTTP request in the request header.
            user_email: Email of the user who made the request (if known)
        """

        client = recaptchaenterprise_v1.RecaptchaEnterpriseServiceClient()

        # Set the properties of the event to be tracked.
        event = recaptchaenterprise_v1.Event()
        event.site_key = self._site_key
        event.token = token
        event.user_ip_address = user_ip_address
        event.user_agent = user_agent
        if user_email:
            event.user_info = recaptchaenterprise_v1.UserInfo()
            event.user_info.account_id = user_email

        assessment = recaptchaenterprise_v1.Assessment()
        assessment.event = event

        project_name = f"projects/{self._project_id}"

        # Build the assessment request.
        request = recaptchaenterprise_v1.CreateAssessmentRequest()
        request.assessment = assessment
        request.parent = project_name

        response = client.create_assessment(request)

        # Check if the token is valid.
        if not response.token_properties.valid:
            logging.warning(
                "create_assessment failed because the token was "
                + "invalid for for the following reasons: %s",
                response.token_properties.invalid_reason,
            )
            return None

        # Check if the expected action was executed.
        if response.token_properties.action != recaptcha_action:
            logging.warning(
                "create_assessment action mismatch response=%s action=%s",
                response.token_properties.action,
                recaptcha_action,
            )
            return None

        assessment_name = client.parse_assessment_path(response.name).get("assessment")

        logging.info(
            "create_assessment email: %s name: %s score: %s reasons: %s",
            user_email,
            assessment_name,
            response.risk_analysis.score,
            list(response.risk_analysis.reasons),
        )

        event = request_insight_publisher.new_generic_event()
        recaptcha_event = event.recaptcha
        if user_email is not None:
            recaptcha_event.email = user_email
        recaptcha_event.assessment_name = assessment_name or ""
        recaptcha_event.assessment_reasons.extend(
            [str(r) for r in response.risk_analysis.reasons]
        )
        recaptcha_event.score = response.risk_analysis.score
        recaptcha_event.action = recaptcha_action
        recaptcha_event.user_agent = user_agent
        recaptcha_event.source_ip = user_ip_address

        if self._ri_publisher is not None:
            self._ri_publisher.publish_generic_events(events=[event])

        return response.risk_analysis.score

    def allow(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        threshold: float,
        user_email: str | None = None,
    ) -> bool:
        if threshold <= 0.0:
            return True

        try:
            score = self.create_assessment(
                token, recaptcha_action, user_ip_address, user_agent, user_email
            )
        except Exception as e:
            logging.exception("create_assessment failed: %s", e)
            # Fail open if we can't contact recaptcha
            _captcha_histogram.labels(recaptcha_action, "failed").observe(1.0)
            return True

        # Fail closed
        if score is None:
            _captcha_histogram.labels(recaptcha_action, "failed").observe(0.0)
            return False

        _captcha_histogram.labels(recaptcha_action, "success").observe(score)
        return score >= threshold

    def site_key(self) -> str | None:
        return self._site_key


class NullRecaptchaChecker(CaptchaChecker):
    def allow(
        self,
        token: str,
        recaptcha_action: str,
        user_ip_address: str,
        user_agent: str,
        threshold: float,
        user_email: str | None = None,
    ) -> bool:
        return True

    def site_key(self) -> str | None:
        return None


class HttpResponse:
    def __init__(self, status_code: int, body: str):
        self.status_code = status_code
        self.body = body


class HttpClient:
    def post(
        self, url: str, headers: dict[str, str], json: Any, timeout: float
    ) -> HttpResponse:
        response = requests.post(url, headers=headers, json=json, timeout=timeout)
        return HttpResponse(response.status_code, response.text)


class VerisoulChecker:
    def __init__(
        self,
        api_key: pydantic.SecretStr,
        env: str,
        http_client: HttpClient | None = None,
        ri_publisher: request_insight_publisher.RequestInsightPublisher | None = None,
    ):
        self._api_key = api_key
        self._env = env
        self._ri_publisher = ri_publisher
        self._http_client = http_client or HttpClient()

    def allow(
        self,
        session_id: str,
        user_id: str,
        user_email: str,
        timeout: float = 3.0,
    ) -> bool:
        if not _VERISOUL_ENABLED.get(base.feature_flags.get_global_context()):
            return True

        account: dict[str, Any] = {
            "id": user_id,
            "email": user_email,
        }

        logging.info("Verisoul check user id %s", user_id)

        try:
            raw_response = self._http_client.post(
                url=f"https://api.{self._env}.verisoul.ai/session/authenticate",
                headers={"x-api-key": self._api_key.get_secret_value()},
                json={"session_id": session_id, "account": account},
                timeout=timeout,
            )

            if raw_response.status_code != 200:
                logging.warning(
                    "Verisoul check %s session id %s failed with status code %d: %s",
                    user_id,
                    session_id,
                    raw_response.status_code,
                    raw_response.body,
                )
                _errors_counter.labels(
                    "verisoul_http_request", str(raw_response.status_code)
                ).inc()
                return False
        except Exception as e:
            logging.warning(
                "Verisoul check %s session id %s failed with exception: %s",
                user_id,
                session_id,
                e,
            )
            _errors_counter.labels("verisoul", "request_exception").inc()
            # Fail open if we failed to contact Verisoul
            return True

        try:
            _ = json.loads(raw_response.body)
        except Exception as e:
            logging.warning(
                "Verisoul check %s session id %s failed to parse response: %s %s",
                user_email,
                session_id,
                e,
                raw_response.body,
            )
            _errors_counter.labels("verisoul", "parse_response").inc()
            # Fail open in this case
            return True

        try:
            event = request_insight_publisher.new_generic_event()
            event.verisoul.CopyFrom(
                request_insight_pb2.Verisoul(
                    opaque_user_id=auth_entities_pb2.UserId(
                        user_id_type=auth_entities_pb2.UserId.UserIdType.AUGMENT,
                        user_id=user_id,
                    ),
                    report=raw_response.body,
                )
            )
            if self._ri_publisher is not None:
                self._ri_publisher.publish_generic_events(events=[event])
                _ops_counter.labels("verisoul", "ri_publish").inc()

        except Exception as e:
            logging.warning(
                "Verisoul check %s session id %s failed to publish to request insight: %s",
                user_id,
                session_id,
                e,
            )
            _errors_counter.labels("verisoul", "ri_publish").inc()

        _ops_counter.labels("verisoul", "success").inc()
        logging.info("Verisoul check success %s", user_id)
        return True


def oauth_error_to_user_message(
    library_error: str,
    library_description: str | None,
    error: str | None,
    description: str | None,
) -> tuple[str, str]:
    message = "Sorry, we encountered an unexpected error."
    details = f"Details: error: {library_error} description: {library_description} time:{datetime.now(timezone.utc)}"

    if error is not None:
        details = f"Details: error: {error} description: {description} time:{datetime.now(timezone.utc)}"

        if error == "access_denied":
            message = "Sign in failed: the system that you used to sign in refused the sign in."

            if description is not None:
                if description.startswith("[DENY]"):
                    message = "Sign up failed: your login did not pass checks."
                    details = f"time:{datetime.now(timezone.utc)}"
                elif "not assigned" in description:
                    message = "Sign in failed: you have not been assigned access to Augment in your organization's sign in system."
                    details = f"time:{datetime.now(timezone.utc)}"

    return message, details


def create_app(
    config: Config,
    secret_key: SecretStr,
    prometheus: bool = True,
    disable_secure_cookie_for_testing: bool = False,
    captcha_checker_inject: CaptchaChecker | None = None,
):
    app = flask.Flask(__name__)

    # Create gRPC channel to the backend
    backend_channel = grpc.insecure_channel(f"localhost:{config.backend_port}")
    front_end_token_service = front_end_token_service_pb2_grpc.FrontEndTokenServiceStub(
        backend_channel
    )

    ri_publisher = (
        request_insight_publisher.RequestInsightPublisher.create_from_file(
            pathlib.Path(config.request_insight_publisher_config_path)
        )
        if config.request_insight_publisher_config_path
        else None
    )

    # Load reCAPTCHA keys from secret manager if configured
    if captcha_checker_inject is not None:
        captcha_checker = captcha_checker_inject
    else:
        captcha_checker = NullRecaptchaChecker()

        if config.recaptcha_config is not None:
            try:
                recaptcha_json = pathlib.Path(
                    config.recaptcha_config.keys_path
                ).read_text(encoding="utf-8")
                recaptcha_config = json.loads(recaptcha_json)
                site_key = recaptcha_config.get("site_key", "")

                logging.info(
                    "Initializing reCAPTCHA checker with site key starting with %s",
                    site_key[0:10],
                )
                captcha_checker = RecaptchaChecker(
                    config.recaptcha_config.project_id, site_key, ri_publisher
                )
            except Exception as e:
                logging.error(f"Failed to load reCAPTCHA keys: {e}")

    verisoul = config.verisoul
    verisoul_checker: VerisoulChecker | None = None
    if verisoul is not None:
        logging.info("Initializing Verisoul with project id %s", verisoul.project_id)
        verisoul_api_key = pydantic.SecretStr(
            pathlib.Path(verisoul.api_key_path).read_text(encoding="utf-8").strip()
        )

        verisoul_checker = VerisoulChecker(
            verisoul_api_key, verisoul.env, ri_publisher=ri_publisher
        )

    invitation_service.initialize(front_end_token_service)

    app.config.update(
        # Used for secure session cookies
        SECRET_KEY=secret_key.get_secret_value(),
        SESSION_COOKIE_SECURE=True,
        SESSION_COOKIE_HTTPONLY=True,
        SESSION_COOKIE_SAMESITE="Lax",
        augment_override_authorize_access_token=None,
        # PERMANENT_SESSION_LIFETIME=2678400  # 31 days in seconds
    )

    if disable_secure_cookie_for_testing:
        logging.warning("DISABLING SECURE COOKIE FOR INTERNAL TESTING")
        app.config.update(
            SESSION_COOKIE_SECURE=False,
        )

    # Initialize Auth0
    auth0_oauth = OAuth(app)

    # Initialize Segment
    setup_segment(config)

    # Register the login app Auth0 client
    if config.login_auth0 is not None:
        client_id, client_secret = map(
            lambda x: x.strip(),
            pathlib.Path(config.login_auth0.credentials_path)
            .read_text(encoding="utf-8")
            .splitlines()[0:2],
        )
        auth0_oauth.register(
            "auth0_login",
            client_id=client_id,
            client_secret=client_secret,
            client_kwargs={"scope": "openid email profile"},
            authorize_params={"prompt": "login"},
            server_metadata_url=config.login_auth0.server_metadata_url,
        )

    # Register the signup app Auth0 client
    if config.signup_auth0 is not None:
        client_id, client_secret = map(
            lambda x: x.strip(),
            pathlib.Path(config.signup_auth0.credentials_path)
            .read_text(encoding="utf-8")
            .splitlines()[0:2],
        )
        auth0_oauth.register(
            "auth0_signup",
            client_id=client_id,
            client_secret=client_secret,
            client_kwargs={"scope": "openid email profile"},
            authorize_params={"prompt": "login"},
            server_metadata_url=config.signup_auth0.server_metadata_url or None,
        )

    audit_logger = audit.AuditLogger()

    if prometheus:
        _ = GunicornPrometheusMetrics(app, port=9090, group_by="endpoint")

    client_credentials = tls_config.get_client_tls_creds(config.grpc.client_mtls)
    tenant_watcher_client = TenantsClient(
        config.tenant_watcher.tenant_watcher_endpoint, client_credentials
    )

    staging_user_email_match = (
        re.compile(config.staging_user_email_regex)
        if config.staging_user_email_regex
        else None
    )

    tenant_map = TenantMap(
        tenant_watcher_client=tenant_watcher_client,
        api_proxy_hostname_domain=config.tenant_watcher.api_proxy_hostname_domain,
    )

    tenant_map_loaded = threading.Event()  # using Event as thread-safe boolean

    dev_callback_url = config.dev_callback_url

    def clear_auth_session_state():
        # Clear our auth state but keep the authlib session state around so
        # we don't get strange CSRF errors
        flask.session.pop("user_id", None)
        flask.session.pop("idp_user_id", None)
        flask.session.pop("user_email", None)
        flask.session.pop("email", None)
        flask.session.pop("nonce", None)
        flask.session.pop("individual", None)
        flask.session.pop("in_usa", None)

    @app.route("/health")
    def health():  # pylint: disable=redefined-outer-name
        # Wait for tenant map to load before declaring healthy
        #
        # Can't call into tenant_map in the body of create_app() because tenant_map
        # will launch a background thread, which won't be inherited across the fork.
        if not tenant_map_loaded.is_set():
            logging.info("Waiting for tenant map to load")
            _ = tenant_map.get_tenant("dummy")
            logging.info("Tenant map loaded")
            tenant_map_loaded.set()
        return "Ok"

    def validate_common_client_request_args(
        label: str, args: list[tuple[str, str]], test_ok: bool = False
    ):
        """
        Validate the common arguments expected from the client.
        """

        def find_arg(arg_name: str):
            return _find_arg_safe(args, arg_name)

        if len(args) != len(set([arg[0] for arg in args])):
            _response_counter.labels(label, "duplicate_args").inc()
            return _redirect_to_client_error(
                None,
                "invalid_request",
                error_description="Duplicate argument",
            )

        if test_ok and find_arg("test") is not None:
            return None

        client_id = find_arg("client_id")
        if not client_id:
            _response_counter.labels(label, "no_client_id").inc()
            return _redirect_to_client_error(
                None,
                "invalid_request",
                error_description="No client id defined",
            )

        # If a redirect isn't provided, default to the empty string. This is
        # helpful for vim as the redirect isn't used and not providing the
        # argument allows the auth url to be shorter. For other clients, the
        # empty string is invalid and will be caught below in the check for
        # valid uris.
        redirect_uri = find_arg("redirect_uri")
        if redirect_uri is None:
            redirect_uri = ""

        # Check that the client ID is valid
        client_config = config.client_config_map.get(client_id)
        if not client_config:
            _response_counter.labels(label, "invalid_client_id").inc()
            return _redirect_to_client_error(
                None,
                "unauthorized_client",
                error_description="client id is invalid",
            )

        client_name = client_config.name

        # Check that the redirect URI is valid
        # All errors above this point are errors that should NOT redirect back to the client as we
        # don't trust the client.
        uri_to_check = redirect_uri
        if uri_to_check in client_config.redirect_uris:
            return None

        if redirect_uri.startswith("http://127.0.0.1:") or redirect_uri.startswith(
            "http://localhost:"
        ):
            uri_to_check = _remove_port_from_url(redirect_uri)

        _login_redirect_uri_counter.labels(uri_to_check).inc()
        if uri_to_check not in client_config.redirect_uris:
            logging.error(
                "Redirect URI %s not in client config %s",
                uri_to_check,
                client_config.redirect_uris,
            )

            # Since we allow the empty string as a valid redirect_uri for the
            # vim client, perform this check here rather than earlier in the
            # client request args validation check.
            if redirect_uri == "":
                return _redirect_to_client_error(
                    None,
                    "invalid_request",
                    error_description="No redirect defined",
                )

            scheme_name = check_if_uri_differs_only_by_scheme(
                client_config.redirect_uris, uri_to_check
            )

            if scheme_name and scheme_name not in ["http", "https"]:
                _response_counter.labels(label, "unsupported_editor").inc()
                return _redirect_to_client_error(
                    None,
                    "invalid_request",
                    error_description=f"Unsupported editor {scheme_name}",
                    client_name=client_name,
                )

            _response_counter.labels(label, "invalid_redirect_uri").inc()

            return _redirect_to_client_error(
                None,
                "invalid_request",
                error_description="redirect uri is incorrect",
                client_name=client_name,
            )
        return None

    def validate_request_args(args: list[tuple[str, str]], test_ok: bool = False):
        """
        # Validate the request according to [RFC 6749, section 5.2](
        # https://datatracker.ietf.org/doc/html/rfc6749#section-5.2)

        # First, check if the redirect URI is present. We can't do much without
        # it. Even errors are meant to be returned to the client using the redirect
        # URI if possible.
        """

        def find_arg(arg_name: str):
            return _find_arg_safe(args, arg_name)

        if test_ok and find_arg("test") is not None:
            return None

        response = validate_common_client_request_args(
            label="authorize", args=args, test_ok=test_ok
        )
        if response is not None:
            # failed validation
            return response

        # Extract the remaining required query parameters
        # Note: client_name and redirect_uri were validated in the common
        # request args validation check.
        client_id = find_arg("client_id")
        assert client_id is not None
        client_config = config.client_config_map.get(client_id)
        assert client_config is not None
        client_name = client_config.name

        redirect_uri = find_arg("redirect_uri")
        response_type = find_arg("response_type")
        code_challenge_method = find_arg("code_challenge_method")
        code_challenge = find_arg("code_challenge")
        state = find_arg("state")

        # The invitations client doesn't use PKCE
        if client_id == "invitations":
            return None

        if not (response_type and code_challenge and state):
            logging.error("Missing required request args: %s", args)
            _response_counter.labels("authorize", "missing_args").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "invalid_request",
                error_description="Missing argument",
            )

        # If the code challenge method isn't specified we'll default to S256
        if code_challenge_method is not None and code_challenge_method != "S256":
            _response_counter.labels("authorize", "invalid_challenge_method").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "invalid_request",
                error_description="Unsupported code challenge method",
            )

        if len(code_challenge) != 43:
            _response_counter.labels("authorize", "invalid_challenge").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "invalid_request",
                error_description="Invalid code challenge",
            )

        # Check that the response type is "code"
        if response_type != "code":
            _response_counter.labels("authorize", "invalid_response_type").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "unsupported_response_type",
                client_name=client_name,
            )

        return None

    def render(template_name: str, enable_fingerprinting: bool = False, **kwargs: Any):
        """Wrap flask.render_template and provide common parameters."""

        if "year" not in kwargs:
            kwargs["year"] = datetime.now(timezone.utc).year

        site_key = captcha_checker.site_key()
        if enable_fingerprinting:
            if "recaptcha_site_key" not in kwargs and site_key:
                kwargs["recaptcha_site_key"] = site_key

            if (
                _VERISOUL_ENABLED.get(base.feature_flags.get_global_context())
                and verisoul is not None
            ):
                kwargs["verisoul"] = {
                    "env": verisoul.env,
                    "project_id": verisoul.project_id,
                }

        return flask.render_template(template_name, **kwargs)

    @app.route("/logout", methods=["GET"])
    def logout():
        """Endpoint used by the client to log the user out.

        Clears the session cookie and redirects to a validated client url.
        """
        # See https://auth0.com/docs/authenticate/login/logout for an example of
        # validating arguments to the logout request.
        logging.debug("Logging out")
        args = list(flask.request.args.items(True))
        response = validate_common_client_request_args("logout", args, False)
        if response is not None:
            return response
        redirect_uri = flask.request.args.get("redirect_uri")
        assert redirect_uri is not None

        clear_auth_session_state()

        return flask.redirect(redirect_uri)

    def identity_provider_allowed(
        user_id: str, allowed_identity_providers: Sequence[str]
    ):
        return len(allowed_identity_providers) == 0 or any(
            user_id.startswith(idp + "|") for idp in allowed_identity_providers
        )

    @app.route("/terms-accept", methods=["POST", "GET"])
    def terms_accept():
        """Accept terms using session cookies.

        Main responsibilities:
            - check that the user maps to a tenant. If not return error
            - display page asking user to accept terms of service
            - process user acceptance of terms of service
        """

        user_email = flask.session.get("user_email")
        idp_user_id = flask.session.get("idp_user_id")

        return accept_internal(
            args=list(flask.request.args.items(True)),
            idp_user_id=idp_user_id,
            user_email=user_email,
            route="terms_accept",
        )

    def user_has_blocking_suspension(user: User):
        """Check if a user has any suspensions that should block login.

        Args:
            user: The user object to check

        Returns:
            bool: True if the user has any blocking suspensions, False otherwise
        """
        if not hasattr(user, "suspensions"):
            return False

        return any(
            suspension.suspension_type
            == UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
            for suspension in user.suspensions
        )

    def check_and_redirect_for_invitations(user_email: str, continue_url: str):
        """Check if a user has pending invitations and redirect if needed.

        Args:
            user_email: The email address to check for invitations
            continue_url: The complete URL to return to after handling invitations

        Returns:
            A redirect response if invitations exist, None otherwise
        """
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return None

        logging.info(f"Checking if user {user_email} has any pending invitations")
        if invitation_service.has_invitations(user_email):
            logging.info(
                f"User {user_email} has pending invitations, redirecting to invitations page"
            )
            # Redirect to invitations page with continue_url
            invitations_url = flask.url_for(
                "invitations_page",
                continue_url=continue_url,
                _external=True,
            )
            return flask.redirect(invitations_url)

        return None

    def accept_internal(
        args: list[tuple[str, str]],
        idp_user_id: str | None,
        user_email: str | None,
        route: str,
    ):
        """Internal implementation of the /terms-accept endpoint.

        This internal version is shared between the /terms-accept endpoint and the
        /authorize endpoint. The customer-ui uses a long-lived authentication
        cookie that, when valid and present in /authorize, will skip right to
        the "accept" workflow. We bypass the endpoint because of proxying code
        in the nginx config.

        Main responsibilities:
            - check that the user maps to a tenant. If not return error
            - display page asking user to accept terms of service
            - process user acceptance of terms of service

        Args:
            idp_user_id (str): The user ID from the identity provider.
            user_email (str): The user's email address.
        """
        try:
            return _accept_internal(args, idp_user_id, user_email, route)
        except Exception:
            clear_auth_session_state()
            raise

    def _accept_internal(
        args: list[tuple[str, str]],
        idp_user_id: str | None,
        user_email: str | None,
        route: str,
    ):
        def find_arg(arg_name: str):
            return _find_arg_safe(args, arg_name)

        logging.info("Checking accept request arguments %s %s", idp_user_id, user_email)

        if user_email is None or idp_user_id is None:
            return (
                render(
                    "unauthenticated.html",
                    user_message="Sorry, we encountered an unexpected error.",
                    try_again="/login?" + urlencode(args),
                ),
                401,
            )

        if find_arg("test") is not None:
            return "Test succeeded"

        response = validate_request_args(args)
        if response is not None:
            return response

        client_id = find_arg("client_id")
        # We validated in args validation
        assert client_id is not None
        is_customer_ui = client_id == "customer-ui"

        login_query_string = urlencode(args)

        staging_url = config.staging_url
        if (
            flask.request.method == "GET"
            and staging_url is not None
            and staging_user_email_match is not None
            and staging_user_email_match.match(user_email)
        ):
            redirect_uri = find_arg("redirect_uri")

            if redirect_uri is not None and redirect_uri.startswith("https://"):
                try:
                    server_name = urlparse(redirect_uri).netloc
                except Exception:
                    server_name = "unknown"

                return (
                    render(
                        "unauthenticated.html",
                        user_message="Sorry, you can't use your staging login with "
                        + server_name
                        + ". Please start at the staging server or use a"
                        " production login.",
                    ),
                    401,
                )

            # This user is trying to log in to a staging tenant. Send them to
            # the staging auth-central, rather than sending them through prod's
            # auth-central
            logging.info("Redirecting to staging auth-central %s", staging_url)
            redirect_url = f"{staging_url}/login?{login_query_string}"
            return render(
                "redirect.html",
                delay_ms=1200,
                redirect_url=redirect_url,
            )

        if (
            flask.request.method == "POST"
            and flask.request.form.get("sign_up") == "true"
            and flask.request.form.get("continue") == "continue"
        ):
            if not captcha_checker.allow(
                flask.request.form.get("g-recaptcha-response", ""),
                "signup",
                parse_x_forwarded_for(flask.request.headers.get("X-Forwarded-For", "")),
                flask.request.headers.get("User-Agent", ""),
                _RECAPTCHA_THRESHOLD.get(base.feature_flags.get_global_context()),
                user_email,
            ):
                _signup_callbacks_counter.labels("individual", "captcha-failed").inc()
                return render(
                    "unauthenticated.html",
                    user_message="Sorry, we were unable to verify you are human.",
                    try_again="/login?" + urlencode(args),
                )

            if verisoul_checker is not None and not verisoul_checker.allow(
                flask.request.form.get("verisoul-session-id", ""),
                idp_user_id,
                user_email,
            ):
                _signup_callbacks_counter.labels("individual", "verisoul-failed").inc()
                return render(
                    "unauthenticated.html",
                    user_message="Sorry, we were unable to verify you are human.",
                    try_again="/login?" + urlencode(args),
                )

            (user, tenant, status) = do_signup(
                is_individual=True,
                in_usa=False,
                idp_user_id=idp_user_id,
                email=user_email,
            )

            _signup_callbacks_counter.labels(
                "individual", status.value if status is not None else "success"
            ).inc()

            if (
                status == SignupStatus.ALREADY_SIGNED_UP
                and user is not None
                and tenant is not None
            ):
                # Maybe user signed up in a different login flow?
                pass
            elif status is not None:
                user_message = f"Sorry, we were unable to sign you up: {status}"

                if status == SignupStatus.SIGNUP_LIMIT_REACHED:
                    user_message = "Sorry, we were unable to sign you up. We have reached our signup limit. Please try again in a few hours."
                elif status == SignupStatus.YOUR_ORG_HAS_AUGMENT:
                    # Shouldn't happen because we're in the sign-in flow
                    user_message = "Sorry, we were unable to sign you up. Your email belongs to an organization that already has an Augment account. Please contact your administrator."
                elif status == SignupStatus.IN_ANOTHER_TENANT:
                    # Shouldn't happen because we're in the sign-in flow
                    user_message = "Sorry, we were unable to sign you up. You are already signed up for Augment in a different plan or organization. Please contact support if you have any questions."
                elif status == SignupStatus.ALREADY_SIGNED_UP:
                    user_message = "Sorry, we were unable to sign you up. You are already signed up for Augment. Please contact support if you have any questions."
                elif status == SignupStatus.INVALID_EMAIL:
                    user_message = "Sorry, we were unable to sign you up. Your e-mail address was missing or invalid."

                return render(
                    "unauthenticated.html",
                    user_message=user_message,
                )

            assert tenant is not None
            assert user is not None

            tenant = tenant_map.tenant_to_tenant_details(tenant)
        else:
            # Table scan lies within!
            try:
                user = front_end_token_service.GetUser(
                    front_end_token_service_pb2.GetUserRequest(
                        idp_user_id=idp_user_id,
                        email_address=user_email,
                    )
                ).user

                # Check if user is blocked
                if user and (user.blocked or user_has_blocking_suspension(user)):
                    logging.warning("Blocked user attempted to log in: %s", user_email)
                    return (
                        render(
                            "unauthenticated.html",
                            user_message=f"Your account has been blocked. User ID {user.id}. Please contact support for assistance.",
                        ),
                        403,
                    )

                # Check for invitations for existing users
                continue_url = f"{flask.url_for(route)}?{login_query_string}"
                invitation_redirect = check_and_redirect_for_invitations(
                    user_email, continue_url
                )
                if invitation_redirect:
                    return invitation_redirect

            except grpc.RpcError as e:
                logging.error("Failed to get user: %s", e)
                raise

            tenant_from_email = tenant_map.get_tenant_for_email_domain(user_email)
            ensure_user_in_tenant_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_REQUIRE_EXISTING

            if user is not None and len(user.tenants) > 0:
                # Get tenant information for every tenant the user belongs to.
                tenants_from_user = []
                for tenant_id in user.tenants:
                    tenant = tenant_map.get_tenant_by_id(tenant_id)
                    if tenant is not None:
                        tenants_from_user.append(tenant)
                    else:
                        logging.warning(
                            "User is in tenant %s but tenant not found",
                            tenant_id,
                        )

                if len(tenants_from_user) == 0:
                    logging.warning(
                        "User is in no valid tenants: %s",
                        user.tenants,
                    )
                    _response_counter.labels("accept", "deleted_tenant").inc()
                    return (
                        render(
                            "unauthenticated.html",
                            user_message="We couldn't find the organization you are part of. Please contact support.",
                            details=f"Details: time:{datetime.now(timezone.utc)}",
                        ),
                        401,
                    )

                # Choose a self-serve team tenant if there is one. The need for this should be
                # temporary, while self-serve teams are all manually created and added to. Once
                # they're created automatically it should be enforced that a user can't belong to
                # more than one tenant.
                tenant_from_user = None
                for tenant in tenants_from_user:
                    if (
                        tenant.config
                        and tenant.config.configs.get("is_self_serve_team") == "true"
                    ):
                        logging.info(
                            "Choosing self-serve team tenant %s for user %s (total tenants %d)",
                            tenant.name,
                            user_email,
                            len(tenants_from_user),
                        )
                        tenant_from_user = tenant
                        break
                # If there wasn't a self-serve team tenant, just choose an arbitrary tenant.
                if tenant_from_user is None:
                    tenant_from_user = tenants_from_user[0]
            else:
                tenant_from_user = None

            if (
                tenant_from_user is not None
                and tenant_from_user.tier == tenant_watcher_pb2.TenantTier.ENTERPRISE
            ):
                # This allows us to move some enterprise users to a test
                # namespace to test new features.
                tenant = tenant_from_user or tenant_from_email
            else:
                # Email address tenant wins over user's current tenant.
                # Used to simplify migrations between tenants for sales.
                # When we have bulk move, we can probably remove this.
                if tenant_from_email:
                    # Email domain tenant exists
                    tenant = tenant_from_email
                    # Add user to tenant if they previously didn't have one
                    if tenant_from_user is None:
                        ensure_user_in_tenant_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY
                    else:
                        # Move user from their current tenant to the email domain tenant (expected for self-serve users migrating to enterprise via domain capturing)
                        ensure_user_in_tenant_mode = front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_OVERWRITE
                else:
                    # Fall back to user's current tenant (if any)
                    tenant = tenant_from_user

            if tenant is not None:
                tenant = tenant_map.tenant_to_tenant_details(tenant)

                if not identity_provider_allowed(
                    idp_user_id, tenant.allowed_identity_providers
                ):
                    logging.warning("User used blocked identity provider")
                    return (
                        render(
                            "unauthenticated.html",
                            user_message="Your organization does not allow login through this method. Please try another method.",
                            try_again=f"{flask.url_for('login')}?{login_query_string}",
                        ),
                        401,
                    )

                logging.info("Ensuring user is in tenant")
                try:
                    request = front_end_token_service_pb2.EnsureUserInTenantRequest(
                        email_address=user_email,
                        tenant_id=tenant.tenant_id,
                        idp_user_id=idp_user_id,
                        mode=ensure_user_in_tenant_mode,
                    )
                    if user:
                        request.augment_user_id = user.id
                    user = front_end_token_service.EnsureUserInTenant(request).user
                except grpc.RpcError as e:
                    logging.error("Failed to ensure user in tenant: %s", e)
                    raise

            else:
                assert tenant is None

                _response_counter.labels("accept", "no_tenant").inc()
                logging.warning("Unable to find tenant")

                # Check for invitations when no tenant is found
                continue_url = f"{flask.url_for(route)}?{login_query_string}"
                invitation_redirect = check_and_redirect_for_invitations(
                    user_email, continue_url
                )
                if invitation_redirect:
                    return invitation_redirect

                return (
                    render(
                        "not_signed_up.html",
                        email=user_email,
                        post_url=f"{flask.url_for(route)}?{login_query_string}",
                        login_url=f"{flask.url_for('login')}?{login_query_string}",
                        enable_fingerprinting=True,
                    ),
                    401,
                )

        logging.info("Found tenant: %s", tenant.name)

        signin_method = flask.request.form.get("continue")
        terms_of_service = flask.request.form.get("terms-of-service")

        def serve_accept(tenant: TenantDetails, user_warning: str | None = None):
            return render(
                "accept.html",
                user_warning=user_warning,
                post_url=f"{flask.url_for(route)}?{login_query_string}",
                community_tos=tenant.community_tos,
                enable_fingerprinting=not tenant.enterprise,
            )

        # Check if user previously agreed to terms of service
        # Update these strings when the terms of service change
        tnc_revision = (
            "vangaurd_tos_306690148_v6"
            if tenant.community_tos
            else "enterprise_tos_295389227_v13"
        )
        logging.info("Checking terms of service for user %s", user_email)
        try:
            terms = front_end_token_service.GetTermsApproval(
                front_end_token_service_pb2.GetTermsApprovalRequest(
                    email=user_email, revision=tnc_revision
                )
            )
            approved = terms.approved
        except grpc.RpcError as e:
            logging.error("Failed to get terms approval: %s", e)
            raise

        if approved and signin_method is None:
            _response_counter.labels("tnc_approval", "prior_approval").inc()
            signin_method = "continue"
            terms_of_service = "accepted"
        elif flask.request.method == "POST" and terms_of_service == "accepted":
            captcha_response = flask.request.form.get("g-recaptcha-response", "")
            sign_up = flask.request.form.get("sign_up") == "true"

            if (
                not sign_up
                and not tenant.enterprise
                and not captcha_checker.allow(
                    captcha_response,
                    "accept",
                    parse_x_forwarded_for(
                        flask.request.headers.get("X-Forwarded-For", "")
                    ),
                    flask.request.headers.get("User-Agent", ""),
                    _RECAPTCHA_THRESHOLD.get(base.feature_flags.get_global_context()),
                    user_email,
                )
            ):
                _response_counter.labels("tnc_approval", "captcha_failed").inc()
                return serve_accept(
                    tenant,
                    "Your browser's behavior looks like a bot. Please try again.",
                )

            if (
                verisoul_checker is not None
                and not sign_up
                and not tenant.enterprise
                and not verisoul_checker.allow(
                    flask.request.form.get("verisoul-session-id", ""),
                    idp_user_id,
                    user_email,
                )
            ):
                _signup_callbacks_counter.labels("individual", "verisoul-failed").inc()
                return render(
                    "unauthenticated.html",
                    user_message="Sorry, we were unable to verify you are human.",
                    try_again="/login?" + urlencode(args),
                )

            _response_counter.labels("tnc_approval", "new_approval").inc()
            front_end_token_service.SetTermsApproval(
                front_end_token_service_pb2.SetTermsApprovalRequest(
                    email=user_email, revision=tnc_revision, approved=True
                )
            )
        elif flask.request.method == "GET" or signin_method is None:
            return serve_accept(tenant)

        if terms_of_service is None or terms_of_service != "accepted":
            return serve_accept(
                tenant, "You must accept the terms of service to use Augment."
            )

        if signin_method != "continue":
            return serve_accept(
                tenant, "We encountered an unexpected error, please try again."
            )

        if user is None:
            raise RuntimeError("Internal error - user is None should not happen")

        is_vim_client = client_id == "augment-vim-extension" or client_id == "v"

        ajs_anonymous_id = flask.request.cookies.get("ajs_anonymous_id")
        identify_user(
            user.id,
            {
                "email": user.email,
            },
            ajs_anonymous_id,
        )

        track_event(user.id, "User Logged In", ajs_anonymous_id)

        # Set up a long-lived session. Currently, the session is only used to
        # prevent repeated login for the customer UI.
        logging.info("Updating session for user %s with id %s", user.email, user.id)

        flask.session["idp_user_id"] = idp_user_id
        flask.session["user_email"] = user.email
        flask.session["user_id"] = user.id
        flask.session["nonce"] = user.nonce

        # "permanent" defaults to 31 days but can be configured using
        # PERMANENT_SESSION_LIFETIME configuration key.
        # https://flask.palletsprojects.com/en/stable/api/#sessions
        flask.session.permanent = True

        if client_id == "invitations":
            # Use the configured customer UI URL if available, otherwise fall back to hardcoded URL
            customer_ui_url = config.customer_ui_url
            return flask.redirect(
                location=f"{customer_ui_url}/login",
                code=302,
            )

        logging.info("Checking authorize request arguments")

        client_config = config.client_config_map[client_id]
        client_name = client_config.name

        redirect_uri = find_arg("redirect_uri")
        # For vim we don't use the redirect_uri so it may not be present. In
        # this case, default to an empty string.
        if redirect_uri is None and is_vim_client:
            redirect_uri = ""
        assert redirect_uri is not None

        state = find_arg("state")
        assert state is not None

        code_challenge = find_arg("code_challenge")
        assert code_challenge is not None

        tenant_id = tenant.tenant_id

        logging.info("Checking that the user is authorized")

        try:
            # Generate an authorization code and store it in the database
            code = "_" + secrets.token_hex(nbytes=16)

            front_end_token_service.CreateCode(
                front_end_token_service_pb2.CreateCodeRequest(
                    code=code,
                    email=user_email,
                    idp_user_id=idp_user_id,
                    augment_user_id=user.id,
                    client_id=client_id,
                    tenant_id=tenant_id,
                    redirect_uri=redirect_uri,
                    code_challenge=code_challenge,
                )
            )
        except RuntimeError as e:
            logging.exception("Error while authorizing user: %s", e)
            _response_counter.labels("authorize", "server_error").inc()
            return _redirect_to_client_error(
                redirect_uri,
                "server_error",
                error_description="Internal server error",
                client_name=client_name,
            )

        logging.info("User is authorized")
        _response_counter.labels("authorize", "success").inc()

        # Redirect the user to the redirect URL with the code and state
        # Note: the customer-ui will be redirected to auth-central. In the future,
        # the customer-ui will be updated to ignore the tenant_url.
        tenant_url = config.auth_url if is_customer_ui else tenant.tenant_url

        if client_config.instant_redirect:
            return _redirect_to_client_no_transition_page(
                redirect_uri,
                {"code": code, "state": state, "tenant_url": tenant_url},
            )
        elif is_vim_client:
            return render(
                "complete.html",
                code=code,
                state=state,
                tenant_url=tenant_url,
            )
        else:
            return _redirect_to_client(
                redirect_uri,
                {"code": code, "state": state, "tenant_url": tenant_url},
                client_name=client_name,
            )

    @app.route("/authorize", methods=["GET"])
    def authorize():
        query_string = flask.request.query_string.decode("utf-8")
        client_id = flask.request.args.get("client_id")
        if client_id == "customer-ui":
            # Look for a session cookie in the request headers. If there, see if
            # the user is still authenticated.
            user_id = flask.session.get("user_id")
            idp_user_id = flask.session.get("idp_user_id")
            email = flask.session.get("user_email") or flask.session.get("email")
            nonce = flask.session.get("nonce")

            if (
                user_id is not None
                and idp_user_id is not None
                and email is not None
                and nonce is not None
            ):
                try:
                    user_nonce = front_end_token_service.GetNonceForUser(
                        front_end_token_service_pb2.GetNonceForUserRequest(
                            user_id=user_id
                        )
                    ).nonce
                except grpc.RpcError as e:
                    logging.error("Failed to get nonce for user: %s", e)
                    user_nonce = None

                is_cookie_valid = nonce == user_nonce
                if is_cookie_valid:
                    logging.info("Found valid session cookie")
                    return accept_internal(
                        args=list(flask.request.args.items(True)),
                        idp_user_id=idp_user_id,
                        user_email=email,
                        route="terms_accept",
                    )
            else:
                logging.debug("No auth cookie found for customer-ui.")

        # If the cookie is not present or invalid, clear the session and
        # redirect to the login page.
        clear_auth_session_state()

        return flask.redirect(f"/login?{query_string}")

    @app.route("/login", methods=["GET"])
    def login():
        """Initialize Auth0 login flow."""
        args = list(flask.request.args.items(True))
        response = validate_request_args(args, test_ok=True)
        if response:
            return response

        # u for uniquifier - add a random value to the args list
        # so the resulting encoded_state below is unique per window.
        #
        # If a user pastes the same URL into two browser windows, then either
        # browser window will work for login (but not both).
        args.append(("u", secrets.token_urlsafe(8)))

        clear_auth_session_state()
        encoded_state = _base64_url_encode(urlencode(args).encode())
        redirect = flask.url_for("oauth2_callback", _external=True)
        if dev_callback_url is not None:
            encoded_state = _base64_url_encode(redirect.encode()) + "." + encoded_state
            redirect = dev_callback_url

        redirect = auth0_oauth.auth0_login.authorize_redirect(  # type: ignore
            redirect_uri=redirect, state=encoded_state
        )

        return flask.render_template(
            "login_redirect.html",
            login_url=redirect.headers["Location"],
        )

    @app.route("/oauth2/callback", methods=["GET"])
    def oauth2_callback():
        """Handle oauth2 callback from Auth0."""

        request_args = [
            arg
            for arg in list(flask.request.args.items(True))
            if arg[0] not in ["code", "token"]
        ]
        login_args: str | None = None

        # authlib modifies flask session even in error path
        debug_session = copy.deepcopy(flask.session)

        error_param = flask.request.args.get("error")
        description_param = flask.request.args.get("error_description")

        try:
            # Reconstruct login args from state
            state = flask.request.args.get("state")
            if not state:
                raise RuntimeError("No state in oauth2 callback")

            if dev_callback_url is not None:
                # Split state on .
                state_parts = state.split(".", 2)
                if len(state_parts) != 2:
                    raise RuntimeError("Invalid state in oauth2 callback")

                state = state_parts[1]

            state = state + "=" * (-len(state) % 4)

            # login_args is under the control of an attacker.
            #
            # An attacker could create a direct link to this endpoint. The
            # attacker could pass a valid code they got from Auth0.
            #
            # The token returned would be for the attacker. The attacker
            # could specify a redirect URI of user's VS.code instance. However,
            # the VS.code instance should reject the attack because it
            # either because there isn't an outstanding authentication or
            # because it's unlikely the state generated by the attacker matches
            # the randomly generated state in the extension.
            #
            # Another layer of protection is that authlib has a session variable
            # with the value of state in its name. If the attacker attempts to present
            # a login_args that wasn't seen by /login, authlib will fail to find the
            # session variable and throw a MismatchingStateError().
            login_args = base64.urlsafe_b64decode(state).decode("utf-8")
            args_list = parse_qsl(login_args)
            args_list = [arg for arg in args_list if arg[0] != "u"]
            login_args = urlencode(args_list)

            override: dict[str, Any] | OAuthError | None = app.config.get(
                "augment_override_authorize_access_token", None
            )
            if isinstance(override, Exception):
                raise override
            if override is None:
                token = auth0_oauth.auth0_login.authorize_access_token()  # type: ignore
            else:
                token = override

            resp = token.get("userinfo")
            if resp is None:
                raise RuntimeError("No user info in oauth2 callback")

            # Okta does not set email_verified, so treat missing as verified.
            email_verified = resp.get("email_verified", True)
            if not email_verified:
                raise RuntimeError("No verified email in oauth2 callback")

            clear_auth_session_state()
            flask.session["user_email"] = resp.get("email")
            flask.session["idp_user_id"] = resp.get("sub")

            client_id = _find_arg_safe(args_list, "client_id")
            if client_id == "invitations":
                return flask.redirect(
                    location=flask.url_for("invitations_page"),
                    code=302,
                )

            return flask.redirect(
                location=flask.url_for("terms_accept") + "?" + login_args,
                code=302,
            )
        except OAuthError as e:
            logging.exception(
                "Error in auth0callback: %s request args: %s", e, request_args
            )
            logging.info("OAuthError session keys: %s", debug_session.keys())
            size = 0
            for k, v in debug_session.items():
                size += len(str(k))
                size += len(str(v))
            logging.info("OAuthError session size: %s", size)
            # Clear session so user has cleaner slate when trying again
            clear_auth_session_state()

            message, details = oauth_error_to_user_message(
                library_error=e.error,
                library_description=e.description,
                error=error_param,
                description=description_param,
            )

            return (
                render(
                    "unauthenticated.html",
                    user_message=message,
                    details=details,
                ),
                401,
            )
        except Exception as e:  # pylint: disable=broad-exception-caught
            logging.exception(
                "Error in auth0callback: %s request args: %s", e, request_args
            )
            return (
                render(
                    "unauthenticated.html",
                    user_message="Sorry, we encountered an unexpected error.",
                    details=f"Details: time:{datetime.now(timezone.utc)}",
                ),
                401,
            )

    @app.route("/token", methods=["POST"])
    def token():
        """Endpoint used by the client to exchange the code for an access token."""

        args = None
        if (
            flask.request.headers.get("Content-Type")
            == "application/x-www-form-urlencoded"
        ):
            args = flask.request.form
        elif flask.request.headers.get("Content-Type") == "application/json":
            args = flask.request.json

        if args is None:
            _response_counter.labels("token", "no_args").inc()
            return _abort({"error": "invalid_request"})

        try:
            code, client_id, redirect_uri, grant_type, code_verifier = (
                args[arg]
                for arg in [
                    "code",
                    "client_id",
                    "redirect_uri",
                    "grant_type",
                    "code_verifier",
                ]
            )
        except KeyError:
            _response_counter.labels("token", "missing_args").inc()
            return _abort({"error": "invalid_request"})

        if client_id not in config.client_config_map:
            _response_counter.labels("token", "invalid_grant").inc()
            return _abort({"error": "invalid_grant"})

        expiration_time_seconds = config.client_config_map[
            client_id
        ].expiration_time_seconds

        request = front_end_token_service_pb2.TokenFromCodeRequest(
            code=code,
            code_verifier=code_verifier,
            client_id=client_id,
            redirect_uri=redirect_uri,
            grant_type=grant_type,
            expiration_time_seconds=expiration_time_seconds,
        )
        response = front_end_token_service.TokenFromCode(request)
        if response.error:
            _response_counter.labels("token", "invalid_grant").inc()
            return _abort({"error": response.error})
        else:
            _response_counter.labels("token", "success").inc()
            return flask.jsonify(
                {
                    "access_token": response.access_token,
                    "expires_in": response.expires_in,
                    "token_type": "Bearer",
                }
            )

    def _signup_done(is_individual: bool, status: str):
        if is_individual:
            url = _INDIVIDUAL_SIGNUP_DONE_REDIRECT.get(
                base.feature_flags.get_global_context()
            )
        else:
            url = _SIGNUP_DONE_REDIRECT.get(base.feature_flags.get_global_context())

        _signup_callbacks_counter.labels(
            "individual" if is_individual else "open-source", status
        ).inc()

        # Remove once AU-6845 lands
        if status == SignupStatus.YOUR_ORG_HAS_AUGMENT.value:
            status = SignupStatus.IN_ANOTHER_TENANT.value

        logging.info("Redirecting to signup callback %s: %s", url, status)
        return _redirect_to_client_no_transition_page(
            redirect_uri=url,
            query_args={"status": status},
        )

    @app.route("/signup/login", methods=["GET"])
    def signup_login():
        """Redirect to the auth0 signup page."""
        is_individual = flask.request.args.get("individual") == "true"

        try:
            if not get_candidate_signup_tenant_list(config, is_individual):
                logging.error("Signup tenant not configured")
                return _signup_done(is_individual, "internal-error")

            credits_available = front_end_token_service.SignupCreditsAvailable(
                front_end_token_service_pb2.SignupCreditsAvailableRequest()
            ).credits_available

            if credits_available < 1:
                return _signup_done(is_individual, "signup-limit-reached")

            in_usa = flask.request.args.get("us") == "true"
            if not is_individual and not in_usa:
                return _signup_done(is_individual, "not-in-usa")

            flask.session["in_usa"] = in_usa
            flask.session["individual"] = is_individual

            if app.config.get("augment_override_authorize_access_token", None):
                return flask.redirect(flask.url_for("signup_callback", _external=True))

            state = secrets.token_urlsafe(16)
            redirect = flask.url_for("signup_callback", _external=True)
            if dev_callback_url is not None:
                state = (
                    base64.urlsafe_b64encode(redirect.encode()).decode() + "." + state
                )
                redirect = dev_callback_url

            redirect = auth0_oauth.auth0_signup.authorize_redirect(  # type: ignore
                redirect_uri=redirect,
                state=state,
            )
            redirect_uri = redirect.headers["Location"]
            if not is_individual:
                redirect_uri += "&ext-plan=community"

            return flask.render_template(
                "login_redirect.html",
                login_url=redirect_uri,
            )
        except Exception as e:  # pylint: disable=broad-exception-caught
            logging.exception("Error processing /signup/login: %s", e)
            return _signup_done(is_individual, "internal-error")

    def _is_email_whitelisted(email: str) -> bool:
        whitelist = _SIMILAR_SIGNUPS_WHITELIST_DOMAINS.get(
            base.feature_flags.get_global_context()
        ).split(",")
        if not whitelist:
            return False
        return any(email.lower().endswith("@" + domain.lower()) for domain in whitelist)

    def do_signup_check(
        is_individual: bool, in_usa: bool, idp_user_id: str, email: str
    ) -> tuple[
        User | None,
        tenant_watcher_pb2.Tenant | None,
        SignupStatus | None,
    ]:
        if not is_valid_email(email):
            return (None, None, SignupStatus.INVALID_EMAIL)

        tenant_names = get_candidate_signup_tenant_list(config, is_individual)
        if not tenant_names:
            raise RuntimeError("Signup tenant not configured")
        else:
            tenant_name = get_signup_tenant(tenant_names, idp_user_id)

        signup_tenant = tenant_map.get_tenant(tenant_name)
        if signup_tenant is None:
            raise RuntimeError("Signup tenant not found: %s", tenant_name)

        if tenant_map.get_tenant_for_email_domain(email) is not None:
            return (None, None, SignupStatus.YOUR_ORG_HAS_AUGMENT)

        try:
            response = front_end_token_service.GetUserAndSimilarEmails(
                front_end_token_service_pb2.GetUserAndSimilarEmailsRequest(
                    idp_user_id=idp_user_id,
                    email_address=email,
                )
            )
            user = response.user
        except grpc.RpcError as e:
            logging.error("Failed to get user: %s", e)
            raise

        if len(response.similar_emails) > 0:
            if _ALLOW_SIMILAR_SIGNUPS.get(base.feature_flags.get_global_context()):
                logging.info(
                    "Allowing signup for similar email: %s %s",
                    email,
                    response.similar_emails,
                )
            elif _is_email_whitelisted(email):
                logging.info(
                    "Allowing signup for whitelisted similar email: %s %s",
                    email,
                    response.similar_emails,
                )
            else:
                logging.info(
                    "Blocking signup for similar email: %s %s",
                    email,
                    response.similar_emails,
                )
                return (None, None, SignupStatus.ALREADY_SIGNED_UP)

        if user is not None:
            for tenant_id in user.tenants:
                tenant = tenant_map.get_tenant_by_id(tenant_id)
                if tenant is None:
                    logging.error(
                        "User %s in deleted tenant %s is trying to sign up",
                        user.id,
                        tenant_id,
                    )
                    # We should handle users in deleted tenants more gracefully but
                    # for now we just fail the signup.
                    return (None, None, SignupStatus.INTERNAL_ERROR)
                if tenant_id == signup_tenant.id:
                    return (user, tenant, SignupStatus.ALREADY_SIGNED_UP)
                # TODO: replace hard-coded string with regexp?
                if is_individual:
                    if "discovery" in tenant.name:
                        return (user, tenant, SignupStatus.ALREADY_SIGNED_UP)
                elif "vanguard" in tenant.name:
                    return (user, tenant, SignupStatus.ALREADY_SIGNED_UP)
            if len(user.tenants) > 0:
                return (None, None, SignupStatus.IN_ANOTHER_TENANT)

        credits_available = front_end_token_service.SignupCreditsAvailable(
            front_end_token_service_pb2.SignupCreditsAvailableRequest()
        ).credits_available

        if credits_available < 1:
            return (None, None, SignupStatus.SIGNUP_LIMIT_REACHED)

        return (user, signup_tenant, None)

    def do_signup(
        is_individual: bool, in_usa: bool, idp_user_id: str, email: str
    ) -> tuple[
        User | None,
        tenant_watcher_pb2.Tenant | None,
        SignupStatus | None,
    ]:
        (user, tenant, status) = do_signup_check(
            is_individual, in_usa, idp_user_id, email
        )
        if status is not None:
            return (user, tenant, status)

        signup_allowed = front_end_token_service.SignupAllowed(
            front_end_token_service_pb2.SignupAllowedRequest()
        ).allowed
        if not signup_allowed:
            return (user, tenant, SignupStatus.SIGNUP_LIMIT_REACHED)

        assert tenant is not None

        audit_logger.write_audit_log(
            email,
            auth_entities_pb2.UserId.UserIdType.Name(auth_entities_pb2.UserId.AUGMENT),
            resource=audit.User(user_id=email),
            tenant_name=tenant.name,
            message=f"User {email} signed up {is_individual=} {in_usa=} {idp_user_id=}",
        )

        try:
            request = front_end_token_service_pb2.EnsureUserInTenantRequest(
                email_address=email,
                tenant_id=tenant.id,
                idp_user_id=idp_user_id,
                mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY,  # Only add user to tenant if they're not already in one (expected for new signups)
            )
            if user is not None:
                request.augment_user_id = user.id

            user = front_end_token_service.EnsureUserInTenant(request).user
        except grpc.RpcError as e:
            logging.error("Failed to ensure user in tenant: %s", e)

        if user is None:
            raise RuntimeError("Failed to ensure user in tenant")

        ajs_anonymous_id = flask.request.cookies.get("ajs_anonymous_id")
        identify_user(
            user.id,
            {
                "email": user.email,
            },
            ajs_anonymous_id,
        )

        track_event(user.id, "User Signed Up", ajs_anonymous_id)

        return (user, tenant, None)

    @app.route("/signup/callback", methods=["GET"])
    def signup_callback():
        """Callback from the auth0 signup page."""

        is_individual = flask.session.get("individual", False)

        try:
            override: dict[str, Any] | OAuthError | None = app.config.get(
                "augment_override_authorize_access_token", None
            )
            if isinstance(override, Exception):
                raise override
            if override is None:
                token = auth0_oauth.auth0_signup.authorize_access_token()  # type: ignore
            else:
                token = override

            if token is None:
                return _signup_done(is_individual, "internal-error")

            userinfo = token.get("userinfo", {})
            idp_user_id = userinfo.get("sub", "")
            email = userinfo.get("email", "")
            if not email:
                return _signup_done(is_individual, "no-email-address")

            if not userinfo.get("email_verified", False):
                return _signup_done(is_individual, "unverified-email-address")

            in_usa = flask.session.get("in_usa", False)
            if not is_individual and not in_usa:
                return _signup_done(is_individual, "not-in-usa")

            (_, _, status) = do_signup(is_individual, in_usa, idp_user_id, email)
            if status is not None:
                return _signup_done(is_individual, status.value)

            del flask.session["in_usa"]

            return _signup_done(is_individual, "success")
        except Exception as e:  # pylint: disable=broad-exception-caught
            logging.exception("Error while signing up user: %s", e)
            return _signup_done(is_individual, "internal-error")

    @app.route("/echo", methods=["GET"])
    def echo():
        return flask.jsonify(flask.request.args)

    @app.route("/invitations", methods=["GET"])
    def invitations_page():
        """Display the invitations page for the current user."""
        # Check if the invitations feature is enabled
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.abort(501, "Invitations feature is not enabled")

        if "user_email" not in flask.session:
            logging.info("No user email in session, redirecting to login")
            return flask.redirect("/login?client_id=invitations")

        user_email = flask.session["user_email"]
        idp_user_id = flask.session["idp_user_id"]
        logging.info(
            f"Invitations page accessed by user_email: {user_email}, idp_user_id: {idp_user_id}"
        )

        continue_url = flask.request.args.get(
            "continue_url", "/terms-accept?client_id=invitations"
        )

        now = datetime.now(timezone.utc)
        try:
            invitations = invitation_service.get_user_invitations(user_email)

            # If the user has no invitations, redirect directly to continue_url
            if not invitations:
                logging.info(
                    f"User {user_email} has no pending invitations, redirecting to {continue_url}"
                )
                return flask.redirect(continue_url)

            is_team_admin = invitation_service.is_team_admin(idp_user_id, user_email)

            return render(
                "invitations.html",
                invitations=invitations,
                continue_url=continue_url,
                is_team_admin=is_team_admin,
            )
        except ValueError as e:
            logging.error(f"Value error: {e}")
            return render(
                "unauthenticated.html",
                details=f"Error occurred at {now}",
            ), 500
        except grpc.RpcError as e:
            logging.error(f"gRPC error: {e}")
            return render(
                "unauthenticated.html",
                details=f"Error occurred at {now}",
            ), 500
        except Exception:
            return render(
                "unauthenticated.html",
                details=f"Error occurred at {now}",
            ), 500

    @app.route("/api/invitation/status/<resolution_id>", methods=["GET"])
    def get_invitation_resolution_status_api(resolution_id: str):
        """API endpoint for checking the status of an invitation resolution.

        Args:
            resolution_id: The ID of the invitation resolution to check

        Returns:
            JSON response with the status of the invitation resolution
        """
        # Check if the invitations feature is enabled
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"error": "Invitations feature is not enabled"}), 501

        try:
            if "user_email" not in flask.session:
                return flask.jsonify({"error": "Not authenticated"}), 401

            user_email = flask.session["user_email"]
            logging.info(
                f"Checking invitation resolution status for user {user_email}, resolution ID: {resolution_id}"
            )

            try:
                status = invitation_service.get_invitation_resolution_status(
                    resolution_id
                )
                return flask.jsonify(status)
            except ValueError as e:
                logging.error(f"Value error: {e}")
                return flask.jsonify({"error": str(e)}), 400
            except grpc.RpcError as e:
                logging.error(f"gRPC error: {e}")
                return flask.jsonify(
                    {"error": "Failed to retrieve resolution status"}
                ), 500
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                return flask.jsonify({"error": "An unexpected error occurred"}), 500

        except Exception as e:
            now = datetime.now(timezone.utc)
            logging.exception(
                f"Error processing invitation status API request at {now}: {e}"
            )
            return flask.jsonify({"error": "An unexpected error occurred"}), 500

    @app.route("/api/invitation/resolve", methods=["POST"])
    def resolve_invitation_api():
        """API endpoint to accept or decline invitations."""
        # Check if the invitations feature is enabled
        if not _LOGIN_INVITATIONS_ENABLED.get(base.feature_flags.get_global_context()):
            return flask.jsonify({"error": "Invitations feature is not enabled"}), 501

        try:
            if "user_email" not in flask.session:
                return flask.jsonify({"error": "Not authenticated"}), 401

            user_email = flask.session["user_email"]
            if not user_email:
                return flask.jsonify({"error": "Not authenticated"}), 401

            data = flask.request.json
            if not data:
                return flask.jsonify({"error": "Invalid request"}), 400

            accept_invitation_id = data.get("accept_invitation_id")
            decline_invitation_ids = data.get("decline_invitation_ids", [])

            if not accept_invitation_id and not decline_invitation_ids:
                return flask.jsonify({"error": "No action specified"}), 400

            user_invitations = invitation_service.get_user_invitations(user_email)
            user_invitation_ids = {inv["id"] for inv in user_invitations}

            if accept_invitation_id and accept_invitation_id not in user_invitation_ids:
                return flask.jsonify({"error": "Invitation not found"}), 404

            invalid_decline_ids = [
                id for id in decline_invitation_ids if id not in user_invitation_ids
            ]
            if invalid_decline_ids:
                return flask.jsonify(
                    {"error": f"Invalid invitation IDs: {invalid_decline_ids}"}
                ), 404

            resolve_request = auth_pb2.ResolveInvitationsRequest(
                accept_invitation_id=accept_invitation_id or "",
                decline_invitation_ids=decline_invitation_ids,
            )

            try:
                response = front_end_token_service.ResolveInvitations(resolve_request)
                invitation_resolution_id = response.invitation_resolution_id

                if accept_invitation_id:
                    logging.info(
                        f"User {user_email} accepted invitation {accept_invitation_id}"
                    )
                    _invitation_counter.labels(
                        operation="accept", status="success"
                    ).inc()
                    return flask.jsonify(
                        {
                            "success": True,
                            "invitation_resolution_id": invitation_resolution_id,
                        }
                    )
                else:
                    logging.info(
                        f"User {user_email} declined invitations {decline_invitation_ids}"
                    )
                    _invitation_counter.labels(
                        operation="decline", status="success"
                    ).inc(len(decline_invitation_ids))
                    return flask.jsonify(
                        {
                            "success": True,
                            "invitation_resolution_id": invitation_resolution_id,
                        }
                    )

            except grpc.RpcError as e:
                if isinstance(e, grpc.Call) and (
                    e.code() == grpc.StatusCode.NOT_FOUND
                    or e.code() == grpc.StatusCode.ALREADY_EXISTS
                ):
                    logging.info(f"Invitation already processed: {e}")
                    _invitation_counter.labels(
                        operation="resolve", status="already_processed"
                    ).inc()
                    return flask.jsonify({"success": True, "already_processed": True})
                else:
                    logging.error(f"Error resolving invitations: {e}")
                    _invitation_counter.labels(
                        operation="resolve", status="error"
                    ).inc()
                    return flask.jsonify(
                        {"error": "Failed to process invitations"}
                    ), 500

        except Exception as e:
            now = datetime.now(timezone.utc)
            logging.exception(f"Error processing invitation API request at {now}: {e}")
            _invitation_counter.labels(operation="resolve", status="error").inc()
            return flask.jsonify({"error": "An unexpected error occurred"}), 500

    def flag():
        _ = flask.request.get_json()
        return flask.jsonify({})

    if config.enable_flag_endpoint:
        app.add_url_rule("/flag", view_func=flag, methods=["POST"])

    @app.route("/tos", methods=["GET"])
    def get_tos():
        """Return the appropriate Terms of Service text based on user's tenant.

        Returns:
            JSON response containing the TOS text and version
        """
        # Get the bearer token from Authorization header
        auth_header = flask.request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return flask.jsonify(
                {"error": "Missing or invalid authorization header"}
            ), 401

        try:
            token = auth_header.split(" ")[1]
        except IndexError:
            return flask.jsonify({"error": "Malformed authorization header"}), 401

        # Validate token and get user info
        tenant_id = front_end_token_service.GetTokenInfo(
            front_end_token_service_pb2.GetTokenInfoRequest(token=token)
        ).tenant_id
        if tenant_id == "":
            return flask.jsonify({"error": "Invalid token"}), 401

        # Get tenant details
        tenant = tenant_map.get_tenant_by_id(tenant_id)
        if tenant is None:
            return flask.jsonify({"error": "Tenant not found"}), 404

        # Error if the tier is not recognized
        if not (
            tenant.tier == tenant_watcher_pb2.TenantTier.ENTERPRISE
            or tenant.tier == tenant_watcher_pb2.TenantTier.PROFESSIONAL
            or tenant.tier == tenant_watcher_pb2.TenantTier.COMMUNITY
        ):
            return flask.jsonify(
                {
                    "error": f"Tenant (tenant id:{tenant_id}) tier not recognized {tenant.tier}"
                }
            ), 500

        # Determine which TOS applies by checking the tenant tier
        is_community = tenant.tier == tenant_watcher_pb2.TenantTier.COMMUNITY
        tos_filename = "community-v1.3.md" if is_community else "enterprise-v1.3.md"

        # Load the appropriate TOS text
        try:
            tos_path = os.path.join(os.path.dirname(__file__), "tos", tos_filename)
            with open(tos_path, "r", encoding="utf-8") as f:
                tos_text = f.read()

            version_match = re.search(r"v(\d+\.\d+)", tos_filename)
            if not version_match:
                logging.error(
                    "Could not extract version from TOS filename: %s", tos_filename
                )
                return flask.jsonify({"error": "Internal server error"}), 500

            tos_version = version_match.group(0)

        except (FileNotFoundError, IOError) as e:
            logging.error("Failed to read TOS file %s: %s", tos_filename, str(e))
            return flask.jsonify({"error": "Terms of service text not found"}), 500

        return flask.jsonify(
            {"tos_text": tos_text, "version": tos_version, "is_community": is_community}
        )

    def _redirect_to_client(
        redirect_uri: str | None,
        query_args: dict[str, str | None],
        client_name: str | None = None,
    ):
        query_args_filtered = {k: v for k, v in query_args.items() if v is not None}
        final_redirect_uri = None
        if redirect_uri:
            final_redirect_uri = (
                redirect_uri + "&" + urlencode(query_args_filtered)
                if "?" in redirect_uri
                else redirect_uri + "?" + urlencode(query_args_filtered)
            )
        return render(
            "client_redirect.html",
            redirect=final_redirect_uri,
            client_name=client_name,
            error=query_args.get("error"),
            error_description=query_args.get("error_description"),
        )

    def _redirect_to_client_no_transition_page(
        redirect_uri: str,
        query_args: dict[str, str | None],
    ):
        query_args_filtered = {k: v for k, v in query_args.items() if v is not None}
        final_redirect_uri = (
            redirect_uri + "&" + urlencode(query_args_filtered)
            if "?" in redirect_uri
            else redirect_uri + "?" + urlencode(query_args_filtered)
        )
        return flask.redirect(final_redirect_uri)

    def _redirect_to_client_error(
        redirect_uri: str | None,
        error: str,
        error_description: str | None = None,
        client_name: str | None = None,
    ):
        clear_auth_session_state()

        return _redirect_to_client(
            redirect_uri,
            {
                "error": error,
                "state": flask.request.args.get("state"),
                "error_description": error_description,
            },
            client_name=client_name,
        )

    def _remove_port_from_url(url: str):
        parsed_url = urlparse(url)
        # Check if the URL contains a port number
        if parsed_url.port is None:
            # URL doesn't contain a port number, return the original URL
            return url
        # Reconstruct the URL without the port number
        reconstructed_url = (
            parsed_url.scheme + "://" + (parsed_url.hostname or "") + parsed_url.path
        )
        if parsed_url.query:
            reconstructed_url += "?" + parsed_url.query
        if parsed_url.fragment:
            reconstructed_url += "#" + parsed_url.fragment
        return reconstructed_url

    def _abort(json_response: dict[str, str], status_code: int = 400):
        response = flask.jsonify(json_response)
        response.status_code = status_code
        return flask.abort(code=response)

    return app


class _App(BaseApplication):
    """Gunicorn wrapper for service Flask App."""

    # pylint: disable=abstract-method

    def __init__(self, app: flask.Flask, options: dict):
        self.application = app
        self.options = options
        super().__init__()

    def load_config(self):
        assert (
            self.cfg is not None
        )  # cfg is never None here, as it is initialized by Gunicorn. adding check for pylint
        for key, value in self.options.items():
            self.cfg.set(key, value)

    def load(self):
        return self.application


def when_ready(host: str, port: int, server: Any):
    del server
    GunicornPrometheusMetrics.start_http_server_when_ready(host=host, port=port)


def child_exit(server: Any, worker: Any):
    del server
    GunicornPrometheusMetrics.mark_process_dead_on_child_exit(worker.pid)


def post_fork(config: Config, app: Any, server: Any, worker: Any):
    del server
    del worker
    # feature flags uses background threads. Background threads
    # are not inherited across forks.
    path = None
    if config.feature_flags_sdk_key_path is not None:
        path = pathlib.Path(config.feature_flags_sdk_key_path)

    context = base.feature_flags.Context.setup(
        path, config.dynamic_feature_flags_endpoint
    )
    base.feature_flags.set_global_context(context)


def main():
    # tini will handle signals appropriately. In particular, if the tini process
    # (PID 1) gets a SIGTERM, it will shut down the whole container.
    if os.getpid() == 1:
        os.environ[KILL_PID_ON_EXIT_ENV_VAR_NAME] = str(os.getpid())
        os.execv(  # nosec (B606)
            "/usr/bin/tini-static",
            ["/usr/bin/tini-static", "-g", "--", sys.executable] + sys.argv[:],
        )

    base.tracing.setup_opentelemetry()
    setup_struct_logging()
    logging.getLogger("opentelemetry").setLevel(logging.INFO)

    parser = argparse.ArgumentParser()
    parser.add_argument("--config", type=str, required=True)
    parser.add_argument(
        "--disable-secure-cookie-for-testing",
        action="store_true",
        default=False,
        help="For internal testing only.",
    )
    args = parser.parse_args()

    config = Config.load(Path(args.config))
    logging.info("Config %s", config)

    app = create_app(
        config=config,
        secret_key=SecretStr(Path(config.secrets_path).read_text().strip()),
        prometheus=True,
        disable_secure_cookie_for_testing=args.disable_secure_cookie_for_testing,
    )

    prometheus_addr_parts = config.prometheus_bind_address.rsplit(":", 1)
    prometheus_port = int(prometheus_addr_parts[1])
    prometheus_addr = prometheus_addr_parts[0]

    options = {
        "bind": config.public_bind_address,
        "threads": 64,
        "when_ready": functools.partial(when_ready, prometheus_addr, prometheus_port),
        "post_fork": functools.partial(post_fork, config, app),
        "child_exit": child_exit,
    }
    try:
        _App(app, options).run()
    finally:
        backend_channel = grpc.insecure_channel(f"localhost:{config.backend_port}")
        front_end_token_service = (
            front_end_token_service_pb2_grpc.FrontEndTokenServiceStub(backend_channel)
        )
        logging.info("Sending shutdown to front end token service")
        try:
            front_end_token_service.Shutdown(
                front_end_token_service_pb2.ShutdownRequest()
            )
        except grpc.RpcError as e:
            logging.warning("Failed to shutdown front end token service: %s", e)


def is_valid_email(email: str) -> bool:
    """
    Perform very basic email validation.

    This is not meant to be 100% accurate. Its primary usecase is to prevent phone number signups.
    """
    # Check that there's exactly one "@".
    if email.count("@") != 1:
        return False

    # Check that there's at least one "." after the "@".
    if "." not in email.split("@")[1]:
        return False

    return True


if __name__ == "__main__":
    main()
