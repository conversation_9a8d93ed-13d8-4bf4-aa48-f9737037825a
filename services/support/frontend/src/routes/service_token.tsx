// Page to show (or better copy to clipboard) service token.
import { <PERSON><PERSON>, Button, Spin, message } from "antd";
import { useLocation, useParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import { LayoutComponent } from "../lib/layout";
import { getServiceToken } from "../lib/service_token";
import axios from "axios";
import Paragraph from "antd/es/typography/Paragraph";
import { useFeatureFlags } from "../contexts/FeatureFlagsProvider";
import { GenieButton } from "../lib/genie";

function TokenComponent({
  tenantId,
  tenantName,
}: {
  tenantId?: string;
  tenantName?: string;
}) {
  const [secret, setSecret] = useState<string | undefined>(undefined);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(
    undefined,
  );
  const [errorAction, setErrorAction] = useState<Button | undefined>(undefined);
  const flags = useFeatureFlags();
  if (
    tenantId === undefined &&
    tenantName === undefined &&
    errorMessage === undefined
  ) {
    setErrorMessage("Invalid tenant");
  }

  useEffect(() => {
    getServiceToken(tenantId, tenantName)
      .then((data) => {
        console.log("Generated token");
        setSecret(data);
      })
      .catch((err) => {
        if (axios.isAxiosError(err) && err.response?.status === 403) {
          setErrorMessage("You do not have access to this page");
          setErrorAction(
            <GenieButton redirect={false} tenantId={tenantId} kind="token" />,
          );
        } else {
          console.error("Failed to generate token:", err);
          setErrorMessage("Failed to generate token");
        }
        setSecret(undefined);
      });
  }, [tenantId, tenantName]);

  const copyToClipboard = () => {
    if (!secret) {
      return;
    }
    navigator.clipboard.writeText(secret);
    message.success("Secret copied to clipboard!");
  };

  if (errorMessage !== undefined) {
    return <Alert message={errorMessage} action={errorAction} type="error" />;
  } else if (secret === undefined) {
    return <Spin />;
  } else {
    return (
      <div>
        <Paragraph>
          A service token has been generated. The token is valid for 1 hour.
          Keep the token safe and do not share it with anyone.
        </Paragraph>
        <Button onClick={copyToClipboard}>Copy Token to Clipboard</Button>
      </div>
    );
  }
}

function useQuery() {
  return new URLSearchParams(useLocation().search);
}

export default function TokenPageComponent() {
  const query = useQuery();
  const tenantId = query.get("tenant_id") || undefined;
  const tenantName = query.get("tenant_name") || undefined;

  const children = (
    <TokenComponent tenantId={tenantId} tenantName={tenantName} />
  );
  return (
    <>
      <LayoutComponent children={children} showTenantDropdown={false} />
    </>
  );
}
