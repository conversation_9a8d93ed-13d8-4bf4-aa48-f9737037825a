import axios from "axios";

export async function getServiceToken(
  tenantId: string | undefined,
  tenantName: string | undefined,
): Promise<string> {
  if (tenantId === undefined && tenantName === undefined) {
    throw new Error("Must specify either tenant_id or tenant_name");
  }
  const { data: response }: { data: any } = await axios.post(
    `/api/service_token`,
    { tenant_id: tenantId, tenant_name: tenantName },
  );
  return response.token_value;
}
