import axios from "axios";

// API to return all request ids in a given session
export async function getRequestsBySession(
  tenantId: string,
  sessionId: string,
): Promise<string[]> {
  const { data: response }: { data: any } = await axios.get(
    `/api/tenant/${tenantId}/session/${sessionId}/requests`,
  );
  return response;
}

// API to return all request ids for a given user
export async function getRequestsByUser(
  tenantId: string,
  userId: string,
  requestType?: string,
): Promise<string[]> {
  const { data: response }: { data: any } = await axios.get(
    `/api/tenant/${tenantId}/user/${userId}/requests`,
    {
      params: {
        request_type: requestType,
      },
    },
  );
  return response;
}

export type ApiHttpResponse = {
  code?: number;
};

export type InferRequest = {
  model?: string;
  prompt?: string;
  suffix?: string;
  path?: string;
  sessionId?: string;
  userId?: string;
  memories?: string[];
};

export type Token = {
  logProbs?: number;
  text: string;
  tokenId: number;
};

export type Tokenization = {
  tokenIds: number[];
  offsets: number[];
  text: string;
  logProbs: number[];
};

export type Blobs = {
  baselineCheckpointId?: string;

  // These blobs are hex encoded. See services/support/backend/api.py.
  added: string[];
  deleted: string[];
};

export type CompletionHostResponse = {
  text: string;
  tokens?: Token[];
  skippedSuffix?: string;
  suffixReplacementText?: string;
  tokenization?: Tokenization;
  embeddingsPrompt?: Token[]; // Deprecated, but could still exist on old requests
  retrievedChunks?: RetrievalChunk[]; // Deprecated, but could still exist on old requests
};

export type CompletionPostProcess = {
  isLowQuality: boolean;
  filterScore: number;
  appliedFilterThreshold?: number;
  filterReason?: string;
};

export type RetrievalResponse = {
  retrievalType: string;
  embeddingsPrompt?: Token[]; // Deprecated, but could still exist on old requests
  retrievedChunks: RetrievalChunk[];
  queryPrompt?: Tokenization;
};

export type RerankerResponse = {
  rerankerPrompts?: Tokenization[];
  rerankedChunks: RerankerChunk[];

  // Deprecated - but could still exist on old requests
  rerankerPrompt?: Tokenization;
};

export type RetrievalChunk = {
  tokens?: Token[];
  text: string;
  path: string;
  blobName: string;
  chunkIndex?: number;
  charOffset: number;
  charEnd: number;
  origin: string;
  score?: number;
};

export type RerankerChunk = {
  tokens?: Token[];
  text: string;
  path: string;
  blobName: string;
  chunkIndex?: number;
  charOffset: number;
  charEnd: number;
  origin: string;
  score: number;
  shortDescription: string;
};

export type CompletionHostRequestPosition = {
  blobName: string;
  prefixBegin: number;
  cursorPosition: number;
  suffixEnd: number;
};

export type EditHostRequestPosition = {
  blobName: string;
  prefixBegin: number;
  suffixEnd: number;
};

export type ChatHostRequestPosition = {
  blobName: string;
  prefixBegin: number;
  suffixEnd: number;
};

export type ChatHostExchange = {
  requestMessage: string;
  responseText?: string;
  requestId?: string;
  requestNodes?: RequestNode[];
  responseNodes?: ResponseNode[];
};

type ToolDefinition = {
  name: string;
  description: string;
  input_schema_json: string;
};

type TextNode = {
  content: string;
};

type ContentNode = {
  text?: string;
  image?: ImageNode;
};

type ToolResultNode = {
  toolUseId: string;
  // Plain text content (ignored when content_nodes is present)
  content: string;
  isError: boolean;
  requestId?: string;
  // List of content nodes (text or images)
  // If present, takes precedence over content and image fields
  contentNodes?: ContentNode[];
};

type ImageNode = {
  imageData: string;
  format: string;
};

type CurrentTerminal = {
  currentWorkingDirectory: string;
  terminalId: number;
};

type WorkspaceFolder = {
  folderRoot: string;
  repositoryRoot: string;
};

type IdeStateNode = {
  currentTerminal?: CurrentTerminal;
  workspaceFolders: WorkspaceFolder[];
  workspaceFoldersUnchanged: boolean;
};

export type RequestNode = {
  id: number;
  type: string;
  textNode?: TextNode;
  toolResultNode?: ToolResultNode;
  imageNode?: ImageNode;
  ideStateNode?: IdeStateNode;
};

type ToolUse = {
  toolName: string;
  toolUseId: string;
  inputJson: string;
};

type AgentMemory = {
  content: string;
};

export type ResponseNode = {
  type: string;
  content?: string;
  toolUse?: ToolUse;
  agentMemory?: AgentMemory;
};

export type ChatHostRequest = {
  request: ChatRequest;
  tokens?: Token[];
  retrievedChunks?: RetrievalChunk[];
  tokenization?: Tokenization;
  externalSourceIds?: string[];
};

export type ChatHostResponse = {
  response: {
    text: string;
    nodes?: ResponseNode[];
  };
  tokens?: Token[];
  tokenization?: Tokenization;
  errorMessage?: string;
};

export type EditHostRequest = {
  request: {
    instruction: string;
    lang?: string;
    modelName?: string;
    prefix?: string;
    suffix?: string;
    path?: string;
    position?: EditHostRequestPosition;
    selectedText: string;
    blobNames?: string[];
    blobs?: Blobs;
  };
  tokens?: Token[];
  retrievedChunks?: RetrievalChunk[];
  tokenization?: Tokenization;
};

export type EditHostResponse = {
  response: {
    text: string;
  };
  tokens?: Token[];
  tokenization?: Tokenization;
};

export type InstructionHostRequest = {
  request: {
    modelName: string;
    path: string;
    prefix: string;
    selectedText: string;
    suffix: string;
    instruction: string;
    position?: EditHostRequestPosition;
    blobs?: Blobs;
    lang?: string;
    chatHistory?: ChatHostExchange[];
    codeBlock?: string;
    targetFilePath?: string;
    targetFileContent?: string;
    contextCodeExchangeRequestId?: string;
  };
  tokens?: Token[];
  retrievedChunks?: RetrievalChunk[];
  tokenization?: Tokenization;
};

export type ReplaceTextResponse = {
  text?: string;
  startLine?: number;
  endLine?: number;
  oldText?: string;
  sequenceId: number;
};

// Aggregate response from request_insight_pb2.RIInstructionResponse
export type InstructionHostResponse = {
  response: {
    text: string;
    replaceText: ReplaceTextResponse[];
  };
  tokens?: Token[];
  tokenization?: Tokenization;
  errorMessage?: string;
};

export type WorkingDirectoryChange = {
  beforePath?: string;
  afterPath?: string;
  changeType: string;
  headBlobName?: string;
  indexedBlobName?: string;
  currentBlobName?: string;
};
export type VCSChange = {
  workingDirectoryChanges: WorkingDirectoryChange[];
};

export type SingleEdit = {
  beforeStart: number;
  afterStart: number;
  beforeText: string;
  afterText: string;
};

export type GranularEditEvent = {
  path: string;
  beforeBlobName: string;
  afterBlobName: string;
  edits: SingleEdit[];
};

export type FileLocation = {
  path: string;
  lineStart: number;
  lineEnd: number;
};

export type Diagnostic = {
  location: FileLocation;
  message: string;
  severity: string;
};

export type NextEditHostRequest = {
  request: {
    modelName?: string;
    lang?: string;
    instruction: string;
    blobs?: Blobs;
    recentChanges?: ReplacementText[];
    vcsChange?: VCSChange;
    editEvents?: GranularEditEvent[];
    path?: string;
    blobName?: string;
    selectionBeginChar?: number;
    selectionEndChar?: number;
    prefix?: string;
    selectedText: string;
    suffix?: string;
    diagnostics?: Diagnostic[];
    mode?: string;
    scope?: string;
    changeProbabilityOverride?: number;
  };
  expandedRange?: CharSpan;
  tokenization?: Tokenization;
};

export type CharSpan = {
  start: number;
  stop: number;
};

export type DiffSpan = {
  original: CharSpan;
  updated: CharSpan;
};

export type ScoredFileHunk = {
  path?: string;
  blobName?: string;
  charStart?: number;
  charEnd?: number;
  existingCode?: string;
  suggestedCode?: string;
  localizationScore?: number;
  editingScore?: number;
  truncationChar?: number;
  diffSpans?: DiffSpan[];
  changeDescription?: string;
  suggestionId?: string;
  editingScoreThreshold?: number;
};

export type NextEditSuggestion = {
  generationId: string;
  suggestionOrder?: number;
  descriptionPrompt: Tokenization;
  descriptionOutput: Tokenization;
  result: {
    suggestedEdit: ScoredFileHunk;
    unknownBlobNames: string[];
    checkpointNotFound: boolean;
  };
  postProcessResult: string;
};

export type NextEditGeneration = {
  generationId: string;
  locationChunk: RetrievalChunk;
  retrievedChunks: RetrievalChunk[];
  generationPrompt: Tokenization;
  generationOutput: Tokenization;
  postProcessResult: string;
  editingScore: number;
};

export type NextEditHostResponse = {
  retrievedLocations: RetrievalChunk[];
  blockedLocations: RetrievalChunk[];
  generation: NextEditGeneration[];
  suggestions: NextEditSuggestion[];
};

export type ReplacementText = {
  blobName: string;
  path: string;

  charStart: number;
  charEnd: number;

  replacementText: string;
  presentInBlob: boolean;
  expectedBlobName: string;
};

export type TabSwitchEvent = {
  path: string;
  blobName: string;
};

export type GitDiffFileInfo = {
  contentBlobName: string;
  fileBlobName: string;
};

export type RecencyInfo = {
  recentChanges: ReplacementText[];
  tabSwitchEvents: TabSwitchEvent[];
  gitDiffFileInfo: GitDiffFileInfo[];
};

export type CompletionHostRequest = {
  outputLen: number;
  seed: number;
  temperature: number;
  topK: number;
  topP: number;
  model?: string;
  prefix?: string;
  suffix?: string;
  path?: string;
  blobNames?: string[];
  tokens?: Token[];
  lang?: string;
  enableFillInTheMiddle: boolean;
  enablePathPrefix: boolean;
  enablePreferenceTokens: boolean;
  enableMemories: boolean;
  enableBm25: boolean;
  enableDenseRetrieval: boolean;
  position?: CompletionHostRequestPosition;
  blobs?: Blobs;
  recencyInfo?: RecencyInfo;
  tokenization?: Tokenization;
  editEvents?: GranularEditEvent[];
};

export type InferenceHostResponse = {
  // the tokens generated by the FTM model (before any truncation or filtering)
  tokens?: Token[];

  // the cumulative log probability reported by FTM
  cumLogProbs?: number;

  tokenization?: Tokenization;
};

export type EmbeddingsSearchRequest = {
  blobNames?: string[];

  numResults?: number;

  transformationKey: string;
  sub_key: string;

  // TODO blobs missing
};

export type EmbeddingsSearchResult = {
  blobName: string;
  // note (undefined means 0)
  chunkIndex?: number;
  value?: number;
};

export type EmbeddingsSearchResponse = {
  missingBlobNames?: string[];

  results?: EmbeddingsSearchResult[];
};

export type RequestMetadata = {
  requestType: string;
  userId: string;
  sessionId: string;
  userAgent: string;
};

export type TenantInfo = {
  tenantId: string;
  tenantName: string;
};

export type AutofixCommand = {
  input: string;
  output: string;
  exitCode?: number;
};

export type AutofixCheckRequest = {
  command: AutofixCommand;
};

export type AutofixCheckResponse = {
  isCodeRelated: boolean;
  containsFailure: boolean;
};

export type AutofixUserSteeringExchange = {
  requestMessage: string;
  summary: string;
  replacements: ReplacementText[];
  requestId: string;
};

export type AutofixPlanRequest = {
  command: AutofixCommand;
  vcsChange: VCSChange;
  blobs: Blobs;
  steeringHistory?: AutofixUserSteeringExchange[];
};

export type AutofixPlanResponse = {
  unknownBlobNames: string[];
  checkpointNotFound: boolean;
  summary: string;
  replacements: ReplacementText[];
};

export type AgentCodebaseRetrievalRequest = {
  informationRequest: string;
  blobs: Blobs;
  dialog: ChatHostExchange[];
};

export type AgentCodebaseRetrievalResponse = {
  formattedRetrieval: string;
};

export type AgentEditFileRequest = {
  filePath: string;
  editSummary: string;
  detailedEditDescription: string;
  fileContents: string;
};

export type AgentEditFileResponse = {
  modifiedFileContents: string;
  isError: boolean;
};

export type RunRemoteToolRequest = {
  toolName: string;
  toolInputJson: string;
};

export type RunRemoteToolResponse = {
  toolOutput: string;
  toolResultMessage: string;
  isError: boolean;
};

// Aggregate request from request_insight_pb2.RIRemoteToolCallRequest
export type RemoteToolCallRequest = {
  codebaseRetrievalRequest?: AgentCodebaseRetrievalRequest;
  editFileRequest?: AgentEditFileRequest;
  runRemoteToolRequest?: RunRemoteToolRequest;
};

export type RemoteToolCallResponse = {
  codebaseRetrievalResponse?: AgentCodebaseRetrievalResponse;
  editFileResponse?: AgentEditFileResponse;
  runRemoteToolResponse?: RunRemoteToolResponse;
};

export type RouterResponse = {
  promptTokens: Tokenization;
  responseTokens?: Tokenization;
  parsedResponse?: {
    category: string;
    filenames: string[];
    docsets: string[];
  };
  errorMessage?: string;
};

export type PostprocessResponse = {
  tokenization?: Tokenization;
  errorMessage?: string;
};

export type RequestData = {
  apiHttpResponse?: ApiHttpResponse;
  apiHttpResponseTime?: string;
  inferRequestTime?: string;
  inferRequest?: InferRequest;
  chatHostRequest?: ChatHostRequest;
  chatHostRequestTime?: string;
  chatHostResponse?: ChatHostResponse;
  chatHostResponseTime?: string;
  editHostRequest?: EditHostRequest;
  editHostRequestTime?: string;
  editHostResponse?: EditHostResponse;
  editHostResponseTime?: string;
  instructionHostRequest?: InstructionHostRequest;
  instructionHostRequestTime?: string;
  instructionHostResponse?: InstructionHostResponse;
  instructionHostResponseTime?: string;
  nextEditHostRequest?: NextEditHostRequest;
  nextEditHostRequestTime?: string;
  nextEditHostResponse?: NextEditHostResponse;
  nextEditHostResponseTime?: string;
  completionHostRequest?: CompletionHostRequest;
  completionHostRequestTime?: string;
  completionHostResponse?: CompletionHostResponse;
  completionPostProcess?: CompletionPostProcess;
  completionHostResponseTime?: string;
  retrievalResponseList?: RetrievalResponse[];
  rerankerResponseList?: RerankerResponse[];
  retrievalResponseTimeList?: string[];
  inferenceHostResponse?: InferenceHostResponse;
  embeddingsSearchRequest?: EmbeddingsSearchRequest;
  embeddingsSearchResponse?: EmbeddingsSearchResponse;
  requestMetadata?: RequestMetadata;
  requestMetadataTime?: string;
  tenantInfo?: TenantInfo;
  autofixCheckRequest?: AutofixCheckRequest;
  autofixCheckRequestTime?: string;
  autofixCheckResponse?: AutofixCheckResponse;
  autofixCheckResponseTime?: string;
  autofixPlanRequest?: AutofixPlanRequest;
  autofixPlanRequestTime?: string;
  autofixPlanResponse?: AutofixPlanResponse;
  autofixPlanResponseTime?: string;
  remoteToolCallRequest?: RemoteToolCallRequest;
  remoteToolCallRequestTime?: string;
  remoteToolCallResponse?: RemoteToolCallResponse;
  remoteToolCallResponseTime?: string;
  routerResponse?: RouterResponse;
  routerResponseTime?: string;
  postprocessResponse?: PostprocessResponse;
  postprocessResponseTime?: string;
};

// Returns detailed information about the given request
// Use desiredEvents when you want to try to wait for certain events to be
// present in the returned response. This is particularly useful for replay,
// when it may take some time before all events make it into the request insight
// database. Note that this does not guarantee the events will be present in the
// response, though.
export async function getRequest(
  tenantId: string,
  requestId: string,
  desiredEvents: string[],
): Promise<RequestData | null> {
  const desiredEventsStr =
    desiredEvents !== undefined && desiredEvents.length > 0
      ? desiredEvents.join(",")
      : undefined;
  const { data: response }: { data: any } = await axios.get(
    `/api/tenant/${tenantId}/request/${requestId}`,
    {
      params: {
        desired_events: desiredEventsStr,
      },
    },
  );
  return response;
}

type CompletionRequest = {
  model_name: string;
  prefix: string;
  suffix: string;
  path: string;
  blobs?: Blobs;
  lang?: string;
  top_k?: number;
  top_p?: number;
  temperature?: number;
  max_tokens?: number;
  seed?: number;
};

export async function requestCompletion(
  tenantId: string,
  request: CompletionRequest,
): Promise<string> {
  const { data: response }: { data: { request_id: string } } = await axios.post(
    `/api/tenant/${tenantId}/request/completion`,
    request,
  );
  return response.request_id;
}

type EditRequest = {
  model_name: string;
  path: string;
  prefix: string;
  selected_text: string;
  suffix: string;
  instruction: string;
  position?: {
    blob_name: string | undefined;
    prefix_begin: number | undefined;
    suffix_end: number | undefined;
  };
  blobs?: Blobs;
  lang?: string;
};

export async function requestEdit(
  tenantId: string,
  request: EditRequest,
): Promise<string> {
  const { data: response }: { data: { request_id: string } } = await axios.post(
    `/api/tenant/${tenantId}/request/edit`,
    request,
  );
  return response.request_id;
}

export type InstructionRequest = {
  model_name: string;
  path: string;
  prefix: string;
  selected_text: string;
  suffix: string;
  instruction: string;
  position?: {
    blob_name: string | undefined;
    prefix_begin: number | undefined;
    suffix_end: number | undefined;
  };
  blobs?: Blobs;
  lang?: string;
  chat_history?: ChatHostExchange[];
  context_code_exchange_request_id?: string;
  code_block?: string;
  target_file_path?: string;
  target_file_content?: string;
};

export async function requestInstruction(
  tenantId: string,
  request: InstructionRequest,
): Promise<string> {
  const { data: response }: { data: { request_id: string } } = await axios.post(
    `/api/tenant/${tenantId}/request/instruction`,
    request,
  );
  return response.request_id;
}

type ChatRequest = {
  modelName: string;
  path: string;
  prefix: string;
  selectedCode: string;
  suffix: string;
  message: string;
  position?: ChatHostRequestPosition;
  blobs: Blobs[];
  lang?: string;
  userGuidedBlobs?: string[];
  chatHistory?: ChatHostExchange[];
  externalSourceIds?: string[];
  disableAutoExternalSources?: boolean;
  promptFormatterName?: string;
  userGuidelines?: string;
  workspaceGuidelines?: string;
  nodes?: RequestNode[];
  toolDefinitions?: ToolDefinition[];
  agentMemories?: string;
};

export async function requestChat(
  tenantId: string,
  request: ChatRequest,
): Promise<string> {
  const { data: response }: { data: { request_id: string } } = await axios.post(
    `/api/tenant/${tenantId}/request/chat`,
    request,
  );
  return response.request_id;
}

type NextEditRequest = {
  modelName: string;
  lang?: string;
  instruction?: string;
  blobs?: Blobs;
  recentChanges?: ReplacementText[];
  vcsChange?: VCSChange;
  path?: string;
  blobName?: string;
  selectionBeginChar: number;
  selectionEndChar: number;
  prefix: string;
  selectedText: string;
  suffix: string;
  diagnostics?: Diagnostic[];
  mode?: string;
  changeProbabilityOverride?: number;
};

export async function requestNextEdit(
  tenantId: string,
  request: NextEditRequest,
): Promise<string> {
  const { data: response }: { data: { request_id: string } } = await axios.post(
    `/api/tenant/${tenantId}/request/next_edit`,
    request,
  );
  return response.request_id;
}

export type ModelData = {
  name: string;
  completion_endpoint?: string;
};

export async function getModels(): Promise<ModelData[]> {
  const { data: response }: { data: any } = await axios.get(`/api/models`);
  return response.models;
}

export function showTimeStamp(date?: Date): string {
  if (!date) {
    return "";
  }
  return date.toLocaleString("en-US", { timeZone: "America/Los_Angeles" });
}

export function showISOTimeStamp(date?: Date): string {
  if (!date) {
    return "";
  }
  return date.toISOString();
}

export function getAddedFromBlobs(blobs: Blobs[] | undefined): string[] {
  if (blobs === undefined) {
    return [];
  }
  return blobs.map((b) => b.added).flat();
}

export function makeBlobs(blobs: Blobs | undefined): Blobs[] | undefined {
  if (blobs === undefined) {
    return undefined;
  }
  return [blobs];
}

export function mergeBlobs(
  a: string[] | undefined,
  b: string[] | undefined,
): string[] {
  const aArray = a || [];
  const bArray = b || [];
  return [...new Set([...aArray, ...bArray])];
}

// Normalize tokens for the benefit of the UI.
export function getTokens(tokenization: Tokenization | undefined): Token[] {
  if (tokenization !== undefined && tokenization.tokenIds.length > 0) {
    return tokenization.tokenIds.map((tokenId, index) => ({
      text: tokenization.text.slice(
        tokenization.offsets[index],
        tokenization.offsets[index + 1],
      ),
      tokenId,
      logProbs: tokenization.logProbs[index],
    }));
  }
  // Fall back to empty tokenization
  return [];
}
