"""Blueprint for the support UI API backend."""

# pylint: disable=no-member
import datetime
import json
import logging
import time
import uuid
from typing import Any

import google.protobuf.json_format as json_format
import grpc
import pydantic
import structlog
from flask import Blueprint, Response, current_app, jsonify, request
from google.protobuf.timestamp_pb2 import Timestamp

import base.blob_names.blob_names_pb2 as blob_names_pb2
import services.api_proxy.model_finder_pb2 as model_finder_pb2
import services.api_proxy.model_finder_pb2_grpc as model_finder_pb2_grpc
import services.auth.central.server.auth_pb2_grpc as auth_pb2_grpc
import services.content_manager.content_manager_pb2 as content_manager_pb2
import services.content_manager.content_manager_pb2_grpc as content_manager_pb2_grpc
import services.grpc_debug.client.client as grpc_debug_client
import services.request_insight.central.request_insight_central_pb2 as request_insight_central_pb2
import services.request_insight.request_insight_pb2 as request_insight_pb2
from base.diff_utils import edit_events_pb2
from base.flask_util.grpc_util import grpc_error_wrap
from base.flask_util.iap_util import extract_user, iap_jwt_verified
from base.logging.audit import audit
from services.auth.central.server import auth_entities_pb2, auth_pb2
from services.chat_host import chat_pb2
from services.chat_host.client import ChatClient
from services.completion_host import completion_pb2
from services.completion_host.client import CompletionClient
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.edit_host import edit_pb2
from services.edit_host.client import EditClient
from services.grpc_debug import grpc_debug_pb2
from services.lib.request_context.request_context import RequestContext
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.client import NextEditClient
from services.request_insight.central.client.client import RequestInsightCentralClient
from services.tenant_watcher.client.client import TenantsClient
from services.token_exchange import token_exchange_pb2
from services.token_exchange.client.client import TokenExchangeClient

bp = Blueprint("api", __name__, url_prefix="/api")

log = structlog.get_logger()


def _get_request_context(
    tenant_id: str, scopes: list[token_exchange_pb2.Scope.ValueType]
) -> RequestContext:
    """Helper function to get the request context."""
    token_exchange_client: TokenExchangeClient = (
        current_app.token_exchange_client  # type: ignore
    )
    iap_token = request.headers.get("X-Goog-IAP-JWT-Assertion")
    service_token = token_exchange_client.get_signed_token_for_iap_token(
        pydantic.SecretStr(iap_token) if iap_token else pydantic.SecretStr(""),
        expiration=datetime.timedelta(minutes=60),
        tenant_id=tenant_id,
        scopes=scopes,
    )

    request_context = RequestContext.create(
        auth_token=service_token,
    )
    return request_context


@bp.route("service_token", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def generate_token():
    data: dict = request.get_json()
    request_scopes = data.get("scopes", [])
    tenant_id = data.get("tenant_id", "")
    tenant_name = data.get("tenant_name", "")

    logging.info(
        "Generate service token for tenant %s/%s with scopes %s",
        tenant_id,
        tenant_name,
        request_scopes,
    )
    if not request_scopes:
        logging.warning("Missing scopes")
        return Response(
            json.dumps({"code": 400}),  # type: ignore
            status=400,
            mimetype="application/json",
        )
    if not tenant_id and not tenant_name:
        logging.warning("Missing tenant_id or tenant_name")
        return Response(
            json.dumps({"code": 400}),  # type: ignore
            status=400,
            mimetype="application/json",
        )
    if tenant_id and tenant_name:
        logging.warning("Cannot specify both tenant_id and tenant_name")
        return Response(
            json.dumps({"code": 400}),  # type: ignore
            status=400,
            mimetype="application/json",
        )

    # Convert the string scopes to the protobuf enum types
    scopes = []
    for scope_str in request_scopes:
        try:
            scopes.append(token_exchange_pb2.Scope.Value(scope_str))
        except ValueError:
            logging.warning("Invalid scope: %s", scope_str)
            return Response(
                json.dumps({"code": 400}),  # type: ignore
                status=400,
                mimetype="application/json",
            )

    if tenant_name:
        tenant_watcher_client: TenantsClient = current_app.tenant_watcher_client  # type: ignore
        tenant_id = tenant_watcher_client.get_tenant_id(tenant_name)
        if not tenant_id:
            logging.warning("Tenant %s not found", tenant_name)
            return Response(
                json.dumps({"code": 404}),  # type: ignore
                status=404,
                mimetype="application/json",
            )

    token_exchange_client: TokenExchangeClient = (
        current_app.token_exchange_client  # type: ignore
    )

    iap_token = request.headers.get("X-Goog-IAP-JWT-Assertion")

    service_token = token_exchange_client.get_signed_token_for_iap_token(
        pydantic.SecretStr(iap_token) if iap_token else pydantic.SecretStr(""),
        expiration=datetime.timedelta(minutes=60),
        tenant_id=tenant_id,
        scopes=scopes,
    )
    return Response(
        json.dumps({"token_value": service_token.get_secret_value()}),  # type: ignore
        status=200,
        mimetype="application/json",
    )


@bp.route("tenant/<tenant_id>/session/<session_id>/requests")
@iap_jwt_verified
@grpc_error_wrap
def session_requests_handler(tenant_id: str, session_id: str):
    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_RESTRICTED_R],
    )

    central_client: RequestInsightCentralClient = (
        current_app.request_insight_central_client  # type: ignore
    )
    with request_context.with_context_logging():
        search_request = request_insight_central_pb2.RestrictedSearchRequest(
            tenant_id=tenant_id,
            time_filter=request_insight_central_pb2.TimeFilter(
                start_time=Timestamp(seconds=0),
            ),
            filters=request_insight_central_pb2.RestrictedSearchRequest.FilterGroup(
                conjunction=request_insight_central_pb2.RestrictedSearchRequest.FilterConjunction.AND,
                filters=[
                    request_insight_central_pb2.RestrictedSearchRequest.Filter(
                        base_filter=request_insight_central_pb2.RestrictedSearchRequest.BaseFilter(
                            session_id=session_id,
                        )
                    )
                ],
            ),
        )
        logging.info("Search request by session %s", search_request)
        response = central_client.restricted_search(search_request, request_context)
        return jsonify([r.request_id for r in response.results])


@bp.route("tenant/<tenant_id>/user/<user_id>/requests")
@iap_jwt_verified
@grpc_error_wrap
def user_requests_handler(tenant_id: str, user_id: str):
    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_RESTRICTED_R],
    )

    # Get the request type from the query parameters
    request_type = request.args.get("request_type")

    central_client: RequestInsightCentralClient = (
        current_app.request_insight_central_client  # type: ignore
    )
    with request_context.with_context_logging():
        filters = [
            request_insight_central_pb2.RestrictedSearchRequest.Filter(
                base_filter=request_insight_central_pb2.RestrictedSearchRequest.BaseFilter(
                    user_id=user_id,
                )
            )
        ]
        if request_type:
            filters.append(
                request_insight_central_pb2.RestrictedSearchRequest.Filter(
                    base_filter=request_insight_central_pb2.RestrictedSearchRequest.BaseFilter(
                        request_type=request_type,
                    )
                )
            )
        search_request = request_insight_central_pb2.RestrictedSearchRequest(
            tenant_id=tenant_id,
            time_filter=request_insight_central_pb2.TimeFilter(
                start_time=Timestamp(seconds=0),
            ),
            filters=request_insight_central_pb2.RestrictedSearchRequest.FilterGroup(
                conjunction=request_insight_central_pb2.RestrictedSearchRequest.FilterConjunction.AND,
                filters=filters,
            ),
        )
        logging.info("Search request by user %s", search_request)
        response = central_client.restricted_search(search_request, request_context)
        return jsonify([r.request_id for r in response.results])


@bp.route("tenant/<tenant_id>/remote_agent/<agent_id>/logs")
@iap_jwt_verified
@grpc_error_wrap
def remote_agent_logs_handler(tenant_id: str, agent_id: str):
    """Returns the remote agent logs for the specified agent as JSON.

    URL parameters:
        start_time: (optional) Start time in seconds since epoch
        end_time: (optional) End time in seconds since epoch
    """

    # Get the start and end time from the query parameters
    data = request.args.to_dict()
    start_time_str: str = data.get("start_time", "")
    end_time_str: str = data.get("end_time", "")

    # Parse the start and end time. Note that the frontend sends the time as a float number of
    # seconds since epoch and we need to convert it to an integer.
    start_time = (
        Timestamp(seconds=int(float(start_time_str)))
        if start_time_str
        else Timestamp(seconds=0)
    )
    end_time = Timestamp(seconds=int(float(end_time_str))) if end_time_str else None

    # Get the logs from request insight central
    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_RESTRICTED_R],
    )
    central_client: RequestInsightCentralClient = (
        current_app.request_insight_central_client  # type: ignore
    )
    with request_context.with_context_logging():
        get_logs_request = request_insight_central_pb2.GetRemoteAgentLogsRequest(
            tenant_id=tenant_id,
            agent_id=agent_id,
            time_filter=request_insight_central_pb2.TimeFilter(
                start_time=start_time,
                end_time=end_time,
            ),
        )
        logging.info("Get remote agent logs request %s", get_logs_request)
        response = central_client.get_remote_agent_logs(
            get_logs_request, request_context
        )

        # Extract messages
        messages = []  # List of {message: str, timestamp: float}
        event_count = 0
        for event in response:
            if event.HasField("remote_agent_log"):
                entries = map(
                    lambda e: {
                        "message": e.message,
                        "timestamp": e.timestamp.ToDatetime().timestamp(),
                    },
                    event.remote_agent_log.entries,
                )
                messages.extend(entries)
                event_count += 1
        messages.sort(key=lambda m: m["timestamp"])

        logging.info("Returning %d messages from %d events", len(messages), event_count)

        return jsonify(messages)


@bp.route("tenant/<tenant_id>/request/<request_id>")
@iap_jwt_verified
@grpc_error_wrap
def request_handler(tenant_id: str, request_id: str):
    """Returns the request information as JSON.

    URL parameters:
        desired_events: (optional) A comma-separated list of events to wait for.
    """
    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_RESTRICTED_R],
    )

    with request_context.with_context_logging():
        logging.info("request for %s", request_id)

        data = request.args.to_dict()

        desired_events = [
            e for e in data.get("desired_events", "").split(",") if e != ""
        ]
        # This loop is a hacky way of trying to wait for certain fields to exist
        # before returning a response
        # With these parameters, we sleep for at most 60s
        max_retries = 5
        sleep_time_seconds = 5

        data = _get_request_insight_data(tenant_id, request_id, request_context)
        for i in range(max_retries):
            have_all = all([field in data for field in desired_events])
            logging.info(
                "Got request data for %s containing these fields: %s",
                request_id,
                list(data.keys()),
            )
            if have_all:
                return jsonify(data)
            else:
                logging.info(
                    "Retry attempt %s to get data for request %s, missing one of these events: %s",
                    i,
                    request_id,
                    desired_events,
                )
                time.sleep(i * sleep_time_seconds)
                data = _get_request_insight_data(tenant_id, request_id, request_context)

        logging.info(
            "Hit max retries for request %s, returning despite missing one of these events: %s",
            request_id,
            desired_events,
        )
        return jsonify(data)


@bp.route("tenant/<tenant_id>/blob_info/<blob_name>")
@iap_jwt_verified
@grpc_error_wrap
def blob_info_handler(tenant_id: str, blob_name: str):
    """Returns the blob information as JSON document."""

    data = request.args.to_dict()

    transformation_key = data.get("transformation_key", "")
    sub_key = data.get("sub_key", "")

    content_manager_rpc_client: content_manager_pb2_grpc.ContentManagerStub = (
        current_app.content_manager_rpc_client  # type: ignore
    )
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.CONTENT_R]
    )
    with request_context.with_context_logging():
        response = content_manager_rpc_client.GetBlobInfo(
            content_manager_pb2.GetBlobInfoRequest(
                blob_name=blob_name,
                transformation_key=transformation_key,
                sub_key=sub_key,
            ),
            metadata=request_context.to_metadata(),
        )
        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(data)


@bp.route("tenant/<tenant_id>/batch_blob_infos", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def batch_blob_infos_handler(tenant_id: str):
    """Returns the blob information as JSON document, batching blob names."""
    data: dict = request.get_json()
    assert data is not None
    keys = data.get("keys")

    blob_content_keys = []
    if keys:
        for key in keys:
            blob_name = key.get("blobName", "")
            transformation_key = key.get("transformationKey", "")
            sub_key = key.get("subKey", "")

            blob_content_keys.append(
                content_manager_pb2.BlobContentKey(
                    blob_name=blob_name,
                    transformation_key=transformation_key,
                    sub_key=sub_key,
                )
            )

    content_manager_rpc_client: content_manager_pb2_grpc.ContentManagerStub = (
        current_app.content_manager_rpc_client  # type: ignore
    )
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.CONTENT_R]
    )
    with request_context.with_context_logging():
        response = content_manager_rpc_client.BatchGetBlobInfo(
            content_manager_pb2.BatchGetBlobInfoRequest(
                blob_content_keys=blob_content_keys,
                tenant_id=tenant_id,
            ),
            metadata=request_context.to_metadata(),
        )
        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(data)


@bp.route("tenant/<tenant_id>/blob_content_keys/<blob_name>")
@iap_jwt_verified
@grpc_error_wrap
def blob_content_keys_handler(tenant_id: str, blob_name: str):
    """Returns the blob content key as JSON document."""

    content_manager_rpc_client: content_manager_pb2_grpc.ContentManagerStub = (
        current_app.content_manager_rpc_client  # type: ignore
    )
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.CONTENT_R]
    )
    with request_context.with_context_logging():
        response = content_manager_rpc_client.ListBlobContentKeys(
            content_manager_pb2.ListBlobContentKeysRequest(blob_name=blob_name),
            metadata=request_context.to_metadata(),
        )
        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(data)


@bp.route("tenant/<tenant_id>/blob_content/<blob_name>")
@iap_jwt_verified
@grpc_error_wrap
def blob_content_handler(tenant_id: str, blob_name: str):
    """Returns the blob content in a sub_key specific format."""

    data = request.args.to_dict()

    transformation_key: str = data.get("transformation_key", "")
    sub_key: str = data.get("sub_key", "")

    content_manager_client: ContentManagerClient = (
        current_app.content_manager_client  # type: ignore
    )
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.CONTENT_RW]
    )
    with request_context.with_context_logging():
        content, metadata = content_manager_client.download_all(
            blob_name=blob_name,
            request_context=request_context,
            transformation_key=transformation_key,
            sub_key=sub_key,
        )
        content_str = content.decode("utf-8", errors="replace")
        return jsonify({"content": content_str, "metadata": list(metadata.items())})


@bp.route("tenant/<tenant_id>/checkpoint_info/<checkpoint_id>")
@iap_jwt_verified
@grpc_error_wrap
def checkpoint_info_handler(tenant_id: str, checkpoint_id: str):
    """Returns the checkpoint information as JSON document."""

    client: content_manager_pb2_grpc.ContentManagerStub = (
        current_app.content_manager_rpc_client  # type: ignore
    )
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.CONTENT_R]
    )
    with request_context.with_context_logging():
        proto_request = content_manager_pb2.GetAllBlobsFromCheckpointRequest(
            checkpoint_id=checkpoint_id
        )
        response = client.GetAllBlobsFromCheckpoint(
            proto_request, metadata=request_context.to_metadata()
        )
        merged_response = content_manager_pb2.GetAllBlobsFromCheckpointResponse()
        for partial_response in response:
            merged_response.MergeFrom(partial_response)

        # Blob names come back to us in bytes, so we need to manually convert to hex before
        # returning to the frontend.
        data = {"blobNames": [b.hex() for b in merged_response.blob_names]}
        return jsonify(data)


@bp.route("models")
@iap_jwt_verified
@grpc_error_wrap
def models_handler():
    """Returns the models information as JSON document."""

    model_finder_client: model_finder_pb2_grpc.ApiProxyStub = (  # type: ignore
        current_app.model_finder_client  # type: ignore
    )
    response = model_finder_client.GetGenerationModels(
        model_finder_pb2.GetModelsRequest()
    )
    data = json_format.MessageToDict(
        response,
        including_default_value_fields=True,  # type: ignore
    )
    return jsonify(data)


@bp.route(rule="tenants")
@iap_jwt_verified
@grpc_error_wrap
def tenants_handler():
    """Returns the tenants information as JSON document."""

    tenant_watcher_client: TenantsClient = (  # type: ignore
        current_app.tenant_watcher_client  # type: ignore
    )
    response = tenant_watcher_client.get_tenants()
    data = [
        json_format.MessageToDict(tenant, including_default_value_fields=True)  # type: ignore
        for tenant in response
    ]
    return jsonify(data)


@bp.route("tenant/<tenant_id>/request/completion", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def create_completion_request_handler(tenant_id: str):
    """Handler to create a completion request."""

    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_R, token_exchange_pb2.CONTENT_RW],
    )
    audit_logger: audit.AuditLogger = current_app.audit_logger  # type: ignore
    tenant_watcher_client: TenantsClient = current_app.tenant_watcher_client  # type: ignore
    with request_context.with_context_logging():
        tenant_name = tenant_watcher_client.get_tenant_name(tenant_id) or "unknown"
        audit_logger.write_audit_log(
            extract_user(request) or "unknown",
            auth_entities_pb2.UserId.UserIdType.Name(
                auth_entities_pb2.UserId.INTERNAL_IAP
            ),
            tenant_name=tenant_name,
            message=f"Playback completion request within tenant {tenant_name}",
        )

        # the request data here comes from `RequestPlaygroundComponent` in playground.tsx.
        data: dict = request.get_json()
        assert data is not None, "No data found from frontend"

        # Override the conversion of Blobs. See _dict_to_blobs docs.
        if "blobs" in data:
            data["blobs"] = _dict_to_blobs(data["blobs"])

        request_id = uuid.uuid4()
        logging.info("Assigning request id %s", request_id)
        model_finder_client: model_finder_pb2_grpc.ApiProxyStub = (  # type: ignore
            current_app.model_finder_client  # type: ignore
        )
        response = model_finder_client.GetGenerationModels(
            model_finder_pb2.GetModelsRequest(),
            metadata=request_context.to_metadata(),
        )
        models = response.models
        assert models is not None, "No models found from backend"
        logging.info("models %s", models)
        for model in models:
            if model.name == data["model_name"]:
                break
        else:
            return Response(
                json.dumps({"code": grpc.StatusCode.NOT_FOUND.value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        logging.info("Model %s", model)
        assert model.inference.completion_endpoint, "Completion endpoint not set"
        completion_client: CompletionClient = (
            current_app.completion_host_client_factory(  # type: ignore
                model.inference.completion_endpoint
            )
        )
        position = data.get("position")
        if position is not None:
            position = completion_pb2.CompletionPosition(
                blob_name=position["blob_name"],
                prefix_begin=position["prefix_begin"],
                cursor_position=position["cursor_position"],
                suffix_end=position["suffix_end"],
            )

        # This field is deprecated
        assert "blob_names" not in data

        recency_info = data.get("recency_info")
        if recency_info is not None:
            assert isinstance(recency_info, dict)
            recency_info = completion_pb2.RecencyInfo(
                tab_switch_events=[
                    completion_pb2.TabSwitchEvent(**event)
                    for event in recency_info.get("tab_switch_events", [])
                ],
                git_diff_file_info=[
                    completion_pb2.GitDiffFileInfo(**info)
                    for info in recency_info.get("git_diff_file_info", [])
                ],
                recent_changes=[
                    completion_pb2.ReplacementText(**change)
                    for change in recency_info.get("recent_changes", [])
                ],
            )

        edit_events = [
            edit_events_pb2.GranularEditEvent(
                path=event.get("path"),
                before_blob_name=event.get("before_blob_name"),
                after_blob_name=event.get("after_blob_name"),
                edits=[
                    edit_events_pb2.SingleEdit(
                        before_start=edit.get("before_start"),
                        after_start=edit.get("after_start"),
                        before_text=edit.get("before_text"),
                        after_text=edit.get("after_text"),
                    )
                    for edit in event.get("edits", [])
                ],
            )
            for event in data.get("edit_events", [])
        ]

        request_pb = completion_pb2.CompletionRequest(
            model_name=data["model_name"],
            prefix=data["prefix"],
            path=data["path"],
            suffix=data.get("suffix", ""),
            blobs=data.get("blobs"),
            max_tokens=int(data.get("max_tokens", 0)),
            lang=data.get("lang", ""),
            position=position,
            seed=completion_pb2.RNGSeed(value=int(data.get("seed", 0))),
            recency_info=recency_info,
            edit_events=edit_events,
        )
        response = completion_client.complete(
            request_pb, request_context=request_context
        )

        return jsonify({"request_id": request_context.request_id})


@bp.route("tenant/<tenant_id>/request/next_edit", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def create_next_edit_request_handler(tenant_id: str):
    """Handler to create a next edit request."""

    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_R, token_exchange_pb2.CONTENT_RW],
    )
    audit_logger: audit.AuditLogger = current_app.audit_logger  # type: ignore
    tenant_watcher_client: TenantsClient = current_app.tenant_watcher_client  # type: ignore
    with request_context.with_context_logging():
        tenant_name = tenant_watcher_client.get_tenant_name(tenant_id) or "unknown"
        audit_logger.write_audit_log(
            extract_user(request) or "unknown",
            auth_entities_pb2.UserId.UserIdType.Name(
                auth_entities_pb2.UserId.INTERNAL_IAP
            ),
            tenant_name=tenant_name,
            message=f"Playback next edit request within tenant {tenant_name}",
        )

        # the request data here comes from `RequestPlaygroundComponent` in playground.tsx.
        data: dict = request.get_json()
        assert data is not None, "No data found from frontend"

        # Override the conversion of Blobs. See _dict_to_blobs docs.
        if "blobs" in data:
            data["blobs"] = _dict_to_blobs(data["blobs"])

        request_id = uuid.uuid4()
        logging.info("Assigning request id %s", request_id)
        model_finder_client: model_finder_pb2_grpc.ApiProxyStub = (  # type: ignore
            current_app.model_finder_client  # type: ignore
        )
        response = model_finder_client.GetGenerationModels(
            model_finder_pb2.GetModelsRequest(),
            metadata=request_context.to_metadata(),
        )
        models = response.models
        assert models is not None, "No models found from backend."
        logging.info("models %s", models)
        logging.info("data %s", data.keys())
        for model in models:
            if model.name == data["model_name"]:
                break
        else:
            return Response(
                json.dumps({"code": grpc.StatusCode.NOT_FOUND.value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        logging.info("Model %s", model)
        assert model.next_edit.next_edit_endpoint, "Next edit endpoint not set"
        next_edit_client: NextEditClient = current_app.next_edit_host_client_factory(  # type: ignore
            model.next_edit.next_edit_endpoint
        )

        recent_changes = [
            completion_pb2.ReplacementText(
                blob_name=change.get("blob_name"),
                path=change.get("path"),
                char_start=change.get("char_start"),
                char_end=change.get("char_end"),
                replacement_text=change.get("replacement_text"),
                present_in_blob=change.get("present_in_blob", False),
                expected_blob_name=change.get("expected_blob_name", ""),
            )
            for change in data.get("recent_changes", [])
        ]

        vcs_change = next_edit_pb2.VCSChange(
            working_directory_changes=[
                next_edit_pb2.WorkingDirectoryChange(
                    before_path=change.get("before_path"),
                    after_path=change.get("after_path"),
                    change_type=next_edit_pb2.ChangeType.Value(
                        change.get("change_type")
                    ),
                    head_blob_name=change.get("head_blob_name"),
                    indexed_blob_name=change.get("indexed_blob_name"),
                    current_blob_name=change.get("current_blob_name"),
                )
                for change in data.get("vcs_change", {}).get(
                    "working_directory_changes", []
                )
            ]
        )

        edit_events = [
            edit_events_pb2.GranularEditEvent(
                path=event.get("path"),
                before_blob_name=event.get("before_blob_name"),
                after_blob_name=event.get("after_blob_name"),
                edits=[
                    edit_events_pb2.SingleEdit(
                        before_start=edit.get("before_start"),
                        after_start=edit.get("after_start"),
                        before_text=edit.get("before_text"),
                        after_text=edit.get("after_text"),
                    )
                    for edit in event.get("edits", [])
                ],
            )
            for event in data.get("edit_events", [])
        ]

        diagnostics = [
            next_edit_pb2.Diagnostic(
                location=next_edit_pb2.FileLocation(
                    path=diagnostic.get("location").get("path"),
                    line_start=diagnostic.get("location").get("line_start"),
                    line_end=diagnostic.get("location").get("line_end"),
                ),
                message=diagnostic.get("message"),
                severity=next_edit_pb2.DiagnosticSeverity.Value(
                    diagnostic.get("severity")
                ),
            )
            for diagnostic in data.get("diagnostics", [])
        ]

        # Reconstruct request message from JSON in request...
        request_pb = next_edit_pb2.NextEditRequest(
            model_name=data["model_name"],
            lang=data.get("lang", ""),
            instruction=data["instruction"],
            blobs=data.get("blobs"),
            recent_changes=recent_changes,
            vcs_change=vcs_change,
            edit_events=edit_events,
            path=data["path"],
            blob_name=data.get("blob_name"),
            selection_begin_char=data.get("selection_begin_char"),
            selection_end_char=data.get("selection_end_char"),
            prefix=data.get("prefix", ""),
            selected_text=data.get("selected_text", ""),
            suffix=data.get("suffix", ""),
            diagnostics=diagnostics,
            mode=next_edit_pb2.NextEditMode.Value(data["mode"]),
            scope=next_edit_pb2.NextEditScope.Value(data["scope"]),
            change_probability_override=data.get("change_probability_override"),
            # sequence_id=data.get("sequence_id"),
        )
        stream_response = next_edit_client.next_edit_stream(
            request_pb, request_context=request_context
        )

        for _ in stream_response:
            pass  # noqa: B007

        return jsonify({"request_id": request_context.request_id})


@bp.route("tenant/<tenant_id>/request/edit", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def create_edit_request_handler(tenant_id: str):
    """Handler to create an edit request."""

    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_R, token_exchange_pb2.CONTENT_RW],
    )
    audit_logger: audit.AuditLogger = current_app.audit_logger  # type: ignore
    tenant_watcher_client: TenantsClient = current_app.tenant_watcher_client  # type: ignore
    with request_context.with_context_logging():
        tenant_name = tenant_watcher_client.get_tenant_name(tenant_id) or "unknown"
        audit_logger.write_audit_log(
            extract_user(request) or "unknown",
            auth_entities_pb2.UserId.UserIdType.Name(
                auth_entities_pb2.UserId.INTERNAL_IAP
            ),
            tenant_name=tenant_name,
            message=f"Playback completion request within tenant {tenant_name}",
        )

        # the request data here comes from `RequestPlaygroundComponent` in playground.tsx.
        data: dict = request.get_json()
        assert data is not None, "No data found from frontend"

        # Override the conversion of Blobs. See _dict_to_blobs docs.
        if "blobs" in data:
            data["blobs"] = _dict_to_blobs(data["blobs"])

        request_id = uuid.uuid4()
        logging.info("Assigning request id %s", request_id)
        model_finder_client: model_finder_pb2_grpc.ApiProxyStub = (  # type: ignore
            current_app.model_finder_client  # type: ignore
        )
        response = model_finder_client.GetGenerationModels(
            model_finder_pb2.GetModelsRequest(),
            metadata=request_context.to_metadata(),
        )
        models = response.models
        assert models is not None, "No models found from backend"
        logging.info("models %s", models)
        for model in models:
            if model.name == data["model_name"]:
                break
        else:
            return Response(
                json.dumps({"code": grpc.StatusCode.NOT_FOUND.value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        logging.info("Model %s", model)
        assert model.edit.edit_endpoint, "Edit endpoint not set"
        edit_client: EditClient = current_app.edit_host_client_factory(  # type: ignore
            model.edit.edit_endpoint
        )

        position = data.get("position")
        if position is not None:
            position = edit_pb2.EditPosition(
                blob_name=position["blob_name"],
                prefix_begin=position["prefix_begin"],
                suffix_end=position["suffix_end"],
            )

        request_pb = edit_pb2.EditRequest(
            model_name=data["model_name"],
            instruction=data["instruction"],
            prefix=data["prefix"],
            selected_text=data["selected_text"],
            suffix=data.get("suffix", ""),
            path=data["path"],
            lang=data.get("lang", ""),
            blobs=data.get("blobs"),
            position=position,
        )
        response = edit_client.edit(request_pb, request_context=request_context)
        return jsonify({"request_id": request_context.request_id})


@bp.route("tenant/<tenant_id>/request/instruction", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def create_instruction_request_handler(tenant_id: str):
    """Handler to create an instruction request."""

    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_R, token_exchange_pb2.CONTENT_RW],
    )
    audit_logger: audit.AuditLogger = current_app.audit_logger  # type: ignore
    tenant_watcher_client: TenantsClient = current_app.tenant_watcher_client  # type: ignore
    with request_context.with_context_logging():
        tenant_name = tenant_watcher_client.get_tenant_name(tenant_id) or "unknown"
        audit_logger.write_audit_log(
            extract_user(request) or "unknown",
            auth_entities_pb2.UserId.UserIdType.Name(
                auth_entities_pb2.UserId.INTERNAL_IAP
            ),
            tenant_name=tenant_name,
            message=f"Playback completion request within tenant {tenant_name}",
        )

        # the request data here comes from `RequestPlaygroundComponent` in playground.tsx.
        data: dict = request.get_json()
        assert data is not None, "No data found from frontend"

        # Override the conversion of Blobs. See _dict_to_blobs docs.
        if "blobs" in data:
            data["blobs"] = _dict_to_blobs(data["blobs"])

        request_id = uuid.uuid4()
        logging.info("Assigning request id %s", request_id)
        model_finder_client: model_finder_pb2_grpc.ApiProxyStub = (  # type: ignore
            current_app.model_finder_client  # type: ignore
        )
        response = model_finder_client.GetGenerationModels(
            model_finder_pb2.GetModelsRequest(),
            metadata=request_context.to_metadata(),
        )
        models = response.models
        assert models is not None, "No models found from backend"
        logging.info("models %s", models)
        for model in models:
            if model.name == data["model_name"]:
                break
        else:
            return Response(
                json.dumps({"code": grpc.StatusCode.NOT_FOUND.value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        logging.info("Model %s", model)
        assert model.edit.edit_endpoint, "Instruction endpoint not set"
        edit_client: EditClient = current_app.edit_host_client_factory(  # type: ignore
            model.edit.edit_endpoint
        )

        position = data.get("position")
        if position is not None:
            position = edit_pb2.EditPosition(
                blob_name=position["blob_name"],
                prefix_begin=position["prefix_begin"],
                suffix_end=position["suffix_end"],
            )
        chat_history = data.get("chat_history", [])
        chat_history = [
            edit_pb2.Exchange(
                request_message=item["requestMessage"],
                response_text=item.get("responseText"),
            )
            for item in chat_history
        ]

        request_pb = edit_pb2.InstructionRequest(
            model_name=data["model_name"],
            instruction=data["instruction"],
            prefix=data["prefix"],
            selected_text=data["selected_text"],
            suffix=data.get("suffix", ""),
            path=data["path"],
            lang=data.get("lang", ""),
            blobs=data.get("blobs"),
            position=position,
            chat_history=chat_history,
            context_code_exchange_request_id=data.get(
                "context_code_exchange_request_id"
            ),
            code_block=data.get("code_block"),
            target_file_path=data.get("target_file_path"),
            target_file_content=data.get("target_file_content"),
        )
        response = edit_client.instruction(request_pb, request_context=request_context)
        return jsonify({"request_id": request_context.request_id})


@bp.route("tenant/<tenant_id>/request/chat", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def create_chat_request_handler(tenant_id: str):
    """Handler to create a chat request."""

    request_context = _get_request_context(
        tenant_id=tenant_id,
        scopes=[token_exchange_pb2.REQUEST_R, token_exchange_pb2.CONTENT_RW],
    )
    with request_context.with_context_logging():
        # the request data here comes from `RequestPlaygroundComponent` in playground.tsx.
        data: dict = request.get_json()
        assert data is not None, "No data found from frontend"

        # Override the conversion of Blobs. See _dict_to_blobs docs.
        if "blobs" in data:
            data["blobs"] = _dict_to_blobs(data["blobs"])

        request_id = uuid.uuid4()
        logging.info("Assigning request id %s", request_id)
        model_finder_client: model_finder_pb2_grpc.ApiProxyStub = (  # type: ignore
            current_app.model_finder_client  # type: ignore
        )
        response = model_finder_client.GetGenerationModels(
            model_finder_pb2.GetModelsRequest(),
            metadata=request_context.to_metadata(),
        )
        models = response.models
        assert models is not None, "No models found from backend"
        logging.info("models %s", models)
        for model in models:
            if model.name == data["modelName"]:
                break
        else:
            return Response(
                json.dumps({"code": grpc.StatusCode.NOT_FOUND.value[0]}),  # type: ignore
                status=404,
                mimetype="application/json",
            )
        logging.info("Model %s", model)
        assert model.chat.chat_endpoint, "Chat endpoint not set"
        chat_client: ChatClient = current_app.chat_host_client_factory(  # type: ignore
            model.chat.chat_endpoint
        )

        position = data.get("position")
        if position is not None:
            position = chat_pb2.ChatPosition(
                blob_name=position["blobName"],
                prefix_begin=position["prefixBegin"],
                suffix_end=position["suffixEnd"],
            )
        chat_history = data.get("chatHistory", [])
        chat_history = [
            chat_pb2.Exchange(
                request_message=item["requestMessage"],
                response_text=item.get("responseText"),
            )
            for item in chat_history
        ]
        blobs = []
        if data.get("blobs"):
            blobs.append(data["blobs"])
        else:
            blobs.append(
                blob_names_pb2.Blobs(
                    baseline_checkpoint_id="",
                    added=[],
                    deleted=[],
                )
            )
        request_pb = chat_pb2.ChatRequest(
            model_name=data["modelName"],
            message=data["message"],
            prefix=data["prefix"],
            selected_code=data["selectedCode"],
            suffix=data.get("suffix", ""),
            path=data["path"],
            lang=data.get("lang", ""),
            blobs=blobs,
            position=position,
            user_guided_blobs=data.get("userGuidedBlobs", []),
            chat_history=chat_history,
            external_source_ids=data.get("externalSourceIds", []),
            disable_auto_external_sources=data.get("disableAutoExternalSources", False),
            prompt_formatter_name=data.get("promptFormatterName", ""),
        )
        response = chat_client.chat(request_pb, request_context=request_context)
        return jsonify({"request_id": request_context.request_id})


@bp.route("tenant/<tenant_id>/auth/users", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def create_users_handler(tenant_id: str):
    user_data = request.get_json()

    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_RW]
    )
    with request_context.with_context_logging():
        if not user_data:
            return Response(
                json.dumps({"code": 400}),  # type: ignore
                status=400,
                mimetype="application/json",
            )

        client: auth_pb2_grpc.AuthServiceStub = (
            current_app.auth_central_client  # type: ignore
        )

        response = client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=user_data["email"].lower(), tenant_id=tenant_id
            ),
            metadata=request_context.to_metadata(),
        )

        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(data)


@bp.route("tenant/<tenant_id>/auth/users", methods=["GET"])
@iap_jwt_verified
@grpc_error_wrap
def list_users_handler(tenant_id: str):
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_R]
    )
    with request_context.with_context_logging():
        client: auth_pb2_grpc.AuthServiceStub = (
            current_app.auth_central_client  # type: ignore
        )

        response = client.ListTenantUsers(
            auth_pb2.ListTenantUsersRequest(tenant_id=tenant_id),
            metadata=request_context.to_metadata(),
        )

        data = json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
        return jsonify(data)


@bp.route("tenant/<tenant_id>/auth/users/revoke", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def revoke_tokens_by_email_handler(tenant_id: str):
    user_data = request.get_json()

    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_RW]
    )

    auth_central_client: auth_pb2_grpc.AuthServiceStub = (
        current_app.auth_central_client  # type: ignore
    )

    with request_context.with_context_logging():
        if not user_data:
            return Response(
                json.dumps({"code": 400}),  # type: ignore
                status=400,
                mimetype="application/json",
            )

        try:
            response = auth_central_client.RevokeUser(
                auth_pb2.RevokeUserRequest(
                    email=user_data["email"], tenant_id=tenant_id
                ),
                metadata=request_context.to_metadata(),
            )
        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
                response = auth_pb2.RevokeUserResponse(tokens_deleted=0)
            else:
                raise

        return Response(
            json.dumps(
                {"message": "Tokens deleted", "tokens_deleted": response.tokens_deleted}
            ),  # type: ignore
            status=200,
            mimetype="application/json",
        )


@bp.route("tenant/<tenant_id>/auth/users/<user_id>", methods=["GET"])
@iap_jwt_verified
@grpc_error_wrap
def get_user_handler(tenant_id: str, user_id: str):
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_R]
    )

    with request_context.with_context_logging():
        client: auth_pb2_grpc.AuthServiceStub = (
            current_app.auth_central_client  # type: ignore
        )
        user_response = client.GetUser(
            auth_pb2.GetUserRequest(tenant_id=tenant_id, user_id=user_id),
            metadata=request_context.to_metadata(),
        )

        additional_details = client.GetUserOnTenant(
            auth_pb2.GetUserOnTenantRequest(tenant_id=tenant_id, user_id=user_id),
            metadata=request_context.to_metadata(),
        )

        data = json_format.MessageToDict(
            user_response.user,
            including_default_value_fields=False,  # type: ignore
        )
        additional_data = json_format.MessageToDict(
            additional_details,
            including_default_value_fields=False,  # type: ignore
        )
        return jsonify({**data, **additional_data})


@bp.route("tenant/<tenant_id>/auth/users/<user_id>", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def update_user_handler(tenant_id: str, user_id: str):
    user_data = request.get_json()
    logging.info("getting request context")
    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_RW]
    )

    client: auth_pb2_grpc.AuthServiceStub = (
        current_app.auth_central_client  # type: ignore
    )
    with request_context.with_context_logging():
        user_response = client.GetUser(
            auth_pb2.GetUserRequest(tenant_id=tenant_id, user_id=user_id),
            metadata=request_context.to_metadata(),
        )

        update_request = auth_pb2.UpdateUserOnTenantRequest(
            tenant_id=tenant_id,
            user_id=user_id,
            customer_ui_roles=[
                auth_entities_pb2.CustomerUiRole.Value(role)
                for role in user_data.get("customerUiRoles", [])
            ],
        )

        update_response = client.UpdateUserOnTenant(
            update_request,
            metadata=request_context.to_metadata(),
        )

        data = json_format.MessageToDict(
            user_response.user,
            including_default_value_fields=False,  # type: ignore
        )
        update_data = json_format.MessageToDict(
            update_response,
            including_default_value_fields=False,  # type: ignore
        )

        return jsonify({**data, **update_data})


@bp.route("tenant/<tenant_id>/auth/users/<user_id>", methods=["DELETE"])
@iap_jwt_verified
@grpc_error_wrap
def remove_users_handler(tenant_id: str, user_id: str):
    auth_central_client: auth_pb2_grpc.AuthServiceStub = (
        current_app.auth_central_client  # type: ignore
    )

    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_RW]
    )
    with request_context.with_context_logging():
        user_response: auth_pb2.GetUserResponse = auth_central_client.GetUser(
            auth_pb2.GetUserRequest(user_id=user_id, tenant_id=tenant_id),
            metadata=request_context.to_metadata(),
        )
        logging.info("Found user [%s] [%s]", user_response.user.email[0:3], user_id)

        auth_central_client.RemoveUserFromTenant(
            auth_pb2.RemoveUserFromTenantRequest(user_id=user_id, tenant_id=tenant_id),
            metadata=request_context.to_metadata(),
        )
        logging.info("Removed user from auth_central successfully [%s]", user_id)
        return Response(
            json.dumps({"message": "User removed"}),  # type: ignore
            status=200,
            mimetype="application/json",
        )


@bp.route("tenant/<tenant_id>/auth/users/<user_id>/tokens", methods=["DELETE"])
@iap_jwt_verified
@grpc_error_wrap
def revoke_tokens_by_user_id_handler(tenant_id: str, user_id: str):
    auth_central_client: auth_pb2_grpc.AuthServiceStub = (
        current_app.auth_central_client  # type: ignore
    )

    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_RW]
    )
    with request_context.with_context_logging():
        user_response: auth_pb2.GetUserResponse = auth_central_client.GetUser(
            auth_pb2.GetUserRequest(user_id=user_id, tenant_id=tenant_id),
            metadata=request_context.to_metadata(),
        )
        logging.info("Found user [%s] [%s]", user_response.user.email[0:3], user_id)

        try:
            response = auth_central_client.RevokeUser(
                auth_pb2.RevokeUserRequest(
                    email=user_response.user.email, tenant_id=tenant_id
                ),
                metadata=request_context.to_metadata(),
            )
        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
                response = auth_pb2.RevokeUserResponse(tokens_deleted=0)
            else:
                raise

        return Response(
            json.dumps(
                {"message": "Tokens deleted", "tokens_deleted": response.tokens_deleted}
            ),  # type: ignore
            status=200,
            mimetype="application/json",
        )


@bp.route("tenant/<tenant_id>/auth/users/<user_id>/block", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def block_user_handler(tenant_id: str, user_id: str):
    auth_central_client: auth_pb2_grpc.AuthServiceStub = (
        current_app.auth_central_client  # type: ignore
    )

    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_RW]
    )
    with request_context.with_context_logging():
        # Verify the user exists
        user_response: auth_pb2.GetUserResponse = auth_central_client.GetUser(
            auth_pb2.GetUserRequest(user_id=user_id, tenant_id=tenant_id),
            metadata=request_context.to_metadata(),
        )
        if any(
            suspension.suspension_type
            == auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE
            for suspension in user_response.user.suspensions
        ):
            return Response(
                json.dumps(
                    {
                        "message": "User is already suspended for API abuse",
                        "status": "no_action_needed",
                        "tokens_deleted": 0,
                    }
                ),  # type: ignore
                status=200,
                mimetype="application/json",
            )

        # If user is exempt from suspensions, remove the exemption
        if user_response.user.suspension_exempt:
            auth_central_client.UpdateSuspensionExemption(
                auth_pb2.UpdateSuspensionExemptionRequest(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    exempt=False,
                ),
                metadata=request_context.to_metadata(),
            )

        # Block the user with support violation
        response = auth_central_client.CreateUserSuspension(
            auth_pb2.CreateUserSuspensionRequest(
                user_id=user_id,
                tenant_id=tenant_id,
                suspension_type=auth_entities_pb2.UserSuspensionType.USER_SUSPENSION_TYPE_API_ABUSE,
                evidence="Support team blocked user",
            ),
            metadata=request_context.to_metadata(),
        )

        return Response(
            json.dumps(
                {"message": "User blocked", "tokens_deleted": response.tokens_deleted}
            ),  # type: ignore
            status=200,
            mimetype="application/json",
        )


@bp.route("tenant/<tenant_id>/auth/users/<user_id>/unblock", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def unblock_user_handler(tenant_id: str, user_id: str):
    auth_central_client: auth_pb2_grpc.AuthServiceStub = (
        current_app.auth_central_client  # type: ignore
    )

    request_context = _get_request_context(
        tenant_id=tenant_id, scopes=[token_exchange_pb2.AUTH_RW]
    )
    with request_context.with_context_logging():
        # Verify the user exists
        user_response: auth_pb2.GetUserResponse = auth_central_client.GetUser(
            auth_pb2.GetUserRequest(user_id=user_id, tenant_id=tenant_id),
            metadata=request_context.to_metadata(),
        )

        if (
            not user_response
            or not hasattr(user_response, "user")
            or not user_response.user
        ):
            return Response(
                json.dumps({"message": "User not found"}),  # type: ignore
                status=404,
                mimetype="application/json",
            )

        suspension_ids = [
            suspension.suspension_id for suspension in user_response.user.suspensions
        ]

        if not suspension_ids:
            return Response(
                json.dumps(
                    {"message": "User is not blocked", "status": "no_action_needed"}
                ),  # type: ignore
                status=200,
                mimetype="application/json",
            )

        # Mark user as exempt from suspensions
        auth_central_client.UpdateSuspensionExemption(
            auth_pb2.UpdateSuspensionExemptionRequest(
                user_id=user_id,
                tenant_id=tenant_id,
                exempt=True,
            ),
            metadata=request_context.to_metadata(),
        )

        # Handle suspensions
        if suspension_ids:
            auth_central_client.DeleteUserSuspensions(
                auth_pb2.DeleteUserSuspensionsRequest(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    suspension_ids=suspension_ids,
                ),
                metadata=request_context.to_metadata(),
            )

        return Response(
            json.dumps({"message": "User unblocked"}),  # type: ignore
            status=200,
            mimetype="application/json",
        )


@bp.route("feature-flags", methods=["GET"])
@iap_jwt_verified
@grpc_error_wrap
def get_feature_flags():
    ff = {
        "flags": {
            "genieUrl": current_app.config["GENIE_URL"],
            "grpcDebugEnabled": current_app.grpc_debug_client is not None,  # type: ignore
        },
    }
    logging.info("Feature flags: %s", ff)
    return Response(
        json.dumps(ff),
        status=200,
        mimetype="application/json",
    )


def _merge_event_into_response(
    event: request_insight_pb2.RequestEvent,
    response: request_insight_pb2.GetRequestInfoResponse,
):
    """Merge event into response."""
    event_type = event.WhichOneof("event")
    assert event_type is not None
    assert hasattr(response.info, event_type) or hasattr(
        response.info, event_type + "_list"
    ), f"Event type {event_type} not found in response"
    use_merge_from = hasattr(response.info, event_type)
    if use_merge_from:
        getattr(response.info, event_type).MergeFrom(getattr(event, event_type))
        getattr(response.info, event_type + "_time").MergeFrom(event.time)
    else:
        getattr(response.info, event_type + "_list").append(getattr(event, event_type))
        getattr(response.info, event_type + "_time_list").append(event.time)


def _get_request_insight_data(
    tenant_id: str,
    request_id: str,
    request_context: RequestContext,
) -> dict[str, Any]:
    """Helper function to get request insight data.

    This gets the request info data from the request insight service, converts
    it into a dict, then returns it. Note that no error handling is done here,
    so any callers may want to do that.
    """
    merged_response = request_insight_pb2.GetRequestInfoResponse()
    central_client: RequestInsightCentralClient = (
        current_app.request_insight_central_client  # type: ignore
    )
    get_request = request_insight_central_pb2.GetRequestEventsRequest(
        tenant_id=tenant_id,
        request_id=request_id,
    )
    logging.info("get request %s", get_request)
    events: list[request_insight_pb2.RequestEvent] = []
    for response in central_client.get_request_events(get_request, request_context):
        events.append(response.request_event)

    # Mock the old request insight service to leverage the existing code
    for event in events:
        _merge_event_into_response(event, merged_response)

    # NOTE: `json_format.MessageToDict` converts the field names to lowerCamelCase.
    data: dict[str, Any] = json_format.MessageToDict(
        merged_response.info,
        including_default_value_fields=True,  # type: ignore
    )  # type: ignore

    # Override the conversion of Blobs. See _blobs_to_dict docs.
    if "inferRequest" in data and "blobs" in data["inferRequest"]:  # type: ignore
        data["inferRequest"]["blobs"] = _blobs_to_dict(  # type: ignore
            merged_response.info.completion_host_request.blobs
        )
    if "editHostRequest" in data and "blobs" in data["editHostRequest"]["request"]:  # type: ignore
        data["editHostRequest"]["request"]["blobs"] = _blobs_to_dict(  # type: ignore
            merged_response.info.edit_host_request.request.blobs
        )
    if (
        "instructionHostRequest" in data
        and "blobs" in data["instructionHostRequest"]["request"]
    ):  # type: ignore
        data["instructionHostRequest"]["request"]["blobs"] = _blobs_to_dict(  # type: ignore
            merged_response.info.instruction_host_request.request.blobs
        )
    if "chatHostRequest" in data and "blobs" in data["chatHostRequest"]["request"]:  # type: ignore
        data["chatHostRequest"]["request"]["blobs"] = [
            _blobs_to_dict(b)
            for b in merged_response.info.chat_host_request.request.blobs
        ]
    if "completionHostRequest" in data and "blobs" in data["completionHostRequest"]:  # type: ignore
        data["completionHostRequest"]["blobs"] = _blobs_to_dict(  # type: ignore
            merged_response.info.completion_host_request.blobs
        )
    if (
        "nextEditHostRequest" in data
        and "blobs" in data["nextEditHostRequest"]["request"]
    ):  # type: ignore
        data["nextEditHostRequest"]["request"]["blobs"] = _blobs_to_dict(  # type: ignore
            merged_response.info.next_edit_host_request.request.blobs
        )
    if (
        "embeddingSearchRequest" in data  # type: ignore
        and "blobs" in data["embeddingSearchRequest"]  # type: ignore
    ):
        data["embeddingSearchRequest"]["blobs"] = _blobs_to_dict(  # type: ignore
            merged_response.info.completion_host_request.blobs
        )
    # name migration (embedding->embeddings)
    if (
        "embeddingsSearchRequest" in data  # type: ignore
        and "blobs" in data["embeddingsSearchRequest"]  # type: ignore
    ):
        data["embeddingsSearchRequest"]["blobs"] = _blobs_to_dict(  # type: ignore
            merged_response.info.completion_host_request.blobs
        )
    if (
        "remoteToolCallRequest" in data  # type: ignore
        and "codebaseRetrievalRequest" in data["remoteToolCallRequest"]  # type: ignore
        and "blobs" in data["remoteToolCallRequest"]["codebaseRetrievalRequest"]  # type: ignore
    ):
        data["remoteToolCallRequest"]["codebaseRetrievalRequest"]["blobs"] = [  # type: ignore
            _blobs_to_dict(b)
            for b in merged_response.info.remote_tool_call_request.codebase_retrieval_request.blobs
        ]

    return data


def _blobs_to_dict(blobs: blob_names_pb2.Blobs) -> dict[str, Any]:
    """Convert a Blobs proto to a dict.

    We need to override the default MessageToDict behavior for Blobs protos because our frontends
    expect hex-encoded blob names, but MessageToDict base64 encodes bytes. Doing the conversion here
    means that the extension and the support site can remain symmetric in their handling of blob
    names.
    """
    added = [blob.hex() for blob in blobs.added]
    deleted = [blob.hex() for blob in blobs.deleted]
    return {
        "baselineCheckpointId": blobs.baseline_checkpoint_id,
        "added": added,
        "deleted": deleted,
    }


def _dict_to_blobs(blobs: dict[str, Any]) -> blob_names_pb2.Blobs:
    """Convert a dict to a Blobs proto.

    This does the opposite of _blobs_to_dict, converting hex from the frontend
    to base64 encoded strings.
    """
    added = sorted([bytes.fromhex(x) for x in blobs["added"]])
    deleted = sorted([bytes.fromhex(x) for x in blobs["deleted"]])
    return blob_names_pb2.Blobs(
        baseline_checkpoint_id=blobs.get("baseline_checkpoint_id", "")
        or blobs.get("baselineCheckpointId", ""),
        added=added,
        deleted=deleted,
    )


@bp.route("grpc_endpoints", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def get_endpoints():
    """Get the list of endpoints.

    Args:
        request_context: The request context to use.

    Returns:
        The list of endpoints.
    """
    client: grpc_debug_client.GrpcDebugClient = (
        current_app.grpc_debug_client  # type: ignore
    )
    request_context = _get_request_context(
        tenant_id="",
        scopes=[],
    )
    response = client.get_endpoints(request_context=request_context)
    return jsonify(
        [
            json_format.MessageToDict(
                endpoint,
                including_default_value_fields=True,  # type: ignore
            )
            for endpoint in response
        ]
    )


@bp.route("grpc_service_methods", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def get_service_methods():
    """Get the endpoint info for a given endpoint.

    Args:
        endpoint_name: The name of the endpoint.
        request_context: The request context to use.

    Returns:
        The endpoint info.
    """
    data = request.get_json()
    endpoint_name = data.get("endpoint", "")
    client: grpc_debug_client.GrpcDebugClient = (
        current_app.grpc_debug_client  # type: ignore
    )
    request_context = _get_request_context(
        tenant_id="",
        scopes=[],
    )
    logging.info("get service methods for endpoint %s", endpoint_name)
    response = client.get_service_methods(
        endpoint=endpoint_name, request_context=request_context, service_name=""
    )
    return jsonify(
        [
            json_format.MessageToDict(method, including_default_value_fields=True)  # type: ignore
            for method in response
        ]
    )


@bp.route("grpc_token_scopes", methods=["GET"])
@iap_jwt_verified
@grpc_error_wrap
def get_token_scopes():
    """Get the list of token scopes.

    Args:
        request_context: The request context to use.

    Returns:
        The list of token scopes.
    """

    return jsonify(
        [
            token_exchange_pb2.Scope.Name(scope)
            for scope in token_exchange_pb2.Scope.values()
        ]
    )


@bp.route("grpc_invoke_method", methods=["POST"])
@iap_jwt_verified
@grpc_error_wrap
def invoke_method():
    """Invoke a method.

    Args:
        endpoint: The endpoint to use.
        service_name: The service name to use.
        method_name: The method name to use.
        request_json: The request json to use.
        request_context: The request context to use.

    Returns:
        The response json.
    """
    data = request.get_json()
    endpoint = data["endpoint"]
    scopes = data.get("scopes", [])
    tenant_id = data.get("tenant_id", "")
    service_name = data["service"]
    method_name = data["method"]
    request_json = data.get("request", "")

    try:
        request_context = _get_request_context(
            tenant_id=tenant_id,
            scopes=scopes,
        )
    except grpc.RpcError as rpc_error:
        log.info("Failed to get service token")
        response = grpc_debug_pb2.MethodInvocationResponse()
        c: grpc.StatusCode = rpc_error.code()  # type: ignore
        response.error.code = c.value[0]  # type: ignore
        response.error.message = rpc_error.details()  # type: ignore
        return json_format.MessageToDict(
            response,
            including_default_value_fields=True,  # type: ignore
        )
    client: grpc_debug_client.GrpcDebugClient = (
        current_app.grpc_debug_client  # type: ignore
    )
    logging.info("invoke method %s", method_name)
    response: grpc_debug_pb2.MethodInvocationResponse = client.invoke_method(
        endpoint=endpoint,
        service_name=service_name,
        method_name=method_name,
        request_json=request_json,
        request_context=request_context,
    )

    return json_format.MessageToDict(
        response,
        including_default_value_fields=True,  # type: ignore
    )
