import { LoaderFunctionArgs } from "@remix-run/router";
import { Duration } from "@bufbuild/protobuf";
import { ConnectError, Code } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { getAuthCentralClient } from "../.server/grpc/auth-central";
import { getTokenExchangeClient } from "../.server/grpc/token-exchange";
import { connectCodeToHttpStatus } from "../utils/grpc-utils";
import {
  UserQueryParamsSchema,
  type User,
  type UsersResponse,
  type ErrorResponse,
} from "../schemas/users";

/* eslint-disable import/no-unresolved */
import { Scope } from "~services/token_exchange/token_exchange_pb";
/* eslint-enable import/no-unresolved */

function getBillingMethodName(billingMethod?: number): string {
  switch (billingMethod) {
    case 0:
      return "Unknown";
    case 1:
      return "Stripe";
    case 2:
      return "Orb";
    case 3:
      return "Migrating to Orb";
    case 4:
      return "Orb with ending Stripe";
    default:
      return "Unknown";
  }
}

function getSuspensionTypeName(suspensionType: number): string {
  switch (suspensionType) {
    case 0:
      return "Unknown";
    case 1:
      return "API Abuse";
    case 2:
      return "Free Trial Abuse";
    default:
      return "Unknown";
  }
}
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);

  const queryParamsResult = UserQueryParamsSchema.safeParse({
    searchString: url.searchParams.get("searchString") || "",
    pageToken: url.searchParams.get("pageToken") || "",
    pageSize: url.searchParams.get("pageSize"),
  });

  // If validation fails, return a 400 error
  if (!queryParamsResult.success) {
    const errorResponse: ErrorResponse = {
      error: queryParamsResult.error.message,
    };
    return Response.json(errorResponse, { status: 400 });
  }

  // Extract validated parameters
  const { searchString, pageToken, pageSize } = queryParamsResult.data;

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  // Exchange IAP token for a service token with AUTH_R scope
  let signedToken: string;
  try {
    const response = await getTokenExchangeClient().getSignedTokenForIAPToken({
      iapToken: IAPJWT,
      scopes: [Scope.AUTH_R],
      expiration: new Duration({ seconds: BigInt(60 * 60) }),
    });
    signedToken = response.signedToken;
  } catch (error) {
    // Handle errors from token exchange
    if (error instanceof ConnectError) {
      logger.error(`Token exchange error: ${error.code} - ${error.message}`);

      // If it's a permission denied error, return 403
      if (error.code === Code.PermissionDenied) {
        const errorResponse: ErrorResponse = {
          error: "Permission denied: You don't have access to this resource",
        };
        return Response.json(errorResponse, { status: 403 });
      }

      // For other Connect errors, return appropriate HTTP status
      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in token exchange: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred during authentication",
    };
    return Response.json(errorResponse, { status: 500 });
  }

  try {
    const response = await getAuthCentralClient().getUsers(
      {
        searchString,
        pageToken,
        pageSize,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    // Transform the response to a more frontend-friendly format
    const users: User[] = response.users.map((user) => ({
      id: user.id,
      email: user.email,
      tenants: user.tenants,
      blocked: user.blocked,
      createdAt: user.createdAt?.toDate().toISOString(),
      suspensions: user.suspensions.map((suspension) => ({
        suspensionId: suspension.suspensionId,
        suspensionType: getSuspensionTypeName(suspension.suspensionType),
        evidence: suspension.evidence,
        createdTime: suspension.createdTime?.toDate().toISOString(),
      })),
      billingMethod: getBillingMethodName(user.billingMethod),
      stripeCustomerId: user.stripeCustomerId,
      orbCustomerId: user.orbCustomerId,
      subscriptionId: user.subscriptionId,
      orbSubscriptionId: user.orbSubscriptionId,
    }));

    const responseData: UsersResponse = {
      users,
      nextPageToken: response.nextPageToken,
    };

    return Response.json(responseData);
  } catch (error) {
    // Handle errors from getUsers
    if (error instanceof ConnectError) {
      logger.error(`GetUsers error: ${error.code} - ${error.message}`);

      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in getUsers: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred while fetching users",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
