package main

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	authservice "github.com/augmentcode/augment/services/auth/central/server/auth"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	orb "github.com/augmentcode/augment/services/integrations/orb"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
)

// MessageProcessor processes billing messages
type MessageProcessor struct {
	featureFlags featureflags.FeatureFlagHandle
	orbClient    orb.OrbClient
	authClient   authclient.AuthClient
	tokenClient  tokenexchangeclient.TokenExchangeClient
}

var (
	// EnableBillingEventIngestion controls whether to process events for billing
	EnableBillingEventIngestion = featureflags.NewBoolFlag("enable_billing_event_ingestion", false)

	// Mapping of ChatUserMessage.ChatMode values to Orb event names
	chatModeToOrbEventName = map[pb.ChatUserMessage_ChatMode]string{
		pb.ChatUserMessage_CHAT_MODE_AGENT: "user_message",
		pb.ChatUserMessage_CHAT_MODE_CHAT:  "user_message",
		// AU-10040: Uncomment this when we want to enable billing for remote agents
		//	pb.ChatUserMessage_CHAT_MODE_REMOTE_AGENT: "user_message",
	}

	// Prometheus metrics for Orb event ingestion
	ingestEventsSuccessCounter = promauto.NewCounter(prometheus.CounterOpts{
		Name: "billing_ingest_events_success",
		Help: "Count of successful calls to Orb IngestEvents API",
	})
	ingestEventsFailureCounter = promauto.NewCounter(prometheus.CounterOpts{
		Name: "billing_ingest_events_failure",
		Help: "Count of failed calls to Orb IngestEvents API",
	})
	ingestedEventsCounter = promauto.NewCounter(prometheus.CounterOpts{
		Name: "billing_ingested_events_count",
		Help: "Count of events ingested to Orb",
	})
)

// NewMessageProcessor creates a new message processor
func NewMessageProcessor(
	ctx context.Context,
	featureFlags featureflags.FeatureFlagHandle,
	orbClient orb.OrbClient,
	authClient authclient.AuthClient,
	tokenClient tokenexchangeclient.TokenExchangeClient,
) *MessageProcessor {
	return &MessageProcessor{
		featureFlags: featureFlags,
		orbClient:    orbClient,
		authClient:   authClient,
		tokenClient:  tokenClient,
	}
}

// Close closes any resources used by the processor
func (p *MessageProcessor) Close() {
	// Nothing to close for now
}

// getUserInfo gets user information from Auth Central
func (p *MessageProcessor) getUserBillingInfo(ctx context.Context, userID string, tenantID string) (*authservice.GetUserBillingInfoResponse, error) {
	token, err := p.tokenClient.GetSignedTokenForService(ctx, tenantID, []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_R})
	if err != nil {
		return nil, fmt.Errorf("failed to get signed token: %w", err)
	}

	sessionId := requestcontext.NewRandomRequestSessionId()
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, ServiceName, token,
	)

	resp, err := p.authClient.GetUserBillingInfo(ctx, requestCtx, userID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user billing info: %w", err)
	}
	return resp, nil
}

// ProcessMessage processes a single message from the request insight topic
func (p *MessageProcessor) ProcessMessage(ctx context.Context, message *pb.RequestInsightMessage) error {
	// Check if billing event ingestion is enabled
	enabled, err := EnableBillingEventIngestion.Get(p.featureFlags)
	if err != nil {
		log.Debug().Err(err).Msg("Error checking billing event processing feature flag")
	}
	if !enabled {
		log.Debug().Msg("Billing event processing is disabled by feature flag")
		return nil
	}

	orbEvents := make([]*orb.OrbEvent, 0)

	switch msg := message.Message.(type) {
	case *pb.RequestInsightMessage_UpdateRequestInfoRequest:
		req := msg.UpdateRequestInfoRequest
		for _, event := range req.GetEvents() {
			user_message := event.GetChatUserMessage()
			if user_message == nil {
				continue
			}

			// Only ingest requests that are mapped to Orb event names
			orbEventName, ok := chatModeToOrbEventName[user_message.GetChatMode()]
			if !ok {
				log.Info().Str("chat_mode", user_message.GetChatMode().String()).Msg("Skipping user message with unsupported chat mode")
				continue
			}

			// Get the tenant ID from the request
			tenantID := req.GetTenantInfo().GetTenantId()
			if tenantID == "" {
				log.Error().Msg("Failed to get tenant id")
				continue
			}

			opaqueUserID := user_message.GetOpaqueUserId().GetUserId()
			if opaqueUserID == "" {
				log.Error().Msg("Failed to get user id from tenant info")
				continue
			}

			// verify the user ID is a UUID, not a token
			_, err := uuid.Parse(opaqueUserID)
			if err != nil {
				log.Info().Err(err).
					Str("user_id", opaqueUserID).
					Msg("User ID is not a UUID, will not ingest event to Orb")
				continue
			}

			// verify the user ID type is AUGMENT
			if user_message.GetOpaqueUserId().GetUserIdType() != auth_entities.UserId_AUGMENT {
				log.Info().
					Str("user_id", opaqueUserID).
					Str("user_id_type", user_message.GetOpaqueUserId().GetUserIdType().String()).
					Msg("User ID type is not augment, will not ingest event to Orb")
				continue
			}

			billingInfo, err := p.getUserBillingInfo(ctx, opaqueUserID, tenantID)
			if err != nil {
				log.Error().Err(err).
					Str("user_id", opaqueUserID).
					Str("tenant_id", tenantID).
					Msg("Failed to get user billing info")
				continue
			}
			// Only send events to Orb if the user has a valid Orb customer ID
			if billingInfo.OrbCustomerId == "" {
				log.Error().
					Str("user_id", opaqueUserID).
					Str("tenant_id", tenantID).
					Msg("User or team does not have an orb customer id")
				continue
			}

			orbEvent := &orb.OrbEvent{
				IdempotencyKey: req.GetRequestId(),
				CustomerOrbID:  &billingInfo.OrbCustomerId,
				EventName:      orbEventName,
				Timestamp:      event.GetTime().AsTime(),
				Properties: map[string]interface{}{
					"chat_mode":  user_message.GetChatMode().String(),
					"user_id":    opaqueUserID,
					"user_email": billingInfo.Email,
					"tenant_id":  tenantID,
					"model_name": user_message.GetModelName(),
				},
			}

			log.Info().Str("orb_event", fmt.Sprintf("%+v", orbEvent)).Msg("Transformed user message to Orb event")
			orbEvents = append(orbEvents, orbEvent)
		}
	}

	if len(orbEvents) > 0 {
		eventCount := len(orbEvents)
		log.Info().Int("event_count", eventCount).Msg("Sending events to Orb")

		// If ingestion fails, return error to trigger pub-sub retry
		if err := p.orbClient.IngestEvents(ctx, orbEvents); err != nil {
			log.Error().Err(err).Msg("Failed to ingest events to Orb")
			ingestEventsFailureCounter.Inc()
			return err
		}
		ingestEventsSuccessCounter.Inc()
		ingestedEventsCounter.Add(float64(eventCount))
	}

	return nil
}
