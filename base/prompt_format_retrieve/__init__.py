"""Module containing prompt formatting logic for different retrieval models."""

import logging
from typing import Any, Optional

from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.chatanol_prompt_formatter import (
    Cha<PERSON>l<PERSON><PERSON><PERSON>ument<PERSON>ormatter,
    Cha<PERSON>l<PERSON><PERSON>ueryFormatter,
)
from base.prompt_format_retrieve.diesel_embedding_prompt_formatter import (
    Diesel1Document<PERSON>ormatter,
    Diesel1QueryFormatter,
)
from base.prompt_format_retrieve.ethanol_embedding_prompt_formatter import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ormatter,
    <PERSON>ol<PERSON><PERSON>ueryFormatter,
    <PERSON>ol<PERSON><PERSON><PERSON><PERSON>SimpleChatFormatter,
    Ethanol6QuerySimpleInstructFormatter,
)
from base.prompt_format_retrieve.passthrough_prompt_formatter import (
    PassthroughDocumentPromptFormatter,
    PassthroughPromptFormatter,
)
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    CompletionRetrieverPromptInput,
    DocumentRetrieverPromptInput,
    InstructRetrieverPromptInput,
    RetrieverPromptFormatter,
)
from base.tokenizers.tokenizer import Tokenizer


def get_retrieval_prompt_formatter_by_name(
    name: str,
    tokenizer: Tokenizer,
    apportionment_config: Optional[TokenApportionmentConfig] = None,
) -> RetrieverPromptFormatter:
    """Returns the prompt formatter by name.

    Args:
        name: name of the formatter
        tokenizer: instance of the tokenizer to use.
        apportionment_config: Hints for the apportionment of tokens during the prompt formatting. If not set, a default is used.
        prompt_formatter_config: optional additional configuration for the prompt formatter.

    If there is no formatter with the given name, an exception is thrown.
    """
    if name == "ethanol6-embedding-with-path-query":
        return Ethanol6QueryFormatter(
            apportionment_config,
            tokenizer,
            add_path=True,
            add_suffix=False,
        )
    elif (
        name == "ethanol6-embedding-with-path-query-add-selected-code-and-instructions"
    ):
        return Ethanol6QuerySimpleInstructFormatter(
            apportionment_config,
            tokenizer,
            add_path=True,
            add_suffix=False,
        )
    elif name == "ethanol6-embedding-with-path-key":
        return Ethanol6DocumentFormatter(
            apportionment_config,
            tokenizer,
            add_path=True,
        )
    elif name == "ethanol6.16.1-query-embedding":
        return Ethanol6QueryFormatter(
            apportionment_config,
            tokenizer,
            add_path=True,
            add_suffix=True,
            prefix_suffix_budget_fraction=0.9,
        )
    elif name == "ethanol6-embedding-simple-chat":
        return Ethanol6QuerySimpleChatFormatter(
            apportionment_config,
            tokenizer,
            # Chathanol is not trained to support selected code in the prompt,
            # so we temporarily disable it.
            add_selected_code=False,
            add_path=False,
        )
    elif name == "chatanol6":
        return Chatanol6QueryFormatter(
            apportionment_config,
            tokenizer,
        )
    elif name == "chatanol6-singleturnisspecial":
        return Chatanol6QueryFormatter(
            apportionment_config,
            tokenizer,
            single_turn_is_special=True,
        )
    elif name == "ethanol6.16.1-query-embedding-add-selected-code-and-instructions":
        return Ethanol6QuerySimpleInstructFormatter(
            apportionment_config,
            tokenizer,
            add_path=True,
            add_suffix=True,
            prefix_suffix_budget_fraction=0.9,
        )
    elif name == "chatanol6-embedding":
        return Chatanol6DocumentFormatter(
            apportionment_config,
            tokenizer,
            # We disable path in `chatanol6-embedding` for backwards compatibility.
            add_path=False,
        )
    elif name == "chatanol6-embedding-with-path-key":
        return Chatanol6DocumentFormatter(
            apportionment_config,
            tokenizer,
            add_path=True,
        )
    elif name == "diesel1-embedding":
        return Diesel1DocumentFormatter(apportionment_config, tokenizer)
    elif name == "diesel1-query-embedding":
        return Diesel1QueryFormatter(
            apportionment_config,
            tokenizer,
            add_suffix=False,
            prefix_budget_fraction=0.375,
            instruction_budget_fraction=0.05,
            selected_code_budget_fraction=0.2,
        )
    elif name == "passthrough":
        return PassthroughPromptFormatter(tokenizer)
    elif name == "passthrough-document":
        return PassthroughDocumentPromptFormatter(
            apportionment_config,
            tokenizer,
        )
    elif name == "next-edit-location-query":
        # TODO(arun): Figure out how to configure this query formatter by fixing the
        # interface problem.
        # NOTE(arun): We need this import here to avoid a circular import situation.
        from base.prompt_format_next_edit.location_prompt_formatter import (
            NextEditLocationQueryFormatter,
        )

        return NextEditLocationQueryFormatter(tokenizer)
    elif name == "autofix-location-query":
        # NOTE(arun): We need this import here to avoid a circular import situation.
        from base.prompt_format_next_edit.location_prompt_formatter import (
            NextEditLocationQueryFormatter,
        )

        return NextEditLocationQueryFormatter(
            tokenizer,
            config=NextEditLocationQueryFormatter.Config(
                diff_context_lines=3,
                max_prompt_tokens=8192,
                max_instruction_tokens=0,
                max_diff_tokens=4096,
                max_command_output_tokens=4096,
                use_smart_header=True,
                deduplicate_identical_paths=True,
                truncate_instructions_tail=True,
            ),
        )
    elif name == "next-edit-gen-query":
        # NOTE(moogi): Following Arun's comments from above, making the import here.
        from base.prompt_format_next_edit.retrieval_prompt_formatter import (
            EditGenRetrievalQueryFormatterConfig,
            EditGenRetrievalQueryPromptFormatter,
        )

        # NOTE(arun): Frustratingly, we can't configure this formatter via an argument.
        # These magic values were chosen after experimentation.
        return EditGenRetrievalQueryPromptFormatter(
            tokenizer=tokenizer,
            config=EditGenRetrievalQueryFormatterConfig(
                diff_context_lines=3,
                max_prompt_tokens=2048,
                section_budgets={
                    "prefix_tks": 125,
                    "selected_tks": 1250,
                    "suffix_tks": 125,
                    "filename_tks": 50,
                    "instruction_tks": 0,
                    "diff_tks": 450,
                },
            ),
        )
    else:
        logging.error("Invalid prompt formatter name '%s'", name)
        raise ValueError(f"Invalid prompt formatter name: {name}.")


__reexported__ = [
    ChatRetrieverPromptInput,
    CompletionRetrieverPromptInput,
    DocumentRetrieverPromptInput,
    InstructRetrieverPromptInput,
]
