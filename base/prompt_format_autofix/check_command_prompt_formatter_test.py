"""Tests for the autofix check command prompt formatter."""

import pytest

from base.prompt_format_autofix.check_command_prompt_formatter import (
    AutofixCheckCommandInput,
    AutofixCheckCommandTokenApportionment,
    AutofixCheckCommandV1PromptFormatter,
)
from base.third_party_clients.token_counter.token_counter_claude import (
    <PERSON><PERSON><PERSON>Counter,
)

expected_prompt = """The command and its output:
```bash
$ echo hello
hello
```
According to the provided output did the command execute any code related checks such as compiling, linting, testing, etc?"""


def generate_dummy_check_command_prompt() -> str:
    token_apportionment = AutofixCheckCommandTokenApportionment()
    token_counter = ClaudeTokenCounter()
    prompter = AutofixCheckCommandV1PromptFormatter(token_apportionment, token_counter)

    command = "echo hello"
    output = "hello"
    prompt_input = AutofixCheckCommandInput(command=command, command_output=output)
    prompt_output = prompter.format_prompt(prompt_input)
    return prompt_output.message


def test_autofix_check_command_prompt_formatter_basic():
    """This is a simple sanity check to catch obvious bugs in the autofix check command's prompt formatting."""
    prompt = generate_dummy_check_command_prompt()
    assert prompt == expected_prompt
