"""Tests for the autofix check command prompt formatter."""

from typing import Sequence
from base.diff_utils.diff_utils import File

from base.diff_utils.changes import Modified
from base.prompt_format_autofix.common import (
    PromptChunkWithLines,
    AutofixSteeringMessage,
    AutofixFileFix,
    AutofixFixPlan,
)
from base.prompt_format_autofix.create_fix_plan_prompt_formatter import (
    AutofixCreateFixPlanInput,
    AutofixCreateFixPlanTokenApportionment,
    AutofixCreateFixPlanV1PromptFormatter,
)

from base.third_party_clients.token_counter.token_counter_claude import (
    <PERSON><PERSON><PERSON><PERSON>ounter,
)

expected_prompt_without_user_feedback = """The following information is provided to assist you in creating a fix plan:
the command and its output are as follows:
```bash
$ echo hello
hello
```

Recent changes in git diff format are included here:
--- file1.py
+++ file1.py
@@ -1,2 +1,2 @@
-line 1
+new line 1
 line 2

The potential edit locations for the fix, as well as some context from the codebase, are:
Here are excerpts from the file 'bar.py':
```
   0: # You can aggregate
   1: # with a maxing
   2: # You can aggregate
   3: # with a maxing
   4: # function.
```

Here is an excerpt from the file 'foo.py':
```
   0: # You can aggregate
   1: # with a pooling function.
```

Carefully review the provided information and create a detailed fix plan accordingly.
Follow these guidelines:
 - Do not make any changes unless absolutely necessary to fix the errors in the log.
 - Reverting changes to fix errors is only allowed if you are absolutely certain that the changes cannot be justified.
 - If the errors are caused by unused code, you are allowed to remove the unused code.
 - Provide updated code blocks along with the starting line number for each change.
 - Each code block must consist of at least 5 (five) lines, and must contain all of the changes required to fix the errors.
 - For conciseness, unchanged lines in the code block can be omitted using '...'.
 - If multiple fix options exist, select the one with the fewest changes.
 - Changes must be limited to the following list of files:
    - bar.py
    - foo.py

Please respond in the following format, think step-by-step:

<thinking>
    <reasoning>
        Analysis of the recent changes...
    </reasoning>
    <reasoning>
        Analysis of the command output...
    </reasoning>
    <reasoning>
        Analysis of the root cause...
    </reasoning>
    <reasoning>
        Analysis of the potential edit locations...
    </reasoning>
    ...
    <reasoning>
        In conclusion, the following changes are required to fix the errors in the log...
    </reasoning>
</thinking>

<critical_thinking>
    Critically evaluate each reasoning step above, determining whether it is completely sound, and conclude by assessing the soundness of the overall conclusion...
</critical_thinking>

<fix_plan>
    <fix_desc>
        To fix the error we need to change...
    </fix_desc>
    <changes>
        <change>
            <path>file1.py</path>
            <change_desc>Detailed and comprehensive description of the changes to be made in file1.py, including references to unique location identifers such as function names, variable names, tags, etc.</change_desc>
            <updated_code_block_start_line>3</updated_code_block_start_line>
            <updated_code_block>
            ```
                def foo(x):
                    if x == 0:
                        print("bar")
            ```
            </updated_code_block>
        </change>
        <change>
            <path>file2.py</path>
            <change_desc>Detailed and comprehensive description of the changes to be made in file2.py, including references to unique location identifers such as function names, variable names, tags, etc.</change_desc>
            <updated_code_block_start_line>5</updated_code_block_start_line>
            <updated_code_block>
            ```
                def bar(y):
                    if x == 1:
                        print("foo")
            ```
            </updated_code_block>
        </change>
        ...
    </changes>
</fix_plan>
"""

expected_prompt_with_user_feedback = """The following information is provided to assist you in creating a fix plan:
the command and its output are as follows:
```bash
$ echo hello
hello
```

Recent changes in git diff format are included here:
--- file1.py
+++ file1.py
@@ -1,2 +1,2 @@
-line 1
+new line 1
 line 2

The potential edit locations for the fix, as well as some context from the codebase, are:
Here are excerpts from the file 'bar.py':
```
   0: # You can aggregate
   1: # with a maxing
   2: # You can aggregate
   3: # with a maxing
   4: # function.
```

Here is an excerpt from the file 'foo.py':
```
   0: # You can aggregate
   1: # with a pooling function.
```

You previously suggested some incorrect fixes. A smart colleague provided some feedback for how to improve these prior fixes.
Here is that feedback from least to most recent. Information for each previous fix attempt is enclosed in <previous_fix_attempt> tags.
<previous_fix_attempt>
<previous_fix_attempt_plan>
Change MAGIC to 2 in file3.py
 - file3.py: Change MAGIC to 2 in file3.py
</previous_fix_attempt_plan>
<previous_fix_attempt_critical_feedback>It should be changed to 5</previous_fix_attempt_critical_feedback>
</previous_fix_attempt>;

<previous_fix_attempt>
<previous_fix_attempt_plan>
This is a fix description
 - file2.py: This is a change description
</previous_fix_attempt_plan>
<previous_fix_attempt_diff>
--- file2.py
+++ file2.py
@@ -1,2 +1,2 @@
-line 1
+new line 1
 line 2
</previous_fix_attempt_diff>
<previous_fix_attempt_critical_feedback>This is a steering message</previous_fix_attempt_critical_feedback>
</previous_fix_attempt>

Carefully review the provided information and create a detailed fix plan accordingly.
Follow these guidelines:
 - Do not make any changes unless absolutely necessary to fix the errors in the log.
 - Reverting changes to fix errors is only allowed if you are absolutely certain that the changes cannot be justified.
 - If the errors are caused by unused code, you are allowed to remove the unused code.
 - Provide updated code blocks along with the starting line number for each change.
 - Each code block must consist of at least 5 (five) lines, and must contain all of the changes required to fix the errors.
 - For conciseness, unchanged lines in the code block can be omitted using '...'.
 - If multiple fix options exist, select the one with the fewest changes.
 - Changes must be limited to the following list of files:
    - bar.py
    - foo.py

Please respond in the following format, think step-by-step:

<thinking>
    <reasoning>
        Analysis of the recent changes...
    </reasoning>
    <reasoning>
        Analysis of the command output...
    </reasoning>
    <reasoning>
        Analysis of the root cause...
    </reasoning>
    <reasoning>
        Analysis of the potential edit locations...
    </reasoning>
    ...
    <reasoning>
        In conclusion, the following changes are required to fix the errors in the log...
    </reasoning>
</thinking>

<critical_thinking>
    Critically evaluate each reasoning step above, determining whether it is completely sound, and conclude by assessing the soundness of the overall conclusion...
</critical_thinking>

<fix_plan>
    <fix_desc>
        To fix the error we need to change...
    </fix_desc>
    <changes>
        <change>
            <path>file1.py</path>
            <change_desc>Detailed and comprehensive description of the changes to be made in file1.py, including references to unique location identifers such as function names, variable names, tags, etc.</change_desc>
            <updated_code_block_start_line>3</updated_code_block_start_line>
            <updated_code_block>
            ```
                def foo(x):
                    if x == 0:
                        print("bar")
            ```
            </updated_code_block>
        </change>
        <change>
            <path>file2.py</path>
            <change_desc>Detailed and comprehensive description of the changes to be made in file2.py, including references to unique location identifers such as function names, variable names, tags, etc.</change_desc>
            <updated_code_block_start_line>5</updated_code_block_start_line>
            <updated_code_block>
            ```
                def bar(y):
                    if x == 1:
                        print("foo")
            ```
            </updated_code_block>
        </change>
        ...
    </changes>
</fix_plan>
"""


def generate_dummy_autofix_create_fix_plan_prompt(
    with_user_feedback: bool = False,
) -> str:
    token_apportionment = AutofixCreateFixPlanTokenApportionment()
    token_counter = ClaudeTokenCounter()
    prompter = AutofixCreateFixPlanV1PromptFormatter(token_apportionment, token_counter)

    edit_locations: list[PromptChunkWithLines] = [
        PromptChunkWithLines(
            text="# You can aggregate\n# with a maxing\n",
            line_offset=0,
            length_in_lines=2,
            path="bar.py",
        ),
        PromptChunkWithLines(
            text="# You can aggregate\n# with a maxing\n# function.\n",
            line_offset=2,
            length_in_lines=3,
            path="bar.py",
        ),
        PromptChunkWithLines(
            text="# You can aggregate\n# with a pooling function.\n",
            line_offset=0,
            length_in_lines=2,
            path="foo.py",
        ),
    ]

    command = "echo hello"
    output = "hello"
    recent_changes: Sequence[Modified[File]] = [
        Modified(
            File("file1.py", "line 1\nline 2\n"),
            File("file1.py", "new line 1\nline 2\n"),
        )
    ]

    if not with_user_feedback:
        steering_history: Sequence[AutofixSteeringMessage] = []
    else:
        steering_history: Sequence[AutofixSteeringMessage] = [
            AutofixSteeringMessage(
                message="This is a steering message",
                relevant_fix_plan=AutofixFixPlan(
                    fix_desc="This is a fix description",
                    changes=[
                        AutofixFileFix(
                            path="file2.py",
                            change_desc="This is a change description",
                            code_block_start_line=3,
                            code_block="new line 1\nline 2\n",
                        )
                    ],
                ),
                fix=[
                    Modified(
                        File("file2.py", "line 1\nline 2\n"),
                        File("file2.py", "new line 1\nline 2\n"),
                    )
                ],
            ),
            AutofixSteeringMessage(
                message="It should be changed to 5",
                relevant_fix_plan=AutofixFixPlan(
                    fix_desc="Change MAGIC to 2 in file3.py",
                    changes=[
                        AutofixFileFix(
                            path="file3.py",
                            change_desc="Change MAGIC to 2 in file3.py",
                            code_block_start_line=3,
                            code_block="MAGIC = 2\n",
                        )
                    ],
                ),
                fix=[
                    Modified(
                        File("file3.py", "MAGIC = 1\n"),
                        File("file3.py", "MAGIC = 2\n"),
                    )
                ],
            ),
        ]

    prompt_input = AutofixCreateFixPlanInput(
        edit_locations=edit_locations,
        command=command,
        command_output=output,
        breaking_change=recent_changes,
        steering_history=steering_history,
    )
    prompt_output = prompter.format_prompt(prompt_input)
    return prompt_output.message


def test_autofix_create_fix_plan_prompt_formatter_basic():
    """This is a simple sanity check to catch obvious bugs in the autofix check command's prompt formatting."""
    prompt = generate_dummy_autofix_create_fix_plan_prompt(with_user_feedback=True)
    assert prompt == expected_prompt_with_user_feedback

    prompt = generate_dummy_autofix_create_fix_plan_prompt(with_user_feedback=False)
    assert prompt == expected_prompt_without_user_feedback
