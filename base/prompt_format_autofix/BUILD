load("//tools/bzl:python.bzl", "py_library")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")
load("@python_pip//:requirements.bzl", "requirement")

py_library(
    name = "common",
    srcs = [
        "common.py",
    ],
    deps = [
        "//base/diff_utils",
        "//base/third_party_clients/token_counter:token_counter_claude",
        requirement("pydantic"),
    ],
)

py_library(
    name = "check_command_prompt_formatter",
    srcs = [
        "check_command_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
    ],
)

pytest_test(
    name = "check_command_prompt_formatter_test",
    srcs = [
        "check_command_prompt_formatter_test.py",
    ],
    deps = [
        ":check_command_prompt_formatter",
    ],
)

py_library(
    name = "contains_errors_prompt_formatter",
    srcs = [
        "contains_errors_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        "//base/diff_utils",
    ],
)

pytest_test(
    name = "contains_errors_prompt_formatter_test",
    srcs = [
        "contains_errors_prompt_formatter_test.py",
    ],
    deps = [
        ":contains_errors_prompt_formatter",
    ],
)

py_library(
    name = "create_fix_plan_prompt_formatter",
    srcs = [
        "create_fix_plan_prompt_formatter.py",
    ],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
    ],
)

pytest_test(
    name = "create_fix_plan_prompt_formatter_test",
    srcs = [
        "create_fix_plan_prompt_formatter_test.py",
    ],
    deps = [
        ":create_fix_plan_prompt_formatter",
    ],
)

py_library(
    name = "generate_chatanol_query",
    srcs = ["generate_chatanol_query.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":common",
        ":create_fix_plan_prompt_formatter",
        "//base/third_party_clients/token_counter",
    ],
)

pytest_test(
    name = "generate_chatanol_query_test",
    srcs = ["generate_chatanol_query_test.py"],
    deps = [
        ":generate_chatanol_query",
    ],
)
