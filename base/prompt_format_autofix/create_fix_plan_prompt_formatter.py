from textwrap import dedent
from dataclasses import field
from typing import Iterable, Protocol, Sequence, TypeVar
from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File
import re
from typing import Optional
from pydantic.dataclasses import dataclass
from base.prompt_format_autofix.common import (
    Reasoning,
    CriticalThinking,
    Thinking,
    format_breaking_change,
    format_fixing_change,
    format_command_output,
    format_chunks,
    SYSTEM_PROMPT,
    PromptChunkWithLines,
    format_user_feedback,
    AutofixSteeringMessage,
    AutofixFixPlan,
    AutofixFileFix,
)
from base.third_party_clients.token_counter.token_counter import TokenCounter
from base.prompt_format.common import Exchange
from base.tokenizers.tokenizer import Tokenizer

"""Prompt formatter for autofix create fix plan."""


class ExceedContextLength(Exception):
    """Raised when some fields exceed the maximum allowed length."""

    def __init__(self, message: str = "Exceed the context length"):
        self.message = message
        super().__init__(self.message)


@dataclass
class AutofixCreateFixPlanOutput:
    thinking: Optional[Thinking] = None
    critical_thinking: Optional[CriticalThinking] = None
    fix_plan: Optional[AutofixFixPlan] = None

    # For diagnostic purposes
    system_prompt: Optional[str] = None
    prompt: Optional[str] = None


@dataclass(frozen=True)
class AutofixCreateFixPlanTokenApportionment:
    """Stores the apportionment of the tokens for the autofix create fix plan model."""

    max_command_len: int = 100
    """The maximum number of tokens in the command."""

    max_command_output_len: int = 5000
    """The maximum number of tokens in the output."""

    max_breaking_change_len: int = 50000
    """The maximum number of tokens in the breaking change."""

    max_editable_files_len: int = 500
    """The maximum number of tokens in the editable files."""

    max_user_feedback_len: int = 2000
    """The maximum number of tokens in the user feedback."""

    max_user_feedback_prior_fix_plan_len: int = 500
    """The maximum number of tokens in the most recent prior fix plan."""

    max_edit_locations_len: int = -1  # Remaining budget
    """The maximum number of tokens in the edit locations."""

    max_total_len: int = 120000
    """The maximum number of tokens total."""


@dataclass(frozen=True)
class AutofixCreateFixPlanInput:
    """The input to the create fix plan model."""

    edit_locations: Sequence[PromptChunkWithLines]
    """Potential edit locations and context."""

    command: str
    """The command executed."""

    command_output: str
    """The output from the command."""

    breaking_change: Sequence[Modified[File]]
    """The breaking change leading up to the error."""

    steering_history: Sequence[AutofixSteeringMessage] = field(default_factory=list)
    """The steering history, from most to least recent."""


@dataclass(frozen=True)
class TokenizedAutofixCreateFixPlanPromptOutput:
    """The set of outputs used for constructing the code chat model prompts."""

    tokens: list[str]
    """The tokenized prompt."""


@dataclass(frozen=True)
class StructuredAutofixCreateFixPlanPromptOutput:
    """The set of outputs used for constructing the code chat model prompts."""

    system_prompt: str | None
    """The system prompt."""

    chat_history: Iterable[Exchange]
    """The truncated conversation history as a list of request_message/response_text pairs."""

    message: str
    """Message to be sent to the model"""


T = TypeVar(
    "T",
    StructuredAutofixCreateFixPlanPromptOutput,
    TokenizedAutofixCreateFixPlanPromptOutput,
    covariant=True,
)


class AutofixCreateFixPlanPromptFormatter(Protocol[T]):
    """The ChatPromptFormatter protocol for the code chat model."""

    token_apportionment: AutofixCreateFixPlanTokenApportionment
    """The token apportionment for the prompt."""

    def format_prompt(self, prompt_input: AutofixCreateFixPlanInput) -> T:
        """Build tokenized prompt.

        Args:
            prompt_input: AutofixCreateFixPlanInput object contains command, output

        Returns:
            prompt
        """
        raise NotImplementedError()


StructuredAutofixCreateFixPlanPromptFormatter = AutofixCreateFixPlanPromptFormatter[
    StructuredAutofixCreateFixPlanPromptOutput
]
TokenizedAutofixCreateFixPlanPromptFormatter = AutofixCreateFixPlanPromptFormatter[
    TokenizedAutofixCreateFixPlanPromptOutput
]


class StructToTokensPromptFormatter(Protocol):
    """The protocol to convert structured prompts to tokens.

    This formatter is intended to be task-agnostic and responsible for converting
    structured prompts into tokenized prompts.
    """

    tokenizer: Tokenizer
    """The tokenizer used by the prompt formatter."""

    def format_prompt(
        self, prompt_input: StructuredAutofixCreateFixPlanPromptOutput
    ) -> TokenizedAutofixCreateFixPlanPromptOutput:
        """Build tokenized prompt.

        Args:
            prompt_input: Structured object describing model prompt

        Returns:
            Tokenized prompt.
        """
        raise NotImplementedError()


class AutofixCreateFixPlanV1PromptFormatter(
    StructuredAutofixCreateFixPlanPromptFormatter
):
    """A prompt formatter to check commands as part of autofix."""

    def __init__(
        self,
        token_apportionment: AutofixCreateFixPlanTokenApportionment,
        token_counter: TokenCounter,
    ):
        self.token_apportionment = token_apportionment
        self.token_counter = token_counter

    def format_prompt(
        self, prompt_input: AutofixCreateFixPlanInput
    ) -> StructuredAutofixCreateFixPlanPromptOutput:
        command_and_output = format_command_output(
            prompt_input.command,
            prompt_input.command_output,
            self.token_counter,
            self.token_apportionment.max_command_len,
            self.token_apportionment.max_command_output_len,
        )
        git_diff = format_breaking_change(
            prompt_input.breaking_change,
            self.token_counter,
            self.token_apportionment.max_breaking_change_len,
            command_output=prompt_input.command_output,
        )
        unique_files = list(
            dict.fromkeys([x.path for x in prompt_input.edit_locations])
        )
        editable_files: str = self.token_counter.truncate_to_budget(
            "\n".join(f"    - {file_path}" for file_path in unique_files),
            self.token_apportionment.max_editable_files_len,
            truncation_indicator="...",
        )

        user_feedback: str = ""
        if len(prompt_input.steering_history) > 0:
            user_feedback = format_user_feedback(
                prompt_input.steering_history[0],
                self.token_counter,
                include_prior_fix_diff=True,
                diff_max_toks=self.token_apportionment.max_user_feedback_prior_fix_plan_len,
            )
            for feedback in prompt_input.steering_history[1:]:
                user_feedback = (
                    format_user_feedback(
                        feedback, self.token_counter, include_prior_fix_diff=False
                    )
                    + ";\n\n"
                    + user_feedback
                )
            user_feedback += "\n\n"

            user_feedback_header = (
                "You previously suggested some incorrect fixes. A smart colleague provided some feedback for how to improve these prior fixes.\n"
                "Here is that feedback from least to most recent. Information for each previous fix attempt is enclosed in <previous_fix_attempt> tags.\n"
            )
            user_feedback_header_budget = self.token_counter.count_tokens(
                user_feedback_header
            )

            user_feedback = self.token_counter.truncate_to_budget(
                user_feedback,
                self.token_apportionment.max_user_feedback_len
                - user_feedback_header_budget,
                truncation_indicator="...",
                reversed=True,
            )
            user_feedback = user_feedback_header + user_feedback

        remaining_budget = self.token_apportionment.max_total_len - (
            self.token_counter.count_tokens(prompt_input.command)
            + self.token_counter.count_tokens(prompt_input.command_output)
            + self.token_counter.count_tokens(git_diff)
            + self.token_counter.count_tokens(editable_files)
            + self.token_counter.count_tokens(user_feedback)
        )
        edit_locations: str = format_chunks(
            prompt_input.edit_locations,
            self.token_counter,
            remaining_budget,
            command_output=prompt_input.command_output,
        )
        prompt = (
            "The following information is provided to assist you in creating a fix plan:\n"
            "the command and its output are as follows:\n"
            f"{command_and_output}\n"
            "\n"
            "Recent changes in git diff format are included here:\n"
            f"{git_diff}\n"
            "The potential edit locations for the fix, as well as some context from the codebase, are:\n"
            f"{edit_locations}"
            f"{user_feedback}"
            "Carefully review the provided information and create a detailed fix plan accordingly.\n"
            "Follow these guidelines:\n"
            " - Do not make any changes unless absolutely necessary to fix the errors in the log.\n"
            " - Reverting changes to fix errors is only allowed if you are absolutely certain that the changes cannot be justified.\n"
            " - If the errors are caused by unused code, you are allowed to remove the unused code.\n"
            " - Provide updated code blocks along with the starting line number for each change.\n"
            " - Each code block must consist of at least 5 (five) lines, and must contain all of the changes required to fix the errors.\n"
            " - For conciseness, unchanged lines in the code block can be omitted using '...'.\n"
            " - If multiple fix options exist, select the one with the fewest changes.\n"
            " - Changes must be limited to the following list of files:\n"
            f"{editable_files}\n"
            "\n"
            "Please respond in the following format, think step-by-step:\n"
            f"{response_xml_example}"
        )

        return StructuredAutofixCreateFixPlanPromptOutput(
            system_prompt=SYSTEM_PROMPT, chat_history=[], message=prompt
        )


response_xml_example = """
<thinking>
    <reasoning>
        Analysis of the recent changes...
    </reasoning>
    <reasoning>
        Analysis of the command output...
    </reasoning>
    <reasoning>
        Analysis of the root cause...
    </reasoning>
    <reasoning>
        Analysis of the potential edit locations...
    </reasoning>
    ...
    <reasoning>
        In conclusion, the following changes are required to fix the errors in the log...
    </reasoning>
</thinking>

<critical_thinking>
    Critically evaluate each reasoning step above, determining whether it is completely sound, and conclude by assessing the soundness of the overall conclusion...
</critical_thinking>

<fix_plan>
    <fix_desc>
        To fix the error we need to change...
    </fix_desc>
    <changes>
        <change>
            <path>file1.py</path>
            <change_desc>Detailed and comprehensive description of the changes to be made in file1.py, including references to unique location identifers such as function names, variable names, tags, etc.</change_desc>
            <updated_code_block_start_line>3</updated_code_block_start_line>
            <updated_code_block>
            ```
                def foo(x):
                    if x == 0:
                        print("bar")
            ```
            </updated_code_block>
        </change>
        <change>
            <path>file2.py</path>
            <change_desc>Detailed and comprehensive description of the changes to be made in file2.py, including references to unique location identifers such as function names, variable names, tags, etc.</change_desc>
            <updated_code_block_start_line>5</updated_code_block_start_line>
            <updated_code_block>
            ```
                def bar(y):
                    if x == 1:
                        print("foo")
            ```
            </updated_code_block>
        </change>
        ...
    </changes>
</fix_plan>
"""


# TODO: liornm - needs to be relocated to somewhere else
@dataclass
class ApplyFileFixInput:
    file_fix: AutofixFileFix
    source_content: str


def extract_pattern(pattern: str, text: str, default: str = "") -> str:
    match = re.search(pattern, text, re.DOTALL)
    return match.group(1).strip() if match else default


def cast_to_int(value: str) -> Optional[int]:
    try:
        return int(value)
    except ValueError:
        return None


def parse_fix_plan_xml(xml: str) -> AutofixCreateFixPlanOutput:
    # Parse <thinking>
    thinking_content = extract_pattern(r"<thinking>(.*?)</thinking>", xml)
    thinking = None
    if thinking_content:
        reasoning_matches = re.findall(
            r"<reasoning>(.*?)</reasoning>", thinking_content, re.DOTALL
        )
        thinking = Thinking(
            reasoning=[Reasoning(text=r.strip()) for r in reasoning_matches]
        )

    # Parse <critical_thinking>
    critical_thinking_text = extract_pattern(
        r"<critical_thinking>(.*?)</critical_thinking>", xml
    )
    critical_thinking = (
        CriticalThinking(text=critical_thinking_text)
        if critical_thinking_text
        else None
    )

    # Parse <fix_plan>
    fix_plan_content = extract_pattern(r"<fix_plan>(.*?)</fix_plan>", xml)
    fix_plan = None
    if fix_plan_content:
        fix_desc = extract_pattern(r"<fix_desc>(.*?)</fix_desc>", fix_plan_content)
        changes = []
        for change_match in re.findall(
            r"<change>(.*?)</change>", fix_plan_content, re.DOTALL
        ):
            changes.append(
                AutofixFileFix(
                    path=extract_pattern(r"<path>(.*?)</path>", change_match),
                    change_desc=extract_pattern(
                        r"<change_desc>(.*?)</change_desc>", change_match
                    ),
                    code_block_start_line=cast_to_int(
                        extract_pattern(
                            r"<updated_code_block_start_line>(.*?)</updated_code_block_start_line>",
                            change_match,
                        )
                    ),
                    code_block=extract_pattern(
                        r"<updated_code_block>(.*?)</updated_code_block>", change_match
                    ),
                )
            )
        fix_plan = AutofixFixPlan(fix_desc=fix_desc, changes=changes)

    return AutofixCreateFixPlanOutput(
        thinking=thinking, critical_thinking=critical_thinking, fix_plan=fix_plan
    )
