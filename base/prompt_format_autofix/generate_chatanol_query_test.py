from base.prompt_format_autofix.generate_chatanol_query import (
    GenerateChatanolQueryInput,
    GenerateChatanolQueryTokenApportionment,
    GenerateBasicChatanolQueryPromptFormatter,
    GenerateSteeringChatanolQueryPromptFormatter,
)
from base.third_party_clients.token_counter.token_counter_claude import (
    Claude<PERSON><PERSON>Counter,
)
from base.prompt_format_autofix.common import (
    PromptChunkWithLines,
    AutofixSteeringMessage,
    AutofixFileFix,
    AutofixFixPlan,
)
from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File

expected_general_context_prompt = """
Below are some code excerpts from the codebase:
Here are excerpts from the file 'bar.py':
```
   0: # You can aggregate
   1: # with a maxing
   2: # You can aggregate
   3: # with a maxing
   4: # function.
```

Here is an excerpt from the file 'foo.py':
```
   0: # You can aggregate
   1: # with a pooling function.
```



Here are some failure logs from a failed test:
```bash
$ echo hello
this is
```

What is one critical question you would ask about the codebase to resolve the error below that cannot be answered by the provided code excerpts?
Specify relevant files, don't specify line numbers, use identifers like classes and functions instead.
Use no prefix or suffix, respond with the question only.
"""

expected_human_steering_prompt = """
Here are some failure logs from a failed test:
```bash
$ echo hello
this is
```

Here is our current plan for how to fix it as well as some feedback from a smart colleague regarding how to improve our plan for how to fix the failure:
<previous_fix_attempt>
<previous_fix_attempt_plan>
This is a fix description
 - file2.py: This is a change description
</previous_fix_attempt_plan>
<previous_fix_attempt_diff>
--- file2.py
+++ file2.py
@@ -1,2 +1,2 @@
-line 1
+new line 1
 line 2
</previous_fix_attempt_diff>
<previous_fix_attempt_critical_feedback>This is a steering message</previous_fix_attempt_critical_feedback>
</previous_fix_attempt>

What files, classes, or functions would you like to look at to implement the feedback?
Use no prefix or suffix, respond with the question only.
"""


def test_generate_chatanol_query_general_basic():
    token_apportionment = GenerateChatanolQueryTokenApportionment()
    token_counter = ClaudeTokenCounter()
    prompter = GenerateBasicChatanolQueryPromptFormatter(
        token_apportionment, token_counter
    )

    command = "echo hello"
    output = "this is"
    edit_locations: list[PromptChunkWithLines] = [
        PromptChunkWithLines(
            text="# You can aggregate\n# with a maxing\n",
            line_offset=0,
            length_in_lines=2,
            path="bar.py",
        ),
        PromptChunkWithLines(
            text="# You can aggregate\n# with a maxing\n# function.\n",
            line_offset=2,
            length_in_lines=3,
            path="bar.py",
        ),
        PromptChunkWithLines(
            text="# You can aggregate\n# with a pooling function.\n",
            line_offset=0,
            length_in_lines=2,
            path="foo.py",
        ),
    ]
    prompt_input = GenerateChatanolQueryInput(
        command=command,
        command_output=output,
        edit_locations=edit_locations,
        steering_message=None,
    )

    prompt = prompter.format_prompt(prompt_input)
    assert prompt == expected_general_context_prompt


def test_generate_chatanol_query_human_steering():
    token_apportionment = GenerateChatanolQueryTokenApportionment()
    token_counter = ClaudeTokenCounter()
    prompter = GenerateSteeringChatanolQueryPromptFormatter(
        token_apportionment, token_counter
    )

    command = "echo hello"
    output = "this is"
    steering_message = AutofixSteeringMessage(
        message="This is a steering message",
        relevant_fix_plan=AutofixFixPlan(
            fix_desc="This is a fix description",
            changes=[
                AutofixFileFix(
                    path="file2.py",
                    change_desc="This is a change description",
                    code_block_start_line=3,
                    code_block="new line 1\nline 2\n",
                )
            ],
        ),
        fix=[
            Modified(
                File("file2.py", "line 1\nline 2\n"),
                File("file2.py", "new line 1\nline 2\n"),
            )
        ],
    )
    prompt_input = GenerateChatanolQueryInput(
        command=command,
        command_output=output,
        edit_locations=None,
        steering_message=steering_message,
    )

    prompt = prompter.format_prompt(prompt_input)
    assert prompt == expected_human_steering_prompt
