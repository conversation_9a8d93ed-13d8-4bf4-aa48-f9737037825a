"""Tests for the autofix contain errors prompt formatter."""

import pytest

from base.prompt_format_autofix.contains_errors_prompt_formatter import (
    AutofixContainErrorsInput,
    AutofixContainErrorsTokenApportionment,
    AutofixContainErrorsV1PromptFormatter,
)
from base.third_party_clients.token_counter.token_counter_claude import (
    <PERSON><PERSON><PERSON>Counter,
)

expected_prompt = """The command and its output:
```bash
$ echo hello
hello
```
Can you confidently identify the code related errors in the provided output?"""


def generate_dummy_contain_errors_prompt() -> str:
    token_apportionment = AutofixContainErrorsTokenApportionment()
    token_counter = ClaudeTokenCounter()
    prompter = AutofixContainErrorsV1PromptFormatter(token_apportionment, token_counter)

    command = "echo hello"
    output = "hello"
    prompt_input = AutofixContainErrorsInput(command=command, command_output=output)
    prompt_output = prompter.format_prompt(prompt_input)
    return prompt_output.message


def test_autofix_contain_errors_prompt_formatter_basic():
    """This is a simple sanity check to catch obvious bugs in the autofix contain errors's prompt formatting."""
    prompt = generate_dummy_contain_errors_prompt()
    assert prompt == expected_prompt
