from dataclasses import dataclass
from dataclasses_json import dataclass_json
from typing import Iterable, Literal, Protocol, Sequence, TypeVar
from base.prompt_format_autofix.common import format_command_output, SYSTEM_PROMPT
from base.third_party_clients.token_counter.token_counter import Token<PERSON>ounter

from base.prompt_format.common import Exchange
from base.tokenizers.tokenizer import Tokenizer

"""Prompt formatter for autofix contain errors."""


@dataclass_json
@dataclass(frozen=True)
class AutofixContainErrorsTokenApportionment:
    """Stores the apportionment of the tokens for the autofix contain errors model."""

    max_command_len: int = 100
    """The maximum number of tokens in the command."""

    max_command_output_len: int = 5000
    """The maximum number of tokens in the output."""


@dataclass_json
@dataclass(frozen=True)
class AutofixContainErrorsInput:
    """The input to the contain errors model."""

    command: str
    """The command to check."""

    command_output: str
    """The output from the command."""


@dataclass_json
@dataclass(frozen=True)
class AutofixContainErrorsOutput:
    """The output from the contain errors model."""

    result: bool
    """Boolean indicating whether the command output contains errors."""

    desc: str
    """Short and concise one line explanation."""


@dataclass_json
@dataclass(frozen=True)
class TokenizedAutofixContainErrorsPromptOutput:
    """The set of outputs used for constructing the code chat model prompts."""

    tokens: list[str]
    """The tokenized prompt."""


@dataclass(frozen=True)
class StructuredAutofixContainErrorsPromptOutput:
    """The set of outputs used for constructing the code chat model prompts."""

    system_prompt: str | None
    """The system prompt."""

    chat_history: Iterable[Exchange]
    """The truncated conversation history as a list of request_message/response_text pairs."""

    message: str
    """Message to be sent to the model"""

    tools: Sequence[Literal["command_output_contain_errors"]] | None = None


T = TypeVar(
    "T",
    StructuredAutofixContainErrorsPromptOutput,
    TokenizedAutofixContainErrorsPromptOutput,
    covariant=True,
)


class AutofixContainErrorsPromptFormatter(Protocol[T]):
    """The ChatPromptFormatter protocol for the code chat model."""

    token_apportionment: AutofixContainErrorsTokenApportionment
    """The token apportionment for the prompt."""

    def format_prompt(self, prompt_input: AutofixContainErrorsInput) -> T:
        """Build tokenized prompt.

        Args:
            prompt_input: AutofixCheckCommandInput object contains command, output

        Returns:
            prompt
        """
        raise NotImplementedError()


StructuredAutofixContainErrorsPromptFormatter = AutofixContainErrorsPromptFormatter[
    StructuredAutofixContainErrorsPromptOutput
]
TokenizedAutofixContainErrorsPromptFormatter = AutofixContainErrorsPromptFormatter[
    TokenizedAutofixContainErrorsPromptOutput
]


class StructToTokensPromptFormatter(Protocol):
    """The protocol to convert structured prompts to tokens.

    This formatter is intended to be task-agnostic and responsible for converting
    structured prompts into tokenized prompts.
    """

    tokenizer: Tokenizer
    """The tokenizer used by the prompt formatter."""

    def format_prompt(
        self, prompt_input: StructuredAutofixContainErrorsPromptOutput
    ) -> TokenizedAutofixContainErrorsPromptOutput:
        """Build tokenized prompt.

        Args:
            prompt_input: Structured object describing model prompt

        Returns:
            Tokenized prompt.
        """
        raise NotImplementedError()


class AutofixContainErrorsV1PromptFormatter(
    StructuredAutofixContainErrorsPromptFormatter
):
    """A prompt formatter to contain errorss as part of autofix."""

    def __init__(
        self,
        token_apportionment: AutofixContainErrorsTokenApportionment,
        token_counter: TokenCounter,
    ):
        self.token_apportionment = token_apportionment
        self.token_counter = token_counter

    def format_prompt(
        self, prompt_input: AutofixContainErrorsInput
    ) -> StructuredAutofixContainErrorsPromptOutput:
        command_and_output = format_command_output(
            prompt_input.command,
            prompt_input.command_output,
            self.token_counter,
            self.token_apportionment.max_command_len,
            self.token_apportionment.max_command_output_len,
        )

        prompt = (
            "The command and its output:\n"
            f"{command_and_output}\n"
            "Can you confidently identify the code related errors in the provided output?"
        )

        return StructuredAutofixContainErrorsPromptOutput(
            system_prompt=SYSTEM_PROMPT,
            chat_history=[],
            message=prompt,
            tools=["command_output_contain_errors"],
        )
