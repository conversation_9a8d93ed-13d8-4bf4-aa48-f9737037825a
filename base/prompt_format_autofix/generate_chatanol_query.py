from dataclasses import dataclass
from typing import List, Optional, Sequence
from enum import Enum
from base.prompt_format_autofix.common import (
    PromptChunkWithLines,
    format_command_output,
    format_chunks,
    format_user_feedback,
    AutofixSteeringMessage,
)
from base.third_party_clients.token_counter.token_counter import TokenCounter


@dataclass
class GenerateChatanolQueryInput:
    command: str
    command_output: str
    edit_locations: Sequence[PromptChunkWithLines] | None
    steering_message: AutofixSteeringMessage | None


@dataclass(frozen=True)
class GenerateChatanolQueryTokenApportionment:
    max_command_len: int = 500
    """The maximum number of tokens in the command."""

    max_command_output_len: int = 5000
    """The maximum number of tokens in the output."""

    max_retrieval_len: int = 50_000
    """The maximum number of tokens in the retrieval."""

    max_user_feedback_prior_fix_plan_len: int = 500
    """The maximum number of tokens in the most recent prior fix plan."""


class GenerateBasicChatanolQueryPromptFormatter:
    def __init__(
        self,
        token_apportionment: GenerateChatanolQueryTokenApportionment,
        token_counter: TokenCounter,
    ):
        self.token_apportionment = token_apportionment
        self.token_counter = token_counter

    def format_prompt(self, input: GenerateChatanolQueryInput) -> str:
        command_and_output = format_command_output(
            input.command,
            input.command_output,
            self.token_counter,
            self.token_apportionment.max_command_len,
            self.token_apportionment.max_command_output_len,
        )
        assert (
            input.edit_locations is not None
        ), "edit_locations must be provided for GENERAL_CONTEXT query type"
        chunk_formatted = format_chunks(
            input.edit_locations,
            self.token_counter,
            self.token_apportionment.max_retrieval_len,
            command_output=input.command_output,
        )
        return f"""
Below are some code excerpts from the codebase:
{chunk_formatted}

Here are some failure logs from a failed test:
{command_and_output}

What is one critical question you would ask about the codebase to resolve the error below that cannot be answered by the provided code excerpts?
Specify relevant files, don't specify line numbers, use identifers like classes and functions instead.
Use no prefix or suffix, respond with the question only.
"""


class GenerateSteeringChatanolQueryPromptFormatter:
    def __init__(
        self,
        token_apportionment: GenerateChatanolQueryTokenApportionment,
        token_counter: TokenCounter,
    ):
        self.token_apportionment = token_apportionment
        self.token_counter = token_counter

    def format_prompt(self, input: GenerateChatanolQueryInput) -> str:
        command_and_output = format_command_output(
            input.command,
            input.command_output,
            self.token_counter,
            self.token_apportionment.max_command_len,
            self.token_apportionment.max_command_output_len,
        )
        assert (
            input.edit_locations is None
        ), "edit_locations must not be provided for HUMAN_STEERING query type"
        assert (
            input.steering_message is not None
        ), "steering_message must be provided for HUMAN_STEERING query type"
        plan_and_user_feedback = format_user_feedback(
            input.steering_message,
            self.token_counter,
            include_prior_fix_diff=True,
            diff_max_toks=self.token_apportionment.max_user_feedback_prior_fix_plan_len,
        )

        return f"""
Here are some failure logs from a failed test:
{command_and_output}

Here is our current plan for how to fix it as well as some feedback from a smart colleague regarding how to improve our plan for how to fix the failure:
{plan_and_user_feedback}

What files, classes, or functions would you like to look at to implement the feedback?
Use no prefix or suffix, respond with the question only.
"""
