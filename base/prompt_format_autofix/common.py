from collections import Counter, defaultdict
import re
from textwrap import dedent
from typing import Callable, Optional, Sequence, TypeVar

from pydantic.dataclasses import dataclass
from base.diff_utils.changes import Modified
from base.diff_utils.diff_utils import File, compute_file_diff
from base.prompt_format.common import PromptChunk
from base.third_party_clients.token_counter.token_counter import TokenCounter

# Common system prompt for all autofix prompts
SYSTEM_PROMPT: str = "You are an expert AI software engineer."


@dataclass(frozen=True)
class PromptChunkWithLines(PromptChunk):
    line_offset: int = 0
    length_in_lines: int = 0


@dataclass(frozen=True)
class Reasoning:
    text: str


@dataclass(frozen=True)
class Thinking:
    reasoning: Sequence[Reasoning]


@dataclass(frozen=True)
class CriticalThinking:
    text: str


@dataclass
class AutofixFileFix:
    path: str
    change_desc: str
    code_block_start_line: Optional[int]
    code_block: str


@dataclass
class AutofixFixPlan:
    fix_desc: str
    changes: Sequence[AutofixFileFix]


@dataclass(frozen=True)
class AutofixSteeringMessage:
    """The steering message for the autofix create fix plan model."""

    message: str
    """The steering message."""

    relevant_fix_plan: AutofixFixPlan
    """The summary of the fix plan that the user steering message pertains to."""

    fix: Sequence[Modified[File]]
    """The fix that the user steering message pertains to."""


def santize_command_output(output: str) -> str:
    # Remove ANSI escape sequences as they can cause issues with the LLM
    ansi_escape_regex = r"\x1B[@-_][0-?]*[ -/]*[@-~]"
    output = re.sub(ansi_escape_regex, "", output)
    return output


def format_command_output(
    command: str,
    command_output: str,
    token_counter: TokenCounter,
    max_command_len: int,
    max_command_output_len: int,
) -> str:
    command = token_counter.truncate_to_budget(
        command, max_command_len, truncation_indicator="..."
    )
    command_output = token_counter.truncate_to_budget(
        santize_command_output(command_output),
        max_command_output_len,
        reversed=True,
        truncation_indicator="...",
    )
    return "```bash\n" f"$ {command}\n" f"{command_output}\n" "```"


T = TypeVar("T")


def fast_sort_by_occurrences(
    long_text: str, seq: Sequence[T], key: Callable[[T], str] | None = None
) -> Sequence[T]:
    """Super fast sorts a sequence by the number of occurrences of the key in a long text."""
    key = key or (lambda x: str(x))
    pattern = re.compile("|".join(re.escape(key(item)) for item in seq))
    matches = Counter(pattern.findall(long_text))
    return sorted(seq, key=lambda item: matches[key(item)], reverse=True)


def format_single_chunk(chunk: PromptChunkWithLines) -> str:
    content_lines = chunk.text.splitlines(keepends=True)
    assert len(content_lines) == chunk.length_in_lines
    # Add line numbers
    content_lines = [
        f"{str(chunk.line_offset + i).rjust(4)}: {line}"
        for i, line in enumerate(content_lines)
    ]
    return "".join(content_lines).rstrip("\r\n")


def format_chunks(
    chunks: Sequence[PromptChunkWithLines],
    token_counter: TokenCounter,
    max_tokens: int,
    command_output: str | None = None,
) -> str:
    if not chunks:
        return ""

    files = defaultdict(list[PromptChunkWithLines])
    for chunk in chunks:
        files[chunk.path].append(chunk)

    # chunks by file
    for file_path in files:
        files[file_path].sort(key=lambda loc: loc.line_offset)

    # Ordered list
    files_list: Sequence[tuple[str, Sequence[PromptChunkWithLines]]] = [
        (file_path, loc) for file_path, loc in files.items()
    ]
    if command_output is not None:
        # Command ouput is an optional field, if specified, prioritize paths that are in the output
        files_list = fast_sort_by_occurrences(
            long_text=command_output,
            seq=files_list,
            key=lambda x: x[0],
        )

    locations: str = ""
    total_tokens: int = 0
    for file_path, file_locations in files_list:
        edit_file = f"Here is an excerpt from the file '{file_path}':\n"
        if len(file_locations) > 1:
            edit_file = f"Here are excerpts from the file '{file_path}':\n"

        # Concatenate chunks
        edit_content = format_single_chunk(file_locations[0])
        prev_chunk = file_locations[0]
        for chunk in file_locations[1:]:
            if chunk.line_offset != prev_chunk.line_offset + prev_chunk.length_in_lines:
                edit_content += "\n..."
            edit_content += "\n"
            edit_content += format_single_chunk(chunk)
            prev_chunk = chunk

        edit_file += f"```\n{edit_content}\n```"
        edit_file += "\n\n"
        token_count: int = token_counter.count_tokens(edit_file)
        if total_tokens + token_count <= max_tokens:
            # Don't break as we want to add as many chunks as possible
            total_tokens += token_count
            locations += edit_file
    return locations


def format_breaking_change(
    recent_changes: Sequence[Modified[File]],
    token_counter: TokenCounter,
    max_tokens: int,
    command_output: str | None = None,
) -> str:
    if command_output is not None:
        # Command ouput is an optional field, if specified, prioritize paths that are in the output
        recent_changes = fast_sort_by_occurrences(
            command_output, recent_changes, key=lambda change: change.after.path
        )

    # initialize with the tokens of the changed files suffix
    total_tokens: int = 0
    diff: str = ""
    for change in recent_changes:
        file_diff: str = compute_file_diff(change.before, change.after)
        token_count: int = token_counter.count_tokens(file_diff)
        if total_tokens + token_count <= max_tokens:
            # Don't break as we want to add as many changes as possible
            total_tokens += token_count
            diff += file_diff
    return diff


def format_fixing_change(
    changes: Sequence[Modified[File]],
    token_counter: TokenCounter,
    max_tokens: int,
) -> str:
    return format_breaking_change(
        changes, token_counter, max_tokens, command_output=None
    )


def format_user_feedback(
    feedback: AutofixSteeringMessage,
    token_counter: TokenCounter,
    include_prior_fix_diff: bool = False,
    diff_max_toks: int = 500,
) -> str:
    """Format the user feedback for the prompt for a specific prior fix attempt."""
    fix_plan_str = feedback.relevant_fix_plan.fix_desc + "\n"
    for change in feedback.relevant_fix_plan.changes:
        fix_plan_str += f" - {change.path}: {change.change_desc}\n"

    if include_prior_fix_diff and len(feedback.fix) > 0:
        diff_str = format_fixing_change(
            feedback.fix,
            token_counter,
            diff_max_toks,
        )
        return (
            f"<previous_fix_attempt>\n"
            f"<previous_fix_attempt_plan>\n{fix_plan_str}</previous_fix_attempt_plan>\n"
            f"<previous_fix_attempt_diff>\n{diff_str}</previous_fix_attempt_diff>\n"
            f"<previous_fix_attempt_critical_feedback>{feedback.message}</previous_fix_attempt_critical_feedback>\n"
            f"</previous_fix_attempt>"
        )
    else:
        return (
            f"<previous_fix_attempt>\n"
            f"<previous_fix_attempt_plan>\n{fix_plan_str}</previous_fix_attempt_plan>\n"
            f"<previous_fix_attempt_critical_feedback>{feedback.message}</previous_fix_attempt_critical_feedback>\n"
            f"</previous_fix_attempt>"
        )
