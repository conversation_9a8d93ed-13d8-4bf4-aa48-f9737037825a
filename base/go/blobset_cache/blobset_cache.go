package blobset_cache

import (
	"container/list"
	"sync"
	"time"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/rs/zerolog/log"
)

type BlobSetKey struct {
	TenantID string
	SetID    string
}

type ErrorCode int

const (
	ErrCodeUnknown ErrorCode = iota
	ErrCodeEntryNotFound
	ErrCodeInvalidParameter
	ErrCodeNoBlobNames
	ErrCodeBlobSetTooLarge
	ErrCodeCacheKeyMismatch
)

type CacheError struct {
	Code ErrorCode
	Msg  string
}

func (e *CacheError) Error() string {
	return e.Msg
}

func (e *CacheError) Is(target error) bool {
	t, ok := target.(*CacheError)
	if !ok {
		return false
	}

	return e.Code == t.Code
}

var (
	ErrEntryNotFound    = &CacheError{Code: ErrCodeEntryNotFound, Msg: "blobset cache entry not found"}
	ErrInvalidParameter = &CacheError{Code: ErrCodeInvalidParameter, Msg: "invalid parameter"}
	ErrNoBlobNames      = &CacheError{Code: ErrCodeNoBlobNames, Msg: "no blob names provided"}
	ErrBlobSetTooLarge  = &CacheError{Code: ErrCodeBlobSetTooLarge, Msg: "blob set too large"}
	ErrCacheKeyMismatch = &CacheError{Code: ErrCodeCacheKeyMismatch, Msg: "cache key mismatch"}
)

type CacheStats struct {
	Hits   int64
	Misses int64
}

// EvictionCallback is called when a blob set is evicted from the cache
// The eviction reason could be either LRU or TTL expiration
type EvictionCallback func(key BlobSetKey, blobNames []blob_names.BlobName)

// cacheEntry represents a single entry in the cache
type cacheEntry struct {
	// Key for the cache entry
	key BlobSetKey
	// Cached blob names
	blobNames []blob_names.BlobName
	// Size of the entry in bytes
	size int
	// Timestamp of last access for TTL expiration
	timestamp time.Time
}

// BlobSetCache provides caching for sets of blob names with LRU+TTL eviction
type BlobSetCache struct {
	// Mutex for protecting concurrent access to the cache
	mu sync.Mutex

	// Map of cache entries for O(1) lookup
	entries map[string]*list.Element

	// Linked list for LRU ordering
	lruList *list.List

	// Current memory usage in bytes
	curMemoryUsage int

	// Maximum memory size in bytes
	maxMemorySizeLimit int

	// TTL for cache entries
	entryTTL time.Duration

	// How often to run the cleanup loop that does TTL eviction
	cleanupInterval time.Duration
	// Cleanup ticker
	cleanupTicker *time.Ticker

	// Eviction callback
	onEvict EvictionCallback

	// Cache statistics
	stats CacheStats

	// Done channel for stopping the cleanup goroutine
	done chan struct{}
}

// NewBlobSetCache creates a new BlobSetCache with the requested configuration
// ttl is the time to live for a cache entry before it is evicted
// maxSizeInMB is the maximum size of the cache in megabytes
// cleanupInterval is the time between cache cleanups
// onEvict is an optional callback that is called when a blob set is evicted from the cache
func NewBlobSetCache(ttl time.Duration, maxSizeInMB int, cleanupInterval time.Duration, onEvict EvictionCallback) (*BlobSetCache, error) {
	if maxSizeInMB <= 0 {
		log.Debug().Msgf("maxSizeInMB must be positive")
		return nil, ErrInvalidParameter
	}

	if ttl <= 0 {
		log.Debug().Msgf("ttl must be positive")
		return nil, ErrInvalidParameter
	}

	if cleanupInterval <= 0 {
		log.Debug().Msgf("cleanupInterval must be positive")
		return nil, ErrInvalidParameter
	}

	cache := &BlobSetCache{
		entries:            make(map[string]*list.Element),
		lruList:            list.New(),
		maxMemorySizeLimit: maxSizeInMB * 1024 * 1024, // Convert MB to bytes
		entryTTL:           ttl,
		cleanupInterval:    cleanupInterval,
		onEvict:            onEvict,
		done:               make(chan struct{}),
	}

	// Start the cleanup goroutine
	cache.cleanupTicker = time.NewTicker(cleanupInterval)
	go cache.cleanupLoop()

	return cache, nil
}

// cleanupLoop periodically checks for expired entries and removes them
func (c *BlobSetCache) cleanupLoop() {
	for {
		select {
		case <-c.cleanupTicker.C:
			c.cleanupExpired()
		case <-c.done:
			c.cleanupTicker.Stop()
			return
		}
	}
}

// Close stops the cleanup goroutine
func (c *BlobSetCache) Close() {
	close(c.done)
}

// cleanupExpired removes expired entries from the cache
func (c *BlobSetCache) cleanupExpired() {
	c.mu.Lock()
	defer c.mu.Unlock()

	now := time.Now()
	for e := c.lruList.Front(); e != nil; {
		next := e.Next() // Save next before potentially removing e
		entry := e.Value.(*cacheEntry)
		if now.Sub(entry.timestamp) > c.entryTTL {
			c.removeElement(e)
		}
		e = next
	}
}

// removeElement removes an element from the cache and calls the eviction callback
// requires the caller to hold the cache lock
func (c *BlobSetCache) removeElement(element *list.Element) {
	entry := element.Value.(*cacheEntry)

	// Remove from the hash map
	delete(c.entries, blobSetKeyToString(entry.key))
	// Remove from the LRU list
	c.lruList.Remove(element)
	// Update memory usage
	c.curMemoryUsage -= entry.size

	// Call the eviction callback if provided
	if c.onEvict != nil {
		c.onEvict(entry.key, entry.blobNames)
	}
}

func blobSetKeyToString(key BlobSetKey) string {
	return key.TenantID + "::" + key.SetID
}

func calculateBlobSetSize(blobNames []blob_names.BlobName) int {
	// Each blob_names.BlobName is a Go string containing a 64-character hex string
	// representing a 32-byte SHA-256 hash
	// We also have the Go string header overhead of 16 bytes on 64-bit systems
	// (8 bytes ptr + 8 bytes len)
	blobNameSize := 64 + 16

	// Slice header overhead: 24 bytes on 64-bit systems (8 bytes ptr + 8 bytes len + 8 bytes cap)
	sliceHeaderSize := 24

	return (len(blobNames) * blobNameSize) + sliceHeaderSize
}

// GetBlobSet retrieves blob names from the cache by key
func (c *BlobSetCache) GetBlobSet(key BlobSetKey) ([]blob_names.BlobName, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	keyStr := blobSetKeyToString(key)
	element, found := c.entries[keyStr]

	if !found {
		c.stats.Misses++
		return nil, ErrEntryNotFound
	}

	entry := element.Value.(*cacheEntry)
	if entry.key != key {
		log.Error().Msgf("Cache key mismatch: expected %v, found %v (possible collision?)", key, entry.key)
		return nil, ErrCacheKeyMismatch
	}

	// Move to front of LRU list
	c.lruList.MoveToFront(element)

	// Update timestamp to extend TTL
	entry.timestamp = time.Now()

	// Update stats
	c.stats.Hits++

	// Return a copy of the blob names to prevent modification
	result := make([]blob_names.BlobName, len(entry.blobNames))
	copy(result, entry.blobNames)

	return result, nil
}

// SetBlobSet stores blob names in the cache
func (c *BlobSetCache) SetBlobSet(key BlobSetKey, blobNames []blob_names.BlobName) error {
	if len(blobNames) == 0 {
		log.Debug().Msgf("Blob set is empty, not caching: %v", key)
		return ErrNoBlobNames
	}

	size := calculateBlobSetSize(blobNames)

	if size > c.maxMemorySizeLimit {
		log.Debug().Msgf("Blob set %v is too large (%d bytes), not caching", key, size)
		return ErrBlobSetTooLarge
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	keyStr := blobSetKeyToString(key)

	if element, found := c.entries[keyStr]; found {
		// Update the existing entry
		entry := element.Value.(*cacheEntry)
		oldSize := entry.size

		newBlobNames := make([]blob_names.BlobName, len(blobNames))
		copy(newBlobNames, blobNames)

		entry.blobNames = newBlobNames
		entry.size = size
		entry.timestamp = time.Now()

		c.curMemoryUsage = c.curMemoryUsage - oldSize + size

		c.lruList.MoveToFront(element)
	} else {
		// Create a new entry
		newBlobNames := make([]blob_names.BlobName, len(blobNames))
		copy(newBlobNames, blobNames)

		entry := &cacheEntry{
			key:       key,
			blobNames: newBlobNames,
			size:      size,
			timestamp: time.Now(),
		}

		element := c.lruList.PushFront(entry)
		c.entries[keyStr] = element

		c.curMemoryUsage += size
	}

	c.evictEntriesIfNeeded()

	return nil
}

// evictEntriesIfNeeded removes entries from the cache if it's over the memory limit
func (c *BlobSetCache) evictEntriesIfNeeded() {
	// Remove the least recently used entry until we are below the memory limit
	for c.curMemoryUsage > c.maxMemorySizeLimit {
		element := c.lruList.Back()
		if element == nil {
			panic("Cache is over memory limit but LRU list is empty")
		}
		c.removeElement(element)
	}
}

// DeleteBlobSet removes a blob set from the cache by its key
func (c *BlobSetCache) DeleteBlobSet(key BlobSetKey) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	keyStr := blobSetKeyToString(key)
	element, found := c.entries[keyStr]
	if !found {
		log.Debug().Msgf("Blob set not found in cache: %v", key)
		return ErrEntryNotFound
	}

	c.removeElement(element)
	return nil
}

// NumEntries returns the number of blob sets in the cache
func (c *BlobSetCache) NumEntries() int {
	c.mu.Lock()
	defer c.mu.Unlock()
	return len(c.entries)
}

// MemorySizeInBytes returns the amount of memory currently being used by the cache in bytes
func (c *BlobSetCache) MemorySizeInBytes() int {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.curMemoryUsage
}

// Stats returns cache statistics
func (c *BlobSetCache) Stats() CacheStats {
	c.mu.Lock()
	defer c.mu.Unlock()
	return CacheStats{
		Hits:   c.stats.Hits,
		Misses: c.stats.Misses,
	}
}
