package blobset_cache

import (
	"errors"
	"fmt"
	"testing"
	"time"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	"github.com/stretchr/testify/assert"
)

// Test basic cache setup and get/set operations
func TestBlobSetCache(t *testing.T) {
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Initial allocation should be 0
	assert.Equal(t, 0, cache.MemorySizeInBytes())

	tenantID := "tenant1"
	setID := "set1"
	key := BlobSetKey{TenantID: tenantID, SetID: setID}

	// Create proper blob names with 32-byte SHA-256 hashes
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
		blob_names.GetBlobName("path/to/file3.txt", []byte("content of file 3")),
	}

	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	// Memory usage should be non-zero now
	assert.Greater(t, cache.MemorySizeInBytes(), 0)

	retrievedBlobNames, err := cache.GetBlobSet(key)
	assert.NoError(t, err)
	assert.Equal(t, len(blobNames), len(retrievedBlobNames))

	for i, blobName := range blobNames {
		assert.Equal(t, string(blobName), string(retrievedBlobNames[i]))
	}

	assert.Equal(t, 1, cache.NumEntries())

	cache.DeleteBlobSet(key)
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err, "Expected error after deleting the blob set")
	assert.ErrorIs(t, err, ErrEntryNotFound, "Expected ErrEntryNotFound error")
	assert.Equal(t, 0, cache.NumEntries(), "Cache should be empty after deletion")

	// Test deleting a non-existent key
	nonExistentKey := BlobSetKey{TenantID: "nonexistent", SetID: "nonexistent"}
	err = cache.DeleteBlobSet(nonExistentKey)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound, "Deleting non-existent entry expected ErrEntryNotFound error")
	_, err = cache.GetBlobSet(nonExistentKey)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound, "Getting non-existent entry expected ErrEntryNotFound error")
}

// Test the eviction callback setup and invocation
func TestEvictionCallback(t *testing.T) {
	var evictedKey BlobSetKey
	var evictedBlobNames []blob_names.BlobName
	evictionCalled := false

	onEvict := func(key BlobSetKey, blobNames []blob_names.BlobName) {
		evictedKey = key
		evictedBlobNames = blobNames
		evictionCalled = true
	}

	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Millisecond /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Millisecond /*onEvict*/, onEvict)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	tenantID := "tenant1"
	setID := "set1"
	key := BlobSetKey{TenantID: tenantID, SetID: setID}

	// Create proper blob names with 32-byte SHA-256 hashes
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
	}

	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	retrievedBlobNames, err := cache.GetBlobSet(key)
	assert.NoError(t, err)
	assert.Equal(t, len(blobNames), len(retrievedBlobNames))

	// Wait for the TTL to expire and the cleanup to run
	time.Sleep(20 * time.Millisecond)

	// Try to get the data again, it should be evicted
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err)

	// Verify the eviction callback was called with the correct data
	assert.True(t, evictionCalled, "Eviction callback should have been called")
	assert.Equal(t, tenantID, evictedKey.TenantID)
	assert.Equal(t, setID, evictedKey.SetID)
	assert.Equal(t, len(blobNames), len(evictedBlobNames))
	for i, blobName := range blobNames {
		assert.Equal(t, string(blobName), string(evictedBlobNames[i]))
	}
}

// TestMemorySizeCalculation tests that the memory size calculation is accurate
func TestMemorySizeCalculation(t *testing.T) {
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 10,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Create blob names with known hex string lengths
	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
	}

	// Verify each blob name is 64 characters (hex-encoded SHA-256)
	for _, blobName := range blobNames {
		assert.Equal(t, 64, len(string(blobName)), "Blob name should be 64 hex characters")
	}

	key := BlobSetKey{TenantID: "tenant1", SetID: "set1"}
	err = cache.SetBlobSet(key, blobNames)
	assert.NoError(t, err)

	memoryUsage := cache.MemorySizeInBytes()

	// Expected calculation:
	// - 2 blob names * (64 bytes string content + 16 bytes string header) = 160 bytes
	// - 24 bytes slice header
	// - Total: 184 bytes
	expectedSize := 2*(64+16) + 24
	assert.Equal(t, expectedSize, memoryUsage,
		"Memory usage should match expected calculation: %d bytes", expectedSize)
}

// TestLRUEviction tests that the least recently used entries are evicted when the cache is full
func TestLRUEviction(t *testing.T) {
	// Create a cache with 1MB limit
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Create a large number of entries to fill the cache
	// Each blob is 32 bytes, so we need many entries to exceed the 1MB limit
	// Using 10k blobs per set to fill the cache faster
	numBlobs := 10000
	for i := 0; i < 10; i++ {
		key := BlobSetKey{TenantID: "tenant1", SetID: fmt.Sprintf("set%d", i)}
		blobNames := make([]blob_names.BlobName, numBlobs)
		for j := 0; j < numBlobs; j++ {
			blobNames[j] = blob_names.GetBlobName(
				fmt.Sprintf("path/to/file%d_%d.txt", i, j),
				[]byte(fmt.Sprintf("content of file %d_%d", i, j)),
			)
		}
		err = cache.SetBlobSet(key, blobNames)
		assert.NoError(t, err)
	}

	// The cache should have evicted some entries to stay under the memory limit
	assert.Less(t, cache.NumEntries(), 10)
	assert.LessOrEqual(t, cache.MemorySizeInBytes(), 1024*1024)

	// The most recently used entry should still be in the cache
	key := BlobSetKey{TenantID: "tenant1", SetID: fmt.Sprintf("set%d", 9)}
	_, err = cache.GetBlobSet(key)
	assert.NoError(t, err)

	// The least recently used entry should have been evicted
	key = BlobSetKey{TenantID: "tenant1", SetID: fmt.Sprintf("set%d", 0)}
	_, err = cache.GetBlobSet(key)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)
}

// TestBlobsetCacheErrorIs tests the Is() method of BlobsetCacheError
func TestBlobsetCacheErrorIs(t *testing.T) {
	// Test that two errors with the same Code but different messages match
	err1 := &CacheError{Code: ErrCodeEntryNotFound, Msg: "test error 1"}
	err2 := &CacheError{Code: ErrCodeEntryNotFound, Msg: "test error 2"}

	// These should match because they have the same Code, even with different messages
	assert.True(t, errors.Is(err1, err2), "Errors with same Code should match")

	// Test that two errors with different Code values don't match
	err3 := &CacheError{Code: ErrCodeUnknown, Msg: "test error"}
	assert.False(t, errors.Is(err1, err3), "Errors with different Code should not match")
}

// TestBlobSetCacheStats tests the Stats method of BlobSetCache
func TestBlobSetCacheStats(t *testing.T) {
	// Create a new cache
	cache, err := NewBlobSetCache( /*ttl*/ 10*time.Minute /*maxSizeInMB*/, 1,
		/*cleanupInterval*/ 1*time.Second /*onEvict*/, nil)
	assert.NoError(t, err)
	assert.NotNil(t, cache)
	defer cache.Close()

	// Initial stats should be zero
	stats := cache.Stats()
	assert.Equal(t, int64(0), stats.Hits, "Initial hits should be zero")
	assert.Equal(t, int64(0), stats.Misses, "Initial misses should be zero")

	// Create a key and blob names
	key1 := BlobSetKey{TenantID: "tenant1", SetID: "set1"}
	key2 := BlobSetKey{TenantID: "tenant1", SetID: "set2"}
	nonExistentKey := BlobSetKey{TenantID: "nonexistent", SetID: "nonexistent"}

	blobNames := []blob_names.BlobName{
		blob_names.GetBlobName("path/to/file1.txt", []byte("content of file 1")),
		blob_names.GetBlobName("path/to/file2.txt", []byte("content of file 2")),
	}

	// Try to get a non-existent key - should increment misses
	_, err = cache.GetBlobSet(nonExistentKey)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)

	stats = cache.Stats()
	assert.Equal(t, int64(0), stats.Hits, "Hits should still be zero")
	assert.Equal(t, int64(1), stats.Misses, "Misses should be incremented to 1")

	// Add an entry
	err = cache.SetBlobSet(key1, blobNames)
	assert.NoError(t, err)

	// Get the entry - should increment hits
	_, err = cache.GetBlobSet(key1)
	assert.NoError(t, err)

	stats = cache.Stats()
	assert.Equal(t, int64(1), stats.Hits, "Hits should be incremented to 1")
	assert.Equal(t, int64(1), stats.Misses, "Misses should still be 1")

	// Get the entry again - should increment hits again
	_, err = cache.GetBlobSet(key1)
	assert.NoError(t, err)

	stats = cache.Stats()
	assert.Equal(t, int64(2), stats.Hits, "Hits should be incremented to 2")
	assert.Equal(t, int64(1), stats.Misses, "Misses should still be 1")

	// Try to get another non-existent key - should increment misses
	_, err = cache.GetBlobSet(key2)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)

	stats = cache.Stats()
	assert.Equal(t, int64(2), stats.Hits, "Hits should still be 2")
	assert.Equal(t, int64(2), stats.Misses, "Misses should be incremented to 2")

	// Add the second entry
	err = cache.SetBlobSet(key2, blobNames)
	assert.NoError(t, err)

	// Get both entries - should increment hits twice
	_, err = cache.GetBlobSet(key1)
	assert.NoError(t, err)
	_, err = cache.GetBlobSet(key2)
	assert.NoError(t, err)

	stats = cache.Stats()
	assert.Equal(t, int64(4), stats.Hits, "Hits should be incremented to 4")
	assert.Equal(t, int64(2), stats.Misses, "Misses should still be 2")

	// Delete an entry and try to get it - should increment misses
	err = cache.DeleteBlobSet(key1)
	assert.NoError(t, err)
	_, err = cache.GetBlobSet(key1)
	assert.Error(t, err)
	assert.ErrorIs(t, err, ErrEntryNotFound)

	stats = cache.Stats()
	assert.Equal(t, int64(4), stats.Hits, "Hits should still be 4")
	assert.Equal(t, int64(3), stats.Misses, "Misses should be incremented to 3")
}
