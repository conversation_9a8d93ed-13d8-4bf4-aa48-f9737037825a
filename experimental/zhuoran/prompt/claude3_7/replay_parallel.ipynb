#%%
%load_ext autoreload
%autoreload 2
#%%
from research.tools.chat_replay.replay_infra import get_input_and_documents

with open("/mnt/efs/augment/user/zhuoran/augment_qa_v3_1_ris.txt", "r") as f:
    request_ids = f.read().splitlines()

chat_prompt_inputs = []
for index, request_id in enumerate(request_ids):
    print(request_id)
    chat_prompt_input, _ = get_input_and_documents(request_id)
    chat_prompt_inputs.append(chat_prompt_input)
    print(f"Collected {index}/{len(request_ids)}")
#%%
from research.tools.chat_replay.replay_utils import chat_prompt_dict_from_input

chat_prompt_dicts = [
    chat_prompt_dict_from_input(chat_prompt_input)
    for chat_prompt_input in chat_prompt_inputs
]
#%%
import json

with open(
    "/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/chat_prompt_dicts.json", "w"
) as f:
    json.dump(chat_prompt_dicts, f, indent=4)
#%%
import json

from research.tools.chat_replay.replay_utils import chat_prompt_input_from_dict

with open("/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/chat_prompt_dicts.json") as f:
    chat_prompt_dicts = json.load(f)

chat_prompt_inputs = []
for index, chat_prompt_dict in enumerate(chat_prompt_dicts):
    print(f"Converting {index}/{len(chat_prompt_dicts)}")
    chat_prompt_input = chat_prompt_input_from_dict(chat_prompt_dict)
    chat_prompt_inputs.append(chat_prompt_input)
#%%
from base.prompt_format_chat.lib.system_prompts import (
    CLAUDE_SYSTEM_PROMPT_V14,
    CLAUDE_SYSTEM_PROMPT_V11,
    CLAUDE_SYSTEM_PROMPT_V12,
)

V11 = CLAUDE_SYSTEM_PROMPT_V11
V12 = CLAUDE_SYSTEM_PROMPT_V12
V14 = CLAUDE_SYSTEM_PROMPT_V14.format(
    model_name="Claude 3.7 Sonnet", creator="Anthropic"
)
#%%
from base.prompt_format_chat.structured_binks_prompt_formatter import (
    StructuredBinksPromptFormatter,
)
from base.prompt_format_chat.lib.string_formatter import StringFormatter
from research.tools.chat_replay.replay_utils import TOKEN_APPORTIONMENT, TOKEN_COUNTER

prompt_formatter = StructuredBinksPromptFormatter.create(
    token_counter=TOKEN_COUNTER,
    token_apportionment=TOKEN_APPORTIONMENT,
    system_prompt_factory=lambda token_counter: StringFormatter(
        V14, token_counter=token_counter
    ),
    retrieval_section_version=2,
)

prompt_outputs = []
for index, chat_prompt_input in enumerate(chat_prompt_inputs):
    print(f"Formatting prompt {index}/{len(chat_prompt_inputs)}")
    prompt_output = prompt_formatter.format_prompt(chat_prompt_input)
    prompt_outputs.append(prompt_output)
#%%
from anthropic import Anthropic

from research.environments import get_eng_secret

anthropic_api_key = get_eng_secret("seal-research-anthropic-key")
anthropic_client = Anthropic(
    api_key=anthropic_api_key,
    max_retries=1,
    timeout=300,
)
#%%
index = 1
prompt_output = prompt_outputs[index]
#%%
import time

from research.tools.chat_replay.replay_utils import get_claude_parameters


def get_claude_output(prompt_output, model_name, system_prompt, print_=False):
    claude_parameters = get_claude_parameters(
        prompt_output, client_type="anthropic", base_model_version="sonnet3.7"
    )
    messages = claude_parameters["messages"]

    total_text = ""
    for retry in range(5):
        try:
            with anthropic_client.messages.stream(
                model=model_name,
                max_tokens=8192,
                temperature=1,
                system=system_prompt,
                messages=messages,
            ) as stream:
                total_text = ""
                for chunk in stream:
                    if (
                        chunk.type == "content_block_delta"
                        and chunk.delta.type == "thinking_delta"
                    ):
                        text = chunk.delta.thinking
                    elif chunk.type == "text":
                        text = chunk.text
                    else:
                        continue
                    if print_:
                        print(text, end="")
                    total_text += text
            break
        except Exception as e:
            print(f"Claude error: {e}")
            time.sleep(60 * 2**retry)
    return total_text


_ = get_claude_output(prompt_output, "claude-3-5-sonnet-20241022", V14, print_=True)
#%%
from concurrent.futures import ThreadPoolExecutor
import concurrent.futures


def get_claude_outputs(prompt_outputs, model_name, system_prompt):
    # Process results in parallel
    executor = ThreadPoolExecutor(max_workers=len(prompt_outputs))
    futures = []
    for index, prompt_output in enumerate(prompt_outputs):
        print(f"Submitting prompt {index}/{len(prompt_outputs)}")
        future = executor.submit(
            get_claude_output, prompt_output, model_name, system_prompt
        )
        futures.append((index, future))

    # Create a list with None values to store results in order
    results = [None] * len(prompt_outputs)
    future_to_index = {f: i for i, f in futures}

    # Collect results as they complete
    completed = 0
    for future in concurrent.futures.as_completed([f for _, f in futures]):
        index = future_to_index[future]
        text = future.result()
        results[index] = text  # Store in correct position
        print(
            f"Completed {completed}/{len(prompt_outputs)} (index {index}): length {len(text)}"
        )
        completed += 1
    return results
#%%
results_3_5 = get_claude_outputs(prompt_outputs, "claude-3-5-sonnet-20241022", V14)
with open("/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/results_3_5.json", "w") as f:
    json.dump(results_3_5, f, indent=4)
#%%
V14_R2 = """\
You are Augment, an AI code assistant developed by Augment Code, based on the Claude 3.7 Sonnet model created by Anthropic.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.
Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.

When answering the developer's questions, please follow these guidelines:

- BE VERY BRIEF. Provide only the most relevant and actionable information. Make code blocks as short as possible by omitting unchanged parts and using placeholder comments.
- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.
- When referencing a file in your response, always include the FULL file path.
- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).
- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: "My search failed to locate the mentioned information." Avoid mentioning access limitations or mentioning "provided excerpts". Then, encourage the user to share more details or, alternatively, attach the relevant files using the "@" syntax in the chat (e.g., "@path/to/file.py").
- Do not apologize.

MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:

1. Excerpts from existing files: Always include both `path=` and `mode="EXCERPT"`. Example:

<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
````python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
````
</augment_code_snippet>

BE VERY BRIEF BY ONLY PROVIDING <10 LINES OF THE CODE. If you give correct XML structure, it will be parsed into a clickable code block, and the user can always click it to see the part in the full file.

2. Proposed edits: Always include `path=` and use `mode="EDIT"`. Example:

<augment_code_snippet path="config/app_config.yaml" mode="EDIT">
````yaml
app:
  name: MyWebApp
  version: 1.3.0

database:
  host: new-db.example.com
  port: 5432
````
</augment_code_snippet>

BE VERY BRIEF BY ONLY PROVIDING NEWLY ADDED OR MODIFIED LINES. If you give correct XML structure, it will be parsed into an appliable code block, and there will be a subsequent model that applies the changes to the user's code. Its success depends on:
2.1. You outputing correct XML tags around the codeblocks.
2.2. You focusing ONLY on added or modified lines, with no extra lines showing existing code.
2.3. Be EXTREMELY BRIEF. The shorter the better. Use placeholders to reduce codeblock length.

3. New code or text: Always include `path=` and use `mode="EDIT"`. Example:

<augment_code_snippet path="hello/world.rb" mode="EDIT">
````ruby
def main
  puts "Hello, world!"
end
````
</augment_code_snippet>"""
#%%
results_3_7 = get_claude_outputs(prompt_outputs, "claude-3-7-sonnet-20250219", V14_R2)
with open(
    "/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/results_3_7_r2.json", "w"
) as f:
    json.dump(results_3_7, f, indent=4)
#%%
import re


def analyze_results(results):
    sorted_results = sorted(results, key=lambda x: len(x))
    code_block_lists = []
    for result in results:
        code_blocks = re.findall(
            r"<augment_code_snippet.*?>(.*?)</augment_code_snippet>", result, re.DOTALL
        )
        code_block_lists.append(code_blocks)
    all_code_blocks = [
        code_block for code_blocks in code_block_lists for code_block in code_blocks
    ]
    result = {
        "Min length": len(sorted_results[0]),
        "Max length": len(sorted_results[-1]),
        "Avg length": sum(len(x) for x in results) / len(results),
        "Median length": len(sorted_results[len(results) // 2]),
        "Avg code block count": sum(
            len(code_blocks) for code_blocks in code_block_lists
        )
        / len(results),
        "Avg code block length": sum(len(code_block) for code_block in all_code_blocks)
        / len(all_code_blocks)
        if all_code_blocks
        else 0,
    }
    return result
#%%
import json
import pandas as pd
from pathlib import Path

# List all files in the directory
directory = Path("/mnt/efs/augment/user/zhuoran/augment_qa/v3_1/")
result_files = list(directory.glob("results_*.json"))

analysis_results = []
for file_path in result_files:
    with open(file_path, "r") as f:
        results = json.load(f)
    result = analyze_results(results)
    result["model"] = file_path.stem
    analysis_results.append(result)

df = pd.DataFrame(analysis_results)
df = df.set_index("model")
df
#%%
results[97]
#%% md
# String replacement
#%%
from base.prompt_format_chat.lib.system_prompts import CLAUDE_SYSTEM_PROMPT_V14

V14_REPLACE = """\
You are Augment, an AI code assistant developed by Augment Code, based on the {model_name} model created by {creator}.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.
Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.

When answering the developer's questions, please follow these guidelines:

- Be EXTREMELY CONCISE and to-the-point in your answers. Provide only the most relevant and actionable information.
- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.
- When referencing a file in your response, always include the FULL file path.
- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).
- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: "My search failed to locate the mentioned information." Avoid mentioning access limitations or mentioning "provided excerpts". Then, encourage the user to share more details or, alternatively, attach the relevant files using the "@" syntax in the chat (e.g., "@path/to/file.py").
- Do not apologize.

MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag or use the str_replace tool. Follow these rules:

1. Excerpts from existing files: Always include both `path=` and `mode="EXCERPT"`. Example:

<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
````python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
````
</augment_code_snippet>

2. Proposed edits: Use `str_replace` tool for edits.

3. New code or text: Use `str_replace` tool with empty `old_text`.

4. Note that the `str_replace` tool will be formatted as

<augment_code_snippet path="<file_path>" mode="EDIT">
````<language>
<new_text>
````
</augment_code_snippet>

without mention of the tool or `old_text`.

""".format(model_name="Claude 3.7 Sonnet", creator="Anthropic")

V14 = CLAUDE_SYSTEM_PROMPT_V14.format(
    model_name="Claude 3.7 Sonnet", creator="Anthropic"
)
#%%
from anthropic.types import ToolParam


claude_parameters = get_claude_parameters(
    prompt_output, client_type="anthropic", base_model_version="sonnet3.7"
)
messages = claude_parameters["messages"]

total_text = ""
finished = False
while not finished:
    with anthropic_client.messages.stream(
        model="claude-3-7-sonnet-20250219",
        # model="claude-3-5-sonnet-20241022",
        max_tokens=8192,
        temperature=1,
        system=V14_REPLACE,
        messages=messages + [{"role": "assistant", "content": total_text.strip()}],
        tools=[
            ToolParam(
                name="str_replace",
                description="Replace text with new text",
                input_schema={
                    "type": "object",
                    "properties": {
                        "old_text": {
                            "type": "string",
                            "description": "The original text to be replaced.",
                        },
                        "new_text": {
                            "type": "string",
                            "description": "The new text to replace with.",
                        },
                        "path": {
                            "type": "string",
                            "description": "The path to the file where the replacement should occur.",
                        },
                        "language": {
                            "type": "string",
                            "description": "The programming language of the code (used as a language hint for Markdown code blocks).",
                        },
                    },
                    "required": ["old_text", "new_text", "path", "language"],
                },
            ),
            ToolParam(
                name="finish_answer",
                description="Finish the answer. Please finish more aggresivelly when the answer seems long.",
                input_schema={
                    "type": "object",
                    "properties": {
                        "done": {
                            "type": "boolean",
                            "description": "This must always be true.",
                        },
                    },
                    "required": [],
                },
            ),
        ],
        # extra_body={"thinking": {"type": "enabled", "budget_tokens": 8000}},
    ) as stream:
        start_time = time.time()
        first_thinking = True
        first_text = True
        chunks = []
        for chunk in stream:
            chunks.append(chunk)
            if (
                chunk.type == "content_block_delta"
                and chunk.delta.type == "thinking_delta"
            ):
                text = chunk.delta.thinking
                if first_thinking:
                    # text = "# Thinking\n\n" + text
                    first_thinking = False
            elif (
                chunk.type == "content_block_delta" and chunk.delta.type == "text_delta"
            ):
                text = chunk.delta.text
                if first_text:
                    # text = "\n\n# Text\n\n" + text
                    first_text = False
            elif chunk.type == "content_block_stop":
                if (
                    chunk.content_block.type == "tool_use"
                    and chunk.content_block.name == "str_replace"
                ):
                    text = (
                        f"\n<augment_code_snippet path={chunk.content_block.input['path']} mode=\"EDIT\">\n"
                        f"```{chunk.content_block.input['language']}\n"
                        + chunk.content_block.input["new_text"]
                        + "\n```\n"
                        + "</augment_code_snippet>\n"
                    )
                elif (
                    chunk.content_block.type == "tool_use"
                    and chunk.content_block.name == "finish_answer"
                ):
                    finished = True
            else:
                text = ""
                # print(type(chunk))
            print(text, end="")
            total_text += text
            with open("/home/<USER>/b.md", "w") as f:
                f.write(total_text)
            latency = time.time() - start_time
            # if len(total_text) > 9000:
            #     break
#%%
[chunk.type for chunk in chunks[-10:]]
#%%
print(chunks[-3].content_block.input["new_text"])
#%%
total_text = ""
start_time = time.time()
first_thinking = True
first_text = True
for chunk in chunks:
    if chunk.type == "content_block_delta" and chunk.delta.type == "thinking_delta":
        text = chunk.delta.thinking
        if first_thinking:
            text = "# Thinking\n\n" + text
            first_thinking = False
    elif chunk.type == "content_block_delta" and chunk.delta.type == "text_delta":
        text = chunk.delta.text
        if first_text:
            text = "\n\n# Text\n\n" + text
            first_text = False
    # elif (
    #     chunk.type == "content_block_delta"
    #     and chunk.delta.type == "input_json_delta"
    # ):
    #     text = chunk.delta.partial_json
    # elif chunk.type == "text":
    #     text = chunk.text
    #     if first_text:
    #         text = "\n\n# Text\n\n" + text
    #         first_text = False
    # elif chunk.type == "input_json":
    #     input_json_string += chunk.partial_json
    #     text = ""
    elif chunk.type == "content_block_stop":
        print(chunk.content_block)
        if (
            chunk.content_block.type == "tool_use"
            and chunk.content_block.name == "str_replace"
        ):
            text = (
                f"\n````{chunk.content_block.input['language']}\n"
                + chunk.content_block.input["new_text"]
                + "\n````\n"
            )
    else:
        text = ""
        # print(type(chunk))
    print(text, end="")
    total_text += text
    with open("/home/<USER>/b.md", "w") as f:
        f.write(total_text)
    latency = time.time() - start_time
    if len(total_text) > 9000:
        break
#%% md
# Prompting
#%%
from base.prompt_format_chat.lib.system_prompts import CLAUDE_SYSTEM_PROMPT_V14

V14_PROMPT = """\
You are Augment, an AI code assistant developed by Augment Code, based on the {model_name} model created by {creator}.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.
Thanks to Augment Code's enhancements, you have access to additional information about the user's project, including relevant code excerpts, documentation, and user actions such as selected code.

When answering the developer's questions, please follow these guidelines:

- When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.
- Always write code in the programming language of the currently open file. For example, if the user currently has the file foo/bar.rs open and is actively working on it, use Rust unless explicitly asked to use a different language.
- When referencing a file in your response, always include the FULL file path.
- When referencing classes, functions, variables or files in your response, always wrap them in backticks (e.g. `MyClassName`).
- If the provided excerpts are not sufficient to answer a question, or if the user asks about files or tabs that are not included, respond as though you searched but couldn’t find the relevant information. For example, say: "My search failed to locate the mentioned information." Avoid mentioning access limitations or mentioning "provided excerpts". Then, encourage the user to share more details or, alternatively, attach the relevant files using the "@" syntax in the chat (e.g., "@path/to/file.py").
- Do not apologize.

MUST ALWAYS WRAP code snippets (codeblocks) in `<augment_code_snippet>` tag. Follow these rules:

1. Excerpts from existing files: Always include both `path=` and `mode="EXCERPT"`. Example:

<augment_code_snippet path="foo/bar.py" mode="EXCERPT">
````python
class AbstractTokenizer():
    def __init__(self, name):
        self.name = name

    ...
````
</augment_code_snippet>

2. Proposed edits: Always include `path=` and use `mode="EDIT"`. Example:

<augment_code_snippet path="config/app_config.yaml" mode="EDIT">
````yaml
app:
  name: MyWebApp
  version: 1.3.0

database:
  host: new-db.example.com
  port: 5432
````
</augment_code_snippet>

ONLY provide added or modified lines. There will be a separate model specialized at taking these new lines and applying them to the existing code. The apply model's success depends on:
2.1. You outputing correct XML tags around the codeblocks.
2.2. You focusing ONLY on added or modified lines, with no extra lines showing existing code.
2.3. Be EXTREMELY BRIEF. The shorter the better. Use placeholders to reduce codeblock length.

3. New code or text: Always include `path=` and use `mode="EDIT"`. Example:

<augment_code_snippet path="hello/world.rb" mode="EDIT">
````ruby
def main
  puts "Hello, world!"
end
````
</augment_code_snippet>

REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.
REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.
REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.
REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.
REMEMBER THE MOST IMPORTANT POINT: When writing code default to extreme brevity. Provide only the smallest code snippet possible to resolve the user's request. Writing extra code results in unneeded latency. Never repeat unchanged parts of code.

""".format(model_name="Claude 3.7 Sonnet", creator="Anthropic")

V14 = CLAUDE_SYSTEM_PROMPT_V14.format(
    model_name="Claude 3.7 Sonnet", creator="Anthropic"
)
#%%
import time

from research.tools.chat_replay.replay_utils import get_claude_parameters

claude_parameters = get_claude_parameters(
    prompt_output, client_type="anthropic", base_model_version="sonnet3.7"
)
messages = claude_parameters["messages"]

with anthropic_client.messages.stream(
    model="claude-3-7-sonnet-20250219",
    # model="claude-3-5-sonnet-20241022",
    max_tokens=8192,
    temperature=1,
    system=V14_PROMPT,
    messages=messages,
    # extra_body={"thinking": {"type": "enabled", "budget_tokens": 8000}},
) as stream:
    total_text = ""
    start_time = time.time()
    first_thinking = True
    first_text = True
    for chunk in stream:
        if chunk.type == "content_block_delta" and chunk.delta.type == "thinking_delta":
            text = chunk.delta.thinking
            if first_thinking:
                text = "# Thinking\n\n" + text
                first_thinking = False
        elif chunk.type == "text":
            text = chunk.text
            if first_text:
                text = "\n\n# Text\n\n" + text
                first_text = False
        else:
            continue
        print(text, end="")
        total_text += text
        with open("/home/<USER>/b.md", "w") as f:
            f.write(total_text)
        latency = time.time() - start_time
        if len(total_text) > 9000:
            break
#%%
