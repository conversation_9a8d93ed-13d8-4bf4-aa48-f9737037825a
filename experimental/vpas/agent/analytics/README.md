# Agent Analytics Module

This module provides tools for analyzing agent conversations and tool usage patterns, with a particular focus on the `str-replace-editor` tool. It helps identify patterns, inefficiencies, and potential improvements in how agents interact with tools.

## Overview

The analytics module consists of several components:

1. **Conversation Data Model**: Structures for representing agent conversations, rounds, turns, and tool calls
2. **BigQuery Integration**: Utilities for fetching conversation data from BigQuery
3. **Analysis Tools**: Functions for analyzing tool usage patterns, particularly for the `str-replace-editor` tool
4. **HTML Report Generation**: Tools for creating visual reports of analysis results

## Key Components

### Conversation Model (`conversation.py`)

Defines the data structures for representing agent conversations:

- `Conversation`: Represents a full conversation with a sequence of agent rounds
- `AgentRound`: A round starting with a user message followed by agent turns
- `AgentTurn`: A single agent response, potentially with a tool call
- `ToolCall`: Represents a tool call with request, response, and analysis data
- `StrReplaceEditorToolCallAnalysis`: Analysis data specific to the str-replace-editor tool

### BigQuery Utilities (`big_query_utils.py`)

Provides functions for fetching conversation data from BigQuery:

- `get_agent_conv_last_request_ids()`: Gets the last request ID for each conversation in a given time range

### Str-Replace-Editor Analysis (`str_replace_editor_tool_analysis.py`)

Analyzes usage patterns of the str-replace-editor tool:

- `add_analysis()`: Adds analysis data to a conversation
- `gen_summary()`: Generates a summary of str-replace-editor tool usage
- `print_summary()`: Prints the summary to the console
- `classify_error_message()`: Classifies error messages into predefined categories

Key metrics tracked:
- View range usage patterns
- Consecutive view calls
- Command distribution
- Success rates
- Paths that were viewed, retrieved, or created
- Error types and frequencies
- Number of entries in str_replace_entries
- Request IDs for str_replace calls with more than 5 entries
- Token statistics for each tool (prompt and completion tokens)
  - Prompt tokens: Tokens sent TO the model (user messages + tool outputs)
  - Completion tokens: Tokens generated BY the model (agent messages + tool inputs)
- Token statistics for text messages (user and agent messages)
- Command-specific token statistics for str-replace-editor (view, str_replace, insert)
- Total token usage across all conversations
- Cost calculations based on Claude API pricing
- Cost breakdown by tool and command
- Cost percentage of total cost for each tool and command

Error categories include:

**File not found errors:**
- `cannot_read_file`: Cannot read file error
- `failed_to_read_file`: Failed to read file error
- `error_while_reading`: Error while trying to read file
- `no_such_file`: No such file or directory error

**Path parameter errors:**
- `missing_path_param`: Missing required path parameter
- `invalid_path_param`: Invalid path parameter
- `empty_path`: Path parameter is empty

**Command errors:**
- `unknown_command`: Unknown command error
- `invalid_command`: Invalid command error

**View range errors:**
- `invalid_view_range`: Invalid view range error
- `invalid_view_range_param`: Invalid view_range parameter

**String replacement match errors:**
- `str_replace_no_verbatim_match`: No verbatim match found for old string
- `str_replace_no_match_at_line`: No match found at the specified line numbers

**String replacement content errors:**
- `str_replace_empty_old_str`: Empty old string when file is not empty
- `str_replace_multiple_matches`: Multiple matches found for the old string
- `str_replace_overlapping_entries`: Overlapping entries in str_replace_entries

**String replacement parameter errors:**
- `str_replace_missing_entries_param`: Missing str_replace_entries parameter
- `str_replace_invalid_entries_param`: Invalid str_replace_entries parameter
- `str_replace_empty_entries`: Empty str_replace_entries parameter
- `str_replace_invalid_start_line`: Invalid old_str_start_line_number parameter
- `str_replace_invalid_end_line`: Invalid old_str_end_line_number parameter

**Insert parameter errors:**
- `insert_missing_entries_param`: Missing insert_line_entries parameter
- `insert_invalid_entries_param`: Invalid insert_line_entries parameter
- `insert_empty_entries`: Empty insert_line_entries parameter
- `insert_invalid_line_param`: Invalid insert_line parameter
- `insert_invalid_line`: Invalid insert_line value

**Other errors:**
- `internal_error`: Internal errors in the tool
- `other`: Other unclassified errors

### HTML Report Generation (`html_report/html_report_generator.py`)

Generates HTML reports for visualizing analysis results:

- `gen_html_report()`: Creates an HTML report for str-replace-editor tool analysis
- Reports include summary statistics, command distribution, and view range histograms
- Reports are saved to a web server directory and accessible via URL

### Run Analysis Script (`run_analysis.py`)

Main script for running the analysis:

- Fetches conversation data from BigQuery
- Processes conversations to extract tool usage patterns
- Generates analysis summaries and HTML reports
- Supports filtering by date range, tenant, and conversation limit

## Usage

To run the analysis:

```bash
# Process data from the last 24 hours
python -m experimental.vpas.agent.analytics.run_analysis --last-hours 24 --limit 100

# Process data from the last 7 days
python -m experimental.vpas.agent.analytics.run_analysis --last-days 7 --limit 100
```

Command-line options:
- `--from-date`: Start date in ISO format (YYYY-MM-DDTHH:MM:SS)
- `--to-date`: End date in ISO format (YYYY-MM-DDTHH:MM:SS)
- `-lh, --last-hours`: Process data from the last N hours
- `-ld, --last-days`: Process data from the last N days
- `--tenant-name`: Tenant name to filter by (default: dogfood-shard)
- `--thread-count`: Number of threads to use for processing (default: 20)
- `--limit`: Maximum number of conversations to process

## HTML Reports

The analysis generates HTML reports with:

1. **Summary Statistics**:
   - Total calls
   - Success rate
   - First view count
   - View range usage metrics
   - Total token count

2. **Token Statistics**:
   - Text token statistics (user and agent messages)
   - Tool token statistics (prompt and completion tokens)
     - Prompt tokens: Tokens sent TO the model (user messages + tool outputs)
     - Completion tokens: Tokens generated BY the model (agent messages + tool inputs)
   - Command-specific token statistics for str-replace-editor (view, str_replace, insert)
   - Percentage of total tokens for each category
   - Average tokens per call for each tool and command
   - Cost calculations based on Claude API pricing
   - Cost breakdown by tool and command
   - Cost percentage of total cost for each tool and command

3. **Command Distribution**:
   - Breakdown of commands used (view, str_replace, insert)
   - Count and percentage for each command

4. **Error Statistics**:
   - Breakdown of error types
   - Count and percentage for each error type
   - Request IDs for each error type

5. **View Range Histogram**:
   - Distribution of view range lengths
   - Visual representation of frequency

6. **Str Replace Entries Histogram**:
   - Distribution of the number of entries in str_replace_entries
   - Visual representation of frequency

7. **Large Str Replace Calls**:
   - List of request IDs for str_replace calls with more than 5 entries
   - Links to view the requests

Reports are accessible via URLs in the format:
`https://webserver.gcp-us1.r.augmentcode.com/{username}/str_replace_editor_analysis/{timestamp}_{random_suffix}/index.html`

## Key Insights

This module helps identify:

1. **Inefficient Tool Usage Patterns**:
   - Multiple small view calls instead of a single larger view
   - Using view range when the full file should be viewed
   - Consecutive view calls on the same file

2. **Tool Success Rates and Error Analysis**:
   - Tracking success/error rates for different tools
   - Identifying common failure patterns
   - Detailed breakdown of error types and frequencies
   - Identifying which error types are most common

3. **Agent Behavior Analysis**:
   - How agents structure their conversations
   - Tool usage sequences and patterns

## Development

When extending this module:

1. Add new analysis metrics to `StrReplaceEditorToolCallAnalysis` class
2. Update the summary generation in `gen_summary()`
3. Extend the HTML report generation to include new metrics
4. Add tests for new functionality

## Testing

Tests are available in `str_replace_editor_tool_analysis_test.py` and can be run with:

```bash
python -m pytest experimental/vpas/agent/analytics/str_replace_editor_tool_analysis_test.py
```
