"""<PERSON><PERSON><PERSON> to run evaluation using dataset created by create_eval_from_agent_logs.py."""

import argparse
import json
import os
import random
import tempfile
import time
import uuid
from difflib import unified_diff
from enum import Enum, auto
from pathlib import Path
from typing import List, Optional, Tuple

from base.augment_client.client import AugmentClient
from base.static_analysis.common import guess_lang_from_fp
from base.static_analysis.parsing import compare_asts
from experimental.guy.agent_qa.builtin_tools import FileEditClient
from experimental.guy.agent_qa.file_edit.edit_file_agent import GenericEditFileAgent
from experimental.guy.agent_qa.file_edit.edit_file_agent_type import (
    EditFileAgentType,
    create_edit_agent,
    get_agent_name,
    get_augment_token,
)
from experimental.guy.agent_qa.file_edit.forger_edit_file_agent import (
    ForgerEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.forger_udiff_edit_file_agent import (
    ForgerUdiffEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.forger_udiff_with_desc_edit_file_agent import (
    ForgerUdiffWithDescEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.indent_utils import force_normalize_indent
from experimental.guy.agent_qa.file_edit.str_replace_edit_file_agent import (
    StrReplaceEditFileAgent,
)
from experimental.guy.agent_qa.file_edit.udiff_edit_file_agent import (
    EditFileWithUDiffAgent,
    ToolImplOutput,
    calc_output_stats,
)
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_dev_deployment_api_proxy_url,
    get_staging_api_proxy_url,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl
from experimental.vpas.agent.edit_agent_eval.create_eval_from_agent_logs import (
    EditAgentEvalSample,
)
from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalOutput,
    EditAgentEvalSummary,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.eval_summary import (
    save_eval_summary_html,
)
from experimental.vpas.agent.edit_agent_eval.router_llm_client import (
    DefaultAnthropicSonnetHaikuRouter,
)
from experimental.vpas.agent.edit_agent_eval.stats import calc_dialog_stats
from experimental.vpas.utils.diff_utils import (
    normalize_for_diff,
    normalize_imports_and_compare_asts,
)
from research.agents.tools import (
    DialogMessages,
    ToolCallLogger,
)
from research.llm_apis.llm_client import (
    AnthropicDirectClient,
    AnthropicVertexClient,
    GeminiVertexClient,
    ToolCall,
)


def get_default_output_path() -> Path:
    """Generate default output path in /tmp with random name."""
    random_suffix = str(uuid.uuid4())[:8]
    return Path(tempfile.gettempdir()) / f"edit_agent_eval_{random_suffix}.json"


def compute_diffs(
    original: str, modified: str, expected_variants: List[str]
) -> Tuple[str, str]:
    """Compute diffs between original/modified and modified/expected."""

    diff_against_original = "\n".join(
        unified_diff(
            original.splitlines(),
            modified.splitlines(),
            fromfile="original",
            tofile="modified",
            lineterm="",
            n=5,
        )
    )

    diffs_against_expected = []
    for i, expected in enumerate(expected_variants):
        diff = "\n".join(
            unified_diff(
                expected.splitlines(),
                modified.splitlines(),
                fromfile=f"expected_{i}",
                tofile="modified",
                lineterm="",
                n=5,
            )
        )
        diffs_against_expected.append(diff)

    diff_against_expected = "\n\n".join(diffs_against_expected)

    return diff_against_original, diff_against_expected


def run_eval_on_sample(
    sample: EditAgentEvalSample,
    file_edit_agent: GenericEditFileAgent,
    workspace_manager: WorkspaceManagerImpl,
    tool_call_logger: ToolCallLogger,
    dialog_messages: Optional[DialogMessages] = None,
) -> EditAgentEvalOutput:
    """Run evaluation on a single sample."""
    # Clear tool call logs before running
    tool_call_logger.logged_calls = []

    # Create temporary file with original content
    file_path = Path(sample.file_path)
    abs_path = workspace_manager.root / file_path
    abs_path.parent.mkdir(parents=True, exist_ok=True)
    original_content = sample.original_file_content
    edit_description = sample.short_edit_description
    abs_path.write_text(original_content)
    workspace_manager.update()

    tool_input = {
        "file_path": str(abs_path),
        "short_edit_description": edit_description,
    }

    # Create dialog messages if not provided
    if dialog_messages is None:
        dialog_messages = DialogMessages()
        dialog_messages.add_user_prompt(
            f"Edit file {sample.file_path}: {edit_description}"
        )
        dialog_messages.add_model_response(
            [
                ToolCall(
                    tool_call_id="test_id",
                    tool_name="edit_file_agent",
                    tool_input=tool_input,
                )
            ]
        )

    # Run the edit agent
    try:
        file_edit_agent.reset()
        tool_impl_output = file_edit_agent.run_impl(
            tool_input=tool_input,
            dialog_messages=dialog_messages,
        )
    except Exception as e:
        print(f"Error running edit agent: {e}")
        import traceback

        traceback.print_exc()
        tool_impl_output = ToolImplOutput(
            tool_output="Error occurred",
            tool_result_message=str(e),
            auxiliary_data={"success": False},
        )

    workspace_manager.update()
    # Read modified content and normalize line endings
    modified_content = abs_path.read_text().replace("\r\n", "\n")

    modified_content = normalize_for_diff(modified_content)
    expected_variants = [
        normalize_for_diff(expected)
        for expected in sample.expected_modified_file_content_variants
    ]

    # Compute diffs
    diff_against_original, diff_against_expected = compute_diffs(
        normalize_for_diff(original_content),
        modified_content,
        expected_variants,
    )

    # Check if correct (exact match with any variant)
    lang = guess_lang_from_fp(sample.file_path)
    ast_comparison = None
    ast_parsing_succeeded = None
    if sample.has_label():
        if sample.expected_exact_match:
            # before normalization
            modified_content = abs_path.read_text().replace("\r\n", "\n")
            expected = sample.expected_modified_file_content_variants[0]
            correct = modified_content == expected
            diff_against_original, diff_against_expected = compute_diffs(
                sample.original_file_content,
                modified_content,
                [expected],
            )
        else:
            if lang is None:
                # Fallback to text comparison if language not supported
                correct = any(
                    modified_content == expected for expected in expected_variants
                )
                ast_comparison = False
            else:
                # Use AST comparison for supported languages
                ast_comparison = True
                matches = [
                    normalize_imports_and_compare_asts(
                        modified_content,
                        expected,
                        lang,
                        Path(sample.file_path),
                        ignore_comments=False,
                    )
                    for expected in expected_variants
                ]
                ast_parsing_succeeded = all(match is not None for match in matches)
                correct = any(match is True for match in matches)

                if not ast_parsing_succeeded:
                    # Fallback to text comparison if ast parsing failed
                    correct = any(
                        modified_content == expected for expected in expected_variants
                    )
    else:
        correct = None  # No label, so we don't know if it's correct

    if correct:
        diff_against_expected = ""

    return EditAgentEvalOutput(
        sample=sample,
        tool_impl_output=tool_impl_output,
        modified_file_content=modified_content,
        diff_against_original=diff_against_original,
        diff_against_expected=diff_against_expected,
        correct=correct,
        success=tool_impl_output.auxiliary_data.get("success", False),
        tool_call_logs=tool_call_logger.logged_calls,  # type: ignore
        dialog_messages=dialog_messages._message_lists,
        lang=lang,
        ast_comparison=ast_comparison,
        ast_parsing_succeeded=ast_parsing_succeeded,
    )


def run_eval(
    dataset_dir: Path,
    output_path: Path,
    model_name: str,
    agent_type: EditFileAgentType,
    limit_samples: Optional[int] = None,
    specific_uuid: Optional[str] = None,
    max_turns: int = 5,
    category: Optional[str] = None,  # Add category parameter
    haiku_rate: float = 0.0,
    client_type: str = "anthropic_direct",  # Add client_type parameter
) -> EditAgentEvalSummary:
    """Run evaluation on all samples in dataset directory."""
    # Initialize clients and managers

    token = get_augment_token()
    augment_prototyping_client = AugmentPrototypingClient(
        get_staging_api_proxy_url(), token
    )
    temp_dir = Path(tempfile.mkdtemp(prefix="edit_agent_eval_workspace_"))
    workspace_manager = WorkspaceManagerImpl(augment_prototyping_client, temp_dir)
    tool_call_logger = ToolCallLogger()
    if model_name == "sonnet_haiku_router":
        llm_client = DefaultAnthropicSonnetHaikuRouter(
            max_retries=50, haiku_rate=haiku_rate
        )
    else:
        # Create LLM client based on client_type
        if client_type == "anthropic_direct":
            llm_client = AnthropicDirectClient(model_name=model_name, max_retries=50)
        elif client_type == "anthropic_vertexai":
            llm_client = AnthropicVertexClient(model_name=model_name, max_retries=50)
        elif client_type == "google_genai":
            llm_client = GeminiVertexClient(model_name=model_name, max_retries=50)
        else:
            raise ValueError(f"Unknown client_type: {client_type}")

    # Initialize the edit agent
    file_edit_agent = create_edit_agent(
        agent_type,
        llm_client,
        tool_call_logger,
        workspace_manager,
        max_turns=max_turns,
        review_stage=False,
        reuse_dialog_messages=True,
        review_inputs=False,
        final_review_stage=False,
        normalize_indentation=False,
    )

    # Load all samples recursively
    samples = []
    for sample_file in dataset_dir.rglob("sample.json"):
        sample_dir = sample_file.parent
        sample = EditAgentEvalSample.load_from_dir(sample_dir)
        samples.append(sample)

    # Filter by UUID if specified
    if specific_uuid:
        samples = [s for s in samples if s.uuid == specific_uuid]
        if not samples:
            raise ValueError(f"No sample found with UUID: {specific_uuid}")
        print(f"Selected sample with UUID: {specific_uuid}")
    else:
        if category:
            samples = [s for s in samples if s.category.startswith(category)]
            if not samples:
                raise ValueError(f"No samples found with category: {category}")
            print(f"Selected {len(samples)} samples with category: {category}")
        if limit_samples is not None:
            random.shuffle(samples)
            samples = samples[:limit_samples]
            print(f"Limited to {limit_samples} samples")

    # Run eval on each sample
    results = []
    for i, sample in enumerate(samples):
        print(f"\nProcessing sample {i+1}/{len(samples)} (UUID: {sample.uuid})")
        try:
            result = run_eval_on_sample(
                sample,
                file_edit_agent,
                workspace_manager,
                tool_call_logger,
            )
            results.append(result)
            print(f"Sample {sample.uuid} processed successfully")
        except Exception as e:
            print(f"Error processing sample {sample.uuid}: {e}")
            import traceback

            traceback.print_exc()

    # Create summary
    outputs = {result.sample.uuid: result for result in results}
    num_correct = sum(1 for result in results if result.correct)
    num_success = sum(1 for result in results if result.success)
    num_samples_with_label = sum(1 for result in results if result.sample.has_label())

    summary = EditAgentEvalSummary(
        dataset_name=dataset_dir.name,
        anthropic_model=model_name,
        agent_name=get_agent_name(agent_type),
        outputs=outputs,
        num_samples=len(samples),
        num_samples_with_label=num_samples_with_label,
        num_correct=num_correct,
        num_success=num_success,
        num_errors=len(samples) - len(results),
        accuracy=(
            num_correct / num_samples_with_label if num_samples_with_label > 0 else 0.0
        ),
        success_rate=(num_success / len(samples) if len(samples) > 0 else 0.0),
        aux_data=calc_output_stats([result.tool_impl_output for result in results]),
    )

    # Save results and summary
    with open(output_path, "w") as f:
        f.write(json.dumps(summary.to_dict(), indent=2))

    print(f"\nResults saved to: {output_path}")
    return summary


def main():
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--dataset-dir",
        type=Path,
        default=Path(__file__).parent / "dataset",
        help="Directory containing evaluation dataset",
    )
    parser.add_argument(
        "--output-path",
        type=Path,
        default=get_default_output_path(),
        help="Path to save evaluation results in json format (default: random path in /tmp)",
    )
    parser.add_argument(
        "--limit-samples",
        type=int,
        help="Limit the number of samples to process",
    )
    parser.add_argument(
        "--uuid",
        type=str,
        help="UUID of a specific sample to evaluate",
    )
    parser.add_argument(
        "--category",
        type=str,
        help="Category of samples to evaluate",
    )
    parser.add_argument(
        "--model-name",
        type=str,
        default="claude-3-7-sonnet-20250219",
        help="Model to use in EditFileAgent",
    )
    parser.add_argument(
        "--haiku-rate",
        type=float,
        default=0.0,
        help="Rate at which to use Haiku model (0.0 to 1.0)",
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Run evaluation even if results already exist",
    )
    parser.add_argument(
        "--agent-types",
        type=str,
        nargs="+",
        choices=[agent_type.name for agent_type in EditFileAgentType],
        default=[EditFileAgentType.UDIFF.name],
        help="Types of EditFileAgent implementations to use (can specify multiple)",
    )
    parser.add_argument(
        "--max-turns",
        type=int,
        default=5,
        help="Maximum number of turns for the agent",
    )
    parser.add_argument(
        "--client-type",
        type=str,
        choices=["anthropic_direct", "anthropic_vertexai", "google_genai"],
        default="anthropic_direct",
        help="Type of LLM client to use (anthropic_direct or anthropic_vertexai)",
    )
    args = parser.parse_args()

    # Set up paths and names
    WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
    JSON_DIR = Path("/mnt/efs/augment/user/vpas/edit_agent_eval")
    dataset_name = args.dataset_dir.name

    # Add limit, uuid, and category to dataset name if specified
    if args.uuid:
        dataset_name = f"uuid_{args.uuid}"
    else:
        if args.category:
            dataset_name = f"{dataset_name}_{args.category}"
        if args.limit_samples:
            dataset_name = f"{dataset_name}_limit_{args.limit_samples}"

    # Store URLs for all agent evaluations
    eval_urls = []

    # Run evaluation for each agent type
    for agent_type_name in args.agent_types:
        agent_type = EditFileAgentType[agent_type_name]
        print(f"\nRunning evaluation for agent type: {agent_type_name}")

        # Define output paths for this agent
        eval_dir_name = f"{dataset_name}_{args.model_name}_{get_agent_name(agent_type)}_mt{args.max_turns}"
        if args.model_name == "sonnet_haiku_router":
            eval_dir_name += f"_haiku{args.haiku_rate}"
        rel_path = f"vpas/edit_agent_eval/{eval_dir_name}"
        summary_path = JSON_DIR / eval_dir_name / "summary.json"
        summary_path.parent.mkdir(parents=True, exist_ok=True)
        html_path = WEB_SERVER_DIR / rel_path / "summary.html"
        html_path.parent.mkdir(parents=True, exist_ok=True)

        summary = None
        if summary_path.exists() and not args.no_cache:
            print(f"Found existing results at {summary_path}")
            with open(summary_path) as f:
                summary = EditAgentEvalSummary.from_json(f.read())
        else:
            # Run evaluation and save results
            summary = run_eval(
                dataset_dir=args.dataset_dir,
                output_path=summary_path,
                limit_samples=args.limit_samples,
                specific_uuid=args.uuid,
                model_name=args.model_name,
                agent_type=agent_type,
                max_turns=args.max_turns,
                category=args.category,
                haiku_rate=args.haiku_rate,
                client_type=args.client_type,
            )

        # Generate HTML report
        save_eval_summary_html(summary, html_path, html_path.parent)

        # Store URL for this evaluation
        URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"
        url = URL_TEMPLATE.format(f"{rel_path}/summary.html")
        eval_urls.append((agent_type_name, url))
        print(f"\nHTML report generated at: {url}")

    # Print summary of all URLs at the end
    if len(eval_urls) > 0:
        print("\n=== Summary of Evaluation Reports ===")
        for agent_type_name, url in eval_urls:
            print(f"{agent_type_name}: {url}")


if __name__ == "__main__":
    main()
