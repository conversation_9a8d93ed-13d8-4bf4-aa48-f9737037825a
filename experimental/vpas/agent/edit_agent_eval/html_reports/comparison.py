"""Generate HTML reports comparing two EditAgentEvalSummary objects side by side."""

import argparse
import html
import pickle
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from pygments import highlight
from pygments.formatters import HtmlF<PERSON>atter
from pygments.lexers import Diff<PERSON>exer

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalSummary,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.dialog import (
    generate_and_save_dialog_html_with_link,
    generate_dialog_html,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.eval_output import (
    save_eval_output_html,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.eval_summary import (
    _generate_result_link_html,
    _generate_tool_call_logs_link,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.stats_utils import (
    detect_available_stats,
    group_stats_by_category,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.templates import (
    generate_filter_section,
    generate_result_data_attributes,
    generate_stats_table,
    read_static_file,
)

# Constants for web server configuration
WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"


def _extract_dialog_content(dialog_html_path: Path) -> str:
    """Extract the dialog content from a dialog HTML file.

    Args:
        dialog_html_path: Path to the dialog HTML file

    Returns:
        The dialog content as HTML
    """
    if not dialog_html_path.exists():
        return "<p>Dialog file not found</p>"

    html_content = dialog_html_path.read_text()

    # Extract the dialog content using regex
    dialog_match = re.search(
        r'<div class="dialog">(.*?)</div>\s*</div>\s*</div>\s*</body>',
        html_content,
        re.DOTALL,
    )

    if dialog_match:
        dialog_content = dialog_match.group(1)
        # Make all sections expanded by default
        dialog_content = dialog_content.replace(
            'class="section"', 'class="section expanded"'
        )
        dialog_content = dialog_content.replace(
            'class="section-content"', 'class="section-content" style="display: block;"'
        )
        # Remove onclick attributes
        dialog_content = re.sub(r'onclick="[^"]+"', "", dialog_content)
        return f'<div class="dialog">{dialog_content}</div>'

    return "<p>Could not extract dialog content</p>"


def _generate_result_content(
    result: Any,
    output_dir: Optional[Path],
    uuid: str,
    is_reference: bool = False,
    reference_html_dir: Optional[Path] = None,
    target_html_dir: Optional[Path] = None,
    static_report: bool = False,
) -> str:
    """Generate HTML content for a single result.

    Args:
        result: The evaluation result
        output_dir: The directory to save individual pages
        uuid: The result UUID
        is_reference: Whether this is the reference model result (True) or target model result (False)
        reference_html_dir: Optional directory containing the reference HTML report
        target_html_dir: Optional directory containing the target HTML report
        static_report: If True, generates a non-interactive, fully expanded static HTML report
                      with inlined dialog content
    """
    # Extract UDiff tool calls
    udiff_calls = []
    if result.tool_call_logs:
        for call in result.tool_call_logs:
            if call.tool.name == "apply_udiff" and not call.started:
                udiff_calls.append(call)

    # Generate UDiff calls HTML
    udiff_calls_html = ""
    if udiff_calls:
        udiff_calls_html = """
        <h3>UDiff Tool Calls</h3>
        <div class="tool-call-logs">
        """
        for i, call in enumerate(udiff_calls, 1):
            udiff_calls_html += f"""
            <div class="tool-call">
                <h4>UDiff Call #{i}</h4>
                <p><strong>File Path:</strong> {html.escape(call.tool_input.get('file_path', 'NO FILE PATH'))}</p>
                <div class="udiff-content">
                    <pre class="diff-display">{highlight(
                        call.tool_input.get('udiff', 'NO UDIFF'),
                        DiffLexer(),
                        HtmlFormatter(style='monokai')
                    )}</pre>
                </div>
                <p><strong>Tool Output:</strong></p>
                <pre>{html.escape(call.tool_output)}</pre>
                <p><strong>Tool Message:</strong></p>
                <pre>{html.escape(call.tool_message)}</pre>
            </div>
            """
        udiff_calls_html += "</div>"

    result_metrics = result.get_all_metrics()
    result_metrics_table_html, result_metrics_data = generate_result_data_attributes(
        result_metrics
    )

    # Determine which HTML directory to use for links and create model-specific subdirectory
    html_dir = None
    model_subdir = None
    model_prefix = ""

    if is_reference and reference_html_dir:
        html_dir = reference_html_dir
        model_subdir = "reference_model"
        model_prefix = "reference_"
    elif not is_reference and target_html_dir:
        html_dir = target_html_dir
        model_subdir = "target_model"
        model_prefix = "target_"

    # Generate links based on the original HTML files
    links_html = ""
    if html_dir and output_dir and model_subdir:
        # Create model-specific subdirectory
        model_output_dir = output_dir / model_subdir
        model_output_dir.mkdir(parents=True, exist_ok=True)

        # Look for existing tool call logs link
        tool_calls_path = html_dir / f"tool_calls_{uuid}.html"
        if tool_calls_path.exists():
            # Copy the file to the model-specific subdirectory with a model-specific prefix
            new_tool_calls_path = (
                model_output_dir / f"{model_prefix}tool_calls_{uuid}.html"
            )
            if not new_tool_calls_path.exists():
                import shutil

                shutil.copy2(tool_calls_path, new_tool_calls_path)

            # Create a relative link to the new file
            rel_path = f"{model_subdir}/{model_prefix}tool_calls_{uuid}.html"
            links_html += (
                f'<p><a href="{rel_path}">View detailed tool call logs</a></p>'
            )

        # Look for existing dialog link
        messages_path = html_dir / f"messages_{uuid}.html"
        if messages_path.exists():
            # Copy the file to the model-specific subdirectory with a model-specific prefix
            new_messages_path = model_output_dir / f"{model_prefix}messages_{uuid}.html"
            if not new_messages_path.exists():
                import shutil

                shutil.copy2(messages_path, new_messages_path)

            if static_report:
                # For static report, inline the dialog content
                dialog_content = _extract_dialog_content(messages_path)
                links_html += f'<div class="inlined-dialog"><h3>Full Dialog</h3>{dialog_content}</div>'
            else:
                # Create a relative link to the new file
                rel_path = f"{model_subdir}/{model_prefix}messages_{uuid}.html"
                links_html += f'<p><a href="{rel_path}">View full dialog</a></p>'

        # Look for existing result link
        result_path = html_dir / f"result_{uuid}.html"
        if result_path.exists():
            # Copy the file to the model-specific subdirectory with a model-specific prefix
            new_result_path = model_output_dir / f"{model_prefix}result_{uuid}.html"
            if not new_result_path.exists():
                import shutil

                shutil.copy2(result_path, new_result_path)

            # Create a relative link to the new file
            rel_path = f"{model_subdir}/{model_prefix}result_{uuid}.html"
            links_html += f'<p><a href="{rel_path}">View detailed report</a></p>'

    return f"""
        <div class="status {'status-correct' if result.correct else 'status-incorrect'}">
            {'✓ Correct' if result.correct else '✗ Incorrect'}
        </div>
        <div class="result-data">
            {result_metrics_table_html}
        </div>
        <h3>Links</h3>
        <div class="links">
            {links_html}
        </div>
        <h3>File Path</h3>
        <pre>{html.escape(result.sample.file_path)}</pre>
        <h3>Edit Request</h3>
        <pre>{html.escape(result.sample.short_edit_description)}</pre>
        <h3>Tool Output</h3>
        <pre>{html.escape(result.tool_impl_output.tool_output)}</pre>
        <h3>Result Message</h3>
        <pre>{html.escape(result.tool_impl_output.tool_result_message)}</pre>
        {udiff_calls_html}
        <h3>Diff Against Original</h3>
        <div class="diff-content">
            <pre class="diff-display">{highlight(
                result.diff_against_original,
                DiffLexer(),
                HtmlFormatter(style='monokai')
            )}</pre>
        </div>
        <h3>Diff Against Expected</h3>
        <div class="diff-content">
            <pre class="diff-display">{highlight(
                result.diff_against_expected,
                DiffLexer(),
                HtmlFormatter(style='monokai')
            )}</pre>
        </div>
    """


def _group_results_by_change(
    base_summary: EditAgentEvalSummary,
    new_summary: EditAgentEvalSummary,
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Group results into improvements and regressions."""
    improvements = {}
    regressions = {}

    # Find common UUIDs
    common_uuids = set(base_summary.outputs.keys()) & set(new_summary.outputs.keys())

    for uuid in common_uuids:
        base_result = base_summary.outputs[uuid]
        new_result = new_summary.outputs[uuid]

        if not base_result.correct and new_result.correct:
            improvements[uuid] = (base_result, new_result)
        elif base_result.correct and not new_result.correct:
            regressions[uuid] = (base_result, new_result)

    return improvements, regressions


def generate_comparison_html(
    base_summary: EditAgentEvalSummary,
    new_summary: EditAgentEvalSummary,
    output_dir: Optional[Path] = None,
    reference_display_name: Optional[str] = None,
    target_display_name: Optional[str] = None,
    reference_html_dir: Optional[Path] = None,
    target_html_dir: Optional[Path] = None,
    static_report: bool = False,
) -> str:
    """Generate HTML report comparing two EditAgentEvalSummary objects.

    Args:
        base_summary: The base EditAgentEvalSummary object
        new_summary: The new EditAgentEvalSummary object to compare against
        output_dir: Optional output directory where individual eval outputs are saved
        reference_display_name: Optional display name for the reference model
        target_display_name: Optional display name for the target model
        reference_html_dir: Optional directory containing the reference HTML report
        target_html_dir: Optional directory containing the target HTML report
        static_report: If True, generates a non-interactive, fully expanded static HTML report
                      with inlined dialog content
    """
    # Get CSS for syntax highlighting and our custom styles
    css = HtmlFormatter(style="monokai").get_style_defs(".highlight")

    # Group results by changes
    improvements, regressions = _group_results_by_change(base_summary, new_summary)

    # Use display names if provided, otherwise fall back to agent_name from summary
    reference_name = reference_display_name or base_summary.agent_name
    target_name = target_display_name or new_summary.agent_name

    # Generate model info section content
    model_info_content = f"""
    <div class="comparison-grid">
        <div class="base-model">
            <h3>Reference Model</h3>
            <table class="stats-table">
                <tr>
                    <th>Dataset</th>
                    <td>{base_summary.dataset_name}</td>
                </tr>
                <tr>
                    <th>Model</th>
                    <td>{reference_name}</td>
                </tr>
            </table>
        </div>
        <div class="new-model">
            <h3>Target Model</h3>
            <table class="stats-table">
                <tr>
                    <th>Dataset</th>
                    <td>{new_summary.dataset_name}</td>
                </tr>
                <tr>
                    <th>Model</th>
                    <td>{target_name}</td>
                </tr>
            </table>
        </div>
    </div>
    """

    # Generate improvements section
    improvements_sections = []
    for uuid, (base_result, new_result) in improvements.items():
        result_id = f"improvement-{uuid}"
        # Remove onclick attribute for static report
        onclick_attr = (
            "" if static_report else f"onclick=\"toggleSection('{result_id}')\""
        )
        improvements_sections.append(f"""
        <div class="section result-item">
            <h2 {onclick_attr}>
                ✓ Improvement: {html.escape(uuid)}
            </h2>
            <div id="{result_id}" class="section-content">
                <div class="comparison-grid">
                    <div class="base-model">
                        <h3>Reference Model Result</h3>
                        {_generate_result_content(base_result, output_dir, uuid, is_reference=True, reference_html_dir=reference_html_dir, target_html_dir=target_html_dir, static_report=static_report)}
                    </div>
                    <div class="new-model">
                        <h3>Target Model Result</h3>
                        {_generate_result_content(new_result, output_dir, uuid, is_reference=False, reference_html_dir=reference_html_dir, target_html_dir=target_html_dir, static_report=static_report)}
                    </div>
                </div>
            </div>
        </div>
        """)

    # Generate regressions section
    regressions_sections = []
    for uuid, (base_result, new_result) in regressions.items():
        result_id = f"regression-{uuid}"
        # Remove onclick attribute for static report
        onclick_attr = (
            "" if static_report else f"onclick=\"toggleSection('{result_id}')\""
        )
        regressions_sections.append(f"""
        <div class="section result-item">
            <h2 {onclick_attr}>
                ✗ Regression: {html.escape(uuid)}
            </h2>
            <div id="{result_id}" class="section-content">
                <div class="comparison-grid">
                    <div class="base-model">
                        <h3>Reference Model Result</h3>
                        {_generate_result_content(base_result, output_dir, uuid, is_reference=True, reference_html_dir=reference_html_dir, target_html_dir=target_html_dir, static_report=static_report)}
                    </div>
                    <div class="new-model">
                        <h3>Target Model Result</h3>
                        {_generate_result_content(new_result, output_dir, uuid, is_reference=False, reference_html_dir=reference_html_dir, target_html_dir=target_html_dir, static_report=static_report)}
                    </div>
                </div>
            </div>
        </div>
        """)

    # Generate HTML content
    # Determine if we should include interactive elements
    script_tag = (
        "" if static_report else f"<script>{read_static_file('sections.js')}</script>"
    )

    # For static report, add CSS to ensure all sections are expanded
    static_css = (
        """
        /* Static report styles - all sections expanded by default */
        .section-content {
            display: block !important;
        }
        .section {
            margin-bottom: 30px;
        }
        h2 {
            cursor: default !important;
        }
    """
        if static_report
        else ""
    )

    # Remove onclick attributes for static report
    onclick_attr = "" if static_report else "onclick=\"toggleSection('model-info')\""
    onclick_improvements = (
        "" if static_report else "onclick=\"toggleSection('improvements')\""
    )
    onclick_regressions = (
        "" if static_report else "onclick=\"toggleSection('regressions')\""
    )

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Model Comparison: {reference_display_name or base_summary.agent_name} vs {target_display_name or new_summary.agent_name}</title>
        <style>
            {css}
            {read_static_file('styles.css')}
            {static_css}
            .comparison-grid {{
                display: grid;
                grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
                gap: 20px;
                margin: 10px 0;
                width: 100%;
                overflow-x: auto;
            }}
            .base-model, .new-model {{
                padding: 10px;
                border: 1px solid #444;
                border-radius: 5px;
                min-width: 0;
                overflow-x: auto;
            }}
            table {{
                width: 100%;
                table-layout: fixed;
            }}
            td {{
                word-wrap: break-word;
                overflow-wrap: break-word;
            }}
            .improvements-section, .regressions-section {{
                margin: 20px 0;
            }}
            .improvements-section h2 {{
                color: #4caf50;
            }}
            .regressions-section h2 {{
                color: #f44336;
            }}
            .inlined-dialog {{
                margin-top: 20px;
                padding: 10px;
                border: 1px solid #444;
                border-radius: 5px;
            }}
        </style>
        {script_tag}
    </head>
    <body>
        <div class="container">
            <div class="section expanded">
                <h2 {onclick_attr}>Model Information</h2>
                <div id="model-info" class="section-content">
                    {model_info_content}
                </div>
            </div>

            <div class="improvements-section">
                <div class="section expanded">
                    <h2 {onclick_improvements}>
                        Improvements ({len(improvements)})
                    </h2>
                    <div id="improvements" class="section-content">
                        {''.join(improvements_sections)}
                    </div>
                </div>
            </div>

            <div class="regressions-section">
                <div class="section expanded">
                    <h2 {onclick_regressions}>
                        Regressions ({len(regressions)})
                    </h2>
                    <div id="regressions" class="section-content">
                        {''.join(regressions_sections)}
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    return html_content


def save_comparison_html(
    base_summary: EditAgentEvalSummary,
    new_summary: EditAgentEvalSummary,
    output_path: Union[str, Path],
    output_dir: Optional[Path] = None,
    reference_display_name: Optional[str] = None,
    target_display_name: Optional[str] = None,
    reference_html_dir: Optional[Path] = None,
    target_html_dir: Optional[Path] = None,
    static_report: bool = False,
) -> None:
    """Save comparison HTML to the specified path.

    Args:
        base_summary: The base EditAgentEvalSummary object
        new_summary: The new EditAgentEvalSummary object to compare against
        output_path: Path where to save the HTML file
        output_dir: Optional output directory where individual eval outputs are saved
        reference_display_name: Optional display name for the reference model
        target_display_name: Optional display name for the target model
        reference_html_dir: Optional directory containing the reference HTML report
        target_html_dir: Optional directory containing the target HTML report
        static_report: If True, generates a non-interactive, fully expanded static HTML report
                      with inlined dialog content
    """
    html_content = generate_comparison_html(
        base_summary,
        new_summary,
        output_dir,
        reference_display_name,
        target_display_name,
        reference_html_dir,
        target_html_dir,
        static_report,
    )
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    output_path.write_text(html_content)
