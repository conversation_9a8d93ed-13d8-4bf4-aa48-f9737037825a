from pathlib import Path

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalOutput,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.common import (
    format_code,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.templates import (
    read_static_file,
)
from research.llm_apis.llm_client import (
    GeneralContentBlock,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
)


def _generate_list_html(items: list) -> str:
    """Generate HTML for a list.

    Args:
        items: List of items to render

    Returns:
        HTML string with list contents rendered as code blocks
    """
    if not items:
        return ""

    parts = []
    for i, item in enumerate(items):
        if isinstance(item, dict):
            formatted_item = _generate_dict_html(item)
        elif isinstance(item, list):
            formatted_item = _generate_list_html(item)
        else:
            formatted_item = format_code(str(item))
        parts.append(
            f'<div class="list-item">'
            f'<span class="list-index">[{i}]</span>'
            f'<div class="list-value">{formatted_item}</div>'
            f"</div>"
        )

    return '<div class="list-container">\n' f"{chr(10).join(parts)}\n" "</div>"


def _generate_dict_html(d: dict[str, str]) -> str:
    """Generate HTML for a dictionary.

    Args:
        d: Dictionary with string keys and values to render

    Returns:
        HTML string with dictionary contents rendered as code blocks
    """
    if not d:
        return ""

    parts = []
    for key, value in d.items():
        if isinstance(value, dict):
            formatted_value = _generate_dict_html(value)
        elif isinstance(value, list) and all(isinstance(item, dict) for item in value):
            formatted_value = _generate_list_html(value)
        else:
            formatted_value = format_code(str(value))
        parts.append(
            f'<div class="dict-item">'
            f'<span class="dict-key">{key}:</span>'
            f'<div class="dict-value">{formatted_value}</div>'
            f"</div>"
        )

    return '<div class="dict-container">\n' f"{chr(10).join(parts)}\n" "</div>"


def _generate_tool_call_html(tool_call: ToolCall) -> str:
    """Generate HTML for a tool call."""

    return (
        f'<div class="tool-call">'
        f"Tool call: {tool_call.tool_name} (id: {tool_call.tool_call_id})"
        f"{_generate_dict_html(tool_call.tool_input)}"
        f"</div>"
    )


def _generate_block_html(block: GeneralContentBlock) -> str:
    """Generate HTML for a single content block."""
    if isinstance(block, TextPrompt):
        formatted_text = format_code(block.text)
        return f'<div class="text-prompt">{formatted_text}</div>'
    elif isinstance(block, TextResult):
        formatted_text = format_code(block.text)
        return f'<div class="text-result">{formatted_text}</div>'
    elif isinstance(block, ToolCall):
        return _generate_tool_call_html(block)
    elif isinstance(block, ToolFormattedResult):
        formatted_output = format_code(block.tool_output)
        return (
            f'<div class="tool-result">'
            f"Tool result: {block.tool_name} (id: {block.tool_call_id})"
            f'<div class="text-result">{formatted_output}</div>'
            f"</div>"
        )
    else:
        raise ValueError(f"Unknown block type: {type(block)}")


def generate_dialog_html(dialog_messages: list[list[GeneralContentBlock]]) -> str:
    """Generate HTML for a dialog.

    Args:
        dialog_messages: A list of message lists, where each message list represents
            a turn in the dialog. Even-indexed turns are user messages, odd-indexed
            turns are assistant messages.

    Returns:
        HTML string representing the dialog.
    """
    html_parts = []

    for i, message_list in enumerate(dialog_messages):
        role = "user" if i % 2 == 0 else "assistant"
        if not message_list:
            continue
        if isinstance(message_list[0], ToolCall):
            shortened_message = (
                f"Tool call: {message_list[0].tool_name} {message_list[0].tool_input}"
            )
        elif isinstance(message_list[0], ToolFormattedResult):
            shortened_message = f"Tool result: {message_list[0].tool_name} {message_list[0].tool_output}"
        else:
            shortened_message = message_list[0].text
        shortened_message = shortened_message[:100].replace("\n", " ")

        message_header = f"{role.title()} Message #{(i//2)+1}: {shortened_message}"
        section_id = f"message-{i}"
        blocks_html = "\n".join(_generate_block_html(block) for block in message_list)
        html_parts.append(
            f'<div class="section">\n'
            f"<h2 onclick=\"toggleSection('{section_id}')\">{message_header}</h2>\n"
            f'<div id="{section_id}" class="section-content">\n'
            f'<div class="message {role}-message">\n'
            f'<div class="role">{role.title()}</div>\n'
            f"{blocks_html}\n"
            f"</div>\n"
            f"</div>\n"
            f"</div>"
        )

    return '<div class="dialog">\n' f"{chr(10).join(html_parts)}\n" "</div>"


def generate_and_save_dialog_html_with_link(
    eval_output: EditAgentEvalOutput,
    output_dir: Path,
) -> str:
    """Generate HTML for a dialog and save it to a file.

    Args:
        eval_output: The EditAgentEvalOutput containing dialog messages
        output_dir: Directory where to save the HTML file

    Returns:
        HTML string containing a link to the generated dialog file
    """
    messages_path = output_dir / f"messages_{eval_output.sample.uuid}.html"

    dialog_html = generate_dialog_html(eval_output.dialog_messages)

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Dialog - {eval_output.sample.uuid}</title>
        <style>
            {read_static_file('styles.css')}
        </style>
        <script>{read_static_file('sections.js')}</script>
    </head>
    <body>
        <div class="container">
            <div class="section expanded">
                <h2 onclick="toggleSection('dialog-header')">Dialog - {eval_output.sample.uuid}</h2>
                <div id="dialog-header" class="section-content">
                    <div style="margin-bottom: 10px;">
                        <button class="action-button" onclick="expandAllSections()" style="margin-right: 10px;">Expand All</button>
                        <button class="action-button" onclick="collapseAllSections()">Collapse All</button>
                    </div>
                    {dialog_html}
                </div>
            </div>
        </div>
    </body>
    </html>
    """

    messages_path.write_text(html_content)
    return f'<p><a href="{messages_path.name}">View full dialog</a></p>'
