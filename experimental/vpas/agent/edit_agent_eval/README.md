## Run evaluations

The `run_eval.py` script runs evaluations on a set of samples and generates an HTML report.

### Usage

```bash
python experimental/vpas/agent/edit_agent_eval/run_eval.py \
    --dataset-dir /mnt/efs/augment/user/vpas/edit_agent_eval/datasets/with_labels \
    --model-name gemini-2.5-pro-preview-03-24 \
    --client-type google_genai \
    --agent-types STR_REPLACE_MULTIEDIT
```

```bash
python experimental/vpas/agent/edit_agent_eval/run_eval.py \
    --dataset-dir /mnt/efs/augment/user/vpas/edit_agent_eval/datasets/with_labels \
    --model-name claude-lycra-eap \
    --agent-types STR_REPLACE_MULTIEDIT
```

### Options

- `--dataset-dir`: Directory containing evaluation dataset (optional, default: `dataset/`)
- `--output-path`: Path to save evaluation results in json format (optional, default: random path in `/tmp/`)
- `--limit-samples`: Limit the number of samples to process (optional)
- `--uuid`: UUID of a specific sample to evaluate (optional)
- `--category`: Category of samples to evaluate (optional)

## Compare HTML reports or JSON summaries

The `compare_html_reports.py` script compares two existing HTML reports or JSON summaries generated by `run_eval.py` and creates a comparison report showing improvements and regressions between the two runs.

### Usage

You can compare using HTML reports:

```bash
python experimental/vpas/agent/edit_agent_eval/compare_html_reports.py \
    --reference-html /mnt/efs/augment/public_html/vpas/edit_agent_eval/with_labels_claude-3-7-sonnet-20250219_STR_REPLACE_MULTIEDIT_EditAgent_mt5/summary.html \
    --reference-name "Claude 3.7 Sonnet" \
    --target-html /mnt/efs/augment/public_html/vpas/edit_agent_eval/with_labels_gemini-2.5-pro-preview-05-06_STR_REPLACE_MULTIEDIT_EditAgent_mt5/summary.html \
    --target-name "Gemini 2.5 Pro" \
    --static-report
```

Or directly using JSON summary files:

```bash
python experimental/vpas/agent/edit_agent_eval/compare_html_reports.py \
    --reference-json /mnt/efs/augment/user/vpas/edit_agent_eval/with_labels_claude-3-7-sonnet-20250219_STR_REPLACE_MULTIEDIT_EditAgent_mt5/summary.json \
    --target-json /mnt/efs/augment/user/vpas/edit_agent_eval/with_labels_gemini-2.5-pro-preview-05-06_STR_REPLACE_MULTIEDIT_EditAgent_mt5/summary.json
```

You can also mix HTML and JSON inputs:

```bash
python experimental/vpas/agent/edit_agent_eval/compare_html_reports.py \
    --reference-html /path/to/reference/summary.html \
    --target-json /path/to/target/summary.json
```

### Options

- `--reference-html`: Path to the reference HTML report (baseline)
- `--reference-json`: Path to the reference JSON summary file (baseline)
- `--target-html`: Path to the target HTML report to compare against reference
- `--target-json`: Path to the target JSON summary file to compare against reference
- `--reference-name`: Display name for the reference run (optional, default: extracted from path)
- `--target-name`: Display name for the target run (optional, default: extracted from path)
- `--output-dir`: Directory to save the comparison report (optional, default: auto-generated)
- `--output-name`: Name for the comparison report (optional, default: auto-generated)

Note: You must provide either `--reference-html` or `--reference-json`, and either `--target-html` or `--target-json`.

### How it works

1. The script extracts the corresponding `summary.json` files from the HTML report paths or loads them directly
2. It loads the `EditAgentEvalSummary` objects from these JSON files
3. It generates a comparison report showing:
   - Model information for both runs
   - Improvements (cases where the second run succeeded but the first failed)
   - Regressions (cases where the first run succeeded but the second failed)
4. The comparison report is saved as HTML and a URL is provided

### Example

```bash
# Compare two evaluation runs using HTML reports
python experimental/vpas/agent/edit_agent_eval/compare_html_reports.py \
    --reference-html /mnt/efs/augment/public_html/vpas/edit_agent_eval/dataset_claude-3-5-sonnet-v2@20241022_udiff_mt5/summary.html \
    --target-html /mnt/efs/augment/public_html/vpas/edit_agent_eval/dataset_claude-3-5-sonnet-v2@20241022_str_replace_mt5/summary.html

# Compare with custom names
python experimental/vpas/agent/edit_agent_eval/compare_html_reports.py \
    --reference-html /mnt/efs/augment/public_html/vpas/edit_agent_eval/dataset_claude-3-5-sonnet-v2@20241022_udiff_mt5/summary.html \
    --target-html /mnt/efs/augment/public_html/vpas/edit_agent_eval/dataset_claude-3-5-sonnet-v2@20241022_str_replace_mt5/summary.html \
    --reference-name "UDiff Agent" \
    --target-name "String Replace Agent"

# Compare directly from JSON files
python experimental/vpas/agent/edit_agent_eval/compare_html_reports.py \
    --reference-json /mnt/efs/augment/user/vpas/edit_agent_eval/with_labels_claude-3-7-sonnet-20250219_STR_REPLACE_MULTIEDIT_EditAgent_mt5/summary.json \
    --target-json /mnt/efs/augment/user/vpas/edit_agent_eval/with_labels_gemini-2.5-pro-preview-03-25_STR_REPLACE_MULTIEDIT_EditAgent_mt5/summary.json \
    --reference-name "Claude 3.7 Sonnet" \
    --target-name "Gemini 2.5 Pro"
```

This will generate a comparison report at a URL like:
```
https://webserver.gcp-us1.r.augmentcode.com/vpas/edit_agent_eval/comparison_dataset_udiff_vs_str_replace/comparison.html
```

### Notes

- The script assumes the standard directory structure where HTML reports are in `/mnt/efs/augment/public_html/vpas/edit_agent_eval/` and JSON files are in `/mnt/efs/augment/user/vpas/edit_agent_eval/`
- The comparison shows side-by-side results for each sample that had different outcomes in the two runs
- Each result includes links to detailed reports, tool call logs, and dialog views
- When using JSON files directly, the script will try to find the corresponding HTML directories to provide links to detailed reports
- If the HTML directories cannot be found, a warning will be displayed and the links will not be available
