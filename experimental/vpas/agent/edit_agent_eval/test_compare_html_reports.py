#!/usr/bin/env python3
"""Test script for compare_html_reports.py."""

import json
import subprocess
import sys
import tempfile
from pathlib import Path

# Removed unused import


def create_test_environment():
    """Create a test environment with mock HTML and JSON files."""
    # Create test data manually to avoid issues with test_data.py
    from experimental.guy.agent_qa.file_edit.udiff_edit_file_agent import (
        ToolImplOutput,
    )
    from experimental.vpas.agent.edit_agent_eval.create_eval_from_agent_logs import (
        EditAgentEvalSample,
    )
    from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
        EditAgentEvalOutput,
        EditAgentEvalSummary,
    )

    # Create a sample
    sample = EditAgentEvalSample(
        uuid="test-uuid",
        file_path="test.py",
        original_file_content="def foo():\n    pass\n",
        expected_modified_file_content_variants=["def foo():\n    return 42\n"],
        short_edit_description="Add return statement",
        category="test",
        expected_exact_match=False,
    )

    # Create tool output
    tool_output = ToolImplOutput(
        tool_output="Applied edit successfully",
        tool_result_message="Success",
        auxiliary_data={"success": True},
    )

    # Create eval output
    eval_output1 = EditAgentEvalOutput(
        sample=sample,
        tool_impl_output=tool_output,
        modified_file_content="def foo():\n    return 42\n",
        diff_against_original="--- original\n+++ modified\n@@ -1,2 +1,2 @@\n def foo():\n-    pass\n+    return 42\n",
        diff_against_expected="",
        correct=True,
        success=True,
        tool_call_logs=[],
        dialog_messages=[],
        lang="python",
        ast_comparison=True,
        ast_parsing_succeeded=True,
    )

    # Create summary 1
    eval_summary1 = EditAgentEvalSummary(
        dataset_name="test_dataset",
        anthropic_model="claude-3-5-sonnet",
        agent_name="test_agent",
        outputs={"test-uuid": eval_output1},
        num_samples=1,
        num_samples_with_label=1,
        num_correct=1,
        num_success=1,
        num_errors=0,
        accuracy=1.0,
        success_rate=1.0,
        aux_data={},
    )

    # Create eval output 2 (incorrect)
    eval_output2 = EditAgentEvalOutput(
        sample=sample,
        tool_impl_output=tool_output,
        modified_file_content="def foo():\n    return 0\n",
        diff_against_original="--- original\n+++ modified\n@@ -1,2 +1,2 @@\n def foo():\n-    pass\n+    return 0\n",
        diff_against_expected="--- expected\n+++ modified\n@@ -1,2 +1,2 @@\n def foo():\n-    return 42\n+    return 0\n",
        correct=False,
        success=True,
        tool_call_logs=[],
        dialog_messages=[],
        lang="python",
        ast_comparison=True,
        ast_parsing_succeeded=True,
    )

    # Create summary 2
    eval_summary2 = EditAgentEvalSummary(
        dataset_name="test_dataset",
        anthropic_model="claude-3-5-sonnet",
        agent_name="test_agent_v2",
        outputs={"test-uuid": eval_output2},
        num_samples=1,
        num_samples_with_label=1,
        num_correct=0,
        num_success=1,
        num_errors=0,
        accuracy=0.0,
        success_rate=1.0,
        aux_data={},
    )

    # Create temporary directories
    temp_dir = tempfile.mkdtemp()
    temp_path = Path(temp_dir)

    # Create mock directory structure
    html_dir1 = (
        temp_path
        / "public_html"
        / "vpas"
        / "edit_agent_eval"
        / "test_dataset_test_agent_mt5"
    )
    html_dir2 = (
        temp_path
        / "public_html"
        / "vpas"
        / "edit_agent_eval"
        / "test_dataset_test_agent_v2_mt5"
    )
    json_dir1 = (
        temp_path / "user" / "vpas" / "edit_agent_eval" / "test_dataset_test_agent_mt5"
    )
    json_dir2 = (
        temp_path
        / "user"
        / "vpas"
        / "edit_agent_eval"
        / "test_dataset_test_agent_v2_mt5"
    )

    for dir_path in [html_dir1, html_dir2, json_dir1, json_dir2]:
        dir_path.mkdir(parents=True, exist_ok=True)

    # Save HTML files (mock)
    (html_dir1 / "summary.html").write_text("<html>Mock HTML 1</html>")
    (html_dir2 / "summary.html").write_text("<html>Mock HTML 2</html>")

    # Save JSON files
    (json_dir1 / "summary.json").write_text(
        json.dumps(eval_summary1.to_dict(), indent=2)
    )
    (json_dir2 / "summary.json").write_text(
        json.dumps(eval_summary2.to_dict(), indent=2)
    )

    return temp_path, html_dir1 / "summary.html", html_dir2 / "summary.html"


def test_compare_html_reports():
    """Test the compare_html_reports script."""
    temp_path, html1, html2 = create_test_environment()

    # Create output directory within the mock public_html structure
    output_dir = (
        temp_path / "public_html" / "vpas" / "edit_agent_eval" / "comparison_test"
    )
    output_dir.mkdir(parents=True, exist_ok=True)

    # Run the script
    cmd = [
        sys.executable,
        "experimental/vpas/agent/edit_agent_eval/compare_html_reports.py",
        "--html1",
        str(html1),
        "--html2",
        str(html2),
        "--output-dir",
        str(output_dir),
    ]

    # Temporarily modify the script to use our test paths
    script_path = Path(
        "experimental/vpas/agent/edit_agent_eval/compare_html_reports.py"
    )
    original_content = script_path.read_text()

    # Replace the hardcoded paths with our test paths
    modified_content = original_content.replace(
        "/mnt/efs/augment/user/vpas/edit_agent_eval",
        str(temp_path / "user" / "vpas" / "edit_agent_eval"),
    ).replace("/mnt/efs/augment/public_html", str(temp_path / "public_html"))

    script_path.write_text(modified_content)

    try:
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            print(f"Error running script: {result.stderr}")
            return False

        # Check if output file was created
        comparison_file = output_dir / "comparison.html"
        if not comparison_file.exists():
            print("Comparison file was not created")
            return False

        # Check if the file contains expected content
        content = comparison_file.read_text()
        if "test_agent" not in content or "test_agent_v2" not in content:
            print("Comparison file doesn't contain expected content")
            return False

        print("Test passed successfully!")
        return True

    finally:
        # Restore original script content
        script_path.write_text(original_content)


if __name__ == "__main__":
    success = test_compare_html_reports()
    sys.exit(0 if success else 1)
