#!/usr/bin/env python3
"""Script to compare two existing HTML reports or JSON summaries generated by run_eval.py."""

import argparse
import json
import re
from pathlib import Path
from typing import Dict, Optional, Tuple, Union

from experimental.vpas.agent.edit_agent_eval.edit_agent_eval_output import (
    EditAgentEvalSummary,
)
from experimental.vpas.agent.edit_agent_eval.html_reports.comparison import (
    save_comparison_html,
)
from experimental.vpas.utils.json_utils import setup_dataclasses_json

# Setup dataclasses_json to handle datetime serialization
setup_dataclasses_json()


def extract_summary_json_path(html_path: Path) -> Path:
    """Extract the path to the summary.json file from the HTML report path."""
    # The HTML report is typically in a directory like:
    # /mnt/efs/augment/public_html/vpas/edit_agent_eval/{eval_dir_name}/summary.html
    # The corresponding JSON is in:
    # /mnt/efs/augment/user/vpas/edit_agent_eval/{eval_dir_name}/summary.json

    # Extract the eval_dir_name from the HTML path
    eval_dir_name = html_path.parent.name

    # Construct the JSON path
    json_dir = Path("/mnt/efs/augment/user/vpas/edit_agent_eval")
    json_path = json_dir / eval_dir_name / "summary.json"

    return json_path


def load_summary_from_html_path(html_path: Path) -> EditAgentEvalSummary:
    """Load the EditAgentEvalSummary from the corresponding JSON file."""
    json_path = extract_summary_json_path(html_path)

    if not json_path.exists():
        raise FileNotFoundError(f"Summary JSON not found at {json_path}")

    with open(json_path) as f:
        return EditAgentEvalSummary.from_json(f.read())


def load_summary_from_json_path(json_path: Path) -> EditAgentEvalSummary:
    """Load the EditAgentEvalSummary directly from a JSON file."""
    if not json_path.exists():
        raise FileNotFoundError(f"Summary JSON not found at {json_path}")

    with open(json_path) as f:
        return EditAgentEvalSummary.from_json(f.read())


def extract_agent_name(html_path: Path) -> str:
    """Extract the agent name from the HTML path."""
    dir_name = html_path.parent.name
    # Format is typically: {dataset}_{model}_{agent}_mt{turns}
    parts = dir_name.split("_")

    # Find the agent name (usually the part before _mt)
    for i, part in enumerate(parts):
        if part.startswith("mt"):
            return parts[i - 1]

    # Fallback to the directory name
    return dir_name


def extract_dataset_name(html_path: Path) -> str:
    """Extract the dataset name from the HTML path."""
    dir_name = html_path.parent.name
    # Format is typically: {dataset}_{model}_{agent}_mt{turns}
    parts = dir_name.split("_")

    # Dataset name is usually the first part
    return parts[0] if parts else "dataset"


def extract_comparison_name(path1: Path, path2: Path) -> str:
    """Generate a comparison name from the two paths (HTML or JSON)."""
    # Extract eval directory names
    dir1 = path1.parent.name
    dir2 = path2.parent.name

    # Extract key components from directory names
    # Format is typically: {dataset}_{model}_{agent}_mt{turns}
    parts1 = dir1.split("_")
    parts2 = dir2.split("_")

    # Find the agent names (usually the part before _mt)
    agent1 = None
    agent2 = None

    for i, part in enumerate(parts1):
        if part.startswith("mt"):
            agent1 = parts1[i - 1]
            break

    for i, part in enumerate(parts2):
        if part.startswith("mt"):
            agent2 = parts2[i - 1]
            break

    if not agent1 or not agent2:
        # Fallback to simple directory names
        agent1 = dir1
        agent2 = dir2

    # Extract dataset name (usually the first part)
    dataset = parts1[0] if parts1 else "dataset"

    # Sanitize names for URL by converting to lowercase and snake case
    sanitized_agent1 = agent1.lower().replace(" ", "_").replace("-", "_")
    sanitized_agent2 = agent2.lower().replace(" ", "_").replace("-", "_")
    sanitized_dataset = dataset.lower().replace(" ", "_").replace("-", "_")

    return f"comparison_{sanitized_dataset}_{sanitized_agent1}_vs_{sanitized_agent2}"


def main():
    parser = argparse.ArgumentParser(description=__doc__)

    # Create mutually exclusive groups for reference and target inputs
    reference_group = parser.add_mutually_exclusive_group(required=True)
    reference_group.add_argument(
        "--reference-html",
        type=Path,
        help="Path to the reference HTML report (baseline)",
    )
    reference_group.add_argument(
        "--reference-json",
        type=Path,
        help="Path to the reference JSON summary file (baseline)",
    )

    target_group = parser.add_mutually_exclusive_group(required=True)
    target_group.add_argument(
        "--target-html",
        type=Path,
        help="Path to the target HTML report (to compare against reference)",
    )
    target_group.add_argument(
        "--target-json",
        type=Path,
        help="Path to the target JSON summary file (to compare against reference)",
    )

    parser.add_argument(
        "--reference-name",
        type=str,
        help="Display name for the reference run (default: extracted from path)",
    )
    parser.add_argument(
        "--target-name",
        type=str,
        help="Display name for the target run (default: extracted from path)",
    )
    parser.add_argument(
        "--output-dir",
        type=Path,
        help="Directory to save the comparison report (default: auto-generated)",
    )
    parser.add_argument(
        "--output-name",
        type=str,
        help="Name for the comparison report (default: auto-generated)",
    )
    parser.add_argument(
        "--static-report",
        action="store_true",
        help="Generate a non-interactive, fully expanded static HTML report with inlined dialog content",
    )
    args = parser.parse_args()

    # Determine reference and target paths
    reference_path = args.reference_html if args.reference_html else args.reference_json
    target_path = args.target_html if args.target_html else args.target_json

    # Validate input files
    if not reference_path.exists():
        raise FileNotFoundError(f"Input file not found: {reference_path}")
    if not target_path.exists():
        raise FileNotFoundError(f"Input file not found: {target_path}")

    # Load summaries from files
    print(f"Loading summary for {reference_path}")
    if args.reference_html:
        reference_summary = load_summary_from_html_path(reference_path)
        reference_html_dir = reference_path.parent
    else:
        reference_summary = load_summary_from_json_path(reference_path)
        # Try to find the corresponding HTML directory
        possible_html_dir = (
            Path("/mnt/efs/augment/public_html/vpas/edit_agent_eval")
            / reference_path.parent.name
        )
        reference_html_dir = possible_html_dir if possible_html_dir.exists() else None
        if reference_html_dir:
            print(
                f"Found corresponding HTML directory for reference: {reference_html_dir}"
            )
        else:
            print(
                "Warning: No HTML directory found for reference. Links to detailed reports will not be available."
            )

    print(f"Loading summary for {target_path}")
    if args.target_html:
        target_summary = load_summary_from_html_path(target_path)
        target_html_dir = target_path.parent
    else:
        target_summary = load_summary_from_json_path(target_path)
        # Try to find the corresponding HTML directory
        possible_html_dir = (
            Path("/mnt/efs/augment/public_html/vpas/edit_agent_eval")
            / target_path.parent.name
        )
        target_html_dir = possible_html_dir if possible_html_dir.exists() else None
        if target_html_dir:
            print(f"Found corresponding HTML directory for target: {target_html_dir}")
        else:
            print(
                "Warning: No HTML directory found for target. Links to detailed reports will not be available."
            )

    # Generate output directory and name if not provided
    if not args.output_dir:
        WEB_SERVER_DIR = Path("/mnt/efs/augment/public_html")
        if args.output_name:
            comparison_name = args.output_name
        else:
            # Use custom names if provided, otherwise extract from paths
            if args.reference_name:
                reference_name = args.reference_name
            elif args.reference_html:
                reference_name = extract_agent_name(reference_path)
            else:
                # For JSON files, use the parent directory name as a fallback
                reference_name = reference_path.parent.name

            if args.target_name:
                target_name = args.target_name
            elif args.target_html:
                target_name = extract_agent_name(target_path)
            else:
                # For JSON files, use the parent directory name as a fallback
                target_name = target_path.parent.name

            # Extract dataset name
            if args.reference_html:
                dataset_name = extract_dataset_name(reference_path)
            else:
                # For JSON files, try to get dataset name from the summary
                dataset_name = reference_summary.dataset_name

            # Sanitize names for URL by converting to lowercase and snake case
            sanitized_reference = (
                reference_name.lower()
                .replace(" ", "_")
                .replace("-", "_")
                .replace("/", "_")
                .replace("\\", "_")
            )
            sanitized_target = (
                target_name.lower()
                .replace(" ", "_")
                .replace("-", "_")
                .replace("/", "_")
                .replace("\\", "_")
            )
            sanitized_dataset = dataset_name.lower().replace(" ", "_").replace("-", "_")

            comparison_name = f"comparison_{sanitized_dataset}_{sanitized_reference}_vs_{sanitized_target}"
        rel_path = f"vpas/edit_agent_eval/{comparison_name}"
        output_dir = WEB_SERVER_DIR / rel_path
    else:
        output_dir = args.output_dir
        rel_path = str(output_dir.relative_to(Path("/mnt/efs/augment/public_html")))

    output_dir.mkdir(parents=True, exist_ok=True)
    comparison_path = output_dir / "comparison.html"

    # Save comparison HTML with display names
    print("Generating comparison report...")
    # Use the original names for display in the report
    if args.reference_name:
        reference_display_name = args.reference_name
    elif args.reference_html:
        reference_display_name = extract_agent_name(reference_path)
    else:
        reference_display_name = reference_summary.agent_name

    if args.target_name:
        target_display_name = args.target_name
    elif args.target_html:
        target_display_name = extract_agent_name(target_path)
    else:
        target_display_name = target_summary.agent_name

    save_comparison_html(
        base_summary=reference_summary,
        new_summary=target_summary,
        output_path=comparison_path,
        output_dir=output_dir,
        reference_display_name=reference_display_name,
        target_display_name=target_display_name,
        reference_html_dir=reference_html_dir,
        target_html_dir=target_html_dir,
        static_report=args.static_report,
    )

    # Print URL
    URL_TEMPLATE = "https://webserver.gcp-us1.r.augmentcode.com/{}"
    url = URL_TEMPLATE.format(f"{rel_path}/comparison.html")
    print(f"\nComparison report generated at: {url}")


if __name__ == "__main__":
    main()
