<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import Expand from "$common-webviews/src/design-system/icons/augment/expand.svelte";
  import Collapse from "$common-webviews/src/design-system/icons/augment/collapse.svelte";

  export let expanded: boolean | undefined = undefined;
  export let maxHeight: number = 200;
  export let transitionDurationMs: number | undefined = undefined;
  export let expandText: string = "Show more";
  export let collapseText: string | undefined = "Show less";
  export let paddingBottom: number = 30;
  export let showOnHover: boolean = false;
  // Threshold in pixels to determine if content is significantly overflowing
  // This prevents showing the "Show More" button for tiny overflows
  export let overflowThreshold: number = 10;
  let className = "";
  export { className as class };

  let overflowSize = 0;
  let hasSignificantOverflow = false;
  let transitioning = !!expanded;
  let transId: ReturnType<typeof setTimeout> | undefined = undefined;

  let durationMs = transitionDurationMs ?? Math.min(Math.max(500, overflowSize + maxHeight), 200);
  $: {
    durationMs = transitionDurationMs ?? Math.min(Math.max(500, overflowSize + maxHeight), 200);
  }

  function toggleExpanded() {
    transitioning = true;
    expanded = !expanded;
    clearTimeout(transId);
    transId = setTimeout(() => {
      transitioning = false;
    }, durationMs);
  }

  function overflow(node: HTMLElement) {
    let resizeObserver: ResizeObserver;
    if (expanded) {
      overflowSize = node.scrollHeight + node.clientHeight;
    }

    function checkOverflow(node: HTMLElement) {
      if (transitioning) {
        return;
      }

      const scrollHeight = node.scrollHeight;
      overflowSize = scrollHeight + node.clientHeight;

      // Check if content significantly exceeds maxHeight
      hasSignificantOverflow = scrollHeight > maxHeight + overflowThreshold;
    }

    function setupResizeObserver(node: HTMLElement) {
      resizeObserver?.disconnect();
      resizeObserver = new ResizeObserver(() => {
        requestAnimationFrame(() => {
          checkOverflow(node);
        });
      });
      resizeObserver.observe(node);
    }

    // Initial check with a small delay to ensure content is rendered
    checkOverflow(node);
    setupResizeObserver(node);

    return {
      update(node: HTMLElement) {
        setupResizeObserver(node);
        checkOverflow(node);
      },
      destroy() {
        resizeObserver?.disconnect();
      },
    };
  }
</script>

<div
  class="c-show-more {className}"
  class:c-show-more--expanded={expanded}
  class:c-show-more--show-on-hover={showOnHover}
>
  <div
    class="c-show-more__content"
    style="max-height: {expanded
      ? overflowSize
        ? overflowSize + paddingBottom + 'px'
        : 'unset'
      : maxHeight + 'px'};--show-more-transition: {durationMs}ms ease-in-out;"
    use:overflow
  >
    <slot />
  </div>
  {#if expanded || (!expanded && hasSignificantOverflow)}
    <div
      class="c-show-more__button-container"
      class:c-show-more__button-container--no-overflow={expanded}
    >
      {#if !expanded || collapseText}
        <ButtonAugment variant="soft" color="neutral" size={1} on:click={toggleExpanded}>
          {#if expanded}
            <Collapse slot="iconLeft" />
            {collapseText}
          {:else}
            <Expand slot="iconLeft" />
            {expandText}
          {/if}
        </ButtonAugment>
      {/if}
    </div>
  {/if}
</div>

<style>
  .c-show-more {
    position: relative;
    width: 100%;
    & .c-show-more__button-container .c-base-btn {
      width: 100%;
      background-color: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      position: relative;
    }

    & > .c-show-more__content {
      position: relative;
      overflow: clip;
      transition:
        max-height var(--show-more-transition),
        padding-bottom var(--show-more-transition);
      padding-bottom: 0;
      /* Ensure text is clipped at line boundaries */
      line-clamp: auto;
      -webkit-line-clamp: auto;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-height: 1.5;
      white-space: normal;
      word-break: break-word;
    }

    &.c-show-more--expanded .c-show-more__content {
      /** alas this is not supported in our version of electron yet */
      padding-bottom: 40px;
    }
    &.c-show-more--expanded .c-show-more__button-container {
      padding-top: 0;
    }

    & .c-show-more__button-container {
      position: absolute;
      bottom: 0;
      width: 100%;
      z-index: 1;
      padding: var(--ds-spacing-2);
      padding-top: 30px;
      background: linear-gradient(to bottom, transparent, var(--augment-window-background));
      opacity: 0;
      transition: opacity 0.2s ease-in-out;
    }
    &.c-show-more--show-on-hover:hover .c-show-more__button-container {
      opacity: 0.99;
    }
    &:not(.c-show-more--show-on-hover) .c-show-more__button-container {
      opacity: 0.99;
    }
    & .c-show-more__button-container:empty {
      display: none;
    }

    &.c-show-more--expanded .c-show-more__content::after,
    & .c-show-more__content--no-overflow::after,
    .c-show-more__button-container--no-overflow::before {
      display: none;
    }

    &.c-show-more--expanded:has(.c-show-more__button-container:empty) .c-show-more__content {
      padding-bottom: 0;
    }
    /* Button container hover behavior is now handled globally */
  }
</style>
