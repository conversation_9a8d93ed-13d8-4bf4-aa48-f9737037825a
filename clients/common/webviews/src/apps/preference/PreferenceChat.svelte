<script lang="ts">
  import { createEventDispatcher, onMount } from "svelte";
  import type {
    ChatPreferenceInput,
    ChatPreferenceResult,
  } from "$vscode/src/webview-panels/preference-panel-types";
  import AugmentMessage from "$common-webviews/src/apps/chat/components/conversation/AugmentMessage.svelte";
  import PreferenceSelector from "./components/PreferenceSelector.svelte";
  import TextInput from "./components/TextInput.svelte";
  import Button from "./components/Button.svelte";
  import Checkbox from "./components/Checkbox.svelte";
  import { ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { SpecialContextInputModel } from "$common-webviews/src/apps/chat/models/context-model";
  import { setChatModel } from "$common-webviews/src/apps/chat/chat-context";

  export let inputData: ChatPreferenceInput;
  const dispatch = createEventDispatcher();

  let chatModel = new ChatModel(new MessageBroker(host), host, new SpecialContextInputModel());
  setChatModel(chatModel);

  let formattingRating: string | null = null;
  let instructionFollowingRating: string | null = null;
  let hallucinationRating: string | null = null;
  let overallRating: string | null = null;
  let textFeedback = "";
  let isHighQuality = false;

  let streamingData = {
    a: null,
    b: null,
  };

  let isStreamingComplete =
    inputData.data.a.response.length > 0 && inputData.data.b.response.length > 0;

  function handleResult() {
    // Hallucination question is disabled for now
    hallucinationRating = "=";

    if (overallRating === null) {
      dispatch("notify", "Overall rating is required");
      return;
    }

    const result: ChatPreferenceResult = {
      overallRating,
      formattingRating: formattingRating || "=",
      hallucinationRating: hallucinationRating || "=",
      instructionFollowingRating: instructionFollowingRating || "=",
      isHighQuality,
      textFeedback,
    };
    dispatch("result", result);
  }

  function getQuestionForRating(overallRating: string | null) {
    if (overallRating === "=" || overallRating === null) {
      return "Is this a high quality comparison?";
    }
    const responseID = overallRating.startsWith("A") ? "A" : "B";
    return `Are you completely happy with response '${responseID}'?`;
  }

  onMount(() => {
    window.addEventListener("message", (event) => {
      const message = event.data;
      if (message.type === WebViewMessageType.chatModelReply) {
        if (message.stream === "A") {
          streamingData.a = message.data.text;
          console.log("Streaming A: ", message.data.text, "streaming: ", streamingData.a);
        } else if (message.stream === "B") {
          streamingData.b = message.data.text;
          console.log("Streaming B: ", message.data.text, "streaming: ", streamingData.b);
        }
        streamingData = streamingData; // Trigger reactivity
      } else if (message.type === WebViewMessageType.chatStreamDone) {
        console.log("Streaming complete");
        isStreamingComplete = true;
      }
    });
  });

  $: question = getQuestionForRating(overallRating);
  $: responseA = streamingData.a !== null ? streamingData.a : inputData.data.a.response;
  $: responseB = streamingData.b !== null ? streamingData.b : inputData.data.b.response;
  $: isStreamingComplete =
    inputData.data.a.response.length > 0 && inputData.data.b.response.length > 0;
</script>

<main>
  <div class="l-pref">
    <h1>Input message</h1>
    <AugmentMessage markdown={inputData.data.a.message} />
    <hr class="l-side-by-side" />

    <div class="l-side-by-side">
      <div class="l-side-by-side__child">
        <h1>Option A</h1>
        <AugmentMessage markdown={responseA} />
      </div>

      <div class="divider"></div>

      <div class="l-side-by-side__child">
        <h1>Option B</h1>
        <AugmentMessage markdown={responseB} />
      </div>
    </div>

    <hr />
    {#if isStreamingComplete}
      <PreferenceSelector
        bind:selected={formattingRating}
        question="Which response is formatted better? (e.g. level of detail style, structure)?"
      />
      <PreferenceSelector
        bind:selected={instructionFollowingRating}
        question="Which response follows your instruction better?"
      />
      <PreferenceSelector
        bind:selected={overallRating}
        question="Which response is better overall?"
      />
      <Checkbox bind:isChecked={isHighQuality} {question} />
      <TextInput
        bind:value={textFeedback}
        question="Any additional feedback?"
        placeholder="Please explain your answers to the above questions."
      />

      <Button label="Submit" onClick={handleResult} />
    {:else}
      <p>Streaming in progress... Please wait for both responses to complete.</p>
    {/if}
  </div>
</main>

<style>
  .l-pref {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .l-side-by-side {
    display: flex;
    gap: 20px;
  }

  .l-side-by-side__child {
    flex: 1;
  }

  .divider {
    width: 2px;
    background-color: var(--vscode-panel-border);
  }

  hr {
    border: none;
    border-top: 2px solid var(--vscode-panel-border);
    margin: 0;
  }

  h1 {
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 10px;
  }
</style>
