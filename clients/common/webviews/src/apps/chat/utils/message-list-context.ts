import type { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
import type { IRemoteAgentsError } from "../../remote-agent-manager/models/remote-agents-state-model/remote-agents-state-model";
import type { ChatModel } from "../models/chat-model";
import { type ConversationModel } from "../models/conversation-model";
import {
  AgentExchangeStatus,
  type ChatItem,
  ExchangeStatus,
  hasToolResult,
  isChatItemAgenticCheckpointDelimiter,
  isChatItemAgentOnboarding,
  isChatItemAutofixMessage,
  isChatItemAutofixStage,
  isChatItemAutofixSteeringMessage,
  isChatItemEducateFeatures,
  isChatItemExchangeWithStatus,
  isChatItemGenerateCommitMessage,
  isChatItemSignInWelcome,
  isChatItemSummaryResponse,
} from "../types/chat-message";
import { ToolUsePhase } from "../types/tool-use-state";

export type GroupedChatItem = { turn: ChatItem; idx: number }[];

export type LastGroupConfig = {
  /**
   * The message to display if the last exchange is retriable. If undefined, no retry message will be shown
   */
  retryMessage: string | undefined;
  /**
   * Whether to show the GeneratingResponse component
   */
  showGeneratingResponse: boolean;
  /**
   * Whether to show the AwaitingUserInput component
   */
  showAwaitingUserInput: boolean;
  /**
   * Whether to show the RunningSpacer component
   */
  showRunningSpacer: boolean;
  /**
   * Whether to show the Stopped component
   */
  showStopped: boolean;
  /**
   * Whether to show the remote agent error message
   */
  remoteAgentErrorConfig?: {
    error: IRemoteAgentsError;
    onRetry?: () => void;
  };
};

export interface MessageListContext {
  /**
   * The raw chat history
   */
  chatHistory: ChatItem[];
  /**
   * Grouped chat history
   */
  groupedChatHistory: GroupedChatItem[];
  /**
   * The options for what to render at the bottom of the message list
   */
  lastGroupConfig: LastGroupConfig;
  /**
   * Whether to show the floating buttons
   */
  doShowFloatingButtons: boolean;
  /**
   * Whether to show the agent setup logs
   */
  doShowAgentSetupLogs: boolean;
}

const getChatHistory = (
  conversationModel: ConversationModel,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  return remoteAgentsModel.isActive
    ? remoteAgentsModel.getCurrentChatHistory() // If we have a remote agents model, the UI is driven by the remote state
    : conversationModel.chatHistory.filter((m) => {
        return (
          isChatItemExchangeWithStatus(m) ||
          isChatItemSignInWelcome(m) ||
          isChatItemGenerateCommitMessage(m) ||
          isChatItemEducateFeatures(m) ||
          isChatItemSummaryResponse(m) ||
          isChatItemAutofixMessage(m) ||
          isChatItemAutofixSteeringMessage(m) ||
          isChatItemAutofixStage(m) ||
          isChatItemAgentOnboarding(m) ||
          isChatItemAgenticCheckpointDelimiter(m)
        );
      });
};

/* We group tool use chains together. E.g., consider an interaction like this:
 *
 * - User: "What are my pending changes?"
 * - Augment: "Let's run `git status`," [Tool Use of `git status`]
 * - User: [Tool Result for `git status`]
 * - Augment: "Great. Now let's run `git diff`," [Tool Use of `git diff`]
 * - User: [Tool Result for `git diff`]
 *
 * This entire sequence of turns is a single "group", which is represented
 * as a single MessageListItem. This impacts scrolling behavior: when streaming,
 * we force scroll to keep the group in view. The grouping allows the user to
 * view any part of the grouped exchange during streaming.
 */
const groupChatHistory = (chatHistory: ChatItem[]) => {
  return chatHistory.reduce(
    (acc: Array<Array<{ turn: ChatItem; idx: number }>>, turn: ChatItem, idx: number) => {
      const turnHasToolResult = isChatItemExchangeWithStatus(turn) && hasToolResult(turn);

      if (turnHasToolResult && acc.length > 0) {
        // Add to the current group if it has a tool result
        acc[acc.length - 1].push({ turn, idx });
      } else if (isChatItemAgenticCheckpointDelimiter(turn) && acc.length > 0) {
        // Add to the current group if it's a checkpoint delimiter
        acc[acc.length - 1].push({ turn, idx });
      } else {
        // Start a new group
        acc.push([{ turn, idx }]);
      }

      return acc;
    },
    [],
  );
};

const wasLastMessageCancelled = (
  conversationModel: ConversationModel,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  const lastExchange = conversationModel?.lastExchange?.status;
  const lastExchangeCancelled = lastExchange === ExchangeStatus.cancelled;
  const lastToolPhase = conversationModel?.getLastToolUseState().phase;
  const lastToolCancelled = lastToolPhase === ToolUsePhase.cancelled;
  const isRemoteAgent = remoteAgentsModel.isActive;

  return !isRemoteAgent && (lastExchangeCancelled || lastToolCancelled);
};

const getAgentExchangeStatus = (
  ideAgentExchangeStatus: AgentExchangeStatus,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  if (remoteAgentsModel.isActive) {
    return remoteAgentsModel.isCurrentAgentRunning
      ? AgentExchangeStatus.running
      : AgentExchangeStatus.notRunning;
  }
  return ideAgentExchangeStatus;
};

export const getLastGroupConfig = (
  conversationModel: ConversationModel,
  agentExchangeStatus: AgentExchangeStatus,
  isCurrConversationAgentic: boolean,
  remoteAgentsModel: RemoteAgentsModel,
) => {
  const config: LastGroupConfig = {
    retryMessage: undefined,
    showGeneratingResponse: false,
    showAwaitingUserInput: false,
    showRunningSpacer: false,
    showStopped: false,
    remoteAgentErrorConfig: undefined,
  };

  if (agentExchangeStatus === AgentExchangeStatus.running) {
    const lastExchange = conversationModel?.lastExchange;
    if (lastExchange?.isRetriable && lastExchange?.display_error_message) {
      config.retryMessage = lastExchange.display_error_message;
    } else if (isCurrConversationAgentic || remoteAgentsModel.isActive) {
      const lastToolState = remoteAgentsModel.isActive
        ? remoteAgentsModel.getLastToolUseState()
        : conversationModel.getLastToolUseState();
      if (lastToolState.phase !== ToolUsePhase.running) {
        config.showGeneratingResponse = true;
      } else {
        config.showRunningSpacer = true;
      }
    } else {
      config.showGeneratingResponse = true;
    }
  } else if (agentExchangeStatus === AgentExchangeStatus.awaitingUserAction) {
    config.showAwaitingUserInput = true;
    config.showRunningSpacer = true;
  } else if (wasLastMessageCancelled(conversationModel, remoteAgentsModel)) {
    config.showStopped = true;
  }

  return config;
};

export const getMessageListContext = (
  chatModel: ChatModel,
  ideAgentExchangeStatus: AgentExchangeStatus,
  isCurrConversationAgentic: boolean,
  remoteAgentsModel: RemoteAgentsModel,
): MessageListContext => {
  const conversationModel = chatModel.currentConversationModel;
  const chatHistory = getChatHistory(conversationModel, remoteAgentsModel);
  const groupedChatHistory = groupChatHistory(chatHistory);
  const agentExchangeStatus = getAgentExchangeStatus(ideAgentExchangeStatus, remoteAgentsModel);
  const lastGroupConfig = getLastGroupConfig(
    conversationModel,
    agentExchangeStatus,
    isCurrConversationAgentic,
    remoteAgentsModel,
  );
  const doShowFloatingButtons = !remoteAgentsModel.isActive;
  const doShowAgentSetupLogs = !!remoteAgentsModel.isActive;

  if (remoteAgentsModel.agentChatHistoryError && remoteAgentsModel.currentAgentId) {
    const agentId = remoteAgentsModel.currentAgentId;
    lastGroupConfig.remoteAgentErrorConfig = {
      error: remoteAgentsModel.agentChatHistoryError,
      onRetry: () => remoteAgentsModel.refreshAgentChatHistory(agentId),
    };
  }

  return {
    chatHistory,
    groupedChatHistory,
    lastGroupConfig,
    doShowFloatingButtons,
    doShowAgentSetupLogs,
  };
};
