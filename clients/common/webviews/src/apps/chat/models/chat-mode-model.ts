import { type MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import {
  type WebViewMessage,
  WebViewMessageType,
} from "$vscode/src/webview-providers/webview-messages";
import { type RemoteAgentsModel } from "../../remote-agent-manager/models/remote-agents-model";
import { type AgentConversationModel } from "./agent-conversation-model";
import { AgentExecutionMode, type ChatModel } from "./chat-model";
import { NEW_AGENT_KEY } from "./agent-constants";
import { get } from "svelte/store";
import { type ChatModeType, getModeDisplayInfo } from "../components/buttons/mode-display-helper";

export class ChatModeModel implements MessageConsumer {
  constructor(
    private readonly _chatModel: ChatModel,
    private readonly _agentConversationModel: AgentConversationModel,
    private readonly _remoteAgentsModel: RemoteAgentsModel,
  ) {
    this._chatModel.flags.subscribe((flags) => {
      if (flags.isRemoteAgentWindow) {
        this.setToRemoteAgent();
      }
    });
  }

  static key = "chatModeModel";

  /**
   * Toggles between chat and agent modes in a cycle:
   * chat -> agent (manual) -> agent (auto) -> remote agent (if available) -> chat
   *
   * @param reverse If true, toggle in reverse direction
   */
  async toggleChatAgentMode(reverse: boolean = false): Promise<void> {
    if (reverse) {
      await this.toggleChatAgentModeReverse();
    } else {
      await this.toggleChatAgentModeForward();
    }
  }

  /**
   * Toggles forward through the modes in this order:
   * chat -> agent (manual) -> agent (auto) -> remote agent (if available) -> chat
   */
  private async toggleChatAgentModeForward(): Promise<void> {
    // Use the get function from svelte/store to get the current values
    const isAgentic = get(this._agentConversationModel.isCurrConversationAgentic);
    const currentMode = get(this._chatModel.agentExecutionMode);
    const isRemoteAgentActive = this._remoteAgentsModel.isActive;
    const remoteAgentsEnabled = this._chatModel.flags.enableBackgroundAgents;
    const enableAgentAutoMode = this._chatModel.flags.enableAgentAutoMode;

    // Determine the next mode in the cycle
    if (isRemoteAgentActive) {
      // Currently in remote agent mode -> switch back to chat mode
      this.setToChat();
    } else if (!isAgentic) {
      // Currently in chat mode -> switch to agent manual mode
      await this.setToAgent(AgentExecutionMode.manual);
    } else if (isAgentic && currentMode === AgentExecutionMode.manual && enableAgentAutoMode) {
      // Currently in agent manual mode -> switch to agent auto mode if enabled
      await this.setToAgent(AgentExecutionMode.auto);
    } else if (isAgentic && currentMode === AgentExecutionMode.auto && remoteAgentsEnabled) {
      // Currently in agent auto mode and remote agents are enabled -> switch to remote agent
      this.setToRemoteAgent();
      // Also set agent execution mode to manual for next cycle
      this._chatModel.agentExecutionMode.set(AgentExecutionMode.manual);
    } else {
      // Otherwise (or if remote agents not enabled) -> switch back to chat mode
      this.setToChat();
    }
  }

  /**
   * Toggles backward through the modes in this order:
   * chat <- agent (manual) <- agent (auto) <- remote agent (if available) <- chat
   */
  private async toggleChatAgentModeReverse(): Promise<void> {
    // Use the get function from svelte/store to get the current values
    const isAgentic = get(this._agentConversationModel.isCurrConversationAgentic);
    const currentMode = get(this._chatModel.agentExecutionMode);
    const isRemoteAgentActive = this._remoteAgentsModel.isActive;
    const remoteAgentsEnabled = this._chatModel.flags.enableBackgroundAgents;
    const enableAgentAutoMode = this._chatModel.flags.enableAgentAutoMode;

    // Determine the previous mode in the cycle
    // Check remote agent first since setToRemoteAgent no longer calls setToAgentic
    if (isRemoteAgentActive) {
      // Currently in remote agent mode -> switch to agent auto mode
      await this.setToAgent(AgentExecutionMode.auto);
    } else if (!isAgentic) {
      // Currently in chat mode -> switch to remote agent mode (if enabled) or agent auto mode
      if (remoteAgentsEnabled) {
        this.setToRemoteAgent();
      } else if (enableAgentAutoMode) {
        await this.setToAgent(AgentExecutionMode.auto);
      } else {
        await this.setToAgent(AgentExecutionMode.manual);
      }
    } else if (isAgentic && currentMode === AgentExecutionMode.auto) {
      // Currently in agent auto mode -> switch to agent manual mode
      await this.setToAgent(AgentExecutionMode.manual);
    } else if (isAgentic && currentMode === AgentExecutionMode.manual) {
      // Currently in agent manual mode -> switch to chat mode
      this.setToChat();
    } else {
      // Fallback - switch to chat mode
      this.setToChat();
    }
  }

  setToChat() {
    // Don't allow changing modes in a remote agent window
    if (this._chatModel.flags.isRemoteAgentWindow) {
      return;
    }

    // Check if dependencies are properly initialized
    if (!this._agentConversationModel) {
      console.error("AgentConversationModel is not initialized");
      return;
    }

    this._remoteAgentsModel.setIsActive(false);
    this._agentConversationModel.setToChat();
    void this._agentConversationModel.refreshAutoModeAcceptance();
    this._chatModel.currentConversationModel.resetTotalCharactersCache();
  }

  async setToAgent(mode: AgentExecutionMode) {
    // Don't allow changing modes in a remote agent window
    if (this._chatModel.flags.isRemoteAgentWindow) {
      return;
    }

    // Check if dependencies are properly initialized
    if (!this._agentConversationModel) {
      console.error("AgentConversationModel is not initialized");
      return;
    }

    this._remoteAgentsModel.setIsActive(false);
    await this._agentConversationModel.setToAgentic();
    this._chatModel.agentExecutionMode.set(mode);
    await this._agentConversationModel.refreshAutoModeAcceptance();
    this._chatModel.currentConversationModel.resetTotalCharactersCache();
  }

  async setToRemoteAgent(agentId?: string | null) {
    // Check if dependencies are properly initialized
    if (!this._agentConversationModel) {
      console.error("AgentConversationModel is not initialized");
      return;
    }

    // In a remote agent window, only allow setting to the specific remote agent
    if (this._chatModel.flags.isRemoteAgentWindow) {
      // If an agent ID is provided, make sure it matches the remote agent ID
      if (
        agentId &&
        this._chatModel.flags.remoteAgentId &&
        agentId !== this._chatModel.flags.remoteAgentId
      ) {
        agentId = this._chatModel.flags.remoteAgentId;
      }

      // If no agent ID is provided, use the remote agent ID
      if (!agentId && this._chatModel.flags.remoteAgentId) {
        agentId = this._chatModel.flags.remoteAgentId;
      }
    }

    this._remoteAgentsModel.setIsActive(true);

    // If we're creating a new agent, use the static key
    if (agentId) {
      this._remoteAgentsModel.setCurrentAgent(agentId);
    } else if (agentId === null) {
      this._remoteAgentsModel.clearCurrentAgent();
    } else if (!this._remoteAgentsModel?.currentAgent) {
      // The agents list is sorted by start time, so the first one is the most recent
      const mostRecentAgent =
        this._remoteAgentsModel.agentOverviews.length > 0
          ? this._remoteAgentsModel.agentOverviews[0]
          : undefined;
      if (mostRecentAgent) {
        this._remoteAgentsModel.setCurrentAgent(mostRecentAgent.remote_agent_id);
      }
    }
    this._chatModel.currentConversationModel.resetTotalCharactersCache();
  }

  /**
   * Shared function to create or use an existing thread
   * This ensures we only have one stub thread regardless of the agent type selected
   */
  private async createOrUseThread(type: ChatModeType, mode?: AgentExecutionMode) {
    // Set the appropriate mode
    if (type === "chat") {
      await this._chatModel.setCurrentConversation(undefined, true, {
        noopIfSameConversation: true,
      });
      this.setToChat();
    } else if (type === "localAgent" && mode) {
      await this._chatModel.setCurrentConversation(undefined, true, {
        noopIfSameConversation: true,
      });
      await this.setToAgent(mode);
    } else if (type === "remoteAgent") {
      this._remoteAgentsModel.setIsActive(true);
      this.setToRemoteAgent(null);
    }
  }

  /**
   * Creates or uses an existing chat thread
   */
  async createNewChatThread() {
    await this.createOrUseThread("chat");
  }

  /**
   * Creates or uses an existing local agent thread with the specified execution mode
   */
  async createNewLocalAgentThread(mode?: AgentExecutionMode) {
    await this.createOrUseThread("localAgent", mode);
  }

  /**
   * Creates or uses an existing remote agent thread
   */
  async createNewRemoteAgentThread() {
    await this.createOrUseThread("remoteAgent");
  }

  /**
   * Creates a thread of the current type based on the current mode info
   * This replicates the logic from NewThreadDropdown.svelte
   */
  async createThreadOfCurrentType() {
    // Get current mode info with the same logic as NewThreadDropdown
    const isCurrConversationAgentic = get(this._agentConversationModel.isCurrConversationAgentic);
    const agentExecutionMode = get(this._chatModel.agentExecutionMode);
    const isBackgroundAgent = get(this._remoteAgentsModel).isActive;

    const currentModeInfo = getModeDisplayInfo({
      isConversationAgentic: isCurrConversationAgentic,
      agentExecutionMode: agentExecutionMode,
      isBackgroundAgent: isBackgroundAgent,
    });

    if (currentModeInfo.type === "remoteAgent") {
      // Remote agent mode
      await this.createNewRemoteAgentThread();
    } else if (currentModeInfo.type === "localAgent") {
      // Local agent mode - determine execution mode
      const currentAgentMode = agentExecutionMode || AgentExecutionMode.manual;
      await this.createNewLocalAgentThread(currentAgentMode);
    } else if (currentModeInfo.type === "chat") {
      // Chat mode
      await this.createNewChatThread();
    } else {
      // Shouldn't happen fallback
      await this.createNewChatThread();
    }
  }

  /**
   * Find the latest chat thread (non-agent conversation)
   */
  private findLatestChatThread(): string | undefined {
    const chatConversations = this._chatModel
      .orderedConversations()
      .filter((conv) => !conv.extraData?.isAgentConversation && conv.id !== NEW_AGENT_KEY);
    return chatConversations.length > 0 ? chatConversations[0].id : undefined;
  }

  /**
   * Find the latest local agent thread (agent but not remote agent)
   */
  private findLatestLocalAgentThread(): string | undefined {
    const localAgentConversations = this._chatModel
      .orderedConversations()
      .filter(
        (conv) =>
          conv.extraData?.isAgentConversation === true &&
          !conv.extraData?.isRemoteAgentConversation &&
          conv.id !== NEW_AGENT_KEY,
      );
    return localAgentConversations.length > 0 ? localAgentConversations[0].id : undefined;
  }

  /**
   * Find the latest remote agent thread
   */
  private findLatestRemoteAgentThread(): string | undefined {
    // For remote agents, we should check if there's an active remote agent
    // or find the latest remote agent conversation
    if (this._remoteAgentsModel.currentAgent) {
      return this._remoteAgentsModel.currentAgent.remote_agent_id;
    }

    const remoteAgents = this._remoteAgentsModel.agentOverviews || [];
    return remoteAgents.length > 0 ? remoteAgents[0].remote_agent_id : undefined;
  }

  /**
   * Handles switching to chat mode, preferring the latest existing chat thread
   */
  handleSetToChat() {
    // Try to find the latest chat thread
    const latestChatThreadId = this.findLatestChatThread();

    if (latestChatThreadId) {
      this.switchToThread("chat", latestChatThreadId);
    } else {
      this.createNewChatThread();
    }
  }

  /**
   * Handles switching to agent mode, preferring the latest existing local agent thread
   */
  async handleSetToAgent(mode?: AgentExecutionMode) {
    // Try to find the latest local agent thread
    const latestAgentThreadId = this.findLatestLocalAgentThread();

    if (latestAgentThreadId) {
      this.switchToThread("localAgent", latestAgentThreadId, mode);
    } else {
      this.createNewLocalAgentThread(mode);
    }
  }

  /**
   * Handles switching to background agent mode, preferring the latest existing remote agent thread
   */
  handleSetToBackgroundAgent() {
    if (!this._remoteAgentsModel) {
      console.error("No remote agents model found");
      return;
    }

    // Try to find the latest remote agent thread
    const latestRemoteAgentThreadId = this.findLatestRemoteAgentThread();

    if (latestRemoteAgentThreadId) {
      this.switchToThread("remoteAgent", latestRemoteAgentThreadId);
    } else {
      this.createNewRemoteAgentThread();
    }
  }

  /**
   * Switches to a thread based on its type and ID.
   * This is a convenience method that handles switching between chat, local agent, and remote agent modes.
   *
   * @param threadType The type of thread to switch to ('chat', 'localAgent', or 'remoteAgent')
   * @param threadId The ID of the thread to switch to
   * @param agentExecutionMode The agent execution mode to use (only applicable for local agents)
   * @returns A boolean indicating whether the switch was successful
   */
  switchToThread(
    threadType: "chat" | "localAgent" | "remoteAgent",
    threadId: string,
    agentExecutionMode: AgentExecutionMode = AgentExecutionMode.manual,
  ): boolean {
    // Don't allow changing modes in a remote agent window unless it's to the current remote agent
    if (
      this._chatModel.flags.isRemoteAgentWindow &&
      (threadType !== "remoteAgent" ||
        (this._chatModel.flags.remoteAgentId && threadId !== this._chatModel.flags.remoteAgentId))
    ) {
      return false;
    }

    // Handle different thread types
    if (threadType === "remoteAgent") {
      // For remote agents, set the current agent and switch to remote agent mode
      if (threadId === NEW_AGENT_KEY) {
        this.setToRemoteAgent(null);
      } else {
        this.setToRemoteAgent(threadId);
      }
    } else {
      // For chat and local agent threads, first set the current conversation
      this._chatModel.setCurrentConversation(threadId, true, { noopIfSameConversation: true });

      // Then switch to the appropriate mode
      if (threadType === "chat") {
        // For chat threads, switch to chat mode
        this.setToChat();
      } else {
        // For local agent threads, switch to agent mode
        void this.setToAgent(agentExecutionMode);
      }
    }

    return true;
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.remoteAgentSelectAgentId: {
        this.setToRemoteAgent(msg.data.agentId);
        return true;
      }
    }
    return false;
  }
}
