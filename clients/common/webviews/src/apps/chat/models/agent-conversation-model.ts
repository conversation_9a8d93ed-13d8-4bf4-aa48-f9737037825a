import { isVSCodeHost } from "$common-webviews/src/common/hosts/vscode/vscode";
import { ChatMode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  AgentSessionEventName,
  FlushMemoriesData,
  FlushMemoriesDebugFlag,
  RememberToolCaller,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import throttle from "lodash/throttle";
import type { Readable, Writable } from "svelte/store";
import { derived, get, writable } from "svelte/store";
import {
  AgentExchangeStatus,
  ChatItemType,
  ExchangeStatus,
  type ExchangeWithStatus,
  isChatItemExchangeWithStatus,
  isChatItemSuccessfulExchange,
} from "../types/chat-message";
import {
  classifyAndDistillMemories,
  computeAgentExchangeStatus,
  currentAgenticTurnHasRemember,
  getLastUserExchange,
  getLastUserExchangeMemory,
  removeUserExchangeMemory,
} from "./agent-conversation-utils";
import { AgentExecutionMode, type ChatModel } from "./chat-model";
import type { CheckpointStore } from "./checkpoint-store";
import { ConversationModel, formatHistory } from "./conversation-model";
import { type ToolsWebviewModel } from "./tools-webview-model";

import { type IAgentConversation, type IConversation, isAgentConversation } from "./types";
import { NEW_AGENT_KEY } from "./agent-constants";

export interface LastConversationModel {
  /** If the previous conversation was in chat mode, this is the ID */
  chat: string | undefined;
  /** If the previous conversation was in agent mode, this is the ID */
  agent: string | undefined;
}

/**
 * AgentConversationModel manages the lifecycle and state of agent-based conversations.
 *
 * An agent conversation is a special type of chat that:
 * - Can make direct changes to the codebase
 * - Maintains state about pending/dirty edits
 * - Has special handling for conversation switching
 * - Can only have one active instance at a time
 *
 * Key features:
 * - Tracks whether the current conversation is agentic via a Svelte store
 * - Handles switching between agent and regular chat modes
 * - Manages dirty state when code edits are pending
 * - Enforces single active agent conversation rule
 * - Handles conversation switching with pending edits
 *
 * The model integrates with:
 * - ChatModel for conversation management
 * - SpecialContextInputModel for context control
 * - ExtensionClient for VS Code integration
 */
export class AgentConversationModel {
  private _agenticExchangeStatus: Writable<AgentExchangeStatus> = writable(
    AgentExchangeStatus.notRunning,
  );
  private _isCurrConversationAgentic = writable(false);
  private readonly _isAutoModeApproved = writable(false);
  private _showAutoModeWarning: Readable<boolean>;
  // Undefined signals the value has not been initialized yet
  private _hasEverUsedAgent: Writable<boolean | undefined> = writable(undefined);

  // Map to track which exchanges have had their memories classified
  private _classifiedMemoriesMap: Map<string, boolean> = new Map();

  private _disposers: Array<() => void> = [];
  public readonly isAgenticTurnComplete: Readable<boolean> = derived(
    this._agenticExchangeStatus,
    ($status) => {
      return $status === AgentExchangeStatus.notRunning;
    },
  );
  private _lastConversation: Writable<LastConversationModel> = writable({
    chat: undefined,
    agent: undefined,
  });

  /**
   * Creates a new AgentConversationModel instance.
   * Sets up event listeners for conversation changes and mode switches.
   *
   * @param _chatModel The ChatModel instance to integrate with
   */
  constructor(
    private _chatModel: ChatModel,
    private _toolsModel: ToolsWebviewModel,
    private _checkpointStore: CheckpointStore,
  ) {
    this._checkpointStore.registerAgentConversationModel(this);
    const hasAgentConversation = Object.values(this._chatModel.conversations).some(
      (conversation) => {
        return conversation.extraData?.isAgentConversation;
      },
    );
    const isCurrentConversationAgent =
      this._chatModel.currentConversationModel.extraData?.isAgentConversation === true;
    const hasUserUsedAgent = hasAgentConversation || isCurrentConversationAgent;

    if (hasUserUsedAgent) {
      this.setHasEverUsedAgent(true);
    }
    this.refreshAutoModeAcceptance();
    this.refreshHasEverUsedAgent();

    this._showAutoModeWarning = derived(
      [
        this._isAutoModeApproved,
        this._chatModel.agentExecutionMode,
        this._isCurrConversationAgentic,
      ],
      ([$isApproved, $agentExecutionMode, $isCurrConversationAgentic]) => {
        return (
          !$isApproved &&
          $agentExecutionMode === AgentExecutionMode.auto &&
          $isCurrConversationAgentic
        );
      },
    );

    // Store disposer for conversation change subscription
    this._disposers.push(
      this._chatModel.currentConversationModel.subscribe(() => {
        const conversation = this._chatModel.currentConversationModel;
        const isAgentic = isAgentConversation(conversation);
        this._isCurrConversationAgentic.set(isAgentic);
      }),
    );

    // Store disposer for agentic turn completion subscription
    // This is used to determine when to an agentic turn is complete, and by extension,
    // when we can trigger things like recording memories
    this._disposers.push(
      this._chatModel.currentConversationModel.subscribe(
        throttle(
          () => {
            const status = computeAgentExchangeStatus(this._chatModel.currentConversationModel);
            this._agenticExchangeStatus.set(status);
          },
          1000,
          { leading: true, trailing: true },
        ),
      ),
    );

    // Store disposer for chat mode changes subscription
    this._disposers.push(
      this._isCurrConversationAgentic.subscribe(($isAgentic) => {
        if ($isAgentic) {
          this._chatModel.extensionClient.setChatMode(ChatMode.agent);
          this._chatModel.specialContextInputModel.enableAgentMemories();
        } else {
          this._chatModel.extensionClient.setChatMode(ChatMode.chat);
          this._chatModel.specialContextInputModel.disableAgentMemories();
        }
        this._agenticExchangeStatus.set(AgentExchangeStatus.notRunning);
      }),
    );

    // Store disposer for before conversation change handler
    this._disposers.push(
      this._chatModel.currentConversationModel.onBeforeChangeConversation((current, next) => {
        // If the current conversation is not agentic, we don't need to do anything
        if (!isAgentConversation(current)) {
          return;
        }

        // If the next conversation is new (doesn't exist yet), we need to mark it as agentic
        // Also, if the next conversation is *explicitly* marked as non-agent conversation,
        // we should not mark it as agentic
        if (
          ConversationModel.isEmpty(next) &&
          next.extraData?.isAgentConversation !== false &&
          this._chatModel.conversations[next.id] === undefined
        ) {
          next = {
            ...next,
            extraData: { ...next.extraData, isAgentConversation: true },
          };
        }

        return next;
      }),
    );

    // Store disposer for conversation model subscription to detect first response token
    this._disposers.push(
      this._chatModel.currentConversationModel.subscribe((conversationModel) => {
        const lastExchange = conversationModel.lastExchange;
        const classifyOnFirstToken = this._chatModel.flags.memoryClassificationOnFirstToken;
        if (!lastExchange || !lastExchange.request_id || !classifyOnFirstToken) {
          return;
        }

        // Only process exchanges that:
        // 1. Have a status of "sent" (meaning they're receiving a response)
        // 2. Have at least some response text (indicating first token received)
        // 3. Haven't already been processed for memory classification
        if (
          lastExchange.status === ExchangeStatus.sent &&
          lastExchange.response_text &&
          lastExchange.response_text.length > 0 &&
          !this._classifiedMemoriesMap.has(lastExchange.request_id)
        ) {
          // Mark this exchange as processed
          this._classifiedMemoriesMap.set(lastExchange.request_id, true);

          // Now call the memory classification function
          void classifyAndDistillMemories(conversationModel, lastExchange);
        }

        // Clean up the map when an exchange completes or fails
        if (
          lastExchange.request_id &&
          (lastExchange.status === ExchangeStatus.success ||
            lastExchange.status === ExchangeStatus.failed ||
            lastExchange.status === ExchangeStatus.cancelled)
        ) {
          this._classifiedMemoriesMap.delete(lastExchange.request_id);
        }
      }),
    );

    this._disposers.push(
      this._chatModel.currentConversationModel.onSendExchange((exchange: ExchangeWithStatus) => {
        const classifyOnFirstToken = this._chatModel.flags.memoryClassificationOnFirstToken;
        if (classifyOnFirstToken) return;
        void classifyAndDistillMemories(this._chatModel.currentConversationModel, exchange);
      }),
    );

    // Store disposer for when a new conversation is created, we need to tell the extension
    // about it so it can correctly track all edits
    this._disposers.push(
      this._chatModel.currentConversationModel.onNewConversation(() => {
        const conversationId = this._chatModel.currentConversationModel.id;
        this._chatModel.extensionClient.setCurrentConversation(conversationId);

        // Create an original checkpoint if needed
        // The checkpoint store will handle all the necessary checks internally
        this._checkpointStore.maybeCreateOriginalCheckpoint();
      }),
    );
    this._chatModel.extensionClient.setCurrentConversation(
      this._chatModel.currentConversationModel.id,
    );

    // Store disposer for tool use state change handler
    this._disposers.push(
      this.isAgenticTurnComplete.subscribe(($isComplete) => {
        if (!$isComplete || !isAgentConversation(this._chatModel.currentConversationModel)) {
          return;
        }
        const currentConversation = this._chatModel.currentConversationModel;
        const memoryData = getLastUserExchangeMemory(currentConversation);
        if (!memoryData) {
          return;
        }

        const trace = FlushMemoriesData.create();
        trace.setFlag(FlushMemoriesDebugFlag.start);

        try {
          const lastExchangeRequestId = getLastUserExchange(currentConversation)?.request_id;
          if (lastExchangeRequestId) {
            trace.setRequestId(
              FlushMemoriesDebugFlag.lastUserExchangeRequestId,
              lastExchangeRequestId,
            );
          }
          const { memoriesRequestId, memory } = memoryData;
          trace.setRequestId(FlushMemoriesDebugFlag.memoriesRequestId, memoriesRequestId);
          if (!memory) {
            trace.setFlag(FlushMemoriesDebugFlag.emptyMemory);
            return;
          }
          if (currentAgenticTurnHasRemember(currentConversation)) {
            trace.setFlag(FlushMemoriesDebugFlag.agenticTurnHasRememberToolCall);
            return;
          }

          this._chatModel.extensionClient.callTool(
            currentConversation.lastExchange?.request_id ?? "",
            "remember",
            "remember",
            {
              memory,
              caller: RememberToolCaller.classify_and_distill,
              isComplexNewMemory: false,
              memoriesRequestId,
            },
            currentConversation.chatHistory
              .filter((m): m is ExchangeWithStatus => isChatItemSuccessfulExchange(m))
              .map(formatHistory),
            currentConversation.id,
          );
          if (lastExchangeRequestId) {
            // We don't need the memory node anymore, remove it
            const successRemove = removeUserExchangeMemory(
              currentConversation,
              lastExchangeRequestId,
            );
            trace.setFlag(FlushMemoriesDebugFlag.removeUserExchangeMemoryFailed, !successRemove);
          }
        } catch (e) {
          trace.setFlag(FlushMemoriesDebugFlag.exceptionThrown);
        } finally {
          trace.setFlag(FlushMemoriesDebugFlag.end);
          this._chatModel.extensionClient.reportAgentSessionEvent({
            eventName: AgentSessionEventName.flushMemories,
            conversationId: this._chatModel.currentConversationModel.id,
            eventData: {
              flushMemoriesData: trace,
            },
          });
        }
      }),
    );

    // When the agentic turn finishes, see if there have been any changes, and if so, create a checkpoint
    this._disposers.push(
      this.isAgenticTurnComplete.subscribe(async ($isComplete) => {
        if ($isComplete) {
          // Does the checkpoint representing the current conversation have any changes?
          const currCheckpoint = get(this._checkpointStore.currentCheckpoint);
          const currConversationId = get(this._chatModel.currentConversationModel).id;
          const hasChanges = await this._chatModel.extensionClient.hasChangesSince(
            currCheckpoint?.fromTimestamp ?? this.getBaselineTimestamp(),
          );

          // If conversation ID has changed in the meantime, don't create a checkpoint
          if (
            hasChanges &&
            currConversationId === get(this._chatModel.currentConversationModel).id
          ) {
            this._checkpointStore.createNewCheckpoint();
          }
        }
      }),
    );

    // When the tracked checkpoints changes change, mark the conversation as dirty
    this._disposers.push(
      this._checkpointStore.targetCheckpointSummary.subscribe(() => {
        // If there are any non-trivial changes
        if (get(this._checkpointStore.targetCheckpointHasChanges)) {
          this.markDirty();
        } else {
          this.markClean();
        }
      }),
    );
  }

  /**
   * Determines whether a new conversation should be created when switching modes.
   *
   * We should create a new conversation if the current conversation is not empty.
   *
   * @returns Whether a new conversation should be created when switching modes
   */
  private _shouldCreateNewConversation = (): boolean => {
    const conversation = this._chatModel.currentConversationModel;
    return !ConversationModel.isEmpty(conversation);
  };

  /**
   * Converts the current conversation to an agent conversation.
   * If an agent conversation already exists, switches to it instead.
   * Only one agent conversation can exist at a time.
   */
  public setToAgentic = async (isRemoteAgent: boolean = false): Promise<void> => {
    if (isAgentConversation(this._chatModel.currentConversationModel)) {
      return;
    }

    let conversationId: string;

    if (this._chatModel.flags.doUseNewDraftFunctionality) {
      conversationId = this._chatModel.currentConversationModel.id;
      const shouldCreateNew = !conversationId;

      if (shouldCreateNew) {
        // Create a new agent thread with the special key
        // This will be converted to a real conversation when the first message is sent
        this._chatModel.setCurrentConversation(NEW_AGENT_KEY);
        conversationId = NEW_AGENT_KEY;
      }

      const isCurrentlyAgentConversation = isAgentConversation(
        this._chatModel.currentConversationModel,
      );
      if (!isCurrentlyAgentConversation) {
        if (isRemoteAgent) {
          // there's a strange relationship between chat threads and remote agents at the moment
          // pick a random local agent conversation for now, to satisfy `isAgentConversation`
          // so remote agents work well
          // TODO: properly separate chat modes and remote agent logic
          const randomAgentConversation = this._chatModel
            .orderedConversations()
            .find(isAgentConversation);
          const lastAgentConversation = randomAgentConversation?.id;
          if (lastAgentConversation) {
            this._chatModel.setCurrentConversation(lastAgentConversation);
          } else {
            // create a new agent conversation
            await this._chatModel.setCurrentConversation();
            this._chatModel.currentConversationModel.extraData = {
              isAgentConversation: true,
              baselineTimestamp: 0,
            };
            conversationId = this._chatModel.currentConversationModel.id;
          }
        } else {
          this._chatModel.currentConversationModel.extraData = {
            isAgentConversation: true,
            baselineTimestamp: 0,
          };
        }
      }
    } else {
      const shouldCreateNew = this._shouldCreateNewConversation();
      const lastAgentConversation = get(this._lastConversation).agent;
      this._lastConversation.update(() => {
        // When switching to agent, the current conversation is chat mode
        return {
          chat: this._chatModel.currentConversationModel.id,
          agent: undefined,
        };
      });

      if (shouldCreateNew || !lastAgentConversation) {
        const newConversation = await this._create();
        newConversation.extraData = { isAgentConversation: true };
        // Since we are making a new empty agent conversation, we need to delete
        // any existing empty agent conversations
        this._chatModel.deleteInvalidConversations("agent");
        this._chatModel.currentConversationModel.setConversation(newConversation);
        conversationId = newConversation.id;
      } else {
        this._chatModel.setCurrentConversation(lastAgentConversation);
        conversationId = lastAgentConversation;
      }
    }

    this._chatModel.extensionClient.reportAgentSessionEvent({
      eventName: AgentSessionEventName.openedAgentConversation,
      conversationId,
    });

    const unsub = this._chatModel.currentConversationModel.subscribe(async (conversation) => {
      if (await this._maybeTriggerWelcomeMessage(conversation.id)) {
        // When message is sent, we immediately unsubscribe to avoid triggering the welcome message again
        unsub();
      }
    });
  };

  /**
   * Switches from agent mode back to regular chat mode.
   * Will switch to the most recent non-agent conversation if one exists,
   * otherwise creates a new regular chat conversation.
   */
  public setToChat = (): void => {
    // If currently not agentic, no-op
    if (!isAgentConversation(this._chatModel.currentConversationModel)) {
      return;
    }

    const shouldCreateNew = this._shouldCreateNewConversation();
    const lastChatConversation = get(this._lastConversation).chat;
    this._lastConversation.update(() => {
      // When switching to chat, the current conversation is agent mode
      return {
        chat: undefined,
        agent: this._chatModel.currentConversationModel.id,
      };
    });

    if (shouldCreateNew || !lastChatConversation) {
      if (this._chatModel.flags.doUseNewDraftFunctionality) {
        // Create a new agent thread with the special key
        // This will be converted to a real conversation when the first message is sent
        this._chatModel.setCurrentConversation(NEW_AGENT_KEY);

        (this._chatModel.currentConversationModel as IConversation).extraData = {
          isAgentConversation: false,
          baselineTimestamp: 0,
        };
      } else {
        // Create an explicitly non-agent conversation
        const newConversation = {
          ...ConversationModel.create(),
          extraData: { isAgentConversation: false },
        };

        // Since we are making a new empty chat conversation, we need to delete
        // any existing empty chat conversations
        this._chatModel.deleteInvalidConversations("chat");
        this._chatModel.currentConversationModel.setConversation(newConversation);
      }
    } else {
      this._chatModel.setCurrentConversation(lastChatConversation);
    }
  };

  private _maybeTriggerWelcomeMessage = async (conversationId: string): Promise<boolean> => {
    const currentConversation = this._chatModel.currentConversationModel;

    if (currentConversation.id !== conversationId) {
      return false;
    }
    await this.refreshHasEverUsedAgent();
    if (
      !ConversationModel.isEmpty(currentConversation) ||
      ConversationModel.isNamed(currentConversation) ||
      currentConversation.extraData?.hasAgentOnboarded ||
      get(this._hasEverUsedAgent)
    ) {
      return true;
    }

    // Set the hasEverUsedAgent state to true when the user switches to agentic mode
    this.setHasEverUsedAgent(true);

    currentConversation.setName("Welcome to the Augment Agent");
    currentConversation.extraData = { isAgentConversation: true, hasAgentOnboarded: true };

    // TODO: Move the onboarding prompt to sidecar
    if (!isVSCodeHost()) {
      return true;
    }

    // Get the onboarding prompt from the extension client
    const onboardingPrompt = await this._chatModel.extensionClient.getAgentOnboardingPrompt();

    await currentConversation.sendExchange({
      chatItemType: ChatItemType.agentOnboarding,
      // eslint-disable-next-line @typescript-eslint/naming-convention
      request_message: onboardingPrompt,
      status: ExchangeStatus.draft,
    });

    return true;
  };

  /**
   * Marks the current agent conversation as having pending/dirty edits.
   * This affects conversation switching behavior - users will be prompted
   * to accept changes before switching away.
   */
  public markDirty = () => {
    const conversationModel = this._chatModel.currentConversationModel;
    conversationModel.extraData = { ...conversationModel.extraData, hasDirtyEdits: true };
  };

  /**
   * Marks the current agent conversation as clean (no pending edits).
   * Called after edits are accepted or discarded.
   */
  public markClean = () => {
    const conversationModel = this._chatModel.currentConversationModel;
    conversationModel.extraData = { ...conversationModel.extraData, hasDirtyEdits: false };
  };

  /**
   * Finds an existing agent conversation if one exists.
   * Used to enforce the single agent conversation rule.
   *
   * @returns The existing agent conversation or undefined if none exists
   */
  private _findAgentic = (): IAgentConversation | undefined => {
    return this._chatModel.orderedConversations().find(isAgentConversation);
  };

  /**
   * Finds an existing chat conversation if one exists.
   *
   * @returns The existing chat conversation or undefined if none exists
   */
  private _findChat = (): IConversation | undefined => {
    return this._chatModel
      .orderedConversations()
      .find((conversation) => !isAgentConversation(conversation));
  };

  /**
   * Creates a new agent conversation with default settings.
   *
   * TODO(arun): does this method really need to exist?
   *
   * @returns A new IAgentConversation instance
   */
  private _create = async (): Promise<IAgentConversation> => {
    return {
      ...ConversationModel.create({
        personaType: await this._chatModel.currentConversationModel.decidePersonaType(),
      }),
      extraData: { isAgentConversation: true, baselineTimestamp: 0 },
    };
  };

  /**
   * Updates the baseline timestamp for this conversation.
   * Checkpoints before this timestamp are considered "accepted" and not shown in the agent edit list.
   *
   * @param timestamp - The new baseline timestamp
   */
  public updateBaselineTimestamp = (timestamp: number): void => {
    // If the timestamp is > current timestamp, cap it to the current timestamp
    const cappedTimestamp = Math.min(timestamp, Date.now());
    const conversation = this._chatModel.currentConversationModel;
    if (isAgentConversation(conversation)) {
      conversation.extraData = { ...conversation.extraData, baselineTimestamp: cappedTimestamp };
    }
  };

  /**
   * Gets the baseline timestamp for the current conversation.
   * If the conversation is an agent conversation, returns the baseline timestamp from extraData.
   * Otherwise, returns 0.
   *
   * @returns The baseline timestamp
   */
  public getBaselineTimestamp = (): number => {
    const conversation = this._chatModel.currentConversationModel;
    return isAgentConversation(conversation) ? (conversation.extraData.baselineTimestamp ?? 0) : 0;
  };

  /**
   * Gets the conversation ID for the current conversation.
   *
   * @returns The conversation ID
   */
  public getConversationId = (): string => {
    return this._chatModel.currentConversationModel.id;
  };

  /**
   * A readable Svelte store that indicates whether the current conversation
   * is an agent conversation.
   */
  public get isCurrConversationAgentic(): Readable<boolean> {
    return this._isCurrConversationAgentic;
  }

  /**
   * A readable Svelte store that indicates the current status of the agent exchange.
   */
  public get agentExchangeStatus(): Readable<AgentExchangeStatus> {
    return this._agenticExchangeStatus;
  }

  /**
   * Interrupts the agent by canceling any active tool runs and unfinished exchanges.
   * This is used when the user wants to either interrupt the agent's current task
   * or switch to a different conversation.
   */
  public interruptAgent = async () => {
    await this._toolsModel.interruptAllTools();
    await this._chatModel.currentConversationModel.cancelMessage();

    // Clear the classified memories map for the current conversation's last exchange
    const lastExchange = this._chatModel.currentConversationModel.lastExchange;
    if (lastExchange?.request_id) {
      this._classifiedMemoriesMap.delete(lastExchange.request_id);
    }
  };

  /**
   * Distills a memory from a specific message and saves it.
   * @param requestId The ID of the message to distill memory from
   */
  public async distillMemory(requestId: string): Promise<void> {
    const exchange = this._chatModel.currentConversationModel.chatHistory.find(
      (m): m is ExchangeWithStatus => {
        return isChatItemExchangeWithStatus(m) && m.request_id === requestId;
      },
    );
    if (!exchange?.request_message) {
      return;
    }

    // Send a silent exchange with the distill prompt
    const { responseText: rawResponse } =
      await this._chatModel.currentConversationModel.sendSilentExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: exchange.request_message,
        disableRetrieval: true,
        disableSelectedCodeDetails: true,
        memoriesInfo: {
          isDistill: true,
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      });

    // Parse response and save memory if valid
    try {
      const json = JSON.parse(rawResponse);
      if (json.content) {
        await this._chatModel.extensionClient.callTool(
          requestId,
          "remember",
          "remember",
          {
            memory: json.content,
            caller: RememberToolCaller.classify_and_distill,
            isComplexNewMemory: false,
          },
          this._chatModel.currentConversationModel.chatHistory
            .filter(isChatItemSuccessfulExchange)
            .map(formatHistory),
          this._chatModel.currentConversationModel.id,
        );
      }
    } catch (e) {
      console.warn("Failed to parse JSON from distill memory response", e);
    }
  }

  /**
   * Private helper to reset the agent store to default values
   * for developer debugging purposes
   */
  _resetStateToDefaultValues = () => {
    this.setHasEverUsedAgent(false);

    this._chatModel.extensionClient.setAgentAutoModeApproved(false);
    this._isAutoModeApproved.set(false);

    // Reset remote agent usage state (now managed by RemoteAgentsModel)
    this._chatModel.extensionClient.setHasEverUsedRemoteAgent(false);

    // Reset agent execution mode to manual
    this._chatModel.agentExecutionMode.set(AgentExecutionMode.manual);
  };

  /**
   * Resets the agent onboarding state, allowing the welcome message to be shown again.
   *
   * This will reset the hasEverUsedAgent state so that the welcome message is shown again
   * next time the user switches to agent mode.
   *
   * If the current conversation is an agent conversation, then the welcome message
   * is shown again.
   *
   * @returns Promise that resolves when the onboarding has been reset
   */
  public async resetOnboarding(): Promise<void> {
    this._resetStateToDefaultValues();

    const currentConversation = this._chatModel.currentConversationModel;
    if (!isAgentConversation(currentConversation)) {
      return;
    }

    // Clear the onboarding flag
    currentConversation.extraData = {
      ...currentConversation.extraData,
      hasAgentOnboarded: false,
    };

    // Trigger welcome message
    await this._maybeTriggerWelcomeMessage(currentConversation.id);
  }

  public get showAutoModeWarning(): Readable<boolean> {
    return this._showAutoModeWarning;
  }

  public acceptAutoMode = () => {
    this._chatModel.extensionClient.setAgentAutoModeApproved(true);
    this._isAutoModeApproved.set(true);
    this._chatModel.agentExecutionMode.set(AgentExecutionMode.auto);
  };

  public refreshAutoModeAcceptance = async () => {
    const isApproved = await this._chatModel.extensionClient.checkAgentAutoModeApproval();
    this._isAutoModeApproved.set(isApproved);
  };

  public rejectAutoMode = () => {
    this._chatModel.extensionClient.setAgentAutoModeApproved(false);
    this._isAutoModeApproved.set(false);
    this._chatModel.agentExecutionMode.set(AgentExecutionMode.manual);
  };

  public setHasEverUsedAgent = (hasUsed: boolean) => {
    this._chatModel.extensionClient.setHasEverUsedAgent(hasUsed);
    this._hasEverUsedAgent.set(hasUsed);
  };

  public get hasEverUsedAgent(): Readable<boolean | undefined> {
    return this._hasEverUsedAgent;
  }

  /**
   * Checks if the user has ever used the agent and updates the state.
   * This is useful when initializing the UI to show appropriate indicators.
   */
  public refreshHasEverUsedAgent = async () => {
    if (get(this.hasEverUsedAgent) !== undefined) {
      return;
    }
    const hasEverUsed = await this._chatModel.extensionClient.checkHasEverUsedAgent();
    if (get(this.hasEverUsedAgent) === undefined) {
      this._hasEverUsedAgent.set(hasEverUsed);
    }
  };



  /**
   * Cleans up all subscriptions and handlers
   */
  public dispose(): void {
    this._disposers.forEach((dispose) => dispose());
    this._disposers = [];

    // Clear the classified memories map
    this._classifiedMemoriesMap.clear();
  }
}
