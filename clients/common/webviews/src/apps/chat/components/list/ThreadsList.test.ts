import { describe, test, expect, vi } from "vitest";
import ThreadsList from "./ThreadsList.svelte";

// Mock the components that are used in ThreadsList
vi.mock("./NewThreadDropdown.svelte", () => ({
  default: {
    render: () => ({
      component: {
        $$: {
          callbacks: {},
        },
      },
    }),
  },
}));

vi.mock("./ThreadsListRowItem.svelte", () => ({
  default: {
    render: () => ({
      component: {
        $$: {
          callbacks: {},
        },
      },
    }),
  },
}));

vi.mock("./ThreadTypeFilterDropdown.svelte", () => ({
  default: {
    render: () => ({
      component: {
        $$: {
          callbacks: {},
        },
      },
    }),
  },
}));

// Mock the RemoteAgentsClient to avoid API calls
vi.mock("$common-webviews/src/apps/remote-agent-manager/models/remote-agents-client", () => {
  return {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    RemoteAgentsClient: vi.fn().mockImplementation(() => {
      return {
        getPinnedAgentsFromStore: vi.fn().mockResolvedValue({}),
        getRemoteAgentNotificationEnabled: vi.fn().mockResolvedValue({}),
        getRemoteAgentOverviews: vi.fn().mockResolvedValue({ overviews: [] }),
        loadPinnedAgentsFromStore: vi.fn().mockResolvedValue({}),
        updateChatConversations: vi.fn(),
        toggleThreadPinned: vi.fn(),
      };
    }),
  };
});

describe("ThreadsList.svelte", () => {
  test("component should exist", () => {
    expect(ThreadsList).toBeDefined();
  });
});
