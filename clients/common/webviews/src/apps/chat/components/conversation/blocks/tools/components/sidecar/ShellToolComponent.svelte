<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import ShellToolUseDetails from "./ShellToolUseDetails.svelte";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import TerminalIcon from "$common-webviews/src/design-system/icons/augment/terminal.svelte";
  import ToolUseHeader from "../ToolUseHeader.svelte";
  import { formatShellCommandLine } from "../tool-util";
  import { parseShellOutput } from "./shell-util";
  import ToolUseStatus from "../../ToolUseStatus.svelte";
  import ShellError from "./ShellError.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { type ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";

  export let toolUseState: ToolUseState;
  export let toolUseInput: Record<string, unknown> = {};
  export let toolUse: ChatResultToolUse;

  let code = formatShellCommandLine(toolUseInput);
  $: code = formatShellCommandLine(toolUseInput);
  let result = parseShellOutput(toolUseState.result?.text ?? "");
  $: result = parseShellOutput(toolUseState.result?.text ?? "");

  function makeName(toolName: string, phase: ToolUsePhase) {
    const isRunning = !(phase === ToolUsePhase.completed || phase === ToolUsePhase.error);
    switch (toolName) {
      case "launch-process":
        return isRunning ? "Launching Process..." : "Launched Process";
      case "kill-process":
        return isRunning ? "Killing Process..." : "Killed Process";
      case "read-process":
        return isRunning ? "Reading from Process..." : "Read from Process";
      case "write-process":
        return isRunning ? "Writing to Process..." : "Wrote to Process";
      case "list-processes":
        return isRunning ? "Listing Processes" : "Listed Processes";
      case "wait-process":
        return isRunning ? "Waiting for Process..." : "Waited for Process";
      case "read-terminal":
        return isRunning ? "Reading from Terminal..." : "Read from Terminal";
    }
    return "";
  }
</script>

<BaseToolComponent>
  <ToolUseHeader slot="header" toolName="Terminal">
    <TerminalIcon slot="icon" />
    <span slot="secondary" class="c-shell-tool-use-header__code-container">
      {#if code}
        <code class="c-shell-tool-use-header__code">
          $&nbsp;{code}
        </code>
      {:else}
        <TextAugment size={1} class="c-shell-tool-use-header__code"
          >{makeName(toolUse.tool_name, toolUseState.phase)}</TextAugment
        >
      {/if}
    </span>
    <ToolUseStatus
      slot="toolStatus"
      isCommandError={result.returnCode ? result.returnCode !== 0 : false}
    />
  </ToolUseHeader>

  <ShellToolUseDetails slot="details" {toolUseInput} {result} {toolUseState} />
  <ShellError
    slot="error"
    isError={result.returnCode ? false : toolUseState.phase === ToolUsePhase.error}
    text={toolUseState.result?.text}
  />
</BaseToolComponent>

<style>
  .c-shell-tool-use-header__code {
    line-clamp: 1;
    -webkit-line-clamp: 1;
    overflow: hidden;
    -webkit-box-orient: vertical;
    white-space: pre-wrap;
    display: -webkit-box;
    line-height: 1.3;
    background: transparent;
    font-family: var(--augment-monospace-font-family), monospace;
    font-size: 0.7rem;
    padding: 0;
    color: var(--augment-text-color-secondary);
  }
  .c-shell-tool-use-header__code-container {
    display: contents;
    & .c-shell-tool-use-header__code {
      display: inline;
      padding: var(--ds-spacing-1) 0;
    }
  }

  .c-shell-tool-use-header__code-container :global(.c-shell-tool-use-header__collapsible-text) {
    font-family: var(--augment-monospace-font-family), monospace;
    font-size: 0.7rem;
    color: var(--augment-text-color-secondary);
    line-height: 1.3;
  }
</style>
