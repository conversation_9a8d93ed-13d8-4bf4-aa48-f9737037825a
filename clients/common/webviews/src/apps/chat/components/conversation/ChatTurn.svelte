<script lang="ts">
  import {
    Exchange<PERSON>tatus,
    hasTool<PERSON><PERSON>ult,
    hasToolUse,
    isChatItemAgentOnboarding,
    type ExchangeWithStatus,
  } from "../../types/chat-message";

  import UserMessage from "./UserMessage.svelte";
  import AugmentMessage from "./AugmentMessage.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import ClipboardCopy from "$common-webviews/src/design-system/icons/clipboard-copy.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { getContext } from "svelte";
  import { RemoteAgentsModel } from "../../../remote-agent-manager/models/remote-agents-model";
  import { type GroupedChatItem } from "../../utils/message-list-context";

  export let chatModel: ChatModel;
  export let turn: ExchangeWithStatus;
  export let isLastTurn: boolean = false;
  export let turnIndex: number = 0;
  export let messageListContainer: HTMLElement | undefined = undefined;
  export let group: GroupedChatItem | undefined = undefined;

  // Get the remote agents model from context
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  $: requestId = turn.request_id;
  $: conversationModel = $chatModel?.currentConversationModel;

  // Check if this is a setup script agent
  $: isSetupScriptAgent =
    $remoteAgentsModel?.isActive &&
    $remoteAgentsModel?.currentAgent?.is_setup_script_agent === true;

  // Check if this is the first message (which would be "SETUP_MODE" for setup script agents)
  $: isFirstMessage = turnIndex === 0;

  // Determine if we should hide the user message and show the setup script info instead
  $: isFirstSetupScriptMessage = isSetupScriptAgent && isFirstMessage;

  // Callbacks
  const resendTurn = () => {
    $conversationModel.resendTurn(turn);
  };

  $: richTextJsonRepr = $chatModel.flags.enableRichTextHistory
    ? turn.rich_text_json_repr
    : undefined;
  $: turnHasToolResult = hasToolResult(turn);

  // Copy request ID functionality
  let tooltipTimer: ReturnType<typeof setTimeout> | undefined = undefined;
  let tooltipText = "Copy request ID";
  let requestClose: () => void = () => {};

  function onOpenChange(open: boolean) {
    if (!open) {
      clearTimeout(tooltipTimer);
      tooltipTimer = undefined;
      tooltipText = "Copy request ID";
    }
  }

  async function copyRequestId() {
    if (!requestId) {
      return;
    }
    await navigator.clipboard.writeText(requestId);
    tooltipText = "Copied!";
    clearTimeout(tooltipTimer);
    tooltipTimer = setTimeout(requestClose, 1500);
  }
</script>

<div class="c-msg-list__turn">
  {#if !turnHasToolResult && !isChatItemAgentOnboarding(turn) && !isFirstSetupScriptMessage}
    <UserMessage
      msg={turn.request_message ?? ""}
      {richTextJsonRepr}
      {chatModel}
      {requestId}
      timestamp={turn.timestamp}
    />
  {/if}
  <AugmentMessage
    chatModel={$chatModel}
    {turn}
    {turnIndex}
    {requestId}
    {isLastTurn}
    showName={!turnHasToolResult}
    {group}
    showFooter={isLastTurn || !hasToolUse(turn) || turn.status === ExchangeStatus.cancelled}
    markdown={turn.response_text ?? ""}
    timestamp={turn.timestamp}
    {messageListContainer}
  />
  {#if turn.status === ExchangeStatus.failed}
    <div class="c-msg-list__turn-response-failure">
      {#if turn.display_error_message}
        {turn.display_error_message}
      {:else if turn.response_text && turn.response_text.length > 0}
        Connection lost. Please
        <button on:click|preventDefault={resendTurn}>try again</button>
        to restart the conversation!
      {:else}
        We encountered an issue sending your message. Please
        <button on:click|preventDefault={resendTurn}>try again</button>
      {/if}
      {#if requestId}
        <div class="c-msg-list__request-id">
          <span>Request ID: {requestId}</span>
          <TextTooltipAugment
            bind:requestClose
            {onOpenChange}
            content={tooltipText}
            triggerOn={[TooltipTriggerOn.Hover]}
          >
            <IconButtonAugment variant="ghost" color="neutral" size={1} on:click={copyRequestId}>
              <ClipboardCopy />
            </IconButtonAugment>
          </TextTooltipAugment>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .c-msg-list__turn {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-msg-list__turn-response-failure {
    padding: var(--ds-spacing-4);
    font-style: italic;
  }
  .c-msg-list__turn-response-failure > button {
    all: unset;
    color: var(--augment-chat-message-note-color);
    text-decoration: underline;
    cursor: pointer;
  }

  .c-msg-list__request-id {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-top: var(--ds-spacing-2);
    font-size: 0.85em;
    color: var(--augment-chat-message-note-color);
  }
</style>
