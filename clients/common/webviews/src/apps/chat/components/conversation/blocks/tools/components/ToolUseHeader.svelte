<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import { getCollapsibleContext } from "$common-webviews/src/design-system/components/CollapsibleAugment/context";
  import { formatInputArgs } from "./tool-util";
  import { getToolUseContext } from "../tool-context";
  import CaretDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import ToolUseStatus from "../ToolUseStatus.svelte";
  import CircleColorFill from "$common-webviews/src/design-system/icons/augment/circle-color-fill.svelte";
  const ctx = getToolUseContext();
  export let toolName: string | undefined = undefined;
  export let toolUseInput: Record<string, unknown> = $ctx.toolUseInput;
  export let formattedToolArgs: string[] | undefined = undefined;
  let className: string = "";
  export { className as class };

  // If formattedToolArgs is provided, create a toolUseInput with args
  $: if (formattedToolArgs) {
    toolUseInput = { args: formattedToolArgs };
  }
  const collapseCtx = getCollapsibleContext();
  const collapsed = collapseCtx.collapsed;
  const expandable = collapseCtx.expandable;
</script>

<div
  class="c-tooluse__main-bar {className}"
  class:is-collapsed={$collapsed}
  class:is-expandable={$expandable}
>
  <div class="c-tooluse-header__label">
    <div class="c-tooluse__icon-wrapper c-carrot">
      {#if $expandable}
        <CaretDown />
      {:else}
        <span class="c-tooluse__icon-wrapper-circle">
          <CircleColorFill />
        </span>
      {/if}
    </div>
    {#if $$slots.icon}
      <div class="c-tooluse__icon-wrapper">
        <slot name="icon" />
      </div>
    {/if}

    <slot name="toolName">
      {#if toolName}
        <div class="c-tooluse__tool-name">
          <TextAugment size={2} weight="medium" class="c-tooluse__content-btn-name">
            <span class="c-tooluse__tool-name-text">{toolName}</span>
          </TextAugment>
        </div>
      {/if}
    </slot>
    <div class="c-tooluse__main-bar__tool-status">
      <slot name="toolAction"><span /></slot>
      <div class="c-tooluse__header-actions">
        <slot name="header-actions"></slot>
      </div>
      <slot name="toolStatus">
        <ToolUseStatus />
      </slot>
    </div>
  </div>
  {#if $$slots.secondary}
    <div class="c-tooluse__content-secondary">
      <slot name="secondary" {toolName} {formattedToolArgs} />
    </div>
  {:else if formatInputArgs(toolUseInput)}
    <div class="c-tooluse__content-secondary">
      <TextAugment size={1} class="c-tooluse__content-btn-args">
        {formatInputArgs(toolUseInput)}
      </TextAugment>
    </div>
  {/if}
</div>

<style>
  .c-tooluse__main-bar {
    max-width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    color: var(--augment-text-color);
    flex: 1;
    min-width: 0;
    /** rely on secondary spacing to make alignment work nicely*/
    gap: 0;
    justify-content: center;
    & .c-tooluse-header__label {
      max-width: 100%;
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-1);
      position: relative;
      min-width: 0;
    }
    & .c-tooluse__content-btn-name {
      display: inline-flex;
      align-items: center;
      height: 100%;
    }
    &.is_expandable .c-tooluse__content-btn,
    &.is_expandable .c-tooluse__content-btn:hover,
    &.is_expandable .c-tooluse__content-btn:focus,
    &.is_expandable .c-tooluse__content-btn:active {
      pointer-events: unset;
    }
    /* The content button should take up the remaining space */
    & .c-tooluse__content-btn,
    .c-tooluse__content-btn:hover,
    .c-tooluse__content-btn:focus,
    .c-tooluse__content-btn:active {
      pointer-events: none;
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      gap: var(--ds-spacing-1);
      color: var(--ds-color-neutral-12);
      background-color: transparent;
      border: none;
      padding: 0;
      cursor: default;
      outline: none;

      /* Needed to ensure the content button doesn't overflow */
      & .c-button--content {
        max-width: 100%;
        justify-content: flex-start;
        flex: 1;
      }

      & .c-tooluse__content-btn-args {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--augment-text-color-secondary);
      }
    }
    & .c-tooluse__main-bar__tooltip {
      display: flex;
      align-items: baseline;
      gap: var(--ds-spacing-1);
    }
    & .c-tooluse__content-secondary {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      color: var(--augment-text-color-secondary);
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      padding-left: calc(var(--ds-spacing-4) + var(--ds-spacing-1));
      padding-top: var(--ds-spacing-1);
    }
  }
  /* Needed to center icon vertically in the entire header */
  .c-tooluse__icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    --icon-size: var(--ds-icon-size-1);
    width: var(--ds-icon-size-1);
    height: var(--ds-icon-size-1);
    padding: 0;
  }

  .c-tooluse__icon-btn {
    &:hover {
      opacity: 0.5;
    }
  }
  .c-carrot {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    --icon-size: 10px;
    transform: rotate(0deg);
    transition: transform 0.2s ease;
  }
  .is-collapsed.is-expandable .c-carrot {
    transform: rotate(-90deg);
  }
  .c-tooluse__main-bar__tool-status {
    flex: 0 0 auto;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;
    align-items: center;
    flex-wrap: nowrap;
    gap: var(--ds-spacing-2);
    margin-left: auto;
  }

  .c-tooluse__header-actions {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    width: max-content;
  }

  .c-tooluse__tool-name {
    display: flex;
    align-items: center;
    height: 100%;
    min-width: 0;
    overflow: hidden;
    flex: 1;
    & .c-tooluse__content-btn-name {
      overflow: hidden;
      width: 100%;
      min-width: 0;
    }
    & .c-tooluse__tool-name-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: block;
      width: 100%;
    }
  }

  .c-tooluse__icon-wrapper-circle {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .c-tooluse__icon-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .c-tooluse__icon-slot :global(*) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .c-tooluse__icon-wrapper-circle.c-tooluse__icon-wrapper-circle.c-tooluse__icon-wrapper-circle
    > :global(svg) {
    width: 6px;
    height: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
