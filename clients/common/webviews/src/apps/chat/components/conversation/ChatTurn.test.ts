/* eslint-disable @typescript-eslint/naming-convention */
import { describe, test, expect } from "vitest";
import { ExchangeStatus, hasToolUse, type ExchangeWithStatus } from "../../types/chat-message";
import { ChatResultNodeType } from "@augment-internal/sidecar-libs/src/chat/chat-types";

describe("ChatTurn showFooter logic", () => {
  test("showFooter is true when turn is cancelled, even with tool uses", () => {
    const turnWithToolUse: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.cancelled,
      structured_output_nodes: [
        {
          id: 1,
          type: ChatResultNodeType.TOOL_USE,
          content: "",
          tool_use: {
            tool_use_id: "tool-1",
            tool_name: "test-tool",
            input_json: "{}",
          },
        },
      ],
    };

    // Test the logic that would be used in ChatTurn.svelte:
    // showFooter={!hasToolUse(turn) || turn.status === ExchangeStatus.cancelled}
    const showFooter =
      !hasToolUse(turnWithToolUse) || turnWithToolUse.status === ExchangeStatus.cancelled;

    // Should be true because turn.status === ExchangeStatus.cancelled
    expect(showFooter).toBe(true);
  });

  test("showFooter is false when turn has tool uses and is not cancelled", () => {
    const turnWithToolUse: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.success,
      structured_output_nodes: [
        {
          id: 1,
          type: ChatResultNodeType.TOOL_USE,
          content: "",
          tool_use: {
            tool_use_id: "tool-1",
            tool_name: "test-tool",
            input_json: "{}",
          },
        },
      ],
    };

    // Test the logic that would be used in ChatTurn.svelte:
    // showFooter={!hasToolUse(turn) || turn.status === ExchangeStatus.cancelled}
    const showFooter =
      !hasToolUse(turnWithToolUse) || turnWithToolUse.status === ExchangeStatus.cancelled;

    // Should be false because hasToolUse(turn) is true and turn.status is not cancelled
    expect(showFooter).toBe(false);
  });

  test("showFooter is true when turn has no tool uses", () => {
    const turnWithoutToolUse: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.success,
      structured_output_nodes: [],
    };

    // Test the logic that would be used in ChatTurn.svelte:
    // showFooter={!hasToolUse(turn) || turn.status === ExchangeStatus.cancelled}
    const showFooter =
      !hasToolUse(turnWithoutToolUse) || turnWithoutToolUse.status === ExchangeStatus.cancelled;

    // Should be true because !hasToolUse(turn) is true
    expect(showFooter).toBe(true);
  });

  test("showFooter is true when turn is cancelled and has no tool uses", () => {
    const turnCancelledNoTools: ExchangeWithStatus = {
      request_message: "Test message",
      response_text: "Test response",
      request_id: "test-123",
      status: ExchangeStatus.cancelled,
      structured_output_nodes: [],
    };

    // Test the logic that would be used in ChatTurn.svelte:
    // showFooter={!hasToolUse(turn) || turn.status === ExchangeStatus.cancelled}
    const showFooter =
      !hasToolUse(turnCancelledNoTools) || turnCancelledNoTools.status === ExchangeStatus.cancelled;

    // Should be true because both conditions are true
    expect(showFooter).toBe(true);
  });
});
