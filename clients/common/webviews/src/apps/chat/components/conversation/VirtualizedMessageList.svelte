<script lang="ts">
  import { getContext, onMount, onD<PERSON>roy, tick } from "svelte";
  import { derived } from "svelte/store";
  import AutofixStage from "./AutofixStage.svelte";
  import { ScrollVelocityTracker } from "../actions/scrollVelocityTracker";
  import { ScrollPositionManager } from "../actions/scrollPositionManager";
  import { calculateBatchSize, calculateLoadingThreshold } from "../../utils/scroll-utils";

  import { type ChatModel } from "../../models/chat-model";
  import { trackScrollBehavior } from "../actions/trackScrollBehavior";
  import {
    isChatItemExchangeWithStatus,
    isChatItemSignInWelcome,
    isChatItemSummaryResponse,
    SeenState,
    isChatItemEducateFeatures,
    type IChatItemSeenState,
    isIChatItemSeenState,
    isChatItemGenerateCommitMessage,
    isChatItemAutofixMessage,
    isChatItemAutofixStage,
    isChatItemAutofixSteeringMessage,
    isChatItemAgentOnboarding,
    isChatItemAgenticCheckpointDelimiter,
    ExchangeStatus,
  } from "../../types/chat-message";
  import { VirtualizedChatHistoryStore } from "../../stores/virtualized-chat-history-store";

  import ChatTurn from "./ChatTurn.svelte";
  import { trackHeight } from "../actions/trackHeight";
  import EducateFeaturesBlock from "./blocks/EducateFeaturesBlock.svelte";
  import MessageListItem from "./MessageListItem.svelte";
  import StreamingResponseBlock from "./blocks/StreamingResponseBlock.svelte";
  import SuggestedQuestions from "./blocks/SuggestedQuestions.svelte";
  import {
    type OnboardingWorkspaceModel,
    SUMMARY_PREAMBLE,
  } from "../../models/onboarding-workspace-model";
  import { trackSeen } from "../actions/trackSeen";

  import AugmentMessage from "./AugmentMessage.svelte";
  import UserMessage from "./UserMessage.svelte";
  import GeneratingResponse from "./status-decorations/GeneratingResponse.svelte";
  import RetryingResponse from "./status-decorations/RetryingResponse.svelte";
  import AwaitingUserInput from "./status-decorations/AwaitingUserInput.svelte";
  import { type AgentConversationModel } from "../../models/agent-conversation-model";
  import AggregateCheckpointVersion from "./checkpoints/AggregateCheckpointVersion.svelte";
  import Stopped from "./status-decorations/Stopped.svelte";

  import MessageListBottomButton from "./MessageListBottomButton.svelte";
  import CheckpointVersionList from "./checkpoints/CheckpointVersionList.svelte";
  import GradientMask from "$common-webviews/src/design-system/components/GradientMask.svelte";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getMessageListContext } from "../../utils/message-list-context";
  import AgentSetupLogs from "./AgentSetupLogs.svelte";

  export let chatModel: ChatModel;
  export let onboardingWorkspaceModel: OnboardingWorkspaceModel;
  export let msgListElement: HTMLElement | undefined = undefined;

  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const { agentExchangeStatus: ideAgentExchangeStatus, isCurrConversationAgentic } =
    agentConversationModel;
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  $: conversationModel = $chatModel.currentConversationModel;
  $: flagsModel = $chatModel.flags;
  $: messageListContext = getMessageListContext(
    $chatModel,
    $ideAgentExchangeStatus,
    $isCurrConversationAgentic,
    $remoteAgentsModel,
  );

  // Create the virtualized chat history store
  const virtualizedChatHistory = new VirtualizedChatHistoryStore(
    derived(chatModel, ($model: ChatModel) => $model.currentConversationModel),
    {
      initialVisibleCount: 20,
      batchSize: 10,
      minBatchSize: 5,
      maxBatchSize: 20,
    },
  );
  const { hasMoreBefore, isLoading, loadingDirection } = virtualizedChatHistory;

  // Create scroll velocity tracker
  const velocityTracker = new ScrollVelocityTracker(3);

  // Create scroll position manager
  const scrollPositionManager = new ScrollPositionManager();

  // Clean up the store when the component is destroyed
  onDestroy(() => {
    virtualizedChatHistory.dispose();
  });

  // For backward compatibility, we still need the full chat history for some operations
  // We use the context's chatHistory instead of filtering directly
  $: chatHistory = messageListContext.chatHistory;

  // Scroll behavior handlers. We use these to determine whether the user
  // controls scroll or Augment controls scroll, and whether we should
  // follow the bottom of the message list
  let userControlsScroll: boolean = false;
  function releaseControlToUser() {
    userControlsScroll = true;
  }

  // Handle loading more messages when scrolling near the top
  async function handleScrollNearTop() {
    if (!msgListElement || !$hasMoreBefore || $isLoading) return;
    releaseControlToUser();

    // Capture scroll position using distance from bottom
    const position = scrollPositionManager.captureScrollPosition(msgListElement);

    // Calculate batch size based on velocity
    const velocity = velocityTracker.getVelocityPPS();
    const batchSize = calculateBatchSize(velocity, virtualizedChatHistory.getCurrentBatchSize());

    // Load more items with the calculated batch size
    const moreLoaded = virtualizedChatHistory.loadMoreBefore(batchSize);

    // After the DOM updates, restore scroll position
    if (position) {
      // Ensure user controls scroll during position restoration
      releaseControlToUser();
      scrollPositionManager.restoreScrollPosition(msgListElement, position);
      await tick();
    }

    // Check if we're still at the top and need to load more
    if (moreLoaded && msgListElement && msgListElement.scrollTop <= 1 && $hasMoreBefore) {
      void handleScrollNearTop();
    }
  }

  onMount(() => {
    // If we have NOT seen the last exchange, the user has control of the scroll
    if ($conversationModel.lastExchange?.seen_state === SeenState.unseen) {
      releaseControlToUser();
    }
  });

  let containerHeight: number = 0;
  $: turnHeight = containerHeight;

  let isAtBottom = true;
  const onSeen = (turn: IChatItemSeenState) => $conversationModel.markSeen(turn);

  $: lastGroupConfig = messageListContext.lastGroupConfig;
  $: doShowFloatingButtons = messageListContext.doShowFloatingButtons;
  $: doShowAgentSetupLogs = messageListContext.doShowAgentSetupLogs;
</script>

<div
  class="c-msg-list-container"
  class:c-msg-list--minimal={!$flagsModel.fullFeatured}
  data-testid="chat-message-list"
>
  <GradientMask>
    <div
      class="c-msg-list"
      class:c-msg-list--minimal={!$flagsModel.fullFeatured}
      bind:this={msgListElement}
      use:trackScrollBehavior={{
        onScrollIntoBottom: () => {
          userControlsScroll = false;
          isAtBottom = true;
          // When scrolling to bottom, reset the virtualized view to show the most recent messages
          virtualizedChatHistory.resetToBottom();
        },
        onScrollAwayFromBottom: () => {
          releaseControlToUser();
          isAtBottom = false;
        },
        onScroll: (scrollTop) => {
          // Update UI state
          if (scrollTop <= 1) {
            releaseControlToUser();
          }
          // Track scroll velocity
          if (msgListElement) {
            velocityTracker.addSample(scrollTop);

            // Get current velocity data
            const velocity = velocityTracker.getVelocityPPS();
            const direction = velocityTracker.getDirection();

            // Calculate adaptive thresholds based on velocity
            const topThreshold = calculateLoadingThreshold(velocity, {
              baseThreshold: 200,
              predictTime: 300,
            });
            // Check if we need to load more messages at the top
            if (direction === "up" && scrollTop < topThreshold && $hasMoreBefore) {
              handleScrollNearTop();
            }
          }
        },
      }}
      use:trackHeight={{ onHeightChange: (height) => (containerHeight = height) }}
    >
      {#if doShowAgentSetupLogs}
        <AgentSetupLogs />
      {/if}

      {#if $isLoading && $loadingDirection === "before"}
        <div class="c-msg-list__loading">Loading earlier messages...</div>
      {/if}

      {#each $virtualizedChatHistory as group, groupIdx (group.firstRequestId ?? `no-request-id-${groupIdx}`)}
        <MessageListItem
          class="c-msg-list__item--grouped"
          {chatModel}
          isLastItem={group.isLastGroup}
          {userControlsScroll}
          requestId={group.firstRequestId}
          releaseScroll={() => (userControlsScroll = true)}
          messageListContainer={msgListElement}
          minHeight={group.isLastGroup ? turnHeight : 0}
          dataRequestId={group.firstRequestId}
        >
          {#each group.turns as { turn, idx } (turn.request_id ?? `no-request-id-${idx}`)}
            {@const isLastTurn = idx + 1 === chatHistory.length}
            {#if isChatItemSignInWelcome(turn)}
              <StreamingResponseBlock {turn} />
            {:else if isChatItemEducateFeatures(turn)}
              <EducateFeaturesBlock {flagsModel} {turn} />
            {:else if isChatItemSummaryResponse(turn)}
              <StreamingResponseBlock
                {turn}
                preamble={SUMMARY_PREAMBLE}
                resendTurn={() => onboardingWorkspaceModel.retryProjectSummary(turn)}
              >
                <SuggestedQuestions conversationModel={$conversationModel} {turn} />
              </StreamingResponseBlock>
            {:else if isChatItemAutofixMessage(turn)}
              <AugmentMessage
                group={group.turns}
                markdown={turn.response_text ?? ""}
                messageListContainer={msgListElement}
              />
            {:else if isChatItemAutofixSteeringMessage(turn)}
              <UserMessage {chatModel} msg={turn.response_text ?? ""} />
            {:else if isChatItemAutofixStage(turn)}
              <AutofixStage
                stage={turn.stage}
                iterationId={turn.iterationId}
                stageCount={turn.stageCount}
              />
            {:else if isChatItemExchangeWithStatus(turn) || isChatItemGenerateCommitMessage(turn) || isChatItemAgentOnboarding(turn)}
              <ChatTurn
                group={group.turns}
                {chatModel}
                {turn}
                turnIndex={idx}
                {isLastTurn}
                messageListContainer={msgListElement}
              />
            {:else if isChatItemAgenticCheckpointDelimiter(turn) && turn.status === ExchangeStatus.success}
              {#if $flagsModel.enableRichCheckpointInfo}
                <CheckpointVersionList {turn} />
              {:else}
                <AggregateCheckpointVersion {turn} />
              {/if}
            {/if}
            {#if isIChatItemSeenState(turn)}
              <!-- When this div enters the screen, the turn is considered "seen" -->
              <div
                class="c-msg-list__turn-seen"
                use:trackSeen={{
                  onSeen: () => onSeen(turn),
                  track: turn.seen_state !== SeenState.seen,
                }}
              ></div>
            {/if}
          {/each}
          {#if group.isLastGroup}
            {#if lastGroupConfig.retryMessage}
              <RetryingResponse message={lastGroupConfig.retryMessage} />
            {:else if lastGroupConfig.showGeneratingResponse}
              <GeneratingResponse />
            {:else if lastGroupConfig.showAwaitingUserInput}
              <AwaitingUserInput />
            {:else if lastGroupConfig.showRunningSpacer}
              <div class="c-agent-running-spacer"></div>
            {:else if lastGroupConfig.showStopped}
              <Stopped />
            {/if}
          {/if}
        </MessageListItem>
      {/each}

      {#if $isLoading && $loadingDirection === "after"}
        <div class="c-msg-list__loading">Loading more messages...</div>
      {/if}
    </div>
  </GradientMask>
  {#if doShowFloatingButtons}
    <MessageListBottomButton messageListElement={msgListElement} showScrollDown={!isAtBottom} />
  {/if}
</div>

<style>
  /* Outermost component for the message list */
  .c-msg-list-container {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: end;
    overflow: hidden;
    flex: 1;
    flex-basis: 0;
  }

  .c-msg-list-container :global(.c-chat-floating-container) {
    z-index: var(--z-conversation-scroll-btn);
    pointer-events: none;

    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: var(--ds-spacing-1);
  }

  .c-msg-list-container :global(.c-chat-floating-container .c-chat-floating-button) {
    pointer-events: auto;
  }

  .c-msg-list > :global(:first-child) {
    margin-top: var(--ds-spacing-2);
  }

  .c-msg-list {
    /* Allows for `offsetTop` to be relative to this */
    position: relative;
    height: 100%;
    width: 100%;
    padding-bottom: var(--ds-spacing-4); /* add extra padding for overscroll for mask effect */
    overflow-y: scroll;
    overflow-x: hidden;
    scrollbar-color: var(--augment-scrollbar-color) transparent;
  }

  .c-msg-list__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-3);
    color: var(--ds-color-text-secondary);
    font-size: var(--ds-font-size-sm);
    font-style: italic;
  }

  .c-agent-running-spacer {
    /* Make space for the Stop button */
    margin-bottom: var(--ds-spacing-8);
  }
</style>
