<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import type { parseShellOutput } from "./shell-util";
  import ShowMore from "$common-webviews/src/common/components/ShowMore.svelte";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";

  export let toolUseInput: Record<string, unknown>;
  export let result: ReturnType<typeof parseShellOutput>;
  export let toolUseState: ToolUseState;

  let entries: [string, string[]][] = [];
  if (typeof toolUseInput.command_type === "string") {
    if (
      toolUseInput.command_type === "simple" &&
      Array.isArray(toolUseInput.simple_command) &&
      toolUseInput.simple_command.length > 0
    ) {
      entries = [
        ["Command", [toolUseInput.simple_command[0]]],
        ["Args", toolUseInput.simple_command.slice(1)],
      ];
    } else if (
      toolUseInput.command_type === "complex" &&
      typeof toolUseInput.complex_command === "string"
    ) {
      entries = [["Command", [toolUseInput.complex_command]]];
    }
  } else if (typeof toolUseInput.command === "string") {
    entries = [["Command", [toolUseInput.command]]];
  }
</script>

<div class="c-shell-tooluse__details">
  {#each entries as [key, values]}
    <div class="c-shell-tooluse__details__group">
      <div class="c-shell-tooluse__details__heading">
        <TextAugment size={1} weight="medium">
          {key}
        </TextAugment>
        <CopyButton text={values.join("\n")} />
      </div>
      {#each values as value}
        <div>
          <ShowMore maxHeight={90}>
            <TextAugment size={1}>
              <pre class="c-shell-tooluse__value">$ {value}</pre>
            </TextAugment>
          </ShowMore>
        </div>
      {/each}
    </div>
  {/each}

  {#if result.output && toolUseState.phase !== ToolUsePhase.error}
    <div class="c-shell-tooluse__details__group">
      <div class="c-shell-tooluse__details__heading">
        <TextAugment size={1} weight="medium">Output</TextAugment>
        <CopyButton text={result.output} />
      </div>
      <div>
        <ShowMore maxHeight={90}>
          <TextAugment size={2} type="monospace">
            <pre class="c-shell-tooluse__value">{result.output?.trim()}</pre>
          </TextAugment>
        </ShowMore>
      </div>
    </div>
  {/if}
</div>

<style>
  :global(.c-shell-tooluse__details .c-copy-button) {
    visibility: hidden;
  }
  :global(.c-shell-tooluse__details:hover .c-copy-button) {
    visibility: unset;
  }
  .c-shell-tooluse__details {
    padding: var(--ds-spacing-2);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    border: 0;
    padding: var(--ds-spacing-2) 0 0;

    & .c-shell-tooluse__value,
    & .c-shell-tooluse__value pre & :global(.c-shell-tooluse__value),
    & :global(.c-shell-tooluse__value pre) {
      font-family: var(--augment-monospace-font-family, monospace);
      white-space: pre-wrap;
      word-break: break-word;
      gap: var(--ds-spacing-1);
      border: 0;
      padding: var(--ds-spacing-2) 0;
    }

    & .c-shell-tooluse__details__heading {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: var(--ds-spacing-1);
      flex: 1;
      padding: var(--ds-spacing-2) 0;
    }

    & .c-shell-tooluse__details__group {
      display: flex;
      flex-direction: column;
      gap: 0;
      border-radius: var(--ds-radius-2);
      background: var(--ds-color-neutral-a2);
      padding: 0 var(--ds-spacing-2);
      padding-left: var(--ds-spacing-4_5);
      display: flex;
      flex-direction: column;
      border-radius: var(--ds-radius-2);

      & pre {
        margin: 0;
        font-family: var(--augment-monospace-font-family, monospace);
        white-space: pre-wrap;
        word-break: break-word;
        gap: var(--ds-spacing-1);
        border: 0;
        padding: 0;
      }
    }
  }
</style>
