<script lang="ts">
  import { type IRemoteAgentsError } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-state-model/remote-agents-state-model";
  import { startCountdown } from "$common-webviews/src/apps/remote-agent-manager/utils";
  import { onDestroy, onMount } from "svelte";

  export let error: IRemoteAgentsError;
  export let onRetry: (() => void) | undefined = undefined;

  $: ({ retryAt, errorMessage } = error);
  $: showTimer = retryAt !== undefined;

  let displayTime: string | undefined = undefined;
  let stopCountdown: (() => void) | undefined;

  function restartCountdown() {
    if (stopCountdown) {
      stopCountdown();
      stopCountdown = undefined;
    }

    if (!retryAt) {
      return;
    }

    stopCountdown = startCountdown(retryAt, (countdown) => {
      displayTime = countdown;
    });
  }

  $: if (retryAt) {
    restartCountdown();
  }

  onMount(restartCountdown);
  onDestroy(() => {
    stopCountdown?.();
  });
</script>

<span class="c-remote-agent-error">
  {errorMessage}
  {#if showTimer && displayTime}
    Retrying in {displayTime}
  {/if}
  <button on:click|preventDefault={onRetry}>Retry now</button>
</span>

<style>
  .c-remote-agent-error {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
    opacity: 50%;
  }
</style>
