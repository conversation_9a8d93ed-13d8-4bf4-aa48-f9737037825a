<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  export let text: string | undefined = undefined;
  export let color: "accent" | "warning" | "error" = "accent";
</script>

<div class={`c-chat-input-banner c-chat-input-banner--${color}`}>
  <slot name="left-content">
    <TextAugment size={1} color="neutral">{text}</TextAugment>
  </slot>
  <slot name="right-content" />
</div>

<style>
  .c-chat-input-banner {
    background-color: var(--chat-input-banner-background-color);
    display: flex;
    gap: var(--ds-spacing-2);
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: var(--ds-spacing-1) var(--ds-spacing-2);
  }

  :global(.light) .c-chat-input-banner {
    opacity: 0.6;
  }

  .c-chat-input-banner--accent {
    --chat-input-banner-background-color: var(--ds-color-accent-a4);
  }

  .c-chat-input-banner--warning {
    --chat-input-banner-background-color: var(--ds-color-warning-a6);
  }

  .c-chat-input-banner--error {
    --chat-input-banner-background-color: var(--ds-color-error-a4);
  }
</style>
