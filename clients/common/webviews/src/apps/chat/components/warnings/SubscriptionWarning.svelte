<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import XMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
  import ChatInputBanner from "./ChatInputBanner.svelte";

  export let daysRemaining: number | null = null;
  export let isInactive: boolean = false;
  export let usageBalanceDepleted: boolean = false;
  export let onDismiss: () => void;

  function openSubscriptionPage() {
    host.postMessage({
      type: WebViewMessageType.augmentLink,
      data: "https://app.augmentcode.com/account",
    });
  }

  let warningText: string = "";

  $: {
    if (isInactive) {
      warningText = "Your subscription is no longer active";
    } else if (usageBalanceDepleted) {
      warningText = "You are out of user messages";
    } else {
      warningText = `Your subscription expires in ${daysRemaining} days`;
    }
  }

  $: isError = isInactive || usageBalanceDepleted;
</script>

<ChatInputBanner text={warningText} color={isError ? "error" : "warning"}>
  <div class="l-subscription-warning__right" slot="right-content">
    <div class="l-subscription-warning__upgrade-btn" class:is-error={isError}>
      <ButtonAugment
        size={1}
        variant="solid"
        color={isError ? "error" : "warning"}
        on:click={openSubscriptionPage}
        data-testid="subscription-page-button"
      >
        Upgrade Now
      </ButtonAugment>
    </div>
    {#if !isInactive && !usageBalanceDepleted}
      <TextTooltipAugment content="Dismiss" triggerOn={[TooltipTriggerOn.Hover]}>
        <IconButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          on:click={onDismiss}
          data-testid="dismiss-subscription-warning-button"
        >
          <XMark />
        </IconButtonAugment>
      </TextTooltipAugment>
    {/if}
  </div>
</ChatInputBanner>

<style>
  .l-subscription-warning__right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-subscription-warning__right :global(svg) {
    --icon-size: 14px;
  }

  /* Override button color to lighter warning color */
  .l-subscription-warning__upgrade-btn:not(.is-error) :global(.c-base-btn) {
    --base-btn-bg-color: var(--ds-color-warning-a8);
    --base-btn-hover-bg-color: var(--ds-color-warning-a9);
  }
</style>
