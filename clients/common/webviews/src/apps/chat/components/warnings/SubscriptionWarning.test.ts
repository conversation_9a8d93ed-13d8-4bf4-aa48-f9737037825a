import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/svelte";
import SubscriptionWarning from "./SubscriptionWarning.svelte";

describe("SubscriptionWarning", () => {
  it("renders the warning message for expiring subscription", () => {
    const mockOnDismiss = vi.fn();
    render(SubscriptionWarning, {
      daysRemaining: 7,
      usageBalanceDepleted: false,
      isInactive: false,
      onDismiss: mockOnDismiss,
    });

    expect(screen.getByText("Your subscription expires in 7 days")).toBeInTheDocument();
    expect(screen.getByTestId("subscription-page-button")).toBeInTheDocument();
    expect(screen.getByTestId("dismiss-subscription-warning-button")).toBeInTheDocument();
  });

  it("renders the warning message for inactive subscription", () => {
    const mockOnDismiss = vi.fn();
    render(SubscriptionWarning, {
      daysRemaining: null,
      usageBalanceDepleted: false,
      isInactive: true,
      onDismiss: mockOnDismiss,
    });

    expect(screen.getByText("Your subscription is no longer active")).toBeInTheDocument();
    expect(screen.getByTestId("subscription-page-button")).toBeInTheDocument();
    // Dismiss button should not be present for inactive subscriptions
    expect(screen.queryByTestId("dismiss-subscription-warning-button")).not.toBeInTheDocument();
  });

  it("renders the warning message for depleted usage", () => {
    const mockOnDismiss = vi.fn();
    render(SubscriptionWarning, {
      daysRemaining: 30,
      usageBalanceDepleted: true,
      isInactive: false,
      onDismiss: mockOnDismiss,
    });

    expect(screen.getByText("You are out of user messages")).toBeInTheDocument();
    expect(screen.getByTestId("subscription-page-button")).toBeInTheDocument();
    // Dismiss button should not be present for depleted usage
    expect(screen.queryByTestId("dismiss-subscription-warning-button")).not.toBeInTheDocument();
  });

  it("calls onDismiss when the dismiss button is clicked", async () => {
    const mockOnDismiss = vi.fn();
    render(SubscriptionWarning, {
      daysRemaining: 7,
      usageBalanceDepleted: false,
      isInactive: false,
      onDismiss: mockOnDismiss,
    });

    const dismissButton = screen.getByTestId("dismiss-subscription-warning-button");
    await fireEvent.click(dismissButton);

    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });

  it("has the correct CSS classes for warning state", () => {
    const mockOnDismiss = vi.fn();
    const { container } = render(SubscriptionWarning, {
      daysRemaining: 7,
      usageBalanceDepleted: false,
      isInactive: false,
      onDismiss: mockOnDismiss,
    });

    const bannerElement = container.querySelector(".c-chat-input-banner");
    expect(bannerElement).toHaveClass("c-chat-input-banner--warning");
    expect(bannerElement).not.toHaveClass("c-chat-input-banner--error");
  });

  it("has the correct CSS classes for error state", () => {
    const mockOnDismiss = vi.fn();
    const { container } = render(SubscriptionWarning, {
      daysRemaining: null,
      usageBalanceDepleted: false,
      isInactive: true,
      onDismiss: mockOnDismiss,
    });

    const bannerElement = container.querySelector(".c-chat-input-banner");
    expect(bannerElement).toHaveClass("c-chat-input-banner--error");
    expect(bannerElement).not.toHaveClass("c-chat-input-banner--warning");
  });
});
