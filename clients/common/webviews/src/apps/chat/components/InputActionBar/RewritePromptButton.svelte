<script lang="ts">
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { getContext } from "svelte";
  import { writable } from "svelte/store";
  import type { ChatModel } from "../../models/chat-model";
  import Sparkles from "$common-webviews/src/design-system/icons/sparkles.svelte";

  export let onRewrite: () => void;
  export let isDisabled: boolean = false;

  const chatModel = getContext<ChatModel>("chatModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }

  // We'll use chatModel.flags.enableDebugFeatures to conditionally show the button

  // Loading state for the spinner
  const isEnhancing = writable(false);

  function handleClick() {
    // Set loading state to true
    $isEnhancing = true;

    try {
      // Call the provided onRewrite function and handle as Promise
      Promise.resolve(onRewrite()).finally(() => {
        // Set loading state back to false when done
        $isEnhancing = false;
      });
    } catch (error) {
      // Set loading state back to false on error
      $isEnhancing = false;
    }
  }
</script>

{#if $chatModel.flags.enablePromptEnhancer}
  <div class="c-rewrite-prompt-button">
    <TextTooltipAugment content="Enhance Prompt">
      <IconButtonAugment
        size={1}
        variant="soft"
        color="neutral"
        disabled={isDisabled}
        on:click={handleClick}
      >
        {#if $isEnhancing}
          <SpinnerAugment size={1} useCurrentColor={true} />
        {:else}
          <Sparkles />
        {/if}
      </IconButtonAugment>
    </TextTooltipAugment>
  </div>
{/if}

<style>
  .c-rewrite-prompt-button {
    margin-right: var(--ds-spacing-1);
  }
</style>
