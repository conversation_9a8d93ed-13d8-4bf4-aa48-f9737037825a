<script lang="ts">
  export let command: string | undefined = undefined;
  export let symbol: string = "/";
</script>

<span class="c-input-action-bar-command">
  <span class="c-input-action-bar-command__symbol"><slot name="symbol">{symbol}</slot></span>
  {#if command}
    <span class="c-input-action-bar-command__command">{command}</span>
  {/if}
</span>

<style>
  .c-input-action-bar-command {
    display: contents;
    cursor: pointer;
  }
  .c-input-action-bar-command__command {
    display: none;
  }
  .c-input-action-bar-command__symbol {
    position: relative;
  }
  /**
   * Hide the command when the container is small
   * `c-input-action-bar` is the container name for the InputActionBar component
   */
  @container c-input-action-bar (min-width: 320px) {
    .c-input-action-bar-command__command {
      display: unset;
    }
  }
</style>
