<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import Paperclip from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paperclip.svg?component";
  import CommandContent from "./CommandContent.svelte";
  import { getRichTextEditorContext } from "$common-webviews/src/design-system/components/RichTextEditorAugment/context";

  export let accept = "*";
  export let disabled: boolean = false;
  export let onAddFiles = handleAddFiles;

  const fileId = `file-${Date.now()}-${Math.floor(Math.random() * 10_000)}`;
  const editorContext = getRichTextEditorContext();

  function handleAddFiles(files: File[]) {
    editorContext.commandManager?.chain()?.insertImagesIntoEditor?.(files);
  }
</script>

<ButtonAugment
  size={1}
  variant="ghost-block"
  color="neutral"
  {disabled}
  class="c-input-action-bar__add-file-btn"
>
  <label for={fileId} class="c-input-action-bar__label">
    <CommandContent>
      <span class="c-input-action-bar__icon" slot="symbol">
        <Paperclip />
      </span>
    </CommandContent>
  </label>
  <input
    type="file"
    hidden
    id={fileId}
    on:click={function () {
      this.value = null;
    }}
    on:input={function (e) {
      onAddFiles([...(e.currentTarget?.files ?? [])]);
    }}
    {accept}
  />
</ButtonAugment>

<style>
  .c-input-action-bar__label {
    display: contents;
    position: relative;

    --add-file-icon-size: 15px;
  }

  .c-input-action-bar__label :global(.c-input-action-bar-command__symbol),
  .c-input-action-bar__icon {
    height: var(--add-file-icon-size);
  }

  .c-input-action-bar__icon :global(svg) {
    width: var(--add-file-icon-size);
    height: var(--add-file-icon-size);
  }
</style>
