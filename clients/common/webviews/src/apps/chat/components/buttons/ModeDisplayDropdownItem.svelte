<script lang="ts">
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import ButtonChaserAugment from "$common-webviews/src/design-system/components/ButtonChaserAugment.svelte";
  import { type AgentExecutionMode } from "../../models/chat-model";
  import ModeDisplay from "./ModeDisplay.svelte";

  export let onSelect: (e: Event) => void;
  export let description: string = "";
  export let isConversationAgentic: boolean = false;
  export let agentExecutionMode: AgentExecutionMode | undefined = undefined;
  export let selected: boolean = false;
  export let isBackgroundAgent: boolean = false;
  export let willStartNewThread: boolean = false;
  export let shouldShowChaser: boolean = false;

  $: ({ class: restClassName = "", ...restProps } = $$restProps);
</script>

<div
  class="c-mode-display-item-container {restClassName}"
  class:c-mode-display-item--selected={selected}
  class:c-mode-display-item--has-chaser={shouldShowChaser}
>
  {#if shouldShowChaser}
    <ButtonChaserAugment startOnMount repeat="infinite" />
  {/if}
  <DropdownMenu.Item
    onSelect={(e) => {
      onSelect(e);
    }}
    {...restProps}
  >
    <div class="c-mode-display-item">
      <ModeDisplay
        {isConversationAgentic}
        {agentExecutionMode}
        {isBackgroundAgent}
        {willStartNewThread}
        {description}
        {selected}
      />
    </div>
  </DropdownMenu.Item>
</div>

<style>
  .c-mode-display-item-container,
  .c-mode-display-item,
  .c-mode-display-item-container :global(.l-tooltip-trigger),
  .c-mode-display-item-container :global(.c-base-btn) {
    width: 100%;
  }
  .c-mode-display-item--selected :global(.c-base-btn--ghost) {
    --base-btn-bg-color: var(--ds-color-a3);
  }

  /* For chaser effect */
  .c-mode-display-item--has-chaser {
    position: relative;
    overflow: hidden;
    z-index: 0;
  }

  .c-mode-display-item--has-chaser :global(.c-base-btn) {
    position: relative;
    z-index: 1;
  }
</style>
