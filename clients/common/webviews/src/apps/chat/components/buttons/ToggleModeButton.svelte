<script lang="ts">
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import KeyboardShortcutHint from "$common-webviews/src/common/components/KeyboardShortcutHint.svelte";
  import { KeyIcon, PlatformKeyIcon } from "$common-webviews/src/common/components/key-icons";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ToggleAugment from "$common-webviews/src/design-system/components/ToggleAugment.svelte";
  import { getContext } from "svelte";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import { ChatModeModel } from "../../models/chat-mode-model";
  import { AgentExecutionMode, type ChatModel } from "../../models/chat-model";
  import ModeDisplayDropdownItem from "./ModeDisplayDropdownItem.svelte";
  import ToggleModeButtonContent from "./ToggleModeButtonContent.svelte";

  export let chatModel = getContext<ChatModel>("chatModel");
  export let agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);

  $: enableBackgroundAgents = $chatModel.flags.enableBackgroundAgents;
  $: enableAgentAutoMode = $chatModel.flags.enableAgentAutoMode;
  $: isCurrConversationAgentic = agentConversationModel.isCurrConversationAgentic;
  $: agentExecutionMode = $chatModel.agentExecutionMode;
  $: hasEverUsedAgent = agentConversationModel.hasEverUsedAgent;
  $: hasEverUsedRemoteAgent = remoteAgentsModel?.hasEverUsedRemoteAgent;
  $: shouldShowChaser =
    $hasEverUsedAgent === false || (enableBackgroundAgents && $hasEverUsedRemoteAgent === false);

  let dropdownRoot: DropdownMenuRoot;

  let isDropdownOpen = false;
  $: showTooltip = $chatModel.flags.enableDebugFeatures;

  function onOpenChange(open: boolean) {
    isDropdownOpen = open;
  }

  function handleSetToChat() {
    chatModeModel.handleSetToChat();
    dropdownRoot?.requestClose();
  }

  async function handleSetToAgent(mode: AgentExecutionMode) {
    await chatModeModel.handleSetToAgent(mode);
    // Mark that the user has used the agent
    if ($hasEverUsedAgent === false) {
      agentConversationModel.setHasEverUsedAgent(true);
    }
    dropdownRoot?.requestClose();
  }

  function handleSetToBackgroundAgent() {
    chatModeModel.handleSetToBackgroundAgent();
    // Mark that the user has used a remote agent
    if ($hasEverUsedRemoteAgent === false) {
      remoteAgentsModel?.setHasEverUsedRemoteAgent(true);
    }
    dropdownRoot?.requestClose();
  }

  let currentMode = "chat";
  $: {
    if ($remoteAgentsModel.isActive) {
      currentMode = "remoteAgent";
    } else if ($isCurrConversationAgentic) {
      currentMode = $agentExecutionMode === AgentExecutionMode.auto ? "agentAuto" : "localAgent";
    } else {
      currentMode = "chat";
    }
  }

  // Determine if we should show the auto/manual toggle
  $: showAgentToggle =
    $isCurrConversationAgentic && !$remoteAgentsModel.isActive && enableAgentAutoMode;

  // Reactive variable for the toggle state (true = auto, false = manual)
  $: isAutoMode = $agentExecutionMode === AgentExecutionMode.auto;

  // Handle toggle change
  async function handleToggleChange() {
    const newMode = isAutoMode ? AgentExecutionMode.manual : AgentExecutionMode.auto;
    await chatModeModel.setToAgent(newMode);
  }
</script>

<div class="c-toggle-mode-button">
  {#if $chatModel.flags.isRemoteAgentWindow}
    <!-- In a remote agent window, just show the mode display without dropdown -->
    <TextTooltipAugment
      content="You can only interact with your remote agent in this window."
      triggerOn={[TooltipTriggerOn.Hover]}
      side="bottom"
    >
      <ToggleModeButtonContent
        shouldShowChaser={false}
        isDropdownOpen={false}
        isCurrConversationAgentic={true}
        agentExecutionMode={AgentExecutionMode.auto}
        isBackgroundAgent={true}
        disabled
        variant="ghost"
        color="accent"
      />
    </TextTooltipAugment>
  {:else}
    <!-- Normal dropdown menu for non-remote agent windows -->
    <DropdownMenu.Root bind:this={dropdownRoot} {onOpenChange} nested={false}>
      <DropdownMenu.Trigger
        on:click={(e) => {
          e.stopPropagation();
        }}
      >
        {#if showTooltip}
          <TextTooltipAugment triggerOn={[TooltipTriggerOn.Hover]} side="right" align="start">
            <div slot="content" class="c-toggle-mode-tooltip">
              <div>
                Toggle forward
                <KeyboardShortcutHint icons={[`${PlatformKeyIcon.cmdOrCtrl}`, "."]} />
              </div>
              <div>
                or backward
                <KeyboardShortcutHint
                  icons={[`${PlatformKeyIcon.cmdOrCtrl}`, `${KeyIcon.shift}`, "."]}
                />
              </div>
            </div>
            <ToggleModeButtonContent
              {shouldShowChaser}
              {isDropdownOpen}
              isCurrConversationAgentic={$isCurrConversationAgentic}
              agentExecutionMode={$agentExecutionMode}
              isBackgroundAgent={$remoteAgentsModel?.isActive}
              variant={shouldShowChaser ? "soft" : "ghost"}
              color="neutral"
            />
          </TextTooltipAugment>
        {:else}
          <ToggleModeButtonContent
            {shouldShowChaser}
            {isDropdownOpen}
            isCurrConversationAgentic={$isCurrConversationAgentic}
            agentExecutionMode={$agentExecutionMode}
            isBackgroundAgent={$remoteAgentsModel?.isActive}
            variant={shouldShowChaser ? "soft" : "ghost"}
            color="neutral"
          />
        {/if}
      </DropdownMenu.Trigger>
      <DropdownMenu.Content size={1} side="top" align="start">
        <div class="c-mode-display-dropdown">
          <ModeDisplayDropdownItem
            onSelect={() => handleSetToChat()}
            isConversationAgentic={false}
            description="Use chat for advice and general knowledge."
            selected={currentMode === "chat"}
          />
          <ModeDisplayDropdownItem
            onSelect={() => handleSetToAgent($agentExecutionMode)}
            isConversationAgentic={true}
            description="Work with an agent on guided tasks."
            selected={currentMode === "localAgent"}
            shouldShowChaser={$hasEverUsedAgent === false}
          />

          {#if enableBackgroundAgents}
            <DropdownMenu.Separator />

            <ModeDisplayDropdownItem
              onSelect={() => handleSetToBackgroundAgent()}
              description="Run an agent in the cloud to lighten your workload."
              isBackgroundAgent={true}
              selected={currentMode === "remoteAgent"}
              shouldShowChaser={$hasEverUsedRemoteAgent === false}
            />
          {/if}
        </div>
      </DropdownMenu.Content>
    </DropdownMenu.Root>

    <!-- Auto/Manual toggle for agent modes -->
    {#if showAgentToggle}
      <div class="c-toggle-mode-button__agent-toggle">
        <TextTooltipAugment
          content={isAutoMode ? "" : "Run tools automatically"}
          triggerOn={[TooltipTriggerOn.Hover]}
          side="bottom"
        >
          <ToggleAugment
            checked={isAutoMode}
            size={2}
            onText="Auto"
            offText="Auto"
            ariaLabel="Toggle between manual and auto agent mode"
            on:change={handleToggleChange}
          />
        </TextTooltipAugment>
      </div>
    {/if}
  {/if}
</div>

<style>
  .c-toggle-mode-button {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);

    & .c-toggle-mode-button__trigger-text {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
  }

  .c-toggle-mode-button__agent-toggle {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-toggle-mode-tooltip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: var(--ds-spacing-1);
  }
  .c-toggle-mode-tooltip :global(.c-keyboard-shortcut-hint__icon) {
    color: var(--gray-1);
    border-color: var(--gray-1);
  }
  .c-mode-display-dropdown {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
</style>
