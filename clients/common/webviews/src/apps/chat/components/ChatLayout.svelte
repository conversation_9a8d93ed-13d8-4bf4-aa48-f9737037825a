<script lang="ts">
  export let isNotFullHeight: boolean = false;
</script>

<div class="l-chat-wrapper">
  <slot name="header-area" />

  <div
    class="l-chat"
    class:l-chat--not-full-height={isNotFullHeight}
    data-testid="main-chat"
    data-vscode-context={JSON.stringify({ preventDefaultContextMenuItems: true })}
  >
    <div class="l-chat__floating">
      <slot name="floating-area" />
    </div>

    <div class="l-chat__msg_area">
      <div class="l-chat__msg_area_body">
        <slot name="message-area" />
      </div>
    </div>

    <div class="l-chat__actions_area">
      <slot name="actions-area" />
    </div>

    <div class="l-chat__input_area">
      <slot name="input-area" />
    </div>
  </div>

  <slot name="footer-area" />
</div>

<style>
  .l-chat-wrapper {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    overflow: hidden;
  }

  .l-chat {
    --chat-padding: var(--ds-spacing-3);
    padding: var(--chat-padding);
    --chat-inline-padding: var(--ds-spacing-2);
    display: grid;
    width: 100%;
    max-width: 100%;
    flex: 1;

    /* Setting --l-chat-height in the parent can override this */
    height: var(--l-chat-height, 100vh);
    margin: auto;

    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto auto;
    grid-template-areas:
      "floating-area"
      "message-area"
      "actions-area"
      "input-area";
    overflow: hidden;
  }

  .l-chat--not-full-height {
    height: var(--l-chat-height, auto);
    flex: 1;
    padding-bottom: 0;
  }

  .l-chat--not-full-height .l-chat__actions_area {
    padding-bottom: 0;
  }

  .l-chat__floating {
    z-index: var(--z-threads-menu-backdrop);
  }

  .l-chat__msg_area {
    grid-area: message-area;

    display: flex;
    flex-direction: column;

    /* Ensure contents doesn't exceed the available space */
    max-width: 100vw;
    overflow: hidden;
  }

  .l-chat__msg_area_body {
    /* Grow to fill available space */
    flex: 1;
    /* Do not exceed the available space */
    flex-basis: 0;
    overflow: hidden;

    display: contents;
  }

  .l-chat__actions_area {
    grid-area: actions-area;
    padding-bottom: var(--ds-spacing-2);
  }

  .l-chat__input_area {
    grid-area: input-area;
  }
</style>
