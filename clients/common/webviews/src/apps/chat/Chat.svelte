<script lang="ts">
  import "./chat.css";

  import { host } from "$common-webviews/src/common/hosts/host";
  import {
    MessageBroker,
    type MessageConsumer,
  } from "$common-webviews/src/common/utils/message-broker";
  import type RichTextEditorRoot from "$common-webviews/src/design-system/components/RichTextEditorAugment/Root.svelte";
  import {
    WebViewMessageType,
    type WebViewMessage,
  } from "$vscode/src/webview-providers/webview-messages";
  import { onMount, setContext } from "svelte";
  import { onKeyDown } from "./chat-keybindings";
  import { ActionsModel } from "./models/actions-model";
  import { ChatModel } from "./models/chat-model";
  import { SpecialContextInputModel } from "./models/context-model";
  import { OnboardingWorkspaceModel } from "./models/onboarding-workspace-model";
  import { ChatInputType, type IChatFlags, type IConversation } from "./models/types";

  import Card from "$common-webviews/src/common/components/Card.svelte";
  import { DerivedStateName } from "$vscode/src/main-panel/action-cards/types";
  import {
    SHARED_AGENT_STORE_CONTEXT_KEY,
    SHARED_AGENT_STORE_NAME,
    validateChatHomeState,
    type ChatHomeWebviewState,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";
  import { ModelRegistry } from "@augment-internal/sidecar-libs/src/chat/model-types";
  import { writable } from "svelte/store";
  import { SharedWebviewStoreModel } from "../../common/models/shared-webview-store-model";
  import { GitReferenceModel } from "../remote-agent-manager/models/git-reference-model";
  import {
    getMessageRenderOptions,
    SELECTED_TURN_INDEX_CONTEXT_KEY,
  } from "../remote-agent-manager/models/message-render-options";
  import { RemoteAgentDiffOpsModel } from "../remote-agent-manager/models/ra-diff-ops-model";
  import { RemoteAgentsModel } from "../remote-agent-manager/models/remote-agents-model";
  import { setChatModel } from "./chat-context";
  import ActionCard from "./components/action-cards/ActionCard.svelte";
  import AgentDiscoveryCard from "./components/action-cards/AgentDiscoveryCard.svelte";
  import ChatInput from "./components/ChatInput/ChatInput.svelte";
  import ChatLayout from "./components/ChatLayout.svelte";
  import CommandInputBar from "./components/CommandInputBar.svelte";
  import MessageSection from "./components/conversation/MessageSection.svelte";
  import ChatDivider from "./components/DraggableChatDivider.svelte";
  import ThreadsList from "./components/list/ThreadsList.svelte";
  import ThreadsMenu from "./components/menu/ThreadsMenu.svelte";
  import SetupScriptSaveBar from "./components/SetupScriptSaveBar.svelte";
  import SwappablePanel from "./components/SwappablePanel.svelte";
  import ThreadHeader from "./components/ThreadHeader.svelte";
  import UserSteeringInputBar from "./components/UserSteeringInputBar.svelte";
  import AgentAutoModeWarning from "./components/warnings/AgentAutoModeWarning.svelte";
  import SubscriptionWarning from "./components/warnings/SubscriptionWarning.svelte";
  import ThreadLengthWarning from "./components/warnings/ThreadLengthWarning.svelte";
  import { AgentConversationModel } from "./models/agent-conversation-model";
  import { AutofixConversationModel } from "./models/autofix-conversation-model";
  import { ChatModeModel } from "./models/chat-mode-model";
  import { CheckpointStore } from "./models/checkpoint-store";
  import { ConversationModel } from "./models/conversation-model";
  import { SlashCommandModel } from "./models/slash-command-model";
  import { SubscriptionModel, type SubscriptionInfo } from "./models/subscription-model";
  import { CurrentConversationTaskStore } from "./models/task-store";
  import { ToolsWebviewModel } from "./models/tools-webview-model";
  import {
    subscribeRemoteAgentsModelToSharedStore,
    subscribeSharedStoreToRemoteAgentsModel,
  } from "./utils/subscribe-store";
  import StatusBars from "./components/status-bar/StatusBars.svelte";
  import RemoteAgentFooter from "./components/RemoteAgentFooter.svelte";
  import { RemoteAgentStatus } from "$vscode/src/remote-agent-manager/types";
  import ClientAnnouncementCard from "./components/action-cards/ClientAnnouncementCard.svelte";

  const LONG_THREAD_CHARACTER_COUNT = 600_000;

  export let initialConversation: IConversation | undefined = undefined;
  export let initialFlags: Partial<IChatFlags> | undefined = undefined;
  export let initialSubscriptionInfo: SubscriptionInfo | undefined = undefined;
  export let initialSubscriptionDismissed: boolean | undefined = undefined;

  const msgBroker = new MessageBroker(host);
  let contextModel = new SpecialContextInputModel();
  msgBroker.registerConsumer(contextModel);
  let actionsModel = new ActionsModel();
  msgBroker.registerConsumer(actionsModel);
  let agentConversationModel: AgentConversationModel;
  let chatModel = new ChatModel(msgBroker, host, contextModel, {
    initialConversation,
    initialFlags,
    onLoaded: () => {
      if (!chatModel.flags.enableAgentMode) {
        agentConversationModel.setToChat();
      } else {
        const notPopulated = $actionsModel.actions.some((action) => {
          return action === DerivedStateName.workspaceNotPopulated;
        });
        const noConversations =
          Object.keys(chatModel.conversations).length <= 1 &&
          ConversationModel.isEmpty(chatModel.currentConversationModel);
        if (notPopulated && noConversations) {
          // If the workspace is not populated, we start in agent mode to kickstart the user
          void agentConversationModel.setToAgentic();
        }
      }
    },
  });
  let toolsWebviewModel = new ToolsWebviewModel(
    chatModel.currentConversationModel,
    chatModel.extensionClient,
    chatModel,
  );
  const checkpointStore = new CheckpointStore(
    chatModel.flags,
    chatModel.currentConversationModel,
    chatModel.extensionClient,
  );

  msgBroker.registerConsumer(checkpointStore);
  msgBroker.registerConsumer(chatModel);

  const autofixConversationModel = new AutofixConversationModel(
    chatModel.currentConversationModel,
    chatModel.extensionClient,
  );
  msgBroker.registerConsumer(autofixConversationModel);
  agentConversationModel = new AgentConversationModel(
    chatModel,
    toolsWebviewModel,
    checkpointStore,
  );
  const taskStore = new CurrentConversationTaskStore(
    chatModel,
    chatModel.extensionClient,
    chatModel.currentConversationModel,
    agentConversationModel,
  );
  msgBroker.registerConsumer(taskStore);
  const { showAutoModeWarning } = agentConversationModel;

  // Remote agents
  const remoteAgentsModel = new RemoteAgentsModel({
    msgBroker,
    isActive: false,
    flagsModel: chatModel.flags,
    host,
    chatModel,
  });
  const gitRefModel = new GitReferenceModel(msgBroker);
  const diffOperationsModel = new RemoteAgentDiffOpsModel(msgBroker);
  msgBroker.registerConsumer(remoteAgentsModel);
  setContext(RemoteAgentsModel.key, remoteAgentsModel);
  setContext(GitReferenceModel.key, gitRefModel);
  setContext(RemoteAgentDiffOpsModel.key, diffOperationsModel);
  setContext(CurrentConversationTaskStore.key, taskStore);

  const selectedTurnIndex = writable(-1);
  setContext(SELECTED_TURN_INDEX_CONTEXT_KEY, selectedTurnIndex);

  const initialSharedState: ChatHomeWebviewState = {
    agentOverviews: [],
    selectedAgentId: undefined,
    activeWebviews: ["chat"], // Start with chat as active
    pinnedAgents: {},
  };
  const sharedWebviewStore = new SharedWebviewStoreModel<ChatHomeWebviewState>(
    msgBroker,
    initialSharedState,
    validateChatHomeState,
    SHARED_AGENT_STORE_NAME,
  );
  msgBroker.registerConsumer(sharedWebviewStore);
  setContext(SHARED_AGENT_STORE_CONTEXT_KEY, sharedWebviewStore);

  // Create the ChatModeModel first
  const chatModeModel = new ChatModeModel(chatModel, agentConversationModel, remoteAgentsModel);
  msgBroker.registerConsumer(chatModeModel);
  setContext(ChatModeModel.key, chatModeModel);

  // Set the ChatModeModel in the ChatModel
  chatModel.setChatModeModel(chatModeModel);

  // Subscribe to remoteAgentsModel changes to update the shared store
  $: subscribeSharedStoreToRemoteAgentsModel(
    sharedWebviewStore,
    $remoteAgentsModel,
    initialSharedState,
  );
  let prevStoreState = sharedWebviewStore.state;
  // Subscribe to shared store changes to update the remote agents model
  $: subscribeRemoteAgentsModelToSharedStore(
    prevStoreState,
    $sharedWebviewStore,
    $remoteAgentsModel,
    chatModel,
    chatModeModel,
  );

  const slashCommandModel = new SlashCommandModel(chatModel, agentConversationModel);
  msgBroker.registerConsumer(slashCommandModel);

  // Create subscription model for subscription status
  const subscriptionModel = new SubscriptionModel(
    chatModel.extensionClient,
    initialSubscriptionInfo,
    initialSubscriptionDismissed,
  );

  // TODO (mattgauntseo): Move flags out of chat model
  const onboardingWorkspaceModel = new OnboardingWorkspaceModel(chatModel, chatModel.flags);
  msgBroker.registerConsumer(onboardingWorkspaceModel);

  const modelRegistry = new ModelRegistry();
  $: {
    modelRegistry.registerModels(
      Object.entries($chatModel.flags.modelRegistry || {}).map(([name, id]) => ({ name, id })),
    );
  }
  // Make the chat model available to subcomponents
  setChatModel(chatModel);
  setContext("autofixConversationModel", autofixConversationModel);
  setContext("agentConversationModel", agentConversationModel);
  setContext("toolsWebviewModel", toolsWebviewModel);
  setContext("checkpointStore", checkpointStore);
  setContext("slashCommandModel", slashCommandModel);
  setContext("modelRegistry", modelRegistry);
  setContext("subscriptionModel", subscriptionModel);

  // UI visibility flags
  $: isNotRemoteAgentWindow = !$chatModel.flags.isRemoteAgentWindow;
  $: shouldNewThreadListBeShown =
    $flagsModel.doUseNewDraftFunctionality || $remoteAgentsModel.isActive;
  $: shouldShowThreadsList =
    $flagsModel.fullFeatured && isNotRemoteAgentWindow && shouldNewThreadListBeShown;
  $: hasCurrentRemoteAgent = $remoteAgentsModel.isActive && $remoteAgentsModel.currentAgent;
  $: hasCurrentLocalThread =
    !$remoteAgentsModel.isActive &&
    $chatModel.currentConversationId &&
    !ConversationModel.isNew($chatModel.currentConversationModel);

  $: shouldShowThreadHeader =
    $flagsModel.doUseNewDraftFunctionality &&
    $flagsModel.fullFeatured &&
    (hasCurrentRemoteAgent || hasCurrentLocalThread);
  $: shouldShowThreadsMenu =
    !shouldNewThreadListBeShown && $flagsModel.fullFeatured && isNotRemoteAgentWindow;

  $: messageRenderOptions = getMessageRenderOptions(
    $remoteAgentsModel,
    $chatModel.flags.isRemoteAgentWindow,
  );

  $: shouldShowStatusBar =
    !$autofixConversationModel.isAutofixConversation() && !messageRenderOptions.doHideStatusBars;

  $: inputType = (() => {
    if (!$autofixConversationModel.isAutofixConversation()) {
      return ChatInputType.normal;
    }

    if ($autofixConversationModel.shouldShowAutofixCommandInput()) {
      return ChatInputType.autofixCommand;
    }

    return ChatInputType.autofixPrompt;
  })();
  $: isRemoteAgentStarting =
    $remoteAgentsModel.isCreatingAgent ||
    ($remoteAgentsModel.isActive &&
      $remoteAgentsModel.currentAgent?.status === RemoteAgentStatus.agentStarting);

  $: inputPlaceholder = (() => {
    if (isRemoteAgentStarting) {
      return "Remote Agent is starting...";
    }
    if ($remoteAgentsModel.isActive) {
      if ($remoteAgentsModel.currentAgentId) {
        return "Ask Remote Agent to edit code, run tests, create a pull request...";
      } else {
        return "Fix this bug, tackle this ticket, review comments in my pull request...";
      }
    }
    if ($isCurrConversationAgentic) {
      return "Ask or instruct Augment Agent";
    }
    return "Ask or instruct Augment";
  })();

  // Set up reactive models
  const flagsModel = $chatModel.flags;
  $: hasActions = $actionsModel.actions.length > 0;
  // Only show agent discovery card if all actions are complete to prevent duplicate actions
  $: showAgentDiscoveryCard = $actionsModel.actions.includes(DerivedStateName.allActionsComplete);
  const { isCurrConversationAgentic } = agentConversationModel;

  $: totalCharacters = $chatModel.currentConversationModel.totalCharactersStore;
  // Track if the long thread banner has been dismissed
  let isLongThreadBannerDismissed = false;
  $: shouldShowLongThreadBanner =
    !isLongThreadBannerDismissed && $totalCharacters > LONG_THREAD_CHARACTER_COUNT;

  // Subscription warning state
  const subscriptionInfo = subscriptionModel.info;
  const dismissed = subscriptionModel.dismissed;
  $: shouldShowSubscriptionWarning =
    $subscriptionInfo && !$dismissed
      ? subscriptionModel.shouldShowWarning($subscriptionInfo)
      : false;
  // Add ResetOnboardingHandler class before onMount
  class ResetOnboardingHandler implements MessageConsumer {
    constructor(private _agentConversationModel: AgentConversationModel) {}

    handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
      if (e.data.type === WebViewMessageType.resetAgentOnboarding) {
        void this._agentConversationModel.resetOnboarding();
        return true;
      }
      return false;
    }
  }

  // Register the handler with the message broker
  msgBroker.registerConsumer(new ResetOnboardingHandler(agentConversationModel));

  const handleIsRemoteAgentWindow = (
    isRemoteAgentWindow: boolean,
    remoteAgentId: string | undefined,
  ) => {
    if (isRemoteAgentWindow && remoteAgentId) {
      chatModeModel.setToRemoteAgent(remoteAgentId);
      remoteAgentsModel.setCurrentAgent(remoteAgentId);
      remoteAgentsModel.setIsActive(true);
    }
  };

  // If the flags change, handle the remote agent window state
  $: handleIsRemoteAgentWindow(
    $chatModel.flags.isRemoteAgentWindow,
    $chatModel.flags.remoteAgentId,
  );

  // ==== Lifecycle ====
  // When the element is first rendered and goes from undefined => an HTMLElement,
  // we need to focus it so that the user can start typing
  onMount(() => {
    // Check remote agent status
    void chatModel.checkRemoteAgentStatus();

    // If we're in a remote agent window, set the chat to remote agent mode
    if (chatModel.flags.isRemoteAgentWindow && chatModel.flags.remoteAgentId) {
      // Force the chat to remote agent mode
      chatModeModel.setToRemoteAgent(chatModel.flags.remoteAgentId);

      // Set the current agent to the remote agent ID
      remoteAgentsModel.setCurrentAgent(chatModel.flags.remoteAgentId);

      // Make sure the remote agents model is active
      remoteAgentsModel.setIsActive(true);
    }

    const disposables = [
      toolsWebviewModel.dispose,
      remoteAgentsModel.dispose,
      subscriptionModel.dispose,
    ];

    return () => {
      disposables.forEach((d) => d?.());
    };
  });

  let inputArea: HTMLElement;

  $: isTallInput = $remoteAgentsModel.isActive;
  $: isShrunk = $remoteAgentsModel.isActive && $remoteAgentsModel.currentAgentId;

  let richTextEditorRoot: RichTextEditorRoot | undefined = undefined;
  $: isNotFullHeight = $remoteAgentsModel.isActive;

  // Track rich text editor focus state for CSS class application
  // Ensure the isFocused is a function before calling it due to weird rendering behavior
  // when the richTextEditor is unrendered
  $: isRichTextEditorFocused = richTextEditorRoot?.isFocused?.() || writable(false);
  $: isInRemoteAgentSetupMode = $remoteAgentsModel.isActive && !$remoteAgentsModel.currentAgentId;
  $: shouldShowChatDivider =
    inputType !== ChatInputType.autofixCommand &&
    inputType !== ChatInputType.autofixPrompt &&
    !isInRemoteAgentSetupMode;

  $: hasChatInputBanner = shouldShowSubscriptionWarning || shouldShowLongThreadBanner;
</script>

<svelte:window
  on:message={msgBroker.onMessageFromExtension}
  on:beforeunload={() => {
    $chatModel.saveImmediate();
  }}
  on:keydown={(e) => onKeyDown(e, chatModel, remoteAgentsModel, chatModeModel, richTextEditorRoot)}
/>

<div class="chat">
  <ChatLayout {isNotFullHeight}>
    <!-- Header Area -->
    <svelte:fragment slot="header-area">
      <div class="l-chat-header">
        {#if shouldShowThreadsList}
          <ThreadsList chatModelProp={chatModel} />
        {/if}
        {#if shouldShowThreadHeader}
          <ThreadHeader />
        {/if}
      </div>
    </svelte:fragment>

    <svelte:fragment slot="floating-area">
      <div class="l-chat-top-bars">
        {#if shouldShowThreadsMenu}
          <ThreadsMenu {chatModel} />
        {/if}
      </div>
    </svelte:fragment>

    <!-- Message List -->
    <MessageSection
      {chatModel}
      {onboardingWorkspaceModel}
      {remoteAgentsModel}
      slot="message-area"
    />
    <svelte:fragment slot="actions-area">
      {#if shouldShowStatusBar}
        <div class="l-chat-actions-area">
          <StatusBars {contextModel} />
          <ClientAnnouncementCard />
          {#if $flagsModel.enableAgentMode && $isCurrConversationAgentic}
            {#if showAgentDiscoveryCard}
              <AgentDiscoveryCard />
            {/if}
            {#if $showAutoModeWarning}
              <AgentAutoModeWarning />
            {:else}
              <SwappablePanel />
            {/if}
          {/if}
          {#if hasActions}
            <ActionCard card={$actionsModel.currentCard} flagsModel={$flagsModel} />
          {/if}
        </div>
      {/if}
    </svelte:fragment>

    <!-- Divider Area -->
    <svelte:fragment slot="divider-area">
      {#if shouldShowChatDivider}
        <ChatDivider
          bind:inputArea
          on:dragEnd={() => {
            if (isShrunk) {
              // focus the input to keep it tall
              richTextEditorRoot?.requestFocus();
            }
          }}
        />
      {/if}
    </svelte:fragment>

    <!-- Input Area -->
    <svelte:fragment slot="input-area">
      {#if inputType === ChatInputType.autofixCommand}
        <Card borders={["top"]} style="padding-top: 0;" overflowVisible>
          <CommandInputBar />
        </Card>
      {:else if inputType === ChatInputType.autofixPrompt}
        <Card borders={["top"]} style="padding-top: 0;" overflowVisible>
          <UserSteeringInputBar />
        </Card>
      {:else if !isInRemoteAgentSetupMode}
        <div
          class="l-input-area-design-system"
          class:l-input-area-design-system--tall={isTallInput}
          class:l-input-area-design-system--shrunk={isShrunk}
          class:l-input-area-design-system--editor-focused={$isRichTextEditorFocused}
          class:l-input-area-design-system--has-banner={hasChatInputBanner}
          bind:this={inputArea}
        >
          <ChatInput
            editable={!$showAutoModeWarning}
            placeholder={inputPlaceholder}
            bind:richTextEditorRoot
          >
            <svelte:fragment slot="banner">
              {#if shouldShowSubscriptionWarning && $subscriptionInfo}
                <SubscriptionWarning
                  daysRemaining={$subscriptionInfo.daysRemaining ?? null}
                  usageBalanceDepleted={$subscriptionInfo.usageBalanceDepleted || false}
                  isInactive={$subscriptionInfo.type === "inactive_subscription"}
                  onDismiss={() => subscriptionModel.dismiss()}
                />
              {:else if shouldShowLongThreadBanner}
                <ThreadLengthWarning
                  onNewThreadClick={() => chatModel.setCurrentConversation()}
                  onDismiss={() => (isLongThreadBannerDismissed = true)}
                />
              {/if}
            </svelte:fragment>
          </ChatInput>
        </div>
      {/if}
    </svelte:fragment>

    <!-- Footer Area -->
    <svelte:fragment slot="footer-area">
      {#if $remoteAgentsModel.isActive && $remoteAgentsModel.currentAgent?.is_setup_script_agent && $remoteAgentsModel.currentConversation}
        <SetupScriptSaveBar />
      {/if}
      {#if $remoteAgentsModel.isActive && $remoteAgentsModel.currentAgent && !$remoteAgentsModel.currentAgent?.is_setup_script_agent}
        <div class="chat-footer">
          <RemoteAgentFooter />
        </div>
      {/if}
    </svelte:fragment>
  </ChatLayout>
</div>

<style>
  .chat {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }

  :global(.c-chat-floating-button) {
    flex: 0 1 auto;
    width: fit-content;
  }

  .l-input-area-design-system {
    min-height: 7.5lh;
    max-height: 50vh;
    height: 100%;

    max-width: 100vw;
    transition:
      min-height 0.1s ease,
      max-height 0.1s ease,
      height 0.1s ease;
  }
  :global(.l-input-area-design-system.is-dragging) {
    transition: none;
  }

  .l-input-area-design-system--tall {
    height: clamp(5vh, 9lh, 15vh);
    flex: none;
    min-height: 2lh;
  }

  /* Consolidated styles for shrunk and inactive input state */
  :global(
      .l-input-area-design-system--shrunk:not(.l-input-area-design-system--editor-focused):not(
          .is-dragging
        )
    ) {
    height: 4lh;
    min-height: 4lh;
    max-height: 0;
    height: 0;
    cursor: pointer;

    &.l-input-area-design-system--has-banner {
      /* Ensure the banner and chat input are visible when shrunk */
      height: fit-content;
      max-height: fit-content;
    }

    /* Hide rich text editor overflow */
    & .l-rich-text-editor-augment {
      overflow: hidden;
    }

    /* Hide actions other than toggle mode button */
    & .c-rewrite-prompt-button,
    & .c-notify-button,
    & .c-model-picker,
    & .c-action-bar-context {
      display: none;
    }

    /* Truncate placeholder text to one line with ellipsis */
    & .tiptap p.is-editor-empty:first-child::before {
      white-space: nowrap;
      position: absolute;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 20px); /* Leave some space for padding */
      width: 100%;
      display: inline-block;
      float: none;
      height: 1.3em;
    }
  }

  .l-chat-actions-area {
    display: contents;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
</style>
