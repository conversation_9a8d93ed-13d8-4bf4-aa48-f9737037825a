import { AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { type IDisposable, type IRange } from "monaco-editor";
import type * as MonacoEditor from "monaco-editor";
import debounce from "lodash.debounce";
import isEqual from "lodash.isequal";
import type { Readable } from "svelte/motion";
import { get as getFromStore, readonly, writable, type Writable } from "svelte/store";
import { getAllLeaves, type ICodeChunkLeaf, type IHydratedCodeChunk } from "../types/chunk";
import { host } from "$common-webviews/src/common/hosts/host";
import { ChatModel } from "../../chat/models/chat-model";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import type { SelectedCodeDetails } from "$vscode/src/utils/types";
import {
  DiffViewResolveType,
  type DiffViewLoadedMessage,
  WebViewMessageType,
  type DiffViewInitializeMessage,
  type DiffViewResolveMessage,
  type WebViewMessage,
  type EmptyMessage,
  type DisposeDiffViewMessage,
  type DiffViewWindowFocusChange,
  type DiffViewFetchPendingStream,
} from "$vscode/src/webview-providers/webview-messages";
import { renderOverlayWidget, renderViewZone } from "./monaco-render-utils";
import {
  type IDiffViewModel,
  type IActionsViewZoneProps,
  type IViewZoneProps,
  type IOverlayWidgetProps,
  type IInstructionsDrawerViewZoneProps,
  ViewZoneOrdinals,
  DiffViewMode,
} from "./types";
import { FocusModel } from "$common-webviews/src/common/models/focus-model";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import { SpecialContextInputModel } from "../../chat/models/context-model";
import { StreamContext, type DeltaDiff } from "./stream-model";
import { tick } from "svelte";

/**
 * The model for the diff view.
 *
 * TODO: Eric - update the model structure in a way that the models are better shared across the different webviews
 */
export class DiffViewModel implements Readable<IDiffViewModel>, IDisposable {
  private _asyncMsgSender: AsyncMsgSender;
  private _editor!: MonacoEditor.editor.IStandaloneDiffEditor;
  private _chatModel!: ChatModel;
  private _focusModel: FocusModel<ICodeChunkLeaf> = new FocusModel();
  private _hasScrolledOnInit: boolean = false;
  private _markHasScrolledOnInit = debounce(() => {
    this._hasScrolledOnInit = true;
  }, 200 /* ms */);
  private _resetScrollOnInit = () => {
    this._markHasScrolledOnInit.cancel();
    this._hasScrolledOnInit = false;
  };

  private _subscribers: Set<(diffViewModel: DiffViewModel) => void> = new Set();
  private _disposables: IDisposable[] = [];
  private _rootChunk: IHydratedCodeChunk | undefined;
  private _keybindings: Writable<{ [key: string]: string }> = writable({});
  private _requestId: Writable<string | undefined> = writable(undefined);
  public requestId: Readable<string | undefined> = this._requestId;
  private _disableResolution: Writable<boolean> = writable(false);
  public disableResolution: Readable<boolean> = readonly(this._disableResolution);
  private _disableApply: Writable<boolean> = writable(false);
  public disableApply: Readable<boolean> = readonly(this._disableApply);

  private _currStream: StreamContext | undefined;
  private _isLoadingDiffChunks: Writable<boolean> = writable(false);

  private _selectionLines: Writable<
    | {
        start: number;
        end: number;
      }
    | null
    | undefined
  > = writable(undefined);
  private _mode: Writable<DiffViewMode> = writable(DiffViewMode.edit);

  constructor(
    private _editorContainer: HTMLElement,
    monacoTheme: string,
    private _monaco: typeof MonacoEditor,
  ) {
    this._asyncMsgSender = new AsyncMsgSender(
      // _host can be a lazy host and postMessage can be overridden after this point
      (message) => host.postMessage(message),
    );

    // Initialize the editor using the monacoReady promise
    this.initializeEditor(monacoTheme);
  }

  private initializeEditor = (monacoTheme: string): void => {
    // Wait for Monaco to be ready using the promise
    this._editor = this._monaco.editor.createDiffEditor(this._editorContainer, {
      automaticLayout: true,
      theme: monacoTheme,
      readOnly: true,
      contextmenu: false,
      renderSideBySide: false,
      renderIndicators: true,
      renderMarginRevertIcon: false,
      originalEditable: false,
      diffCodeLens: false,
      renderOverviewRuler: false,
      ignoreTrimWhitespace: false,
      scrollBeyondLastLine: true,
      maxComputationTime: 0,
      minimap: {
        enabled: false,
      },
      padding: {
        top: 16,
      },
    });

    this._editor.getOriginalEditor().updateOptions({
      lineNumbers: "off",
    });

    this._chatModel = new ChatModel(new MessageBroker(host), host, new SpecialContextInputModel());

    // Register commands
    this._monaco.editor.registerCommand?.("acceptFocusedChunk", this.acceptFocusedChunk);
    this._monaco.editor.registerCommand?.("rejectFocusedChunk", this.rejectFocusedChunk);
    this._monaco.editor.registerCommand?.("acceptAllChunks", this.acceptAllChunks);
    this._monaco.editor.registerCommand?.("rejectAllChunks", this.rejectAllChunks);
    this._monaco.editor.registerCommand?.("focusNextChunk", this.focusNextChunk);
    this._monaco.editor.registerCommand?.("focusPrevChunk", this.focusPrevChunk);

    this._disposables.push(
      this._editor,
      this._editor.onDidUpdateDiff(this.onDidUpdateDiff),
      this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),
      { dispose: this._focusModel.subscribe((_) => this.notifySubscribers()) },
    );

    void this.initialize();
  };

  subscribe = (sub: (diffViewModel: DiffViewModel) => void): (() => void) => {
    this._subscribers.add(sub);
    sub(this);
    return () => {
      this._subscribers.delete(sub);
    };
  };

  public dispose = (): void => {
    this._editor.dispose();
    this._subscribers.clear();
    this._disposables.forEach((disposable) => disposable.dispose());
  };

  private notifySubscribers = (): void => {
    this._subscribers.forEach((sub) => sub(this));
  };

  private onDidUpdateDiff = (): void => {
    this.updateCodeChunk();

    // If we haven't scrolled yet and we have chunks, scroll to first chunk
    if (!this._hasScrolledOnInit && this.leaves?.length) {
      this._markHasScrolledOnInit();
      const firstChunk = this.leaves[0];
      this.revealChunk(firstChunk);
    }

    this.notifyDiffViewUpdated();
    this.notifySubscribers();
  };

  private onMouseMoveModified = (e: MonacoEditor.editor.IEditorMouseEvent): void => {
    // If there is no line number, or no leaves, we can't do anything
    if (e.target.position?.lineNumber === undefined || this.leaves === undefined) {
      return;
    }

    // Calculate the offset of the _editorContainer
    const editorContainerTop = this.editorOffset;

    // Find the range containing the current line
    const lineNumber = e.target.position?.lineNumber;
    for (let idx = 0; idx < this.leaves.length; idx++) {
      const leaf = this.leaves[idx];
      const modifiedStart = leaf.unitOfCodeWork.lineChanges?.lineChanges[0].modifiedStart;
      const modifiedEnd = leaf.unitOfCodeWork.lineChanges?.lineChanges[0].modifiedEnd;
      const originalStart = leaf.unitOfCodeWork.lineChanges?.lineChanges[0].originalStart;
      const originalEnd = leaf.unitOfCodeWork.lineChanges?.lineChanges[0].originalEnd;

      // If current chunk has no info, skip it
      if (
        modifiedStart === undefined ||
        modifiedEnd === undefined ||
        originalStart === undefined ||
        originalEnd === undefined
      ) {
        continue;
      }

      // If current chunk has only deletions, we need to check the position
      // of the mouse relative to the original editor. We get the top and bottom y positions
      // of the first and last lines of the chunk in the original editor. If the mouse is
      // between these two lines, we set the current chunk to this chunk
      if (modifiedStart === modifiedEnd && lineNumber === modifiedStart) {
        if (e.target.type === this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE) {
          const originalEditor = this._editor.getOriginalEditor();
          const lineHeight: number = originalEditor.getOption(
            this._monaco.editor.EditorOption.lineHeight,
          );

          // Get the scrolled positions
          const topVisiblePosition = originalEditor.getScrolledVisiblePosition({
            lineNumber: originalStart,
            column: 0,
          });
          const bottomVisiblePosition = originalEditor.getScrolledVisiblePosition({
            lineNumber: originalEnd + 1,
            column: 0,
          });
          if (topVisiblePosition === null || bottomVisiblePosition === null) {
            continue;
          }

          // Adjust the y positions by the _editorContainer's top offset
          const originalTopY = topVisiblePosition.top - lineHeight / 2 + editorContainerTop;
          const originalBottomY = bottomVisiblePosition.top - lineHeight / 2 + editorContainerTop;
          if (e.event.posy >= originalTopY && e.event.posy <= originalBottomY) {
            this.setCurrFocusedChunkIdx(idx, false);
            break;
          }
          break;
        }
        continue;
      }

      // If current chunk also has additions, we can rely on default calculation
      if (modifiedStart <= lineNumber && lineNumber < modifiedEnd) {
        this.setCurrFocusedChunkIdx(idx, false);
        break;
      }
    }
  };

  public get editorOffset(): number {
    const editorContainerRect = this._editorContainer.getBoundingClientRect();
    return editorContainerRect.top;
  }

  public get currFocusedChunkIdx(): number | undefined {
    return this._focusModel.focusedItemIdx;
  }

  public get selectionLines(): Writable<
    | {
        start: number;
        end: number;
      }
    | null
    | undefined
  > {
    return this._selectionLines;
  }

  public get mode(): Writable<DiffViewMode> {
    return this._mode;
  }

  public get keybindings(): Writable<{ [key: string]: string }> {
    return this._keybindings;
  }

  public getOriginalEditor(): MonacoEditor.editor.IStandaloneCodeEditor {
    return this._editor.getOriginalEditor();
  }

  public getModifiedEditor(): MonacoEditor.editor.IStandaloneCodeEditor {
    return this._editor.getModifiedEditor();
  }

  public get isLoading(): Readable<boolean> {
    return { subscribe: this._isLoadingDiffChunks.subscribe };
  }

  private setLoading(loading: boolean): void {
    this._isLoadingDiffChunks.set(loading);
  }

  public updateIsWebviewFocused = async (isFocused: boolean): Promise<void> => {
    await this._asyncMsgSender.send<DiffViewWindowFocusChange, EmptyMessage>({
      type: WebViewMessageType.diffViewWindowFocusChange,
      data: isFocused,
    });
  };

  public setCurrFocusedChunkIdx = (idx: number | undefined, reveal: boolean = true) => {
    if (this._focusModel.focusedItemIdx === idx) {
      return;
    }
    this._focusModel.setFocusIdx(idx);

    if (reveal) {
      this.revealCurrFocusedChunk();
    }
    this.notifySubscribers();
  };

  /**
   * A command for Monaco editor to reveal (scroll to) the currently focused chunk.
   * This method is typically used to ensure the focused chunk is visible in the editor.
   */
  public revealCurrFocusedChunk = () => {
    const currChunk = this._focusModel.focusedItem;
    if (!currChunk) {
      return;
    }
    this.revealChunk(currChunk);
  };

  /**
   * Reveals a specific chunk in the editor.
   * This method scrolls the editor to make the specified chunk visible,
   * positioning it near the top of the viewport.
   *
   * @param chunk - The code chunk to reveal in the editor.
   */
  private revealChunk = (chunk: ICodeChunkLeaf) => {
    const lineChange = chunk.unitOfCodeWork.lineChanges?.lineChanges[0];
    const startLine = lineChange?.modifiedStart;
    if (startLine !== undefined) {
      // Scroll to one line above the chunk to provide context
      this._editor.revealLineNearTop(startLine - 1);
    }
  };

  public renderCentralOverlayWidget = (element: HTMLElement) => {
    const getProps = (): IOverlayWidgetProps => {
      return {
        editor: this._editor,
        id: "central-overlay-widget",
      };
    };

    const baseAction = renderOverlayWidget(element, getProps(), { monaco: this._monaco });
    return {
      update: () => {
        baseAction.update(getProps());
      },
      destroy: baseAction.destroy,
    };
  };

  // A svelte action to render a view zone for the instructions drawer
  public renderInstructionsDrawerViewZone = (
    element: HTMLElement,
    initialProps: IInstructionsDrawerViewZoneProps,
  ) => {
    let hasInitFocused: boolean = false;
    let lastProps = initialProps;
    const autoFocus: boolean = initialProps.autoFocus ?? true;

    const tryFocusOnInit = (line: number) => {
      if (!autoFocus || hasInitFocused) {
        return;
      }
      this._editor.revealLineNearTop(line);
      hasInitFocused = true;
    };

    const wrapProps = (props: IInstructionsDrawerViewZoneProps): IViewZoneProps => {
      return {
        ...props,
        ordinal: ViewZoneOrdinals.instructionDrawer,
        editor: this._editor,
        afterLineNumber: props.line,
      };
    };

    const baseAction = renderViewZone(element, wrapProps(initialProps));

    // When diff view updates, try to focus the instructions drawer
    const disposers: IDisposable[] = [];
    if (autoFocus) {
      disposers.push(
        this._editor.onDidUpdateDiff(() => {
          tryFocusOnInit(lastProps.line);
        }),
      );
    }

    return {
      update: (newProps: Partial<IInstructionsDrawerViewZoneProps>) => {
        const fullNewProps = { ...lastProps, ...newProps };
        if (!isEqual(fullNewProps, lastProps)) {
          baseAction.update(wrapProps(fullNewProps));
          lastProps = fullNewProps;
          tryFocusOnInit(fullNewProps.line);
        }
      },
      destroy: () => {
        baseAction.destroy();
        disposers.forEach((d) => d.dispose());
      },
    };
  };

  // A svelte action
  public renderActionsViewZone = (element: HTMLElement, props: IActionsViewZoneProps) => {
    const wrapProps = (props: IActionsViewZoneProps): IViewZoneProps => {
      let modifiedStart: number | undefined;
      if (props.chunk) {
        modifiedStart = props.chunk.unitOfCodeWork.lineChanges?.lineChanges[0].modifiedStart;
      } else {
        modifiedStart = 1;
      }

      return {
        ...props,
        ordinal: ViewZoneOrdinals.chunkActionPanel,
        editor: this._editor,
        afterLineNumber: modifiedStart ? modifiedStart - 1 : undefined,
      };
    };

    const baseAction = renderViewZone(element, wrapProps(props));
    return {
      update: (newProps: IActionsViewZoneProps) => {
        baseAction.update(wrapProps(newProps));
      },
      destroy: baseAction.destroy,
    };
  };

  public acceptAllChunks = () => {
    if (!this.leaves) {
      return;
    }
    this.acceptChunks(this.leaves, true);
  };

  public rejectAllChunks = () => {
    if (!this.leaves) {
      return;
    }
    this.rejectChunks(this.leaves, true);
  };

  // A command for monaco to accept the focused chunk
  public acceptFocusedChunk = () => {
    const currChunk = this._focusModel.focusedItem;
    if (!currChunk) {
      return;
    }
    this.acceptChunk(currChunk);
  };

  // A command for monaco to reject the focused chunk
  public rejectFocusedChunk = () => {
    const currChunk = this._focusModel.focusedItem;
    if (!currChunk) {
      return;
    }
    this.rejectChunk(currChunk);
  };

  // A command for monaco to focus the next chunk
  public focusNextChunk = () => {
    this._focusModel.focusNext();
    this.revealCurrFocusedChunk();
  };

  // A command for monaco to focus the previous chunk
  public focusPrevChunk = () => {
    this._focusModel.focusPrev();
    this.revealCurrFocusedChunk();
  };

  private initialize = async (): Promise<void> => {
    const initMsg = await this._asyncMsgSender.send<
      DiffViewLoadedMessage,
      DiffViewInitializeMessage
    >(
      {
        type: WebViewMessageType.diffViewLoaded,
      },
      2000 /* 2 second timeout */,
    );

    this._resetScrollOnInit();

    const { file, instruction, keybindings, editable } = initMsg.data;
    this._editor.updateOptions({
      readOnly: !editable,
    });

    const currKeybindings = getFromStore(this._keybindings);
    this._keybindings.set(keybindings ?? currKeybindings);

    const selection = instruction?.selection;
    if (selection) {
      if (
        selection.start.line === selection.end.line &&
        selection.start.character === selection.end.character
      ) {
        this._mode.set(DiffViewMode.instruction);
      }

      // Check if the selection has never been set (undefined)
      // and only set it if it is. This is to prevent the selection
      // from coming back on subsequent re-initializations
      if (getFromStore(this.selectionLines) === undefined) {
        this._selectionLines.set({
          start: selection.start.line,
          end: selection.end.line,
        });
      }
    }

    this.updateModels(file.originalCode ?? "", file.modifiedCode ?? "", {
      rootPath: file.repoRoot,
      relPath: file.pathName,
    });
    this._currStream?.finish();
    this._currStream = undefined;
    this._disableResolution.set(!!initMsg.data.disableResolution);
    this._disableApply.set(!!initMsg.data.disableApply);

    // If we get a new base model, we should swap out the original that
    // the current stream is applying to for a new one
    await this._tryFetchStream();
    this._syncStreamToModels();
  };

  public disposeDiffViewPanel = async (): Promise<void> => {
    await this._asyncMsgSender.send<DisposeDiffViewMessage, EmptyMessage>({
      type: WebViewMessageType.disposeDiffView,
    });
  };

  private _tryFetchStream = async (): Promise<void> => {
    const stream = this._asyncMsgSender.stream<DiffViewFetchPendingStream, WebViewMessage>(
      { type: WebViewMessageType.diffViewFetchPendingStream },
      15000 /* 15 second timeout */,
      60000 /* 1 minute timeout */,
    );
    for await (const msg of stream) {
      switch (msg.type) {
        case WebViewMessageType.diffViewDiffStreamStarted: {
          this.setLoading(true);
          this._requestId.set(msg.data.requestId);
          const originalValue = this._editor.getOriginalEditor().getValue();
          this._currStream = new StreamContext(msg.data.streamId, originalValue, this._monaco);
          this._syncStreamToModels();
          break;
        }
        case WebViewMessageType.diffViewDiffStreamEnded: {
          // If the message comes in for what is NOT the current active stream, ignore it
          if (this._currStream?.id !== msg.data.streamId) {
            return;
          }

          this.setLoading(false);
          this._cleanupStream();
          break;
        }
        case WebViewMessageType.diffViewDiffStreamChunk: {
          // If the message comes in for what is NOT the current active stream, ignore it
          if (this._currStream?.id !== msg.data.streamId) {
            return;
          }

          const originalModel = this._editor.getOriginalEditor().getModel();
          const modifiedModel = this._editor.getModifiedEditor().getModel();

          // If there is no model for some reason, we need to kill the stream.
          if (!modifiedModel || !originalModel) {
            this.setLoading(false);
            this._cleanupStream();
            return;
          }

          const deltaDiff = this._currStream?.onReceiveChunk(msg);
          if (deltaDiff) {
            this._applyDeltaDiff(deltaDiff);

            const selection = getFromStore(this._selectionLines);
            if (selection != null) {
              this._selectionLines.set(null);
            }
          }
          break;
        }
      }
    }
  };

  handleMessageFromExtension = async (e: MessageEvent<WebViewMessage>) => {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.diffViewNotifyReinit: {
        this.setLoading(false);
        this._cleanupStream();
        this.initialize();
        break;
      }
      case WebViewMessageType.diffViewAcceptAllChunks: {
        this.acceptAllChunks();
        break;
      }
      case WebViewMessageType.diffViewAcceptFocusedChunk: {
        this.acceptFocusedChunk();
        break;
      }
      case WebViewMessageType.diffViewRejectFocusedChunk: {
        this.rejectFocusedChunk();
        break;
      }
      case WebViewMessageType.diffViewFocusPrevChunk: {
        this.focusPrevChunk();
        break;
      }
      case WebViewMessageType.diffViewFocusNextChunk: {
        this.focusNextChunk();
        break;
      }
    }
  };

  private _applyDeltaDiff = (deltaDiff: DeltaDiff) => {
    const originalModel = this._editor.getOriginalEditor().getModel();
    const modifiedModel = this._editor.getModifiedEditor().getModel();
    if (!originalModel || !modifiedModel) {
      return;
    }
    originalModel.pushEditOperations([], deltaDiff.resetOriginal, () => []);
    deltaDiff.original.forEach((e: MonacoEditor.editor.IIdentifiedSingleEditOperation) => {
      originalModel.pushEditOperations([], [e], () => []);
    });
    deltaDiff.modified.forEach((e: MonacoEditor.editor.IIdentifiedSingleEditOperation) => {
      modifiedModel.pushEditOperations([], [e], () => []);
    });
  };

  private _cleanupStream = () => {
    if (this._currStream) {
      const deltaDiff = this._currStream?.finish();
      this._applyDeltaDiff(deltaDiff);
      this._currStream = undefined;

      // Prepare to scroll, now that the stream is done, we can scroll again
      this._resetScrollOnInit();
    }
  };

  private _syncStreamToModels = () => {
    const originalValue = this._currStream?.originalValue;
    const modifiedValue = this._currStream?.modifiedValue;
    if (originalValue && originalValue !== this._editor.getOriginalEditor().getValue()) {
      this._editor.getOriginalEditor().setValue(originalValue);
    }
    if (modifiedValue && modifiedValue !== this._editor.getModifiedEditor().getValue()) {
      this._editor.getModifiedEditor().setValue(modifiedValue);
    }
  };

  /**
   * Takes the diff specified by `chunk` and applies it to the editor such that
   * `originalCode` has the changes applied to it from `modifiedCode`.
   *
   * @param chunk: ICodeChunkLeaf
   * @returns
   */
  acceptChunk = async (chunk: ICodeChunkLeaf) => {
    if (getFromStore(this._disableApply)) {
      return;
    }
    this.acceptChunks([chunk]);
  };

  /**
   * Takes the diff specified by `chunk` and applies it to the editor such that
   * `originalCode` has the changes applied to it from `modifiedCode`.
   *
   * @param chunk: ICodeChunkLeaf
   * @returns
   */
  acceptChunks = async (chunks: ICodeChunkLeaf[], shouldApplyToAll = false) => {
    if (getFromStore(this._disableApply)) {
      return;
    }
    this.executeDiffChunks(chunks, true /* apply */);
    this.notifyResolvedChunks(chunks, DiffViewResolveType.accept, shouldApplyToAll);

    await tick();
    // If the models are equal now and we are not loading, we can close the diff view
    if (this.areModelsEqual() && !getFromStore(this.isLoading)) {
      this.disposeDiffViewPanel();
    }
  };

  areModelsEqual = (): boolean => {
    const originalModel = this._editor.getModel()?.original;
    const modifiedModel = this._editor.getModel()?.modified;
    return originalModel?.getValue() === modifiedModel?.getValue();
  };

  rejectChunk = async (chunk: ICodeChunkLeaf) => {
    this.rejectChunks([chunk]);
  };

  /**
   * Rejects the given chunks and reverts modified code back to the original.
   *
   * @param chunk: ICodeChunkLeaf
   */
  rejectChunks = async (chunks: ICodeChunkLeaf[], shouldApplyToAll = false) => {
    this.executeDiffChunks(chunks, false /* reject */);
    this.notifyResolvedChunks(chunks, DiffViewResolveType.reject, shouldApplyToAll);
    await tick();
    // If the models are equal now and we are not loading, we can close the diff view
    if (this.areModelsEqual() && !getFromStore(this.isLoading)) {
      this.disposeDiffViewPanel();
    }
  };

  // This always returns the *logical* original code. This is not necessarily the same as the
  // editor's original editor code, because during a stream we will modify the original editor
  // to make rendering smoother
  private get _originalCode(): string {
    return this._currStream?.originalCode ?? this._editor.getOriginalEditor().getValue();
  }

  private get _modifiedCode(): string {
    return this._editor.getModifiedEditor().getValue();
  }

  /**
   * Notifies the extension that the diff has been updated.
   */
  private notifyDiffViewUpdated = debounce(() => {
    void this.notifyResolvedChunks([], DiffViewResolveType.accept);
  }, 1000 /* ms */);

  private notifyResolvedChunks = async (
    chunks: ICodeChunkLeaf[],
    type: DiffViewResolveType,
    shouldApplyToAll = false,
  ): Promise<void> => {
    // We must be operating on a value file path in order to commit changes
    const pathName = this._editor.getModel()?.original.uri.path;
    if (!pathName) {
      return;
    }
    await this._asyncMsgSender.send<DiffViewResolveMessage, EmptyMessage>(
      {
        type: WebViewMessageType.diffViewResolveChunk,
        data: {
          file: {
            repoRoot: "",
            pathName,
            originalCode: this._originalCode,
            modifiedCode: this._modifiedCode,
          },
          changes: chunks.map((chunk) => chunk.unitOfCodeWork),
          resolveType: type,
          shouldApplyToAll,
        },
      },
      2000 /* 2 second timeout */,
    );
  };

  /**
   * Actually perform the diffing of the chunks.
   * If `apply` is true, we apply the diff. If `apply` is false, we revert the diff.
   *
   * @param chunks: chunks to apply
   * @param apply: whether to apply or revert the diff
   * @returns
   */
  private executeDiffChunks = (chunks: ICodeChunkLeaf[], apply: boolean): void => {
    if (getFromStore(this._disableResolution)) {
      return;
    } else if (apply && getFromStore(this._disableApply)) {
      return;
    }

    const originalModel = this._editor.getModel()?.original;
    const modifiedModel = this._editor.getModel()?.modified;
    // If we are currently streaming we cannot apply diff chunks
    if (!originalModel || !modifiedModel || this._currStream !== undefined) {
      return;
    }

    const originalEditOps: MonacoEditor.editor.IIdentifiedSingleEditOperation[] = [];
    const modifiedEditOps: MonacoEditor.editor.IIdentifiedSingleEditOperation[] = [];
    // Assemble all the changes from all the chunks
    for (const chunk of chunks) {
      // If this chunk has no tracked changes, there is nothing to do
      const lineChange = chunk.unitOfCodeWork.lineChanges?.lineChanges[0];
      if (
        !lineChange ||
        chunk.unitOfCodeWork.originalCode === undefined ||
        chunk.unitOfCodeWork.modifiedCode === undefined
      ) {
        continue;
      }

      let originalRange: IRange = {
        startLineNumber: lineChange.originalStart,
        startColumn: 1,
        endLineNumber: lineChange.originalEnd,
        endColumn: 1,
      };
      let modifiedRange: IRange = {
        startLineNumber: lineChange.modifiedStart,
        startColumn: 1,
        endLineNumber: lineChange.modifiedEnd,
        endColumn: 1,
      };

      const code = apply ? chunk.unitOfCodeWork.modifiedCode : chunk.unitOfCodeWork.originalCode;
      if (code === undefined) {
        continue;
      }
      originalEditOps.push({ range: originalRange, text: code });
      modifiedEditOps.push({ range: modifiedRange, text: code });
    }
    originalModel.pushEditOperations([], originalEditOps, () => []);
    modifiedModel.pushEditOperations([], modifiedEditOps, () => []);

    // If the next idx is this one, we want to go backwards
    const nextIdx = this._focusModel.nextIdx({ nowrap: true });
    if (nextIdx === undefined) {
      return;
    }

    // If the next idx is the same as the current one, we want to go backwards
    const scrollToIdx = nextIdx === this._focusModel.focusedItemIdx ? nextIdx - 1 : nextIdx;
    const scrollToChunk: ICodeChunkLeaf | undefined = this._focusModel.items[scrollToIdx];
    if (scrollToChunk) {
      this.revealChunk(scrollToChunk);
    }
  };

  public get leaves(): ICodeChunkLeaf[] | undefined {
    const codeChunk = this.codeChunk;
    if (!codeChunk) {
      return undefined;
    }
    return getAllLeaves(codeChunk);
  }

  public get codeChunk(): IHydratedCodeChunk | undefined {
    return this._rootChunk;
  }

  /**
   * Gets triggered to update the root code chunk whenever we deem it necessary.
   * Usually this is when the diff view has been updated and calls `onDidUpdateDiff`
   */
  private updateCodeChunk = () => {
    this._rootChunk = this.computeCodeChunk();
    this._focusModel.setItems(this.leaves ?? []);
    this._focusModel.initFocusIdx(0);

    this.notifySubscribers();
  };

  /**
   * Computes the code chunk tree given a diff.
   *
   * This allows us to visualize the diff as a tree of chunks.
   * We can do different things for each node of the tree, in terms of actions, etc.
   *
   * We do this by utilizing Monaco's built-in diff computation with `getLineChanges`.
   * Once we have this information, we generate the tree.
   */
  private computeCodeChunk(): IHydratedCodeChunk | undefined {
    const leaves: IHydratedCodeChunk[] = [];
    const lineChanges = this._editor.getLineChanges();
    const originalModel = this._editor.getModel()?.original;
    const modifiedModel = this._editor.getModel()?.modified;
    if (!lineChanges || !originalModel || !modifiedModel) {
      return undefined;
    }

    for (const lineChange of lineChanges) {
      const currOriginalRange: IRange = normalizeRange({
        startLineNumber: lineChange.originalStartLineNumber,
        startColumn: 1,
        endLineNumber: lineChange.originalEndLineNumber,
        endColumn: 1,
      });
      const currModifiedRange: IRange = normalizeRange({
        startLineNumber: lineChange.modifiedStartLineNumber,
        startColumn: 1,
        endLineNumber: lineChange.modifiedEndLineNumber,
        endColumn: 1,
      });

      const modifiedLeaf = createModifiedLeaf(this._editor, currOriginalRange, currModifiedRange);
      leaves.push(modifiedLeaf);
    }
    return {
      id: crypto.randomUUID(),
      name: "",
      title: "",
      description: "",
      generationSource: "",
      supportedActions: [],
      children: leaves,
      childIds: leaves.map((leaf) => leaf.id),
    };
  }

  public handleInstructionSubmit = (instruction: string) => {
    const modifiedEditor = this._editor.getModifiedEditor();
    const selectedCodeDetails = this.getSelectedCodeDetails(modifiedEditor);

    if (!selectedCodeDetails) {
      throw Error("No selected code details found");
    }

    this._chatModel.currentConversationModel.sendInstructionExchange(
      instruction,
      selectedCodeDetails,
    );
  };

  /**
   * Updates the monaco models of the diff view editor
   *
   * @param originalCode: original code
   * @param modifiedCode: modified code
   * @param path
   */
  private updateModels = (
    originalCode: string,
    modifiedCode: string | undefined,
    path?: IQualifiedPathName,
  ): void => {
    // Maybe get new URI. If no new URI, attempt to use existing URI
    const currUri = this._editor.getModel()?.original?.uri;
    const uri = (path && this._monaco.Uri.file(path.relPath)) ?? currUri;
    if (!uri) {
      console.warn("No URI found for diff view. Not updating models.");
      return;
    }

    // If the URI is different, we need to create a new URI and new models
    if (currUri?.fsPath !== uri.fsPath || currUri?.authority !== uri.authority) {
      const originalUri = uri.with({ fragment: crypto.randomUUID() });
      const modifiedUri = uri.with({ fragment: crypto.randomUUID() });

      this._editor.setModel({
        original: this._monaco.editor.createModel(originalCode, undefined, originalUri),
        modified: this._monaco.editor.createModel(modifiedCode ?? "", undefined, modifiedUri),
      });
    } else {
      // Check if the values have changed. If so, update them. Otherwise, don't do anything
      if (this._originalCode !== originalCode) {
        this.getOriginalEditor().setValue(originalCode);
      }
      if (this._modifiedCode !== modifiedCode) {
        this.getModifiedEditor().setValue(modifiedCode ?? "");
      }
    }
  };

  public updateTheme = (theme: string) => {
    this._monaco.editor.setTheme(theme);
  };

  private getSelectedCodeDetails(
    editor: MonacoEditor.editor.IStandaloneCodeEditor,
  ): SelectedCodeDetails | null {
    const model = editor.getModel();
    if (!model) {
      return null;
    }
    const language = model.getLanguageId();

    // Get the start and end of the document
    const startOfFile = { lineNumber: 1, column: 1 };
    const endOfFile = {
      lineNumber: model.getLineCount(),
      column: model.getLineMaxColumn(model.getLineCount()),
    };

    const selectionLines = getFromStore(this._selectionLines);
    if (!selectionLines) {
      throw new Error("No selection lines found");
    }

    const selectionEndLine = Math.min(selectionLines.end + 1, endOfFile.lineNumber);
    const selectionRange = new this._monaco.Range(
      selectionLines.start + 1,
      1,
      selectionEndLine,
      model.getLineMaxColumn(selectionEndLine),
    );
    let selectedCode = model.getValueInRange(selectionRange);

    // Monaco doesn't include the newline at the end of the last selected line, so we need to add it
    if (selectionEndLine < model.getLineCount()) {
      selectedCode += model.getEOL();
    }

    const prefixRange = new this._monaco.Range(
      startOfFile.lineNumber,
      startOfFile.column,
      selectionRange.startLineNumber,
      selectionRange.startColumn,
    );
    const suffixEndLine = Math.min(selectionRange.endLineNumber + 1, endOfFile.lineNumber);
    const suffixRange = new this._monaco.Range(
      suffixEndLine,
      1,
      endOfFile.lineNumber,
      endOfFile.column,
    );

    let prefix = model.getValueInRange(prefixRange);
    let suffix = model.getValueInRange(suffixRange);

    return {
      selectedCode,
      prefix,
      suffix,
      path: model.uri.path,
      language,
      prefixBegin: prefixRange.startLineNumber - 1,
      suffixEnd: suffixRange.endLineNumber - 1,
    };
  }
}

// ============================
// UTILITY FUNCTIONS
// ============================

function createModifiedLeaf(
  editor: MonacoEditor.editor.IStandaloneDiffEditor,
  originalRange: IRange,
  modifiedRange: IRange,
): ICodeChunkLeaf & IHydratedCodeChunk {
  const originalModel = editor.getModel()?.original;
  const modifiedModel = editor.getModel()?.modified;
  if (!originalModel || !modifiedModel) {
    throw new Error("No models found");
  }

  return createLeaf(
    originalModel.getValueInRange(originalRange),
    modifiedModel.getValueInRange(modifiedRange),
    originalRange,
    modifiedRange,
  );
}

/**
 * Creates a new leaf unit of work
 * @param originalCode
 * @param modifiedCode
 * @param originalRange
 * @param modifiedRange
 * @returns
 */
function createLeaf(
  originalCode: string,
  modifiedCode: string,
  originalRange: IRange,
  modifiedRange: IRange,
): ICodeChunkLeaf & IHydratedCodeChunk {
  return {
    id: crypto.randomUUID(),
    name: "",
    title: "",
    description: "",
    generationSource: "",
    supportedActions: [],
    unitOfCodeWork: {
      repoRoot: "",
      pathName: "",
      originalCode,
      modifiedCode,
      lineChanges: {
        lineChanges: [
          {
            originalStart: originalRange.startLineNumber,
            originalEnd: originalRange.endLineNumber,
            modifiedStart: modifiedRange.startLineNumber,
            modifiedEnd: modifiedRange.endLineNumber,
          },
        ],
        lineOffset: 0,
      },
    },
    children: [],
    childIds: [],
  };
}

/**
 * Normalizes a range to [startLine, endLine) for
 * easier manipulation elsewhere
 *
 * @param range: range to normalize
 * @returns: normalized range
 */
function normalizeRange(range: IRange): IRange {
  // If the current range is empty, we start it on the next line boundary
  if (range.endLineNumber === 0) {
    return {
      startLineNumber: range.startLineNumber + 1,
      startColumn: 1,
      endLineNumber: range.startLineNumber + 1,
      endColumn: 1,
    };
  }
  // Otherwise, we use the current line boundaries
  return {
    startLineNumber: range.startLineNumber,
    startColumn: 1,
    // Monaco's endline is inclusive -- we need to increment by 1
    // to use exclusive format
    endLineNumber: range.endLineNumber + 1,
    endColumn: 1,
  };
}
