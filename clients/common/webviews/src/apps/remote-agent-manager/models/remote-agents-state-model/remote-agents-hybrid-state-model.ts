import {
  AgentHistoryUpdateType,
  type RemoteAgent,
  type RemoteAgentExchange,
  type RemoteWorkspaceSetupStatus,
  RemoteWorkspaceSetupStepStatus,
} from "$vscode/src/remote-agent-manager/types";
import type { ChatFlagsModel } from "../../../chat/models/chat-flags-model";
import { mergeRemoteAgentChatHistory } from "../../utils/agent-history";
import { mergeAgentSetupLogs, sortByStepAndSequenceNumber } from "../../utils/agent-logs";
import { sortOverviews } from "../../utils/index";
import { type RemoteAgentsClient } from "../remote-agents-client";
import { AgentsPollingManager } from "./agents-polling-manager";
import {
  isHistoryStreamUpdate,
  type RemoteAgentsHistoryStreamUpdate,
  StreamRetryExhaustedError,
} from "./remote-agents-history-stream-with-retry";
import type {
  IRemoteAgentsState,
  IRemoteAgentsStateModel,
  RemoteAgentsStateUpdate,
  StateUpdateOpts,
} from "./remote-agents-state-model";

// Polling intervals for agent overviews and logs
const OVERVIEWS_POLLING_INTERVAL = 5000;
const LOGS_POLLING_INTERVAL = 1000;

/**
 * Implementation of RemoteAgentsStateModel that uses:
 * - Polling for agent overviews and logs
 * - Streaming for chat history
 */
export class RemoteAgentsHybridStateModel implements IRemoteAgentsStateModel {
  private _state: IRemoteAgentsState = {
    agentOverviews: [],
    agentConversations: new Map<string, RemoteAgentExchange[]>(),
    agentLogs: new Map<string, RemoteWorkspaceSetupStatus | undefined>(),
    maxRemoteAgents: 0,
    maxActiveRemoteAgents: 0,

    overviewError: undefined,
    conversationError: undefined,
    logsError: undefined,

    isOverviewsLoading: false,
    isConversationLoading: false,
    isLogsLoading: false,

    logPollFailedCount: 0,
  };

  /** The maximum number of times we should try to poll for logs before giving up */
  private _loggingMaxRetries = 8;

  // Polling managers for different data types
  private _overviewsPollingManager: AgentsPollingManager<RemoteAgent[]>;
  private _logsPollingManager: AgentsPollingManager<RemoteWorkspaceSetupStatus | undefined>;

  // State tracking
  private _isInitialOverviewFetch: boolean = true;

  // Subscribers
  private _stateUpdateSubscribers = new Set<(update: RemoteAgentsStateUpdate) => void>();

  constructor(
    private readonly _flagsModel: ChatFlagsModel,
    private readonly _remoteAgentsClient: RemoteAgentsClient,
  ) {
    this._overviewsPollingManager = new AgentsPollingManager<RemoteAgent[]>({
      defaultInterval: OVERVIEWS_POLLING_INTERVAL,
      refreshFn: async () => this.refreshAgentOverviews(),
    });

    // When an agent is created, it may not immediately appear in the overviews
    // list. Retry a few times before giving up.
    this._logsPollingManager = new AgentsPollingManager<RemoteWorkspaceSetupStatus | undefined>({
      defaultInterval: LOGS_POLLING_INTERVAL,
      refreshFn: async (agentId) => {
        if (!agentId) throw new Error("Agent ID is required for logs polling");
        return this.refreshAgentLogs(agentId);
      },
      stopCondition: (_, agentId) => {
        if (!agentId) {
          // Stop the polling if we don't have an agent ID
          return true;
        }
        const agent = this._state.agentOverviews.find((a) => a.remote_agent_id === agentId);
        if (!agent) {
          // If we just created the agent, it may not be in the overviews list yet so we
          // should retry a few times before giving up.
          this._state.logPollFailedCount++;
          if (this._state.logPollFailedCount > this._loggingMaxRetries) {
            this._state.logPollFailedCount = 0;
            return true;
          } else {
            return false;
          }
        }
        // Stop polling if the agent has finished starting up. For now, we assume
        // that the last step is the indexing step. We cannot assume that we can
        // stop polling when the agent leaves the AGENT_STARTING state because opening
        // an existing agent will require polling a few times to get all the logs.
        // TODO: backend needs to send a stop condition so that we can stop polling
        const currentLogs = this.state.agentLogs.get(agentId);
        const lastStep = currentLogs?.steps.at(-1);
        if (
          lastStep?.step_description === "Indexing" &&
          lastStep.status === RemoteWorkspaceSetupStepStatus.success
        ) {
          return true;
        }
        return false;
      },
    });

    // Subscribe to flag changes to start/stop polling
    this._flagsModel.subscribe((flags) => {
      const isPolling =
        this._overviewsPollingManager.isPolling() ||
        this._remoteAgentsClient.activeHistoryStreams.size > 0 ||
        this._logsPollingManager.isPolling();

      const isEnabled = flags.enableBackgroundAgents;

      if (isEnabled && !isPolling) {
        // Start polling based on current state
        this.startStateUpdates();
      } else if (!isEnabled && isPolling) {
        // Stop all polling
        this.stopStateUpdates();
      }
    });
  }

  get state(): IRemoteAgentsState {
    return this._state;
  }

  /**
   * Start receiving state updates
   * If options are provided, only start updates for the specified types
   * If no options are provided, start all updates
   */
  startStateUpdates(options?: StateUpdateOpts): void {
    if (!this._flagsModel.enableBackgroundAgents) {
      return;
    }

    // If no options provided, start all updates
    if (!options) {
      this._overviewsPollingManager.start();
      return;
    }

    // Start specific updates based on options
    if (options.overviews) {
      this._overviewsPollingManager.start();
    }

    if (options.conversation?.agentId) {
      this.startConversationStream(options.conversation.agentId);
    }

    if (options.logs?.agentId) {
      this._state.logPollFailedCount = 0;
      this._logsPollingManager.start(options.logs.agentId);
    }
  }

  /**
   * Stop receiving state updates for the specified types
   * If options are provided, only stop updates for the specified types
   * If no options are provided, stop all updates
   */
  stopStateUpdates(options?: StateUpdateOpts): void {
    // If no options provided, stop all updates
    if (!options) {
      this._overviewsPollingManager.stop();
      this._logsPollingManager.stop();
      this.stopAllConversationStreams();
      return;
    }

    // Stop specific updates based on options
    if (options.overviews) {
      this._overviewsPollingManager.stop();
    }

    if (options.conversation?.agentId) {
      this.stopConversationStream(options.conversation.agentId);
    }

    if (options.logs?.agentId) {
      this._logsPollingManager.stop(options.logs.agentId);
    }
  }

  /**
   * Refresh the current agent conversation with the most recent messages
   * @param agentId The ID of the agent to refresh
   */
  async refreshCurrentAgent(agentId: string): Promise<void> {
    // For the hybrid model, we'll start a stream instead of polling
    this.startConversationStream(agentId);
  }

  /**
   * Refresh all agent overviews with the most recent data
   */
  async refreshAgentOverviews(): Promise<RemoteAgent[]> {
    if (this._isInitialOverviewFetch) {
      this._state.overviewError = undefined;
      this._state.isOverviewsLoading = true;
      this._isInitialOverviewFetch = false;
    }

    try {
      const response = await this._remoteAgentsClient.getRemoteAgentOverviews();

      if (response.data.error) {
        throw new Error(response.data.error);
      }

      if (response.data.maxRemoteAgents !== undefined) {
        this._state.maxRemoteAgents = response.data.maxRemoteAgents;
      }
      if (response.data.maxActiveRemoteAgents !== undefined) {
        this._state.maxActiveRemoteAgents = response.data.maxActiveRemoteAgents;
      }
      const sortedOverviews = sortOverviews(response.data.overviews);

      this._state.agentOverviews = sortedOverviews;
      this._state.overviewError = undefined;
      this._state.isOverviewsLoading = false;

      this.notifySubscribers({
        type: "overviews",
        data: sortedOverviews,
      });

      return sortedOverviews;
    } catch (err) {
      this._state.isOverviewsLoading = false;
      const errorMessage = err instanceof Error ? err.message : String(err);

      if (this._isInitialOverviewFetch || this._state.agentOverviews.length === 0) {
        // For initial fetch or empty state, show the error directly
        this._state.overviewError = { errorMessage };
      } else {
        // For background refreshes, log the error but don't disrupt the UI
        console.warn("Background refresh failed:", err);

        // If we have stale data that's too old (over 30 seconds), show a non-disruptive error
        const dataAge = this._overviewsPollingManager.timeSinceLastSuccessfulFetch;
        if (dataAge > 30 * 1000 && this._overviewsPollingManager.failedAttempts > 1) {
          // Show a warning but don't clear the data
          // Only update the error if it's not already set to avoid flickering
          if (!this._state.overviewError) {
            this._state.overviewError = {
              errorMessage: `Using cached data. Refresh failed: ${errorMessage}`,
            };
          }
        }
      }

      // Notify subscribers of the error
      this.notifySubscribers({
        type: "overviews",
        data: this._state.agentOverviews,
        error: this._state.overviewError,
      });

      // Return the current overviews even in case of error
      return this._state.agentOverviews;
    }
  }

  /**
   * Refresh agent logs with the most recent data
   * @param agentId The ID of the agent to get logs for
   */
  async refreshAgentLogs(agentId: string): Promise<RemoteWorkspaceSetupStatus | undefined> {
    try {
      // Get the current logs to determine the last processed sequence ID
      const currentLogs = this.state.agentLogs.get(agentId);

      // Find the last processed step and sequence ID
      let lastProcessedStep: number | undefined = undefined;
      let lastProcessedSequenceId: number | undefined = undefined;
      const lastStep = currentLogs?.steps.at(-1);
      if (lastStep) {
        lastProcessedStep = lastStep.step_number;
        // Special case for step 0 as it never increments the sequence ID
        lastProcessedSequenceId = lastStep.step_number === 0 ? 0 : lastStep.sequence_id + 1;
      } else {
        lastProcessedStep = 0;
        lastProcessedSequenceId = 0;
      }

      const response = await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(
        agentId,
        lastProcessedStep,
        lastProcessedSequenceId,
      );

      if (!response.data.workspaceSetupStatus) {
        return undefined;
      }

      const newLogs = response.data.workspaceSetupStatus;

      if (newLogs.steps.length === 0) {
        // if there are no new logs, return the current logs and we do not need to
        // notify subscribers
        return currentLogs;
      }

      const sortedLogs = sortByStepAndSequenceNumber(currentLogs ?? { steps: [] }, newLogs);
      const processedLogs = mergeAgentSetupLogs(sortedLogs);

      this._state.agentLogs.set(agentId, processedLogs);
      this._state.logsError = undefined;
      // Notify subscribers
      this.notifySubscribers({
        type: "logs",
        agentId,
        data: processedLogs,
      });

      return processedLogs;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      this._state.logsError = { errorMessage };

      // Notify subscribers of the error
      this.notifySubscribers({
        type: "logs",
        agentId,
        data: this._state.agentLogs.get(agentId) || { steps: [] },
        error: this._state.logsError,
      });

      // Return the current logs even in case of error
      return this._state.agentLogs.get(agentId);
    }
  }

  /**
   * Register a callback to be called when state updates are received
   * @param callback The callback to call with updated state
   */
  onStateUpdate(callback: (update: RemoteAgentsStateUpdate) => void): () => void {
    this._stateUpdateSubscribers.add(callback);

    // Immediately call with current state
    callback({
      type: "all",
      data: this._state,
    });

    return () => {
      this._stateUpdateSubscribers.delete(callback);
    };
  }

  dispose(): void {
    this.stopStateUpdates();
    this._stateUpdateSubscribers.clear();
  }

  private notifySubscribers(update: RemoteAgentsStateUpdate): void {
    this._stateUpdateSubscribers.forEach((callback) => callback(update));
  }

  /**
   * Start streaming conversation updates for an agent
   * @param agentId The ID of the agent to stream updates for
   */
  private async startConversationStream(agentId: string): Promise<void> {
    if (this._remoteAgentsClient.hasActiveHistoryStream(agentId)) {
      this.stopConversationStream(agentId);
    }

    // Get the current conversation state to determine the last processed sequence ID
    const currentConversation = this._state.agentConversations.get(agentId) || [];
    let lastProcessedSequenceId = 0;

    if (currentConversation.length > 0) {
      // Find the highest sequence ID in the current conversation
      lastProcessedSequenceId =
        Math.max(
          ...currentConversation
            .filter((exchange) => exchange.sequence_id !== undefined)
            .map((exchange) => exchange.sequence_id || 0),
        ) - 1;
      // The sequence ID should be the last "Completed" exchange.
      // So subtract 1 to get the last "In Progress" exchange
      if (lastProcessedSequenceId < 0) {
        lastProcessedSequenceId = 0;
      }
    }

    this._state.isConversationLoading = true;
    this._state.conversationError = undefined;
    this.notifySubscribers({
      type: "conversation",
      agentId,
      data: currentConversation,
      error: this._state.conversationError,
    });

    try {
      const stream = this._remoteAgentsClient.startRemoteAgentHistoryStreamWithRetry(
        agentId,
        lastProcessedSequenceId,
      );

      (async () => {
        try {
          for await (const update of stream) {
            if (
              !this._remoteAgentsClient.hasActiveHistoryStream(agentId) ||
              this._remoteAgentsClient.getActiveHistoryStream(agentId)?.isCancelled
            ) {
              // We've cancelled the stream, stop processing updates from this agent
              break;
            }
            this.processHistoryStreamUpdate(agentId, update);
          }
        } catch (error) {
          // Only update the error if the stream is still active
          if (this._remoteAgentsClient.hasActiveHistoryStream(agentId)) {
            let errorMessage: string | undefined = undefined;

            if (error instanceof StreamRetryExhaustedError) {
              // All retry attempts were exhausted
              errorMessage = `Failed to connect: ${error.message}`;
              console.error(`Stream retry exhausted for agent ${agentId}: ${error.message}`);
            } else {
              // Other unexpected errors
              errorMessage = error instanceof Error ? error.message : String(error);
              console.error(`Stream error for agent ${agentId}: ${errorMessage}`);
            }

            this._state.conversationError = { errorMessage };
            this._state.isConversationLoading = false;

            const currentConversation = this._state.agentConversations.get(agentId) || [];
            this.notifySubscribers({
              type: "conversation",
              agentId,
              data: currentConversation,
              error: this._state.conversationError,
            });
          }
        } finally {
          // The stream has completed, either successfully or with an error
          this._state.isConversationLoading = false;
        }
      })();
    } catch (error) {
      // Handle initial stream setup errors
      let errorMessage: string;

      if (error instanceof StreamRetryExhaustedError) {
        errorMessage = `Failed to connect: ${error.message}`;
      } else {
        errorMessage = error instanceof Error ? error.message : String(error);
      }

      this._state.conversationError = { errorMessage };
      this._state.isConversationLoading = false;

      // Notify subscribers of the error
      this.notifySubscribers({
        type: "conversation",
        agentId,
        data: currentConversation,
        error: this._state.conversationError,
      });
    }
  }

  /**
   * Stop streaming conversation updates for an agent
   * @param agentId The ID of the agent to stop streaming updates for
   */
  private stopConversationStream(agentId: string) {
    this._remoteAgentsClient.cancelRemoteAgentHistoryStream(agentId);
  }

  /**
   * Stop all active conversation streams
   */
  private stopAllConversationStreams(): void {
    this._remoteAgentsClient.cancelAllRemoteAgentHistoryStreams();
  }

  /**
   * Process a history stream update
   * @param agentId The ID of the agent the update is for
   * @param update The update to process
   */
  private processHistoryStreamUpdate(
    agentId: string,
    update: RemoteAgentsHistoryStreamUpdate,
  ): void {
    if (!isHistoryStreamUpdate(update)) {
      this.state.conversationError = update;

      const currentConversation = this._state.agentConversations.get(agentId) || [];
      this.notifySubscribers({
        type: "conversation",
        agentId,
        data: currentConversation,
        error: this._state.conversationError,
      });

      return;
    }
    this._state.conversationError = undefined;

    for (const historyUpdate of update.updates) {
      const currentConversation = this._state.agentConversations.get(agentId) || [];

      switch (historyUpdate.type) {
        case AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE:
          if (historyUpdate.exchange) {
            // Merge the new exchange into the current conversation
            const mergedHistory = mergeRemoteAgentChatHistory(currentConversation, [
              historyUpdate.exchange,
            ]);
            this._state.agentConversations.set(agentId, mergedHistory);
          }
          break;

        case AgentHistoryUpdateType.AGENT_HISTORY_EXCHANGE_UPDATE:
          if (historyUpdate.exchange_update) {
            // Find the exchange with the matching sequence ID
            const sequenceId = historyUpdate.exchange_update.sequence_id;
            const exchangeIndex = currentConversation.findIndex(
              (exchange) => exchange.sequence_id === sequenceId,
            );

            if (exchangeIndex >= 0) {
              const exchange = currentConversation[exchangeIndex];
              const currentText = exchange.exchange?.response_text || "";
              exchange.exchange.response_text =
                currentText + historyUpdate.exchange_update.appended_text;

              const appendedNodes = historyUpdate.exchange_update.appended_nodes;
              if (appendedNodes && appendedNodes.length > 0) {
                const responseNodes = exchange.exchange.response_nodes ?? [];
                exchange.exchange.response_nodes = [...responseNodes, ...appendedNodes];
              }

              const appendedChangedFiles = historyUpdate.exchange_update.appended_changed_files;
              if (appendedChangedFiles && appendedChangedFiles.length > 0) {
                const currentChangedFiles = exchange.changed_files ?? [];
                exchange.changed_files = [...currentChangedFiles, ...appendedChangedFiles];
              }
            }
          }
          break;

        case AgentHistoryUpdateType.AGENT_HISTORY_AGENT_STATUS:
          if (historyUpdate.agent) {
            // Update the agent status in the overviews
            const overviewIndex = this._state.agentOverviews.findIndex(
              (agent) => agent.remote_agent_id === agentId,
            );

            if (overviewIndex >= 0) {
              this._state.agentOverviews[overviewIndex] = historyUpdate.agent;
            } else {
              // If the agent isn't in the overviews yet, add it
              this._state.agentOverviews.push(historyUpdate.agent);
              this._state.agentOverviews = sortOverviews(this._state.agentOverviews);
            }

            this.notifySubscribers({
              type: "overviews",
              data: this._state.agentOverviews,
              error: this._state.overviewError,
            });
          }
          break;
      }
    }

    this._state.isConversationLoading = false;

    this.notifySubscribers({
      type: "conversation",
      agentId,
      data: this._state.agentConversations.get(agentId) || [],
      error: this._state.conversationError,
    });
  }
}
