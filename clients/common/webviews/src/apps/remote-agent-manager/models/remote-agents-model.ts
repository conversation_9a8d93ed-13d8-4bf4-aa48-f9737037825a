import type {
  <PERSON><PERSON><PERSON><PERSON>,
  MessageConsumer,
} from "$common-webviews/src/common/utils/message-broker";
import {
  type RemoteAgentChatRequestDetails,
  RemoteAgentStatus,
  type ChangedFile,
  type CommitRef,
  type GithubBranch,
  type IRemoteAgentDiffPanelOptions,
  type RemoteAgent,
  type RemoteAgentExchange,
  type RemoteAgentWorkspaceSetup,
  type RemoteWorkspaceSetupStatus,
  RemoteAgentWorkspaceStatus,
} from "$vscode/src/remote-agent-manager/types";
import {
  type RemoteAgentCreationMetrics,
  WebViewMessageType,
  type SavedSetupScriptResponseData,
} from "$vscode/src/webview-providers/webview-messages";

import { type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import {
  type SetupScript,
  type SetupScriptLocation,
} from "$vscode/src/utils/remote-agent-setup/types";
import { type RemoteAgentNotificationSettingsContext } from "$vscode/src/webview-panels/remote-agents/types";
import {
  ChatRequestNodeType,
  type ChatRequestToolResult,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { type Readable, writable, type Writable, get } from "svelte/store";
import { ExtensionClient, type IExtensionClient } from "../../chat/extension-client";
import type { ChatFlagsModel } from "../../chat/models/chat-flags-model";
import { ExchangeStatus, SeenState, type ExchangeWithStatus } from "../../chat/types/chat-message";
import { ToolUsePhase, type ToolUseState } from "../../chat/types/tool-use-state";
import { getAggregateChanges } from "../utils";
import { type IConversation } from "../../chat/models/types";
import type { ChatModel } from "../../chat/models/chat-model";
import { sortOverviews } from "../utils/index";
import { RemoteAgentDiffOpsModel } from "./ra-diff-ops-model";
import { RemoteAgentsClient } from "./remote-agents-client";
import { RemoteAgentsHybridStateModel } from "./remote-agents-state-model/remote-agents-hybrid-state-model";
import {
  type IRemoteAgentsError,
  type ISendMessageError,
  SendMessageErrorType,
  type ConversationUpdate,
  type IRemoteAgentsStateModel,
  type LogsUpdate,
  type OverviewsUpdate,
  type RemoteAgentsStateUpdate,
} from "./remote-agents-state-model/remote-agents-state-model";
import { RemoteAgentsPollingStateModel } from "./remote-agents-state-model/remote-agents-polling-state-model";
import type { RemoteAgentSessionEventData } from "@augment-internal/sidecar-libs/src/metrics/types";

const ENABLE_STREAMING = true;

/**
 * Represents a draft for a new remote agent with workspace selection
 */
export interface NewAgentDraft {
  commitRef: CommitRef | null;
  selectedBranch: GithubBranch | null;
  setupScript: SetupScript | null;
  isDisabled: boolean;
  enableNotification: boolean;
  isSetupScriptAgent?: boolean;
}

export interface IRemoteAgentConversation {
  exchanges: RemoteAgentExchange[];
  lastFetched: Date;
}

export interface IRemoteAgentsModelDeps {
  msgBroker: MessageBroker;
  isActive: boolean;
  flagsModel: ChatFlagsModel;
  host: HostInterface;
  stateModel?: IRemoteAgentsStateModel;
  chatModel?: ChatModel;
}

export const AGENT_FAILED_MESSAGE =
  "This agent is in a failed state and can no longer accept messages";

export interface IRemoteAgentsState {
  /** Whether the user is currently interacting with a remote agent view */
  isActive: boolean;
  /** Whether the remote agent panel is focused */
  isPanelFocused: boolean;

  currentAgentId: string | undefined;
  currentConversation: IRemoteAgentConversation | undefined;
  currentAgent: RemoteAgent | undefined;
  agentOverviews: RemoteAgent[];
  /** Chat conversations from the chat model */
  chatConversations: IConversation[];
  /** Local agent conversations from the chat model */
  localAgentConversations: IConversation[];
  isLoading: boolean;
  /** Whether the focused agent's details are currently loading */
  isCurrentAgentDetailsLoading: boolean;
  /** Timestamp of the last successful overview fetch */
  lastSuccessfulOverviewFetch: number;
  /** Number of consecutive failed refresh attempts */
  failedRefreshAttempts: number;
  /** The maximum number of total remote agents this user can have */
  maxRemoteAgents: number;
  /** The maximum number of active remote agents this user can have (not including paused agents) */
  maxActiveRemoteAgents: number;
  isDiffPanelOpen: boolean;
  /** The agent ID whose changes are being displayed in the diff panel */
  diffPanelAgentId: string | undefined;
  /** The file path that is currently focused in the diff panel */
  focusedFilePath: string | null;
  isCreatingAgent: boolean;
  /** General error state (used for various operations) */
  error: string | undefined;
  /** Specific error for logs */
  agentLogsError: IRemoteAgentsError | undefined;
  /** Specific error for agent threads list */
  agentThreadsError: IRemoteAgentsError | undefined;
  /** Error for fetching agent chat history */
  agentChatHistoryError: IRemoteAgentsError | undefined;
  /** Specific error for remote agent creation */
  remoteAgentCreationError: string | null;
  /** Draft for a new agent with workspace selection */
  newAgentDraft: NewAgentDraft | null;
  /** Notification settings for all remote agents */
  notificationSettings: RemoteAgentNotificationSettingsContext;
  /** Pinned status for remote agents */
  pinnedAgents: { [agentId: string]: boolean };
  setCurrentAgent: (agentId: string) => Promise<void>;
  clearCurrentAgent: () => void;
  sendMessage: (prompt: string) => Promise<boolean>;
  interruptAgent: () => Promise<void>;
  createRemoteAgent: (
    initialPrompt: string,
    workspaceSetup: RemoteAgentWorkspaceSetup,
  ) => Promise<string | undefined>;
  createRemoteAgentFromDraft: (initialPrompt: string) => Promise<string | undefined>;
  deleteAgent: (agentId: string) => Promise<void>;
  setNewAgentDraft: (draft: NewAgentDraft | null) => void;
  setRemoteAgentCreationError: (error: string | null) => void;
  hasFetchedOnce: boolean;
  showRemoteAgentDiffPanel: (opts: IRemoteAgentDiffPanelOptions) => void;
  closeRemoteAgentDiffPanel: () => void;
  setIsCreatingAgent: (isCreating: boolean) => void;
  toggleAgentPinned: (
    agentId: string,
    isPinned: boolean,
  ) => Promise<{ [agentId: string]: boolean }>;
  setPinnedAgents: (pinnedAgents: { [agentId: string]: boolean }) => void;
  pauseRemoteAgentWorkspace: (agentId: string) => Promise<void>;
  resumeRemoteAgentWorkspace: (agentId: string) => Promise<void>;
}

/**
 * RemoteAgentsModel manages the state and interactions for remote agent chats.
 * It uses a state model (IRemoteAgentsStateModel) to abstract away the details of
 * how state is updated (polling vs streaming).
 *
 * In the Remote Agents webview, we add it to context from the root component. Then
 * in the chat components, if it has been set, we use it to drive the chat UI.
 */
export class RemoteAgentsModel implements Readable<RemoteAgentsModel>, MessageConsumer {
  private _state: IRemoteAgentsState = {
    isActive: false,
    isPanelFocused: false,
    currentAgentId: undefined as string | undefined,
    currentConversation: undefined as IRemoteAgentConversation | undefined,
    currentAgent: undefined as RemoteAgent | undefined,
    agentOverviews: [],
    chatConversations: [],
    localAgentConversations: [],
    isLoading: false,
    isCurrentAgentDetailsLoading: false,
    lastSuccessfulOverviewFetch: 0,
    failedRefreshAttempts: 0,
    maxRemoteAgents: 0,
    maxActiveRemoteAgents: 0,
    isDiffPanelOpen: false,
    diffPanelAgentId: undefined as string | undefined,
    focusedFilePath: null,
    isCreatingAgent: false,
    error: undefined as string | undefined,
    agentThreadsError: undefined as IRemoteAgentsError | undefined,
    agentLogsError: undefined as IRemoteAgentsError | undefined,
    agentChatHistoryError: undefined as IRemoteAgentsError | undefined,
    remoteAgentCreationError: null,
    newAgentDraft: null,
    notificationSettings: {},
    pinnedAgents: {},
    setCurrentAgent: this.setCurrentAgent.bind(this),
    clearCurrentAgent: this.clearCurrentAgent.bind(this),
    sendMessage: this.sendMessage.bind(this),
    interruptAgent: this.interruptAgent.bind(this),
    createRemoteAgent: this.createRemoteAgent.bind(this),
    createRemoteAgentFromDraft: this.createRemoteAgentFromDraft.bind(this),
    deleteAgent: this.deleteAgent.bind(this),
    setNewAgentDraft: this.setNewAgentDraft.bind(this),
    setRemoteAgentCreationError: this.setRemoteAgentCreationError.bind(this),
    hasFetchedOnce: false,
    showRemoteAgentDiffPanel: this.showRemoteAgentDiffPanel.bind(this),
    closeRemoteAgentDiffPanel: this.closeRemoteAgentDiffPanel.bind(this),
    setIsCreatingAgent: this.setIsCreatingAgent.bind(this),
    toggleAgentPinned: this.toggleAgentPinned.bind(this),
    setPinnedAgents: this.setPinnedAgents.bind(this),
    pauseRemoteAgentWorkspace: this.pauseRemoteAgentWorkspace.bind(this),
    resumeRemoteAgentWorkspace: this.resumeRemoteAgentWorkspace.bind(this),
  };
  private _agentConversations = new Map<string, IRemoteAgentConversation>();
  private _initialPrompts = new Map<string, string>();
  private _agentSetupLogsCache = new Map<string, RemoteWorkspaceSetupStatus>();
  private _creationMetrics: RemoteAgentCreationMetrics | undefined;
  // Cache for preloaded diff explanations
  private _preloadedDiffExplanations = new Map<
    string,
    {
      explanation: any;
      changedFiles: ChangedFile[];
      userPrompt: string;
      timestamp: number;
      changedFilesHash: string;
      lastAccessed?: number; // Track when this cache entry was last accessed
      turnIdx?: number; // Store the turn index for the explanation
    }
  >();

  // Maximum number of entries to keep in the cache
  private readonly maxCacheEntries = 10;

  // Maximum total size of all cached explanations (in bytes, roughly estimated)
  private readonly maxCacheSizeBytes = 10 * 1024 * 1024; // 10MB
  private _diffOpsModel: RemoteAgentDiffOpsModel;

  private subscribers = new Set<(value: RemoteAgentsModel) => void>();

  public agentSetupLogs: RemoteWorkspaceSetupStatus | undefined = undefined;
  public static key = "remoteAgentsModel"; // for svelte context

  private _remoteAgentsClient: RemoteAgentsClient;
  private _stateModel: IRemoteAgentsStateModel;
  private _extensionClient: IExtensionClient;
  private readonly _flagsModel: ChatFlagsModel;
  private _cachedUrls = new Map<string, [number, string]>();

  // The webviews that are relying on this model
  private _externalRefCount = 0;
  private _chatModel?: ChatModel;

  // Consolidated timeout tracking for pending messages per agent
  private _pendingMessageTracking = new Map<
    string,
    Map<
      string,
      {
        timeout: NodeJS.Timeout;
        timestamp: number;
      }
    >
  >();

  // Per-agent send message error state
  private _agentSendMessageErrors = new Map<string, ISendMessageError>();

  /** Timeout for sending messages to the remote agent, after which we'll show an error to the user */
  private readonly sendMessageTimeoutMs = 90_000; // 90 seconds

  // Remote agent usage tracking
  private _hasEverUsedRemoteAgent: Writable<boolean | undefined> = writable(undefined);

  constructor({
    msgBroker,
    isActive,
    flagsModel,
    host,
    stateModel,
    chatModel,
  }: IRemoteAgentsModelDeps) {
    this._state.isActive = isActive;
    this._flagsModel = flagsModel;
    this._diffOpsModel = new RemoteAgentDiffOpsModel(msgBroker);
    this._remoteAgentsClient = new RemoteAgentsClient(msgBroker);
    this._chatModel = chatModel;
    this._extensionClient = new ExtensionClient(host, msgBroker, flagsModel);

    this._stateModel = (() => {
      if (stateModel) {
        return stateModel;
      }
      if (ENABLE_STREAMING) {
        return new RemoteAgentsHybridStateModel(this._flagsModel, this._remoteAgentsClient);
      } else {
        return new RemoteAgentsPollingStateModel(this._flagsModel, this._remoteAgentsClient);
      }
    })();

    // Set up state update handler
    this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this));

    // Set active state on the state model
    if (isActive) {
      this._stateModel.startStateUpdates();
    }

    // Load pinned agents from the global store
    this.loadPinnedAgentsFromStore();

    // Initialize remote agent usage tracking
    this.refreshHasEverUsedRemoteAgent();
  }

  /**
   * Set the chat model - public method for external use
   * @param chatModel The chat model to set
   */
  _setChatModel(chatModel: ChatModel): void {
    this._chatModel = chatModel;
  }

  private handleOverviewsUpdate(update: OverviewsUpdate) {
    const overviews = update.data;
    const oldOverviews = this._state.agentOverviews;
    const newOverviews = overviews;

    // Update current agent if it exists in the overviews
    if (this._state.currentAgentId) {
      this._state.currentAgent = this._state.agentOverviews.find(
        (agent) => agent.remote_agent_id === this._state.currentAgentId,
      );
    }

    // Check if the current agent's status has changed. If so, update the send message error state
    if (this._state.currentAgentId) {
      const currentAgentId = this._state.currentAgentId;
      const oldStatus = currentAgentId
        ? oldOverviews.find((agent) => agent.remote_agent_id === currentAgentId)?.status
        : undefined;
      const newStatus = currentAgentId
        ? newOverviews.find((agent) => agent.remote_agent_id === currentAgentId)?.status
        : undefined;
      if (newStatus !== oldStatus) {
        if (newStatus === RemoteAgentStatus.agentRunning) {
          // Clear any sendMessage error when the agent starts running again
          this._agentSendMessageErrors.delete(currentAgentId);
        } else if (newStatus === RemoteAgentStatus.agentFailed) {
          // Set sendMessage error when the agent fails
          const error: ISendMessageError = {
            type: SendMessageErrorType.agentFailed,
            errorMessage: AGENT_FAILED_MESSAGE,
            canRetry: false,
          };

          this._agentSendMessageErrors.set(currentAgentId, error);
        }
      }
    }

    this.maybeSendNotifications(newOverviews, oldOverviews);

    const sortedOverviews = sortOverviews(newOverviews);
    this._state.agentOverviews = sortedOverviews;
    this._state.hasFetchedOnce = true;
    this._state.agentThreadsError = update.error;
    this._state.lastSuccessfulOverviewFetch = update.error
      ? this._state.lastSuccessfulOverviewFetch
      : Date.now();

    const currentAgentIndex = sortedOverviews.findIndex(
      (agent) => agent.remote_agent_id === this._state.currentAgentId,
    );
    if (currentAgentIndex === -1) {
      // The currently selected agent no longer exists in the list of agents.
      // This can happen if the agent was deleted from another window or a dev
      // changed their API URL
      this.clearCurrentAgent();
    }
  }

  private handleConversationUpdate(update: ConversationUpdate) {
    if (update.agentId === this._state.currentAgentId) {
      // Convert to IRemoteAgentConversation format
      const conversation: IRemoteAgentConversation = {
        exchanges: update.data,
        lastFetched: new Date(),
      };

      this._agentConversations.set(update.agentId, conversation);
      this._state.currentConversation = conversation;
      this._state.agentChatHistoryError = update.error;

      // Check if we had a sendMessage error for a pending message that's now resolved
      // The failedExchangeId will be a "pending-" + requestId message. When we get updates
      // from the server though, the "pending" prefix isn't in any of the request_ids. So
      // if the failedExchangeId is no longer in the conversation, we can clear the error.
      const agentError = this._agentSendMessageErrors.get(update.agentId);
      if (agentError?.failedExchangeId) {
        const failedExchangeId = agentError.failedExchangeId;

        const isExchangeStillPending = this._state.currentConversation?.exchanges.some(
          (exchange) => exchange.exchange.request_id === failedExchangeId,
        );

        if (!isExchangeStillPending) {
          // The pending exchange is no longer in the conversation, meaning it was replaced
          // by a real server response. Clear the error.
          this._agentSendMessageErrors.delete(update.agentId);
        }
      }

      // Set the loading flag to false when the conversation is loaded
      this._state.isCurrentAgentDetailsLoading = false;
    }
  }

  private handleLogsUpdate(update: LogsUpdate) {
    if (update.agentId === this._state.currentAgentId) {
      this.agentSetupLogs = update.data;
      this._agentSetupLogsCache.set(update.agentId, update.data);
    }
  }

  /**
   * Handle state updates from the state model
   */
  private handleStateUpdate(update: RemoteAgentsStateUpdate): void {
    this._state.maxRemoteAgents = this._stateModel.state.maxRemoteAgents;
    this._state.maxActiveRemoteAgents = this._stateModel.state.maxActiveRemoteAgents;
    switch (update.type) {
      case "overviews":
        this.handleOverviewsUpdate(update);
        break;
      case "conversation":
        this.handleConversationUpdate(update);
        break;
      case "logs":
        this.handleLogsUpdate(update);
        break;
      case "all":
        this.handleOverviewsUpdate({
          type: "overviews",
          data: update.data.agentOverviews,
          error: update.data.overviewError,
        });
        update.data.agentConversations.forEach((exchanges, agentId) => {
          this._agentConversations.set(agentId, {
            exchanges,
            lastFetched: new Date(),
          });
        });
        update.data.agentLogs.forEach((logs, agentId) => {
          if (logs) {
            this._agentSetupLogsCache.set(agentId, logs);
            if (agentId === this._state.currentAgentId) {
              this.agentSetupLogs = logs;
            }
          }
        });

        this._state.hasFetchedOnce = true;
        this._state.agentThreadsError = update.data.overviewError;
        this._state.agentChatHistoryError = update.data.conversationError;
        this._state.agentLogsError = update.data.logsError;
        break;
    }

    if (this.currentAgentId) {
      this.checkForHistoryErrors(this.currentAgentId);
    }

    this.notifySubscribers();
  }

  subscribe(run: (value: RemoteAgentsModel) => void): () => void {
    this.subscribers.add(run);
    run(this);

    return () => {
      this.subscribers.delete(run);
    };
  }

  private notifySubscribers(): void {
    this.subscribers.forEach((sub) => sub(this));
  }

  public showRemoteAgentDiffPanel(opts: IRemoteAgentDiffPanelOptions): void {
    // Check if we have preloaded explanations for this agent
    const agentId = this._state.currentAgentId;
    // Only use preloaded explanations for aggregate view (turnIdx === -1 and isShowingAggregateChanges === true)
    if (
      agentId &&
      opts.changedFiles.length > 0 &&
      opts.turnIdx === -1 &&
      opts.isShowingAggregateChanges
    ) {
      // Generate a hash of the changed files to find the cached explanation
      const changedFilesHash = this.generateChangedFilesHash(opts.changedFiles);
      const cacheKey = `${agentId}-${changedFilesHash}`;

      // Check if we have a valid cached explanation
      const cachedExplanation = this._preloadedDiffExplanations.get(cacheKey);
      if (cachedExplanation) {
        // Update the lastAccessed timestamp
        cachedExplanation.lastAccessed = Date.now();
        this._preloadedDiffExplanations.set(cacheKey, cachedExplanation);

        // Send the message with the cached data
        this._remoteAgentsClient.showRemoteAgentDiffPanel({
          ...opts,
          preloadedExplanation: cachedExplanation.explanation,
        });

        // Set the diff panel state
        this._state.isDiffPanelOpen = true;
        this._state.diffPanelAgentId = agentId;
        this.notifySubscribers();
        return;
      }
    }

    // Fall back to the original implementation if no cache is available
    // or if we're showing specific turn changes
    this._remoteAgentsClient.showRemoteAgentDiffPanel(opts);

    // Set the diff panel state
    this._state.isDiffPanelOpen = true;
    this._state.diffPanelAgentId = agentId;
    this.notifySubscribers();
  }

  public closeRemoteAgentDiffPanel(): void {
    this._remoteAgentsClient.closeRemoteAgentDiffPanel();
    this._state.isDiffPanelOpen = false;
    this._state.diffPanelAgentId = undefined;
    this.notifySubscribers();
  }

  get flagsModel(): ChatFlagsModel {
    return this._flagsModel;
  }

  private _getChatHistory(agentId: string): ExchangeWithStatus[] {
    const conversation = this._agentConversations.get(agentId);
    if (!conversation) {
      return [];
    }
    const isRunning = this.isAgentRunning(agentId);

    return conversation.exchanges.map(({ exchange }, index) => {
      // Check if this is a pending message (optimistically added)
      const isPending = exchange.request_id.startsWith("pending-");

      return {
        /* eslint-disable @typescript-eslint/naming-convention */
        seen_state: SeenState.seen,
        structured_request_nodes: exchange.request_nodes ?? [],
        status:
          isPending || (index === conversation.exchanges.length - 1 && isRunning)
            ? ExchangeStatus.sent
            : ExchangeStatus.success,
        request_message: exchange.request_message,
        response_text: isPending ? "" : exchange.response_text,
        structured_output_nodes: exchange.response_nodes ?? [],
        request_id: exchange.request_id ?? `remote-agent-${index}`,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    });
  }

  /**
   * Converts the remote agent conversation into a format that we can plop into the
   * existing chat UI
   */
  public getCurrentChatHistory(): ExchangeWithStatus[] {
    const agentSetupLogs = this.agentSetupLogs;

    // get agent logs if not cached
    if (this.currentAgentId && !agentSetupLogs) {
      // Initialize empty logs
      this.agentSetupLogs = { steps: [] };

      // Start logs polling via state model
      this._stateModel.startStateUpdates({
        logs: { agentId: this.currentAgentId },
      });
    }

    return this._getChatHistory(this.currentAgentId ?? "");
  }

  /**
   * Gets a map of all tool states in the current conversation
   * @returns Map of tool use IDs to their current state
   */
  private getToolStates(): Map<string, ToolUseState> {
    const toolStates = new Map<string, ToolUseState>();

    const toolUses = new Set<string>();
    const toolResults = new Map<string, ChatRequestToolResult>();

    // Look for all tool uses and results in the current conversation
    this.currentConversation?.exchanges.forEach((exchange) => {
      exchange.exchange.response_nodes?.forEach((node) => {
        if (node.tool_use) {
          toolUses.add(node.tool_use.tool_use_id);
        }
      });

      exchange.exchange.request_nodes?.forEach((node) => {
        if (node.type === ChatRequestNodeType.TOOL_RESULT && node.tool_result_node) {
          toolResults.set(node.tool_result_node.tool_use_id, node.tool_result_node);
        }
      });
    });

    const finalExchange =
      this.currentConversation?.exchanges[this.currentConversation.exchanges.length - 1];
    let maxId = 0;
    // This will be the last tool use in the conversation, if the last node is a tool use, otherwise null
    let finalToolId: string | null = null;
    finalExchange?.exchange.response_nodes?.forEach((node) => {
      if (node.id > maxId) {
        maxId = node.id;
        if (node.tool_use?.tool_use_id) {
          finalToolId = node.tool_use.tool_use_id;
        } else {
          finalToolId = null;
        }
      }
    });

    // For each tool use, check if we have a result with the same id.
    // If so, set the state to completed or error. Otherwise, set the state
    // to running or cancelled based on the current agent status.
    toolUses.forEach((toolUseId) => {
      const toolResult = toolResults.get(toolUseId);
      if (toolResult) {
        toolStates.set(toolUseId, {
          phase: toolResult.is_error ? ToolUsePhase.error : ToolUsePhase.completed,
          result: {
            isError: toolResult.is_error,
            text: toolResult.content,
          },
          requestId: "",
          toolUseId: toolUseId,
        });
      } else {
        // The tool use is either running or cancelled, because we never got a result for it.
        // If the tool use isn't the final tool use, it must have been cancelled.
        const currentConversationRunning = this.isCurrentAgentRunning;
        if (toolUseId === finalToolId) {
          toolStates.set(toolUseId, {
            phase: currentConversationRunning ? ToolUsePhase.running : ToolUsePhase.cancelled,
            requestId: "",
            toolUseId: toolUseId,
          });
        } else {
          toolStates.set(toolUseId, {
            phase: ToolUsePhase.cancelled,
            requestId: "",
            toolUseId: toolUseId,
          });
        }
      }
    });

    return toolStates;
  }

  public getLastToolUseState(): { phase: ToolUsePhase } {
    const toolStates = this.getToolStates();
    const lastToolUseId = [...toolStates.keys()].pop();
    const lastToolState = toolStates.get(lastToolUseId ?? "");
    return lastToolState ?? { phase: ToolUsePhase.unknown };
  }

  /**
   * Gets the state of a specific tool
   * @param toolUseId The tool use ID to check
   * @returns The tool's state (phase and optional result)
   */
  public getToolUseState(toolUseId: string): ToolUseState {
    const key = toolUseId;
    return (
      this.getToolStates().get(key) ?? {
        phase: ToolUsePhase.completed,
        requestId: "",
        toolUseId: toolUseId,
      }
    );
  }

  /**
   * Set the current agent and load its conversation if needed
   * If agentId is undefined, we clear the current agent
   */
  async setCurrentAgent(agentId: string | undefined): Promise<void> {
    // Stop polling for previous agent
    if (this._state.currentAgentId) {
      this._stateModel.stopStateUpdates({
        conversation: { agentId: this._state.currentAgentId },
        logs: { agentId: this._state.currentAgentId },
      });
    }

    this._state.currentAgentId = agentId;

    // Set the loading flag to true when we start loading agent details
    if (agentId) {
      this._state.isCurrentAgentDetailsLoading = true;
    } else {
      this._state.isCurrentAgentDetailsLoading = false;
    }

    // Check if we have cached logs for this agent
    if (agentId && this._agentSetupLogsCache.has(agentId)) {
      this.agentSetupLogs = this._agentSetupLogsCache.get(agentId);
    } else {
      // No cached logs, clear the current logs
      this.agentSetupLogs = undefined;
    }

    // Check for pending message timeouts
    if (agentId) {
      this.checkForHistoryErrors(agentId);
    }

    this.notifySubscribers();

    if (!agentId) {
      return;
    }

    // Start polling for the new agent's conversation and logs
    this._stateModel.startStateUpdates({
      conversation: { agentId },
      logs: { agentId },
    });

    // After fetching the conversation, preload the diff explanation
    this.preloadDiffExplanation(agentId);
  }

  /**
   * Clear the current agent selection
   */
  clearCurrentAgent(): void {
    // Stop polling for the current agent
    if (this._state.currentAgentId) {
      this._stateModel.stopStateUpdates({
        conversation: { agentId: this._state.currentAgentId },
        logs: { agentId: this._state.currentAgentId },
      });
    }

    this._state.currentAgentId = undefined;

    // Clear current agent setup logs display
    this.agentSetupLogs = undefined;

    this.notifySubscribers();
  }

  /**
   * Preload diff explanation for an agent
   * This is called when an agent is selected to start loading the diff explanation
   * before the user clicks on the "View Changes" button
   */
  private async preloadDiffExplanation(agentId: string): Promise<void> {
    const conversation = this._agentConversations.get(agentId);
    if (!conversation || conversation.exchanges.length === 0) {
      return;
    }

    // Get aggregate changes from all exchanges
    const changedFiles = getAggregateChanges(conversation.exchanges);
    if (changedFiles.length === 0) {
      return;
    }

    // Generate a hash of the changed files to use as part of the cache key
    const changedFilesHash = this.generateChangedFilesHash(changedFiles);
    const cacheKey = `${agentId}-${changedFilesHash}`;

    // Check if we already have a valid cached explanation for these exact changes
    const cachedExplanation = this._preloadedDiffExplanations.get(cacheKey);
    if (cachedExplanation) {
      // We already have a valid cached explanation, no need to preload again
      return;
    }

    // Check if the changedFiles are too large (more than 12 files or 500KB total)
    const MAX_FILES = 12;
    const MAX_CONTENT_SIZE = 500 * 1024; // 500KB
    if (changedFiles.length > MAX_FILES) {
      return;
    }

    let totalSize = 0;
    changedFiles.forEach((file) => {
      totalSize += (file.old_contents?.length || 0) + (file.new_contents?.length || 0);
    });

    if (totalSize > MAX_CONTENT_SIZE) {
      return;
    }

    try {
      // Start preloading the diff explanation with a longer timeout (60 seconds)
      const explanation = await this._diffOpsModel.getDiffExplanation(
        changedFiles,
        undefined,
        60000,
      );
      if (explanation && explanation.length > 0) {
        // Generate a hash of the changed files to use as part of the cache key
        const changedFilesHash = this.generateChangedFilesHash(changedFiles);
        const cacheKey = `${agentId}-${changedFilesHash}`;

        this._preloadedDiffExplanations.set(cacheKey, {
          explanation,
          changedFiles,
          userPrompt: this.getUserMessagePrecedingTurn(conversation.exchanges, 0),
          timestamp: Date.now(),
          lastAccessed: Date.now(),
          changedFilesHash,
          turnIdx: -1, // Default to aggregate view for preloaded explanations
        });

        // Manage the cache size to prevent memory issues
        this.manageCacheSize();
      }
    } catch (error) {
      // Just log the error and continue - this is a background operation
      // and shouldn't affect the main functionality
      console.error("Failed to preload diff explanation:", error);
    }
  }

  /**
   * Helper method to get the user message preceding a turn
   */
  private getUserMessagePrecedingTurn(exchanges: RemoteAgentExchange[], turnIndex: number): string {
    if (exchanges.length === 0 || turnIndex < 0 || turnIndex >= exchanges.length) {
      return "";
    }
    return exchanges[turnIndex].exchange.request_message || "";
  }

  /**
   * Generate a hash for the changed files to use as part of the cache key
   * This helps us identify when the files have changed and we need to regenerate the explanation
   */
  private generateChangedFilesHash(changedFiles: ChangedFile[]): string {
    // Create a simplified representation of the files to hash
    const fileRepresentation = changedFiles.map((file) => ({
      oldPath: file.old_path,
      newPath: file.new_path,
      oldSize: file.old_contents?.length || 0,
      newSize: file.new_contents?.length || 0,
      // Include a small hash of the content to detect changes
      oldHash: this.simpleHash(file.old_contents || ""),
      newHash: this.simpleHash(file.new_contents || ""),
    }));

    // Convert to string and hash
    return this.simpleHash(JSON.stringify(fileRepresentation));
  }

  /**
   * A simple hash function for strings
   * This is not cryptographically secure, but it's good enough for our purposes
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString(36);
  }

  /**
   * Manage the cache size to prevent memory issues
   * This removes old or large entries when the cache gets too big
   */
  private manageCacheSize(): void {
    // If we're under the limit, no need to do anything
    if (this._preloadedDiffExplanations.size <= this.maxCacheEntries) {
      return;
    }

    // Get all entries and sort them by last accessed time (oldest first)
    // If lastAccessed is not set, use timestamp
    const entries = Array.from(this._preloadedDiffExplanations.entries())
      .map(([key, value]) => ({
        key,
        value,
        accessTime: value.lastAccessed || value.timestamp,
      }))
      .sort((a, b) => a.accessTime - b.accessTime);

    // Calculate the total size of all cached explanations (rough estimate)
    let totalSize = 0;
    entries.forEach((entry) => {
      // Estimate the size of the explanation (JSON stringified)
      const explanationSize = JSON.stringify(entry.value.explanation).length;
      // Estimate the size of the changed files (original + new contents)
      const changedFilesSize = entry.value.changedFiles.reduce(
        (size, file) => size + (file.old_contents?.length || 0) + (file.new_contents?.length || 0),
        0,
      );
      totalSize += explanationSize + changedFilesSize;
    });

    // If we're over the size limit or entry count limit, remove oldest entries until we're under
    while (
      entries.length > 0 &&
      (entries.length > this.maxCacheEntries || totalSize > this.maxCacheSizeBytes)
    ) {
      const oldestEntry = entries.shift();
      if (oldestEntry) {
        // Remove the oldest entry from the cache
        this._preloadedDiffExplanations.delete(oldestEntry.key);

        // Recalculate the total size
        const explanationSize = JSON.stringify(oldestEntry.value.explanation).length;
        const changedFilesSize = oldestEntry.value.changedFiles.reduce(
          (size, file) =>
            size + (file.old_contents?.length || 0) + (file.new_contents?.length || 0),
          0,
        );
        totalSize -= explanationSize + changedFilesSize;
      }
    }
  }

  /**
   * Send a message to the current agent
   */
  async sendMessage(prompt: string, modelId?: string): Promise<boolean> {
    /* eslint-disable @typescript-eslint/naming-convention */
    const agentId = this._state.currentAgentId;
    if (!agentId) {
      this._state.error = "No active remote agent";
      this.notifySubscribers();
      return false;
    }

    // Check if agent is in failed state
    const currentAgent = this._state.agentOverviews.find(
      (agent) => agent.remote_agent_id === agentId,
    );
    if (currentAgent?.status === RemoteAgentStatus.agentFailed) {
      const error: ISendMessageError = {
        type: SendMessageErrorType.agentFailed,
        errorMessage: AGENT_FAILED_MESSAGE,
        canRetry: false,
      };

      this._agentSendMessageErrors.set(agentId, error);
      this.notifySubscribers();
      return false;
    }

    this._state.isLoading = true;
    this._state.error = undefined;
    this.notifySubscribers();

    let optimisticExchangeId: string | undefined;

    try {
      // Add the user message optimistically to the conversation
      const conversation = this._agentConversations.get(agentId) || {
        exchanges: [],
        lastFetched: new Date(),
      };

      const lastSequenceId = this.getfinalSequenceId(agentId) || 0;
      const nextSequenceId = lastSequenceId + 1;
      optimisticExchangeId = "pending-" + Date.now();

      const optimisticExchange: RemoteAgentExchange = {
        exchange: {
          request_message: prompt,
          response_text: "", // Empty response since we're waiting
          request_id: optimisticExchangeId,
          response_nodes: [],
          request_nodes: [],
        },
        changed_files: [],
        sequence_id: nextSequenceId,
      };

      // Add to the conversation immediately
      conversation.exchanges.push(optimisticExchange);
      this._agentConversations.set(agentId, conversation);
      this._state.currentConversation = conversation;
      this.notifySubscribers();

      // Set up timeout for this message
      this.setupMessageTimeout(agentId, optimisticExchangeId);

      // Now send the actual message
      const requestDetails: RemoteAgentChatRequestDetails = {
        request_nodes: [
          {
            id: 1,
            type: ChatRequestNodeType.TEXT,
            text_node: {
              content: prompt,
            },
          },
        ],
        model_id: modelId,
      };

      const response = await this._remoteAgentsClient.sendRemoteAgentChatRequest(
        agentId,
        requestDetails,
        // Long timeout since the remote agent may need to resume
        this.sendMessageTimeoutMs,
      );
      if (response.data.error) {
        throw new Error(response.data.error);
      }

      // Clear the timeout since the request succeeded
      this.clearMessageTimeout(agentId, optimisticExchangeId);

      // When we get a direct chat response (not from polling), trigger preloading of diff explanations
      // This is a more immediate response than waiting for the next poll cycle
      if (this._state.currentAgentId) {
        // Use setTimeout to avoid blocking the UI thread
        setTimeout(() => {
          this.preloadDiffExplanation(this._state.currentAgentId!);
        }, 0);
      }

      // Refresh the conversation and overviews
      await this._stateModel.refreshCurrentAgent(agentId);
      await this._stateModel.refreshAgentOverviews();

      // Preload diff explanation
      this.preloadDiffExplanation(agentId);

      return true;
    } catch (err) {
      // Clear the timeout on error
      if (optimisticExchangeId) {
        this.clearMessageTimeout(agentId, optimisticExchangeId);
      }
      // Set specific sendMessage error
      const error: ISendMessageError = {
        type: SendMessageErrorType.chatRequestFailed,
        errorMessage: `There was an error sending your message. Please try again. Agent ID: ${agentId}. ${err}`,
        canRetry: true,
        failedExchangeId: optimisticExchangeId,
      };

      this._agentSendMessageErrors.set(agentId, error);
      console.error("Failed to send message:", err);
      this.notifySubscribers();
      return false;
    } finally {
      this._state.isLoading = false;
      this.notifySubscribers();
    }
    /* eslint-enable @typescript-eslint/naming-convention */
  }

  /**
   * Set up a timeout for a pending message
   */
  private setupMessageTimeout(agentId: string, exchangeId: string): void {
    const timeout = setTimeout(() => {
      this.handleMessageTimeout(agentId, exchangeId);
    }, this.sendMessageTimeoutMs);

    // Get or create the agent's timeout tracking map
    if (!this._pendingMessageTracking.has(agentId)) {
      this._pendingMessageTracking.set(agentId, new Map());
    }
    const agentTracking = this._pendingMessageTracking.get(agentId)!;

    agentTracking.set(exchangeId, {
      timeout,
      timestamp: Date.now(),
    });
  }

  /**
   * Clear a message timeout
   */
  private clearMessageTimeout(agentId: string, exchangeId: string): void {
    const agentTracking = this._pendingMessageTracking.get(agentId);
    if (agentTracking) {
      const tracking = agentTracking.get(exchangeId);
      if (tracking) {
        clearTimeout(tracking.timeout);
        agentTracking.delete(exchangeId);

        // Clean up empty agent tracking map
        if (agentTracking.size === 0) {
          this._pendingMessageTracking.delete(agentId);
        }
      }
    }
  }

  /**
   * Handle message timeout
   * If the agent isn't running and we've timed out, interrupt the agent and refresh the state.
   * We'll show an error to the user and allow them to retry.
   */
  private async handleMessageTimeout(agentId: string, exchangeId: string): Promise<void> {
    // Remove the timeout from tracking
    const agentTracking = this._pendingMessageTracking.get(agentId);
    if (agentTracking) {
      agentTracking.delete(exchangeId);
      if (agentTracking.size === 0) {
        this._pendingMessageTracking.delete(agentId);
      }
    }

    // Check if agent is running. If it is, do nothing.
    const currentAgent = this._state.agentOverviews.find(
      (agent) => agent.remote_agent_id === agentId,
    );
    const isRunning = currentAgent?.status === RemoteAgentStatus.agentRunning;
    if (isRunning) {
      return;
    }
    // Check if optimistic exchange is still in the conversation. If not, we've
    // already received a response and we don't need to do anything.
    const conversation = this._agentConversations.get(agentId);
    if (!conversation) {
      return;
    }
    const optimisticExchange = conversation.exchanges.find(
      (exchange) => exchange.exchange.request_id === exchangeId,
    );
    if (!optimisticExchange) {
      return;
    }

    const error: ISendMessageError = {
      type: SendMessageErrorType.messageTimeout,
      errorMessage: `There was an error sending your message. Please try again. Agent ID: ${agentId}`,
      canRetry: true,
      failedExchangeId: exchangeId,
    };

    this._agentSendMessageErrors.set(agentId, error);

    try {
      await this._remoteAgentsClient.interruptRemoteAgent(agentId);
      // Refresh agent state after interrupt
      await this._stateModel.refreshAgentOverviews();
    } catch (err) {
      console.error("Failed to interrupt agent after timeout:", err);
    }

    this.notifySubscribers();
  }

  /**
   * Remove optimistic exchange from conversation
   */
  private removeOptimisticExchange(agentId: string, exchangeId: string): void {
    const conversation = this._agentConversations.get(agentId);
    if (conversation) {
      conversation.exchanges = conversation.exchanges.filter(
        (exchange) => exchange.exchange.request_id !== exchangeId,
      );
      this._agentConversations.set(agentId, conversation);
      if (agentId === this._state.currentAgentId) {
        this._state.currentConversation = conversation;
      }
    }
  }

  /**
   * Retry a failed message
   */
  async retryFailedMessage(agentId: string, exchangeId: string): Promise<boolean> {
    // Find the failed exchange
    const conversation = this._agentConversations.get(agentId);
    if (!conversation) {
      return false;
    }

    const failedExchange = conversation.exchanges.find(
      (exchange) => exchange.exchange.request_id === exchangeId,
    );

    if (!failedExchange) {
      return false;
    }

    // Remove the failed exchange
    this.removeOptimisticExchange(agentId, exchangeId);

    // Clear the error for this agent
    this._agentSendMessageErrors.delete(agentId);

    this.notifySubscribers();

    // Retry sending the message
    return this.sendMessage(failedExchange.exchange.request_message);
  }

  /**
   * Interrupt the current agent
   */
  async interruptAgent(): Promise<void> {
    const agentId = this._state.currentAgentId;
    if (!agentId) {
      this._state.error = "No active remote agent";
      this.notifySubscribers();
      return;
    }

    this._state.isLoading = true;
    this._state.error = undefined;
    this.notifySubscribers();

    try {
      // Note: The `remoteAgentInterruptResponse` message is never used by the webview
      await this._remoteAgentsClient.interruptRemoteAgent(agentId);

      // Refresh the agent state
      await this._stateModel.refreshCurrentAgent(agentId);
      await this._stateModel.refreshAgentOverviews();
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
    } finally {
      this._state.isLoading = false;
      this.notifySubscribers();
    }
  }

  /**
   * Create a new remote agent
   */
  async createRemoteAgent(
    initialPrompt: string,
    workspaceSetup: RemoteAgentWorkspaceSetup,
    setupScript?: string,
    isSetupScriptAgent?: boolean,
    modelId?: string,
  ): Promise<string | undefined> {
    // Validate that the prompt is not empty
    if (!initialPrompt || !initialPrompt.trim()) {
      this._state.error = "Cannot create a remote agent with an empty prompt";
      this.notifySubscribers();
      return undefined;
    }

    // Clear any existing agent setup logs when creating a new agent
    this.agentSetupLogs = undefined;

    this._state.isLoading = true;
    this._state.error = undefined;
    this.notifySubscribers();

    try {
      const response = await this._remoteAgentsClient.createRemoteAgent(
        initialPrompt,
        workspaceSetup,
        setupScript,
        isSetupScriptAgent,
        modelId,
        this._creationMetrics,
      );

      if (response.data.error) {
        // Pass the error to the catch
        throw new Error(response.data.error);
      }

      if (response.data.agentId && response.data.success) {
        // Store the initial prompt for this agent
        this._initialPrompts.set(response.data.agentId, initialPrompt);
        // Set the notification setting for the newly created agent
        await this.setNotificationEnabled(
          response.data.agentId,
          this.newAgentDraft?.enableNotification ?? true,
        );

        // Set as current agent and fetch its conversation
        await this.setCurrentAgent(response.data.agentId);
        return response.data.agentId;
      } else {
        throw new Error("Failed to create remote agent: No agent ID returned");
      }
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
      this.notifySubscribers();
      throw err;
    } finally {
      this._state.isLoading = false;
      this.notifySubscribers();
    }
  }

  /**
   * Create a new remote agent using the workspace setup info from the current draft
   */
  async createRemoteAgentFromDraft(
    initialPrompt: string,
    modelId?: string,
  ): Promise<string | undefined> {
    // Clear any previous creation errors
    this.setRemoteAgentCreationError(null);

    // Clear any existing agent setup logs
    this.agentSetupLogs = undefined;

    // Validate that the prompt is not empty
    if (!initialPrompt || !initialPrompt.trim()) {
      this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");
      return undefined;
    }

    const draft = this._state.newAgentDraft;
    if (!draft) {
      this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");
      return undefined;
    }

    if (draft.isDisabled) {
      this.setRemoteAgentCreationError(
        "Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.",
      );
      return undefined;
    }

    if (!draft.commitRef || !draft.selectedBranch) {
      this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");
      return undefined;
    }

    const workspaceSetup: RemoteAgentWorkspaceSetup = {
      /* eslint-disable @typescript-eslint/naming-convention */
      starting_files: draft.commitRef,
      /* eslint-enable @typescript-eslint/naming-convention */
    };

    this._state.isLoading = true;
    this.notifySubscribers();

    try {
      // Check if this is a setup script generation agent
      const isSetupScriptAgent =
        draft.isSetupScriptAgent || draft.setupScript?.isGenerateOption === true;

      // For setup script generation, we don't need the script content
      let scriptContent = isSetupScriptAgent ? undefined : draft.setupScript?.content;

      // If we have a regular setup script, check if its contents have been updated
      if (draft.setupScript && !isSetupScriptAgent) {
        const updatedScript = (await this.listSetupScripts()).find(
          (script) => script.path === draft.setupScript!.path,
        );
        if (updatedScript) {
          scriptContent = updatedScript.content;
        }
      }

      try {
        // Create the agent
        const agentId = await this.createRemoteAgent(
          initialPrompt,
          workspaceSetup,
          scriptContent,
          isSetupScriptAgent,
          modelId,
        );
        return agentId;
      } catch (error) {
        // Extract more detailed error information
        let errorMessage = "Failed to create remote agent. Please try again.";

        if (error instanceof Error) {
          // Check for specific error types or messages
          if (error.message.includes("too large") || error.message.includes("413")) {
            errorMessage =
              "Repository or selected files are too large. Please select a smaller repository or branch.";
          } else if (error.message.includes("timeout") || error.message.includes("504")) {
            errorMessage =
              "Request timed out. The repository might be too large or the server is busy.";
          } else if (error.message.includes("rate limit") || error.message.includes("429")) {
            errorMessage = "Rate limit exceeded. Please try again later.";
          } else if (error.message.includes("unauthorized") || error.message.includes("401")) {
            errorMessage = "Authentication failed. Please check your GitHub credentials.";
          } else if (error.message.includes("not found") || error.message.includes("404")) {
            errorMessage = "Repository or branch not found. Please check your selection.";
          } else if (error.message.includes("bad request") || error.message.includes("400")) {
            errorMessage = "Invalid request. Please check your workspace setup and try again.";
          } else if (error.message.length > 0) {
            // Use the actual error message if available
            errorMessage = `Failed to create remote agent: ${error.message}`;
          }
        }

        this.setRemoteAgentCreationError(errorMessage);
        return undefined;
      }
    } finally {
      this._state.isLoading = false;
      this.notifySubscribers();
    }
  }

  /**
   * Delete a remote agent with confirmation dialog
   */
  async deleteAgent(agentId: string, doSkipConfirmation = false): Promise<void> {
    // If we have a chat model, use its extension client to show a confirmation dialog
    if (this._chatModel && !doSkipConfirmation) {
      const ok = await this._chatModel.extensionClient.openConfirmationModal({
        title: "Delete Remote Agent",
        message: "Are you sure you want to delete this remote agent?",
        confirmButtonText: "Delete",
        cancelButtonText: "Cancel",
      });

      if (!ok) {
        return; // User cancelled the deletion
      }
    }

    this._state.isLoading = true;
    this._state.error = undefined;
    this.notifySubscribers();

    try {
      const success = await this._remoteAgentsClient.deleteRemoteAgent(agentId);

      if (!success) {
        this._state.error = "Failed to delete remote agent";
        this.notifySubscribers();
        return;
      }

      // Remove the agent from all local caches
      this._agentConversations.delete(agentId);
      this._agentSetupLogsCache.delete(agentId);
      this._state.agentOverviews = this._state.agentOverviews.filter(
        (agent) => agent.remote_agent_id !== agentId,
      );
      // Remove the notification setting
      void this.removeNotificationEnabled(agentId);

      // If this was the current agent, clear it
      if (this._state.currentAgentId === agentId) {
        this.clearCurrentAgent();
      }
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
    } finally {
      this._state.isLoading = false;
      this.notifySubscribers();
    }
  }

  async sshToRemoteAgent(agent: RemoteAgent): Promise<boolean> {
    this._state.isLoading = true;
    this._state.error = undefined;
    this.notifySubscribers();

    try {
      if (agent.workspace_status !== RemoteAgentWorkspaceStatus.workspaceRunning) {
        await this._remoteAgentsClient.resumeRemoteAgentWorkspace(agent.remote_agent_id);
        // Wait for the workspace to be fully resumed
        await new Promise((resolve) => setTimeout(resolve, 5_000)); // 5 seconds
      }
      return await this._remoteAgentsClient.sshToRemoteAgent(agent.remote_agent_id);
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
      this.notifySubscribers();
      return false;
    } finally {
      this._state.isLoading = false;
      this.notifySubscribers();
    }
  }

  private async maybeSendNotifications(newOverviews: RemoteAgent[], oldOverviews: RemoteAgent[]) {
    const oldOverviewMap = new Map(
      oldOverviews.map((overview) => [overview.remote_agent_id, overview]),
    );

    // Get the notification settings for all agents
    const notificationSettings = await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(
      newOverviews.map((overview) => overview.remote_agent_id),
    );

    // Notify for agents that have just finished running
    newOverviews.forEach((overview) => {
      const oldOverview = oldOverviewMap.get(overview.remote_agent_id);
      const notificationEnabled = notificationSettings[overview.remote_agent_id];
      const wasRunning = oldOverview?.status === RemoteAgentStatus.agentRunning;
      const nowFinished =
        overview.status === RemoteAgentStatus.agentIdle ||
        overview.status === RemoteAgentStatus.agentFailed;
      const notCurrentAgent = overview.remote_agent_id !== this._state.currentAgentId;
      const isPanelFocused = this._state.isPanelFocused;
      if (
        notificationEnabled &&
        wasRunning &&
        nowFinished &&
        (notCurrentAgent ||
          // if this agent is currently selected and the panel is focused, we do not need to notify
          !isPanelFocused)
      ) {
        this._remoteAgentsClient.notifyRemoteAgentReady(overview.remote_agent_id);
      }
    });
  }

  async setNotificationEnabled(agentId: string, enabled: boolean) {
    await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(agentId, enabled);
    this._state = {
      ...this._state,
      notificationSettings: {
        ...this._state.notificationSettings,
        [agentId]: enabled,
      },
    };
    this.notifySubscribers();
  }

  async removeNotificationEnabled(agentId: string) {
    await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(agentId);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { [agentId]: _, ...remainingSettings } = this._state.notificationSettings;
    this._state = {
      ...this._state,
      notificationSettings: remainingSettings,
    };
    this.notifySubscribers();
  }

  /**
   * Whether the model has fetched overviews at least once
   */
  get hasFetchedOnce(): boolean {
    return this._state.hasFetchedOnce;
  }

  /**
   * Gets the currently focused file path in the diff view
   */
  get focusedFilePath(): string | null {
    return this._state.focusedFilePath;
  }

  /**
   * Sets the focused file path in the diff view
   * @param filePath The file path to focus, or null to clear focus
   */
  setFocusedFilePath(filePath: string | null): void {
    this._state.focusedFilePath = filePath;
    this.notifySubscribers();
  }

  /**
   * Handle messages from the extension
   */
  handleMessageFromExtension(message: MessageEvent): boolean {
    switch (message.data.type) {
      case WebViewMessageType.diffViewFileFocus:
        // Handle diff file focus change
        this.setFocusedFilePath(message.data.data.filePath.replace(/^\/+/, ""));
        return true;
      case WebViewMessageType.showRemoteAgentDiffPanel:
        this._state.isDiffPanelOpen = true;
        return true;
      case WebViewMessageType.closeRemoteAgentDiffPanel:
        this._state.isDiffPanelOpen = false;
        return true;

      default:
        return false;
    }
  }

  /**
   * Get the current agent ID
   */
  get currentAgentId(): string | undefined {
    return this._state.currentAgentId;
  }

  /**
   * Get the current conversation
   */
  get currentConversation(): IRemoteAgentConversation | undefined {
    return this._agentConversations.get(this._state.currentAgentId ?? "") ?? undefined;
  }

  private _getAgentExchanges(agentId: string): RemoteAgentExchange[] {
    return this._agentConversations.get(agentId)?.exchanges || [];
  }

  /**
   * Get the exchanges for the current agent
   */
  get currentExchanges(): RemoteAgentExchange[] {
    const agentId = this._state.currentAgentId;
    return agentId ? this._getAgentExchanges(agentId) : [];
  }

  /**
   * Get the status of the current agent
   */
  get currentStatus(): RemoteAgentStatus {
    const agentId = this._state.currentAgentId;
    return agentId
      ? this._state.agentOverviews.find((agent) => agent.remote_agent_id === agentId)?.status ||
          RemoteAgentStatus.agentIdle
      : RemoteAgentStatus.agentIdle;
  }

  /**
   * Get the details of the current agent
   */
  get currentAgent(): RemoteAgent | undefined {
    const agentId = this._state.currentAgentId;
    return agentId
      ? this._state.agentOverviews.find((agent) => agent.remote_agent_id === agentId)
      : undefined;
  }

  /**
   * Get all agent overviews
   */
  get agentOverviews(): RemoteAgent[] {
    return this._state.agentOverviews;
  }

  /**
   * Get whether the model is currently loading
   */
  get isLoading(): boolean {
    return this._state.isLoading;
  }

  /**
   * Get whether the focused agent's details are currently loading
   */
  get isCurrentAgentDetailsLoading(): boolean {
    return this._state.isCurrentAgentDetailsLoading;
  }

  /**
   * Get the timestamp of the last successful overview fetch
   */
  get lastSuccessfulOverviewFetch(): number {
    return this._state.lastSuccessfulOverviewFetch;
  }

  /**
   * Get the current error message, if any
   */
  get error(): string | undefined {
    return this._state.error;
  }

  /**
   * Get the current agent threads error message, if any
   */
  get agentThreadsError(): IRemoteAgentsError | undefined {
    return this._state.agentThreadsError;
  }

  /**
   * Get the current agent chat history error message, if any
   */
  get agentChatHistoryError(): IRemoteAgentsError | undefined {
    return this._state.agentChatHistoryError;
  }

  /**
   * Clear the sendMessage error for the current agent
   */
  clearSendMessageError(): void {
    if (this._state.currentAgentId) {
      this._agentSendMessageErrors.delete(this._state.currentAgentId);
      this.notifySubscribers();
    }
  }

  get sendMessageError(): ISendMessageError | undefined {
    return this._agentSendMessageErrors.get(this._state.currentAgentId ?? "") ?? undefined;
  }

  /**
   * Check for pending message timeouts and set error state if needed
   * This method has side effects and should be called from appropriate places
   */
  private checkForHistoryErrors(agentId: string): void {
    const exchanges = this._getAgentExchanges(agentId);

    const isFailed =
      this._state.agentOverviews.find((agent) => agent.remote_agent_id === agentId)?.status ===
      RemoteAgentStatus.agentFailed;

    if (isFailed) {
      const error: ISendMessageError = {
        type: SendMessageErrorType.agentFailed,
        errorMessage: AGENT_FAILED_MESSAGE,
        canRetry: false,
      };

      this._agentSendMessageErrors.set(agentId, error);
      this.notifySubscribers();
      return;
    }

    const hasPendingMessage =
      exchanges.length > 0 &&
      exchanges[exchanges.length - 1].exchange.request_id.startsWith("pending-");

    const currentError = this._agentSendMessageErrors.get(agentId);
    if (hasPendingMessage && !currentError) {
      const pendingExchangeId = exchanges[exchanges.length - 1].exchange.request_id;
      const agentTracking = this._pendingMessageTracking.get(agentId);
      const pendingTracking = agentTracking?.get(pendingExchangeId);

      // Check if the pending message has timed out
      if (pendingTracking && Date.now() - pendingTracking.timestamp > this.sendMessageTimeoutMs) {
        // Set the sendMessage error for the timed out message
        const error: ISendMessageError = {
          type: SendMessageErrorType.messageTimeout,
          errorMessage: `There was an error sending your message. Please try again. Agent ID: ${agentId}`,
          canRetry: true,
          failedExchangeId: pendingExchangeId,
        };

        this._agentSendMessageErrors.set(agentId, error);

        // Clean up the timeout tracking
        this.clearMessageTimeout(agentId, pendingExchangeId);

        // Notify subscribers of the error state change
        this.notifySubscribers();
      }
    }
  }

  private isAgentRunning(agentId: string): boolean {
    const agent = this._state.agentOverviews.find((agent) => agent.remote_agent_id === agentId);
    const isRunning = !!(
      agent &&
      (agent.status === RemoteAgentStatus.agentRunning ||
        agent.status === RemoteAgentStatus.agentStarting)
    );

    // Also check if we have any pending messages (optimistically added)
    // But don't consider it running if there's a sendMessage error for this agent
    const exchanges = this._getAgentExchanges(agentId);
    const agentError = this._agentSendMessageErrors.get(agentId);
    const isPending =
      exchanges.length > 0 &&
      exchanges[exchanges.length - 1].exchange.request_id.startsWith("pending-") &&
      !agentError;

    return isRunning || isPending;
  }

  /**
   * Get whether the current agent is running
   */
  get isCurrentAgentRunning(): boolean {
    return this._state.currentAgentId ? this.isAgentRunning(this._state.currentAgentId) : false;
  }

  /**
   * Get the maximum number of total remote agents allowed
   */
  get maxRemoteAgents(): number {
    return this._state.maxRemoteAgents;
  }

  /**
   * Get the maximum number of active remote agents allowed (not including paused agents)
   */
  get maxActiveRemoteAgents(): number {
    return this._state.maxActiveRemoteAgents;
  }

  /**
   * Get the initial prompt for an agent if available
   * @param agentId The agent ID to get the initial prompt for
   * @returns The initial prompt or undefined if not found
   */
  getInitialPrompt(agentId: string): string | undefined {
    return this._initialPrompts.get(agentId);
  }

  /**
   * Clear the initial prompt for an agent
   * @param agentId The agent ID to clear the initial prompt for
   */
  clearInitialPrompt(agentId: string): void {
    this._initialPrompts.delete(agentId);
  }

  get notificationSettings(): RemoteAgentNotificationSettingsContext {
    return this._state.notificationSettings;
  }

  /**
   * Get the pinned status for all remote agents
   */
  get pinnedAgents(): { [agentId: string]: boolean } {
    return this._state.pinnedAgents;
  }

  /**
   * Get the final sequence ID for the agent conversation
   */
  getfinalSequenceId(agentId: string): number | undefined {
    const conversation = this._agentConversations.get(agentId);
    const exchanges = conversation?.exchanges;
    if (!exchanges) {
      return undefined;
    }
    return exchanges[exchanges.length - 1]?.sequence_id ?? undefined;
  }

  /**
   * List all available setup scripts from multiple locations
   * Searches in ~/.augment/env/, <git_root>/.augment/env/, and <workspace_root>/.augment/env/
   * @returns Array of setup scripts with name, path, and content
   */
  async listSetupScripts(): Promise<SetupScript[]> {
    try {
      const response = await this._remoteAgentsClient.listSetupScripts();
      return response.data.scripts;
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
      this.notifySubscribers();
      return [];
    }
  }

  /**
   * Save a setup script to a specific location
   * @param name The name of the script (filename without path)
   * @param content The content of the script
   * @param location The location to save the script (home, git, workspace)
   * @returns True if the script was saved successfully, false otherwise
   */
  async saveSetupScript(
    name: string,
    content: string,
    location: SetupScriptLocation,
  ): Promise<SavedSetupScriptResponseData> {
    try {
      const response = await this._remoteAgentsClient.saveSetupScript(name, content, location);

      if (!response.data.success) {
        this._state.error = response.data.error || "Failed to save setup script";
        this.notifySubscribers();
      }

      return response.data;
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
      this.notifySubscribers();
      return { success: false, error: err instanceof Error ? err.message : String(err) };
    }
  }

  /**
   * Delete a setup script
   * @param name The name of the script to delete
   * @param location The location of the script (home, git, workspace)
   * @returns True if the script was deleted successfully, false otherwise
   */
  async deleteSetupScript(
    name: string,
    location: SetupScriptLocation,
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this._remoteAgentsClient.deleteSetupScript(name, location);

      if (!response.data.success) {
        this._state.error = response.data.error || "Failed to delete setup script";
        this.notifySubscribers();
      }

      return response.data;
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
      this.notifySubscribers();
      return { success: false, error: err instanceof Error ? err.message : String(err) };
    }
  }

  /**
   * Rename a setup script
   * @param oldName The current name of the script
   * @param newName The new name for the script
   * @param location The location of the script (home, git, workspace)
   * @returns The path to the renamed script if successful, undefined otherwise
   */
  async renameSetupScript(
    oldName: string,
    newName: string,
    location: SetupScriptLocation,
  ): Promise<{ success: boolean; path?: string; error?: string }> {
    try {
      const response = await this._remoteAgentsClient.renameSetupScript(oldName, newName, location);

      if (!response.data.success) {
        this._state.error = response.data.error || "Failed to rename setup script";
        this.notifySubscribers();
      }

      return response.data;
    } catch (err) {
      this._state.error = err instanceof Error ? err.message : String(err);
      this.notifySubscribers();
      return { success: false, error: err instanceof Error ? err.message : String(err) };
    }
  }

  get isActive(): boolean {
    return this._state.isActive;
  }

  setIsActive(isActive: boolean): void {
    this._state.isActive = isActive;

    // Update the state model's active state
    if (isActive) {
      this._stateModel.startStateUpdates();
    } else if (this._externalRefCount === 0) {
      this._stateModel.stopStateUpdates();
    } else if (this._externalRefCount > 0) {
      // Restart polling (only overviews)
      this._stateModel.startStateUpdates();
    }

    this.notifySubscribers();
  }

  setExternalRefCount(refCount: number): void {
    if (refCount === this._externalRefCount) {
      return;
    }
    this._externalRefCount = refCount;
    if (refCount === 0 && !this._state.isActive) {
      this._stateModel.stopStateUpdates();
    } else if (refCount > 0 && !this._state.isActive) {
      this._stateModel.startStateUpdates();
    }
  }

  get isPanelFocused(): boolean {
    return this._state.isPanelFocused;
  }

  setIsPanelFocused(isPanelFocused: boolean): void {
    this._state.isPanelFocused = isPanelFocused;
    this.notifySubscribers();
  }

  /**
   * Optimistically clear the has_updates flag for an agent
   * This provides immediate visual feedback when switching to an agent thread
   * @param agentId The ID of the agent to clear updates for
   */
  optimisticallyClearAgentUpdates(agentId: string): void {
    const agentIndex = this._state.agentOverviews.findIndex(
      (agent) => agent.remote_agent_id === agentId,
    );

    if (agentIndex !== -1 && this._state.agentOverviews[agentIndex].has_updates) {
      // Create a new array with the updated agent
      const updatedOverviews = [...this._state.agentOverviews];
      updatedOverviews[agentIndex] = {
        ...updatedOverviews[agentIndex],
        /* eslint-disable @typescript-eslint/naming-convention */
        has_updates: false,
      };

      this._state.agentOverviews = updatedOverviews;

      // Also update current agent if it matches
      if (this._state.currentAgent?.remote_agent_id === agentId) {
        this._state.currentAgent = {
          ...this._state.currentAgent,
          /* eslint-disable @typescript-eslint/naming-convention */
          has_updates: false,
        };
      }

      this.notifySubscribers();
    }
  }

  /**
   * Set the error message for remote agent creation
   * @param error The error message or null to clear
   */
  setRemoteAgentCreationError(error: string | null): void {
    this._state.remoteAgentCreationError = error;
    this.notifySubscribers();
  }

  get isDiffPanelOpen(): boolean {
    return this._state.isDiffPanelOpen;
  }

  get diffPanelAgentId(): string | undefined {
    return this._state.diffPanelAgentId;
  }

  /**
   * Get the current error message for remote agent creation
   */
  get remoteAgentCreationError(): string | null {
    return this._state.remoteAgentCreationError;
  }

  /**
   * Set the draft for a new agent with workspace selection
   * @param draft The draft data or null to clear
   */
  setNewAgentDraft(draft: NewAgentDraft | null): void {
    this._state.newAgentDraft = draft;
    this.notifySubscribers();
  }

  /**
   * Set the creation metrics for the new agent
   * @param metrics The metrics data or null to clear
   */
  setCreationMetrics(metrics: RemoteAgentCreationMetrics | undefined): void {
    this._creationMetrics = metrics;
  }

  /**
   * Get the current creation metrics
   */
  get creationMetrics(): RemoteAgentCreationMetrics | undefined {
    return this._creationMetrics;
  }

  refreshAgentChatHistory(agentId: string): void {
    this._stateModel.refreshCurrentAgent(agentId);
  }

  /**
   * Get the current draft for a new agent
   */
  get newAgentDraft(): NewAgentDraft | null {
    return this._state.newAgentDraft;
  }

  // Use arrow function to preserve `this` context without `.bind(this)`
  public dispose = (): void => {
    // Stop all state updates
    this._stateModel.dispose();

    this._remoteAgentsClient.dispose();

    // Clear all caches
    this._agentConversations.clear();
    this._agentSetupLogsCache.clear();
    this._preloadedDiffExplanations.clear();
    this._cachedUrls.clear();
    // Clear all pending message timeouts
    this._pendingMessageTracking.forEach((agentTracking) => {
      agentTracking.forEach((tracking) => clearTimeout(tracking.timeout));
    });
    this._pendingMessageTracking.clear();
    this._agentSendMessageErrors.clear();

    this.subscribers.clear();
  };

  /**
   * Sets the creating agent flag
   */
  public setIsCreatingAgent(isCreating: boolean): void {
    this._state.isCreatingAgent = isCreating;
    this.notifySubscribers();
  }

  /**
   * Returns whether an agent is currently being created
   */
  public get isCreatingAgent(): boolean {
    return this._state.isCreatingAgent;
  }

  /**
   * Show the remote agent home panel
   */
  async showRemoteAgentHomePanel(): Promise<void> {
    await this._remoteAgentsClient.showRemoteAgentHomePanel();
  }

  /**
   * Close the remote agent home panel
   */
  async closeRemoteAgentHomePanel(): Promise<void> {
    await this._remoteAgentsClient.closeRemoteAgentHomePanel();
  }

  /**
   * Save the last used remote agent setup preferences
   * @param gitRepo The git repository URL
   * @param gitBranch The git branch name
   * @param setupScriptPath The setup script path
   */
  async saveLastRemoteAgentSetup(
    gitRepoUrl: string | null,
    gitBranch: string | null,
    setupScriptPath: string | null,
  ): Promise<void> {
    try {
      await this._remoteAgentsClient.saveLastRemoteAgentSetup(
        gitRepoUrl,
        gitBranch,
        setupScriptPath,
      );
    } catch (error) {
      console.error("Failed to save last remote agent setup:", error);
    }
  }

  /**
   * Get the last used remote agent setup preferences
   * @returns Object containing the last used git branch and setup script
   */
  async getLastRemoteAgentSetup(): Promise<{
    lastRemoteAgentGitRepoUrl: string | null;
    lastRemoteAgentGitBranch: string | null;
    lastRemoteAgentSetupScript: string | null;
  }> {
    try {
      const response = await this._remoteAgentsClient.getLastRemoteAgentSetup();
      return response.data;
    } catch (error) {
      console.error("Failed to get last remote agent setup:", error);
      return {
        lastRemoteAgentGitRepoUrl: null,
        lastRemoteAgentGitBranch: null,
        lastRemoteAgentSetupScript: null,
      };
    }
  }

  /**
   * Load pinned agents from the global store
   */
  private async loadPinnedAgentsFromStore(): Promise<void> {
    try {
      const pinnedAgents = await this._remoteAgentsClient.getPinnedAgentsFromStore();
      this._state.pinnedAgents = pinnedAgents;
      this.notifySubscribers();
    } catch (error) {
      console.error("Failed to load pinned agents from store:", error);
    }
  }

  /**
   * Toggle the pinned status of a remote agent thread
   * @param agentId The ID of the agent to toggle pinned status for
   * @param isPinned The current pinned status
   * @returns The updated pinnedAgents object from the store
   */
  async toggleAgentPinned(
    agentId: string,
    isPinned: boolean,
  ): Promise<{ [agentId: string]: boolean }> {
    if (!agentId) {
      return this._state.pinnedAgents;
    }

    isPinned = isPinned ?? false;

    try {
      // Update the pinned status in the local state
      this._state.pinnedAgents = {
        ...this._state.pinnedAgents,
        [agentId]: !isPinned,
      };

      // If it's currently pinned, remove it from the pinned agents
      if (isPinned) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { [agentId]: _, ...rest } = this._state.pinnedAgents;
        this._state.pinnedAgents = rest;

        // Also remove from the global store
        await this._remoteAgentsClient.deletePinnedAgentFromStore(agentId);
      } else {
        // Save to the global store
        await this._remoteAgentsClient.savePinnedAgentToStore(agentId, true);
      }

      this.notifySubscribers();

      // Get the latest pinned agents from the store to ensure consistency
      const pinnedAgents = await this._remoteAgentsClient.getPinnedAgentsFromStore();
      return pinnedAgents;
    } catch (error) {
      console.error("Failed to toggle pinned status for remote agent:", error);
      return this._state.pinnedAgents;
    }
  }
  async getConversationUrl(agentId: string): Promise<string> {
    const lastUrl = this._cachedUrls.get(agentId);
    const conversation = this._agentConversations.get(agentId);
    const numExchanges = conversation?.exchanges.length ?? 0;
    if (lastUrl && conversation && lastUrl[0] === numExchanges) {
      return lastUrl[1];
    }

    // Convert to Exchange[] format
    const chatHistory = this._getChatHistory(agentId).map((exchange) => ({
      ...exchange,
      /* eslint-disable @typescript-eslint/naming-convention */
      request_id: exchange.request_id || "",
      request_message: exchange.request_message,
      response_text: exchange.response_text || "",
      /* eslint-enable @typescript-eslint/naming-convention */
    }));
    if (chatHistory.length === 0) {
      throw new Error("No chat history to share");
    }

    const msg = await this._extensionClient.saveChat(
      agentId,
      chatHistory,
      `Remote Agent ${agentId}`,
    );

    if (!msg.data) {
      throw new Error("Failed to create URL");
    }

    const url = msg.data?.url;
    if (url) {
      this._cachedUrls.set(agentId, [numExchanges, url]);
    }
    return url;
  }

  /**
   * Manually refresh agent threads
   * This can be used to retry after a network error
   */
  async refreshAgentThreads(): Promise<void> {
    // Clear the error state
    this._state.agentThreadsError = undefined;

    // Set loading state
    this._state.isLoading = true;
    this.notifySubscribers();

    try {
      // Fetch agent overviews
      await this._stateModel.refreshAgentOverviews();
    } catch (error) {
      console.error("Failed to refresh agent threads:", error);
      // Error will be set by getRemoteAgentOverviews
    } finally {
      this._state.isLoading = false;
      this.notifySubscribers();
    }
  }

  public async openDiffInBuffer(
    oldContents: string,
    newContents: string,
    filePath: string,
  ): Promise<void> {
    await this._remoteAgentsClient.openDiffInBuffer(oldContents, newContents, filePath);
  }

  public async pauseRemoteAgentWorkspace(agentId: string): Promise<void> {
    await this._remoteAgentsClient.pauseRemoteAgentWorkspace(agentId);
    return;
  }

  public async resumeRemoteAgentWorkspace(agentId: string): Promise<void> {
    await this._remoteAgentsClient.resumeRemoteAgentWorkspace(agentId);
    return;
  }

  public async reportRemoteAgentEvent(event: RemoteAgentSessionEventData): Promise<void> {
    await this._remoteAgentsClient.reportRemoteAgentEvent(event);
  }
  /**
   * Set the pinned agents from an external source (e.g., shared store)
   * @param pinnedAgents The pinned agents object to set
   */
  setPinnedAgents(pinnedAgents: { [agentId: string]: boolean }): void {
    this._state.pinnedAgents = { ...pinnedAgents };
    this.notifySubscribers();
  }

  /**
   * Sets whether the user has ever used a remote agent.
   * This is called when the user first interacts with a remote agent.
   */
  public setHasEverUsedRemoteAgent = (hasUsed: boolean) => {
    this._extensionClient.setHasEverUsedRemoteAgent(hasUsed);
    this._hasEverUsedRemoteAgent.set(hasUsed);
  };

  /**
   * Gets the current state of whether the user has ever used a remote agent.
   */
  public get hasEverUsedRemoteAgent(): Readable<boolean | undefined> {
    return this._hasEverUsedRemoteAgent;
  }

  /**
   * Checks if the user has ever used a remote agent and updates the state.
   * This is useful when initializing the UI to show appropriate indicators.
   */
  public refreshHasEverUsedRemoteAgent = async () => {
    if (get(this.hasEverUsedRemoteAgent) !== undefined) {
      return;
    }
    const hasEverUsed = await this._extensionClient.checkHasEverUsedRemoteAgent();
    if (get(this.hasEverUsedRemoteAgent) === undefined) {
      this._hasEverUsedRemoteAgent.set(hasEverUsed);
    }
  };
}
