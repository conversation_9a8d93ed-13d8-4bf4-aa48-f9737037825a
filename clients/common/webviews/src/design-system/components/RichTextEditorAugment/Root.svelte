<script lang="ts">
  import { setContext } from "svelte";
  import { RichTextEditorContext } from "./context";
  import CardAugment, { type CardSize } from "../CardAugment.svelte";

  export let editable: boolean | undefined = undefined;
  export let focusOnInit: boolean | undefined = undefined;
  export let onFocus: (() => unknown) | undefined = undefined;
  export let onBlur: (() => unknown) | undefined = undefined;
  export let size: CardSize = 1;

  // Initialize the context for this component
  const context = new RichTextEditorContext();
  setContext<RichTextEditorContext>(RichTextEditorContext.CONTEXT_KEY, context);
  if (focusOnInit) {
    context.commandManager.requestFocus();
  }

  // Expose public controls to parent components
  export const requestFocus = () => context.commandManager.requestFocus();
  export const forceFocus = () => context.commandManager.focus();
  export const blur = () => context.commandManager.blur();
  export const isFocused = () => context.eventManager.footerAwareFocused;

  // Handle footer click using the event manager
  function handleFooterMouseDown() {
    context.eventManager.handleFooterClick();
  }
</script>

<CardAugment class="c-rich-text-editor-augment" insetContent {size}>
  {#if $$slots.banner}
    <div class="c-rich-text-editor-augment__banner">
      <slot name="banner" />
    </div>
  {/if}
  <!-- Capture for keydown/click events to focus the editor -->
  <div
    class="l-rich-text-editor-augment"
    role="button"
    tabindex="-1"
    on:keydown={forceFocus}
    on:click={forceFocus}
    on:click
    on:dblclick
  >
    <slot name="header" />
    <!-- Register the root so the context can link this component to the rich text editor -->
    <div
      class="c-rich-text-editor-augment__editor"
      use:context.registerRoot={{ editable, focusOnInit, onFocus, onBlur }}
    >
      <slot />
    </div>
    <!-- svelte-ignore a11y-click-events-have-key-events -->
    <!-- svelte-ignore a11y-no-static-element-interactions -->
    <div
      class="c-rich-text-editor-augment__footer-wrapper"
      on:click={handleFooterMouseDown}
      on:mousedown={handleFooterMouseDown}
    >
      <slot name="footer" />
    </div>
  </div>
</CardAugment>

<style>
  /* We want to support overflows for editor components */
  :global(.c-rich-text-editor-augment.c-card:has(> .l-rich-text-editor-augment)) {
    overflow: visible;
    height: 100%; /* Ensure card fills its container */
    width: 100%; /* Ensure card fills its container */
    max-width: 100%; /* Ensure card does not exceed its container width */
    max-height: 100%; /* Ensure card does not exceed its container height */
    display: flex; /* Enable flex layout */
    flex-direction: column;
    position: relative; /* For absolute positioning of the overlay */
  }

  /* Style for the banner to inherit border radius from CardAugment */
  .c-rich-text-editor-augment__banner {
    width: 100%;
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
    overflow: hidden; /* Ensure content doesn't overflow the rounded corners */
    display: flex; /* Enable flex layout for child elements */
    align-items: center; /* Center content vertically */
  }
  /* Add padding around the entire component + support children styling */
  .l-rich-text-editor-augment {
    display: flex;
    flex-direction: column;
    padding: var(--ds-spacing-2);
    gap: var(--ds-spacing-2);
    overflow: auto;
    /* Fill parent dimensions */
    height: 100%;
    width: 100%;
    max-width: 100%;
    max-height: 100%;
    flex: 1;
  }

  /* Add styles for the editor container */
  .c-rich-text-editor-augment__editor {
    flex: 1; /* Grow to fill available space */
    overflow-y: auto; /* Enable vertical scrolling when content exceeds height */
    width: 100%;
    scrollbar-color: var(--augment-scrollbar-color) transparent;
    margin: 0;
    & p {
      margin: 0; /* Remove default margins for paragraphs */
      padding: 0; /* Remove default padding for paragraphs */
    }
  }
</style>
