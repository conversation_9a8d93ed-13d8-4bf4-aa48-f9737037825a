<script lang="ts">
  export let checked: boolean = false;
  export let disabled: boolean = false;
  export let size: 1 | 2 | 3 = 2;
  export let ariaLabel: string | undefined = undefined;

  function handleKeydown(event: KeyboardEvent) {
    if (disabled) return;

    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      checked = !checked;
    }
  }
</script>

<label on:change class="c-toggle-track c-toggle-track-size--{size}" class:checked class:disabled>
  <input
    type="checkbox"
    class="c-toggle-input"
    class:disabled
    bind:checked
    {disabled}
    aria-label={ariaLabel}
    role="switch"
    on:keydown={handleKeydown}
  />
</label>

<style>
  .c-toggle-track {
    --track-padding: 2px;
    --thumb-translate-x: calc(var(--track-width) - var(--thumb-size) - (2 * var(--track-padding)));

    /* Color variables - using DS variables with fallbacks */
    --off-bg-color: var(--ds-color-neutral-a3);
    --off-border-color: var(--ds-color-neutral-a5);
    --on-bg-color: var(--ds-color-success-9);
    --on-border-color: var(--ds-color-success-9);
    --thumb-bg-color: white;
    --focus-ring-color: var(--ds-color-focus-ring, var(--ds-color-accent-8));

    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    align-items: center;
    position: relative;
    border-radius: var(--ds-radius-full, 9999px);
    border: 1px solid var(--off-border-color);
    background-color: var(--off-bg-color);
    transition:
      background-color 0.2s ease-in-out,
      border-color 0.2s ease-in-out;
    padding: 0; /* Reset button padding */
    box-sizing: content-box; /* To make w/h define outer box including padding if any was added */
  }

  /* Apply base dimensions */
  .c-toggle-track {
    height: var(--track-height);
    width: var(--track-width);
  }

  /* Size-specific overrides */
  .c-toggle-track.c-toggle-track-size--1 {
    --track-height: 20px;
    --track-width: 36px;
    --thumb-size: 16px;
  }
  .c-toggle-track.c-toggle-track-size--2 {
    --track-height: 24px;
    --track-width: 44px;
    --thumb-size: 20px;
  }

  .c-toggle-track.c-toggle-track-size--3 {
    --track-height: 28px;
    --track-width: 56px;
    --thumb-size: 24px;
  }

  .c-toggle-track.checked {
    background-color: var(--on-bg-color);
    border-color: var(--on-border-color);
  }

  .c-toggle-track.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .c-toggle-input {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    position: absolute;
    display: block;
    margin: 0;
    padding: 0;
    border: none;
    border-radius: var(--ds-radius-round, 50%);
    background-color: var(--thumb-bg-color);
    box-shadow: var(--ds-shadow-2, 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06));
    transition: transform 0.2s ease-in-out;
    top: var(--track-padding);
    left: var(--track-padding);
    height: var(--thumb-size);
    width: var(--thumb-size);
    cursor: pointer; /* Added for clarity, though label also has it */
  }
  .c-toggle-input.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  .c-toggle-input:focus {
    outline: 2px solid var(--focus-ring-color);
  }

  .c-toggle-input:checked {
    transform: translateX(var(--thumb-translate-x));
  }
</style>
