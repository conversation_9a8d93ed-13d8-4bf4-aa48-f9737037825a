import type { StorybookConfig } from "@storybook/svelte-vite";
import { createRequire } from "module";

// @ts-expect-error - need this to use require.resolve later
const require = createRequire(import.meta.url);

const config: StorybookConfig = {
  stories: [
    "../mocks/Intro.mdx",
    "../mocks/**/*.mdx",
    "../mocks/**/*.stories.@(svelte|ts|tsx|js|jsx)",
  ],

  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@chromatic-com/storybook",
    "@storybook/addon-interactions",
  ],

  core: {
    crossOriginIsolated: false,
  },

  framework: {
    name: require.resolve("@storybook/svelte-vite/package.json") + "/..",
    options: {},
  },

  docs: {},
};
export default config;
