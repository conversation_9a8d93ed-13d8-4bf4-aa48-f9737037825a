/* eslint-disable @typescript-eslint/naming-convention */
import {
    ChatRequestNode,
    ChatResultNode,
    Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";

// Enum for Remote Agent Status
export enum RemoteAgentStatus {
    agentUnspecified = 0,
    // The agent has been created, but has not yet been assigned to a workspace.
    agentPending = 5,
    // The agent is starting and setting up its environment
    agentStarting = 1,
    // The agent is actively working on its task
    agentRunning = 2,
    // The agent is idle and waiting for further instructions, it may have
    // completed its task or may need further instruction
    agentIdle = 3,
    // The agent encountered an error and cannot continue
    agentFailed = 4,
}

export enum RemoteAgentWorkspaceStatus {
    workspaceUnspecified = 0,
    workspaceRunning = 1,
    workspacePausing = 2,
    workspacePaused = 3,
    workspaceResuming = 4,
}

// Enum for Agent History Update Type
export enum AgentHistoryUpdateType {
    AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED = 0,
    AGENT_HISTORY_EXCHANGE = 1,
    AGENT_HISTORY_EXCHANGE_UPDATE = 2,
    AGENT_HISTORY_AGENT_STATUS = 3,
}

// Enum for File Change Type
export enum FileChangeType {
    added = 0,
    deleted = 1,
    modified = 2,
    renamed = 3,
}

export interface ChangedFile {
    id: string;
    // Empty if the file was added
    old_path: string;
    // Empty if the file was deleted
    new_path: string;
    old_contents: string;
    new_contents: string;
    change_type: FileChangeType;
}

export type CommitRef = {
    github_commit_ref: {
        repository_url: string;
        git_ref: string;
        patch?: string;
    };
};

export interface ExchangeUpdate {
    // The request ID associated with this text update
    request_id?: string;
    // The sequence ID of the exchange being updated
    sequence_id: number;
    // The text to append to the current response
    appended_text: string;
    // The nodes to append to the current response.
    // These are additional result nodes that should be added
    // to the existing response.
    appended_nodes: ChatResultNode[];
    // Files changed as a result of this exchange update.
    // These are incremental file changes that should be added
    // to the existing changed_files.
    appended_changed_files: ChangedFile[];
}

export interface AgentHistoryUpdate {
    // The type of update, which determines which field is set
    type: AgentHistoryUpdateType;
    // For AGENT_HISTORY_EXCHANGE updates
    exchange?: RemoteAgentExchange;
    // For AGENT_STATUS updates
    agent?: RemoteAgent;
    // For TEXT_UPDATE updates
    exchange_update?: ExchangeUpdate;
}

export interface GetRemoteAgentHistoryStreamResponse {
    // A list of structured updates
    updates: AgentHistoryUpdate[];
}

export interface GetRemoteAgentChatHistoryResponse {
    chat_history: RemoteAgentExchange[];
}

export interface RemoteAgentExchange {
    exchange: Exchange;
    // Files changed as a result of this exchange
    changed_files: ChangedFile[];
    // Sequence ID for tracking the order of exchanges
    sequence_id?: number;
    // Optional user-facing summary of the turn (or last few turns)
    turn_summary?: string;
    // Timestamp when the exchange was finished
    finished_at?: string; // ISO string format
}

export interface RemoteAgentWorkspaceSetup {
    starting_files?: CommitRef;
}

export interface RemoteAgent {
    remote_agent_id: string;
    workspace_setup?: RemoteAgentWorkspaceSetup;
    status: RemoteAgentStatus;
    started_at: string; // ISO string format
    updated_at: string; // ISO string format
    // A summary of the whole agentic session
    session_summary: string;
    // Summaries of what happened in each turn
    turn_summaries: string[];
    workspace_status: RemoteAgentWorkspaceStatus;
    expires_at: string; // ISO string format
    is_setup_script_agent?: boolean;
    has_updates?: boolean;
}

export interface CreateRemoteAgentRequest {
    workspace_setup: RemoteAgentWorkspaceSetup;
    initial_request_details: RemoteAgentChatRequestDetails;
    model?: string;
    token?: string;
    setup_script?: string;
    is_setup_script_agent?: boolean;
}

export interface CreateRemoteAgentResponse {
    remote_agent_id: string;
    status: RemoteAgentStatus;
}

export interface DeleteRemoteAgentRequest {
    remote_agent_id: string;
}

export interface DeleteRemoteAgentResponse {}

export interface ListRemoteAgentsRequest {}

export interface ListRemoteAgentsResponse {
    remote_agents: RemoteAgent[];
    /** The maximum number of total remote agents this user can have */
    max_remote_agents: number;
    /** The maximum number of active remote agents this user can have (not including paused agents) */
    max_active_remote_agents: number;
}

export interface GetRemoteAgentChatHistoryRequest {
    remote_agent_id: string;
}

export interface GetRemoteAgentHistoryStreamRequest {
    remote_agent_id: string;
    last_processed_sequence_id: number;
}

export interface McpServerConfig {
    command: string;
    args?: string[];
    timeout_ms?: number;
    env?: Record<string, string>;
    use_shell_interpolation?: boolean;
    name?: string;
    disabled?: boolean;
}

export type RemoteAgentChatRequestDetails = {
    request_nodes?: ChatRequestNode[];
    user_guidelines?: string;
    workspace_guidelines?: string;
    agent_memories?: string;
    model_id?: string;
    mcp_servers?: McpServerConfig[];
};

export interface RemoteWorkspaceSetupStatus {
    steps: RemoteWorkspaceSetupStep[];
}

export interface RemoteWorkspaceSetupStep {
    step_description: string;
    logs: string;
    status: RemoteWorkspaceSetupStepStatus;
    step_number: number;
    sequence_id: number;
}

export enum RemoteWorkspaceSetupStepStatus {
    unknown = 0,
    running = 1,
    success = 2,
    failure = 3,
    skipped = 4,
}

export interface RemoteAgentWorkspaceLogsResponse {
    workspace_setup_status: RemoteWorkspaceSetupStatus;
}

export interface RemoteAgentWorkspaceLogsRequest {
    remote_agent_id: string;
    last_processed_step?: number;
    last_processed_sequence_id?: number;
}

export type DiffExplanation = DiffExplanationSection[];
export type DiffExplanationSection = {
    title: string;
    description: string;
    sections: DiffExplanationSubSection[];
};
export type DiffType =
    | "fix"
    | "feature"
    | "refactor"
    | "documentation"
    | "style"
    | "test"
    | "chore"
    | "revert"
    | "other";
export type DiffExplanationSubSectionDescription = {
    text: string;
    range: { start: number; end: number };
};
export type DiffExplanationSubSection = {
    title: string;
    descriptions: DiffExplanationSubSectionDescription[];
    type: DiffType;
    warning?: string;
    changes: Diff[];
};
export type Diff = {
    id: string;
    path: string;
    diff: string;
    originalCode: string;
    modifiedCode: string;
};

export interface IRemoteAgentDiffPanelOptions {
    sessionSummary: string;
    userPrompt: string;
    isShowingAggregateChanges: boolean;
    turnIdx: number;
    changedFiles: ChangedFile[];
    preloadedExplanation?: DiffExplanation;
    isAgentFromDifferentRepo?: boolean;
}

export type Comment = {
    text: string;
    range: { start: number; end: number };
    changeType: FileChangeType;
};

export type DiffAppliedState = "none" | "pending" | "applied";

//////////////////// SSH API ////////////////////

export interface SshConfigOption {
    key: string;
    value: string;
}

export interface RemoteAgentSSHConfig {
    public_keys: string[];

    // The hostname and config are the same for all public keys.
    hostname: string;
    ssh_config_options: SshConfigOption[];
}

export interface RemoteAgentAddSSHKeyRequest {
    remote_agent_id: string;
    public_keys: string[];
}

export interface RemoteAgentAddSSHKeyResponse {
    ssh_config: RemoteAgentSSHConfig;
}

//////////////////// GitHub API ////////////////////

export type GithubRepo = {
    owner: string;
    name: string;
    html_url?: string;
    created_at?: string;
    updated_at?: string;
    default_branch?: string;
};

export type GithubBranchCommit = {
    sha: string;
    url: string;
};

export type GithubBranch = {
    name: string;
    commit: GithubBranchCommit;
    protected: boolean;
};

export type GithubUser = {
    login: string;
    avatar_url: string;
    html_url: string;
};

export type ListGithubReposForAuthenticatedUserResponse = {
    repos: GithubRepo[];
};

export type ListGithubRepoBranchesRequest = {
    repo: GithubRepo;
    page?: number;
};

export type ListGithubRepoBranchesResponse = {
    branches: GithubBranch[];
    has_next_page: boolean;
    next_page: number;
};

export type IsUserGithubConfiguredResponse = {
    is_configured: boolean;
    oauth_url: string;
};
