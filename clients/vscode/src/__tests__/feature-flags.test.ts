/**
 * Unit tests for FeatureFlagManager class.
 */
import { generateMockWorkspaceConfig } from "../__mocks__/mock-augment-config";
import { mockWorkspaceConfigChange, workspace } from "../__mocks__/vscode-mocks";
import {
    defaultFeatureFlags,
    FeatureFlagChangeEvent,
    FeatureFlagManager,
    FeatureFlags,
    IFeatureFlagManagerOptions,
} from "../feature-flags";
import { SmartPastePrecomputeMode } from "../webview-providers/webview-messages";

describe("feature-flags", () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    // we can create a feature flag manager and it has the right default flags
    test("initial-flags", async () => {
        const manager = new FeatureFlagManager();
        expect(manager.currentFlags).toStrictEqual(defaultFeatureFlags);
        manager.dispose();
    });

    // Check that update correctly change flags to new ones
    test("change-flag", async () => {
        const manager = new FeatureFlagManager();
        const newFlags: FeatureFlags = {
            gitDiff: false,
            gitDiffPollingFrequencyMSec: 623,
            additionalChatModels: "",
            smallSyncThreshold: 1,
            bigSyncThreshold: 2,
            enableWorkspaceManagerUi: false,
            enableInstructions: false,
            enableSmartPaste: false,
            enableSmartPasteMinVersion: "",
            enablePromptEnhancer: false,
            enableViewTextDocument: false,
            bypassLanguageFilter: false,
            enableHindsight: false,
            vscodeTaskListMinVersion: "",
            maxUploadSizeBytes: 100,
            vscodeNextEditMinVersion: "0.0.0",
            vscodeNextEditBottomPanelMinVersion: "0.0.0",
            vscodeNextEditUx1MaxVersion: "0.0.0",
            vscodeNextEditUx2MaxVersion: "0.0.0",
            vscodeFlywheelMinVersion: "0.0.0",
            vscodeExternalSourcesInChatMinVersion: "0.0.0",
            vscodeShareMinVersion: "",
            maxTrackableFileCount: 200,
            maxTrackableFileCountWithoutPermission: 100,
            minUploadedPercentageWithoutPermission: 90,
            vscodeSourcesMinVersion: "",
            vscodeChatHintDecorationMinVersion: "",
            nextEditDebounceMs: 500,
            enableCompletionFileEditEvents: false,
            vscodeEnableCpuProfile: false,
            verifyFolderIsSourceRepo: false,
            refuseToSyncHomeDirectories: false,
            enableFileLimitsForSyncingPermission: false,
            enableChatMermaidDiagrams: false,
            enableSummaryTitles: false,
            smartPastePrecomputeMode: SmartPastePrecomputeMode.visibleHover,
            vscodeNewThreadsMenuMinVersion: "",
            vscodeEditableHistoryMinVersion: "",
            vscodeEnableChatMermaidDiagramsMinVersion: "",
            userGuidelinesLengthLimit: 2000,
            workspaceGuidelinesLengthLimit: 2000,
            enableGuidelines: false,
            useCheckpointManagerContextMinVersion: "",
            validateCheckpointManagerContext: false,
            vscodeDesignSystemRichTextEditorMinVersion: "",
            allowClientFeatureFlagOverrides: false,
            vscodeChatWithToolsMinVersion: "",
            vscodeChatMultimodalMinVersion: "",
            vscodeAgentModeMinVersion: "",
            vscodeAgentModeMinStableVersion: "",
            vscodeBackgroundAgentsMinVersion: "",
            vscodeAgentEditTool: "backend_edit_tool",
            vscodeRichCheckpointInfoMinVersion: "",
            vscodeDirectApplyMinVersion: "",
            vscodeVirtualizedMessageListMinVersion: "",
            memoriesParams: {},
            eloModelConfiguration: {
                highPriorityModels: [],
                regularBattleModels: [],
                highPriorityThreshold: 0.5,
            },
            vscodeChatStablePrefixTruncationMinVersion: "",
            agentEditToolMinViewSize: 0,
            agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested",
            agentEditToolEnableFuzzyMatching: true,
            agentEditToolFuzzyMatchSuccessMessage:
                "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
            agentEditToolFuzzyMatchMaxDiff: 50,
            agentEditToolFuzzyMatchMaxDiffRatio: 0.15,
            agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs: 5,
            agentEditToolInstructionsReminder: false,
            agentEditToolShowResultSnippet: true,
            agentEditToolMaxLines: 200,
            vscodePersonalitiesMinVersion: "",
            memoryClassificationOnFirstToken: false,
            agentSaveFileToolInstructionsReminder: false,
            useMemorySnapshotManager: false,
            vscodeGenerateCommitMessageMinVersion: "",
            enableRules: false,
            memoriesTextEditorEnabled: false,
            enableModelRegistry: false,
            modelRegistry: {},
            openFileManagerV2Enabled: false,
            clientAnnouncement: "",
            vscodeRemoteAgentSSHMinVersion: "",
            grepSearchToolEnable: false,
            grepSearchToolTimelimitSec: 10,
            grepSearchToolOutputCharsLimit: 5000,
            grepSearchToolNumContextLines: 5,
            enableAgentAutoMode: false,
        };
        expect(manager.currentFlags).not.toEqual(newFlags);
        manager.update(newFlags);
        expect(manager.currentFlags).toStrictEqual(newFlags);
        manager.dispose();
    });

    // we can subscribe to change events
    test("subscribe", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest
            .fn()
            .mockImplementation(
                ({ previousFlags, newFlags, changedFlags }: FeatureFlagChangeEvent) => {
                    expect(previousFlags).toStrictEqual(defaultFeatureFlags);
                    expect(newFlags).not.toStrictEqual(defaultFeatureFlags);
                    expect(changedFlags).toStrictEqual(["maxUploadSizeBytes"]);
                    expect(manager.currentFlags).toStrictEqual(newFlags);
                }
            );
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const flags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags);
        expect(callback).toHaveBeenCalled();
        manager.dispose();
    });

    // Test vscodeVirtualizedMessageListMinVersion flag
    test("vscodeVirtualizedMessageListMinVersion flag is correctly initialized", () => {
        const manager = new FeatureFlagManager();
        expect(manager.currentFlags.vscodeVirtualizedMessageListMinVersion).toBe("");
    });

    test("vscodeVirtualizedMessageListMinVersion flag is correctly updated", () => {
        const manager = new FeatureFlagManager();
        const updatedFlags = {
            ...manager.currentFlags,
            vscodeVirtualizedMessageListMinVersion: "1.0.0",
        };
        manager.update(updatedFlags);
        expect(manager.currentFlags.vscodeVirtualizedMessageListMinVersion).toBe("1.0.0");
    });

    // Test vscodePersonalitiesMinVersion flag
    test("vscodePersonalitiesMinVersion flag is correctly initialized", () => {
        const manager = new FeatureFlagManager();
        expect(manager.currentFlags.vscodePersonalitiesMinVersion).toBe("");
    });

    test("vscodePersonalitiesMinVersion flag is correctly updated", () => {
        const manager = new FeatureFlagManager();
        const updatedFlags = {
            ...manager.currentFlags,
            vscodePersonalitiesMinVersion: "1.0.0",
        };
        manager.update(updatedFlags);
        expect(manager.currentFlags.vscodePersonalitiesMinVersion).toBe("1.0.0");
    });

    // Test vscodeDirectApplyMinVersion flag
    test("vscodeDirectApplyMinVersion flag is correctly initialized", () => {
        const manager = new FeatureFlagManager();
        expect(manager.currentFlags.vscodeDirectApplyMinVersion).toBe("");
    });

    test("vscodeDirectApplyMinVersion flag is correctly updated", () => {
        const manager = new FeatureFlagManager();
        const updatedFlags = {
            ...manager.currentFlags,
            vscodeDirectApplyMinVersion: "1.0.0",
        };
        manager.update(updatedFlags);
        expect(manager.currentFlags.vscodeDirectApplyMinVersion).toBe("1.0.0");
    });

    // Test vscodeTaskListMinVersion flag
    test("vscodeTaskListMinVersion flag is correctly initialized", () => {
        const manager = new FeatureFlagManager();
        expect(manager.currentFlags.vscodeTaskListMinVersion).toBe("");
    });

    test("vscodeTaskListMinVersion flag is correctly updated", () => {
        const manager = new FeatureFlagManager();
        const updatedFlags = {
            ...manager.currentFlags,
            vscodeTaskListMinVersion: "1.0.0",
        };
        manager.update(updatedFlags);
        expect(manager.currentFlags.vscodeTaskListMinVersion).toBe("1.0.0");
    });

    // Test agentEditToolMaxLines flag
    test("agentEditToolMaxLines flag is correctly initialized", () => {
        const manager = new FeatureFlagManager();
        expect(manager.currentFlags.agentEditToolMaxLines).toBe(200);
    });

    test("agentEditToolMaxLines flag is correctly updated", () => {
        const manager = new FeatureFlagManager();
        const updatedFlags = {
            ...manager.currentFlags,
            agentEditToolMaxLines: 300,
        };
        manager.update(updatedFlags);
        expect(manager.currentFlags.agentEditToolMaxLines).toBe(300);
    });

    // subscription can be disposed
    test("dispose-subscription", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        subscription.dispose();
        const flags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags);
        expect(callback).not.toHaveBeenCalled();
        manager.dispose();
    });

    // when subscribing to multiple flags, individual changes to each
    // flag should all trigger the callback
    test("multiple-flags", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes", "gitDiff"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const flags1 = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags1);
        expect(callback).toHaveBeenCalled();
        const flags2 = {
            ...flags1,
            gitDiff: !manager.currentFlags.gitDiff,
        };
        manager.update(flags2);
        expect(callback).toHaveBeenCalledTimes(2);
        manager.dispose();
    });

    // Multiple subscriptions on the same flag should work for each callbacks.
    test("multiple-subscriptions", async () => {
        const manager = new FeatureFlagManager();
        const callback1 = jest.fn();
        const callback2 = jest.fn();
        const subscription1 = manager.subscribe(["maxUploadSizeBytes"], callback1);
        const subscription2 = manager.subscribe(["maxUploadSizeBytes"], callback2);
        expect(subscription1).not.toBeUndefined();
        expect(subscription2).not.toBeUndefined();
        expect(callback1).not.toHaveBeenCalled();
        expect(callback2).not.toHaveBeenCalled();
        const flags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.update(flags);
        expect(callback1).toHaveBeenCalled();
        expect(callback2).toHaveBeenCalled();
        manager.dispose();
    });

    // updating a flag to the same value should not trigger the callback
    // even when other flags are changed.
    test("no-change", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        manager.update({
            ...manager.currentFlags,
            gitDiff: !manager.currentFlags.gitDiff,
        });
        expect(callback).not.toHaveBeenCalled();
        manager.dispose();
    });

    // the list of changed flags received in the callback should be correct
    test("changed-flags", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes", "gitDiff"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const oldFlags = manager.currentFlags;
        const flags = {
            ...oldFlags,
            maxUploadSizeBytes: oldFlags.maxUploadSizeBytes + 7,
            gitDiff: !oldFlags.gitDiff,
        };
        manager.update(flags);
        expect(callback).toHaveBeenCalledWith({
            previousFlags: oldFlags,
            newFlags: flags,
            changedFlags: ["maxUploadSizeBytes", "gitDiff"],
        });
        manager.dispose();
    });

    // Disposing the feature flag manager disables all subscriptions
    test("dispose-manager", async () => {
        const manager = new FeatureFlagManager();
        const callback = jest.fn();
        const subscription = manager.subscribe(["maxUploadSizeBytes"], callback);
        expect(subscription).not.toBeUndefined();
        expect(callback).not.toHaveBeenCalled();
        const newFlags = {
            ...manager.currentFlags,
            maxUploadSizeBytes: manager.currentFlags.maxUploadSizeBytes + 7,
        };
        manager.dispose();
        // accessing flags should throw
        expect(() => manager.currentFlags).toThrow();
        // subscription should be disposed
        expect(subscription.disposed).toBeTruthy();
        // calling update should throw
        expect(() => manager.update(newFlags)).toThrow();
        // calling subscribe should throw
        expect(() => manager.subscribe(["gitDiff"], callback)).toThrow();
        // disposing manager again should not throw
        expect(() => manager.dispose()).not.toThrow();
        manager.dispose();
    });

    // Test automatic loading of feature flags
    test("automatic-loading", async () => {
        jest.useFakeTimers();

        const mockFetcher = jest.fn().mockImplementation(async () => ({
            ...defaultFeatureFlags,
            gitDiff: true,
            maxUploadSizeBytes: 256 * 1024,
        }));

        const options: IFeatureFlagManagerOptions = {
            refreshIntervalMSec: 5000,
            fetcher: mockFetcher,
        };

        const manager = new FeatureFlagManager(options);

        // Initial state should be default flags
        expect(manager.currentFlags).toStrictEqual(defaultFeatureFlags);

        // Advance timer to trigger first fetch
        jest.advanceTimersByTime(5000);
        await Promise.resolve(); // Allow pending promises to resolve

        // Check if flags were updated
        expect(manager.currentFlags.gitDiff).toBe(true);
        expect(manager.currentFlags.maxUploadSizeBytes).toBe(256 * 1024);

        // Fetcher should have been called once
        expect(mockFetcher).toHaveBeenCalledTimes(1);

        // Advance timer again to trigger second fetch
        await jest.advanceTimersByTimeAsync(6000);
        expect(mockFetcher).toHaveBeenCalledTimes(2);

        // Advance timer again to trigger third fetch
        await jest.advanceTimersByTimeAsync(6000);
        expect(mockFetcher).toHaveBeenCalledTimes(3);

        // Clean up
        manager.dispose();
        jest.useRealTimers();
    });

    describe("feature flag overrides", () => {
        test("overrides not allowed", () => {
            const manager = new FeatureFlagManager();
            const newFlags = {
                ...manager.currentFlags,
                allowClientFeatureFlagOverrides: false,
                vscodeNextEditMinVersion: "",
            };
            manager.update(newFlags);
            expect(manager.currentFlags.allowClientFeatureFlagOverrides).toBe(false);
            const callback = jest.fn();
            const subscription = manager.subscribe(["vscodeNextEditMinVersion"], callback);
            expect(subscription).not.toBeUndefined();
            expect(callback).not.toHaveBeenCalled();

            const mockConfig = generateMockWorkspaceConfig({
                advanced: {
                    featureFlagOverrides: {
                        vscodeNextEditMinVersion: "0.0.0",
                    },
                } as any,
            });

            mockWorkspaceConfigChange(mockConfig);
            // we want to ALWAYS return the mock, not just return it once.
            workspace.getConfiguration.mockImplementation(() => {
                return mockConfig;
            });

            expect(callback).not.toHaveBeenCalled();
            expect(manager.currentFlags.vscodeNextEditMinVersion).toBe("");
            manager.dispose();
        });

        test("valid overrides", () => {
            const manager = new FeatureFlagManager();
            const newFlags = {
                ...manager.currentFlags,
                allowClientFeatureFlagOverrides: true,
                vscodeNextEditMinVersion: "",
                vscodeNextEditUx1MaxVersion: "",
            };
            manager.update(newFlags);
            expect(manager.currentFlags.allowClientFeatureFlagOverrides).toBe(true);
            const callback = jest.fn();
            const subscription = manager.subscribe(["vscodeNextEditMinVersion"], callback);
            expect(subscription).not.toBeUndefined();
            expect(callback).not.toHaveBeenCalled();
            const mockConfig = generateMockWorkspaceConfig({
                advanced: {
                    featureFlagOverrides: {
                        vscodeNextEditMinVersion: "0.0.0",
                        vscodeNextEditUx1MaxVersion: "9.0.0",
                    },
                } as any,
            });

            mockWorkspaceConfigChange(mockConfig);
            // we want to ALWAYS return the mock, not just return it once.
            workspace.getConfiguration.mockImplementation(() => {
                return mockConfig;
            });

            expect(callback).toHaveBeenCalled();
            expect(manager.currentFlags.vscodeNextEditMinVersion).toBe("0.0.0");
            expect(manager.currentFlags.vscodeNextEditUx1MaxVersion).toBe("9.0.0");
            manager.dispose();
        });

        test("valid override names but wrong type", () => {
            const manager = new FeatureFlagManager();
            const newFlags = {
                ...manager.currentFlags,
                allowClientFeatureFlagOverrides: true,
                vscodeNextEditMinVersion: "",
                vscodeNextEditUx1MaxVersion: "",
            };
            manager.update(newFlags);
            expect(manager.currentFlags.allowClientFeatureFlagOverrides).toBe(true);

            const mockConfig = generateMockWorkspaceConfig({
                advanced: {
                    featureFlagOverrides: {
                        vscodeNextEditMinVersion: true,
                        vscodeNextEditUx1MaxVersion: false,
                    },
                } as any,
            });

            mockWorkspaceConfigChange(mockConfig);
            // we want to ALWAYS return the mock, not just return it once.
            workspace.getConfiguration.mockImplementation(() => {
                return mockConfig;
            });

            expect(manager.currentFlags.vscodeNextEditMinVersion).toBe("");
            expect(manager.currentFlags.vscodeNextEditUx1MaxVersion).toBe("");
            manager.dispose();
        });
    });
});
