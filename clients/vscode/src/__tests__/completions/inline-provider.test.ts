import { ReplacementText } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { InlineCompletionItem } from "vscode";
import * as vscode from "vscode";

import { mockFSUtils } from "../../__mocks__/fs-utils";
import { MockAPIServer } from "../../__mocks__/mock-api-server";
import { generateMockWorkspaceConfig } from "../../__mocks__/mock-augment-config";
import {
    CancellationToken,
    ExtensionContext,
    InlineCompletionTriggerKind,
    mockWorkspaceConfigChange,
    MutableTextDocument,
    newMockStatusBar,
    openTextDocument,
    Position,
    publishTextDocumentChange,
    Range,
    resetMockWorkspace,
    StatusBarItem,
    Uri,
    window,
} from "../../__mocks__/vscode-mocks";
import { APITiming } from "../../api/api-timing";
import { CompletionResult } from "../../augment-api";
import { AugmentConfigListener } from "../../augment-config-listener";
import { AuthSessionStore } from "../../auth/auth-session-store";
import { ChatRequest } from "../../chat/chat-model";
import { AssetManager } from "../../client-interfaces/asset-manager";
import { AugmentInstruction } from "../../code-edit-types";
import { AugmentCompletion } from "../../completions/augment-completion";
import { onCompletionRequest } from "../../completions/completion-events";
import { CompletionsModel } from "../../completions/completions-model";
import { InlineCompletionProvider } from "../../completions/inline-provider";
import { RecentCompletions } from "../../completions/recent-completions";
import { SkipCompletion } from "../../exceptions";
import { AugmentExtension } from "../../extension";
import { ActionsModel } from "../../main-panel/action-cards/actions-model";
import { ChatExtensionMessage } from "../../main-panel/apps/chat-webview-app";
import { ClientMetricsReporter } from "../../metrics/client-metrics-reporter";
import { CompletionAcceptanceReporter } from "../../metrics/completion-acceptance-reporter";
import { ClientCompletionTimelineReporter } from "../../metrics/completion-timeline-reporter";
import { OnboardingSessionEventReporter } from "../../metrics/onboarding-session-event-reporter";
import { FileEditEvent } from "../../next-edit/file-edit-events";
import { NextEditResultInfo } from "../../next-edit/next-edit-types";
import { StatusBarManager } from "../../statusbar/status-bar-manager";
import { completionFailed, enabled, generatingCompletion } from "../../statusbar/status-bar-states";
import { AugmentGlobalState } from "../../utils/context";
import { RecentItems } from "../../utils/recent-items";
import { MainPanelWebviewProvider } from "../../webview-providers/main-panel-webview-provider";
import { MainPanelApp, NextEditWebViewMessage } from "../../webview-providers/webview-messages";
import { SyncingEnabledTracker } from "../../workspace/syncing-enabled-tracker";

class InlineProviderTestKit {
    public apiCompletionResult: CompletionResult = {
        completionItems: [
            {
                text: "basic completion example",
                suffixReplacementText: "",
                skippedSuffix: "",
            },
        ],
        unknownBlobNames: [],
        checkpointNotFound: false,
    };
    public document: MutableTextDocument;
    public position = new Position(0, 0);

    readonly provider: InlineCompletionProvider;
    readonly mockEventListener: jest.Mock;

    constructor(
        readonly extension: AugmentExtension,
        readonly completionsModel: CompletionsModel,
        readonly apiServer: MockAPIServer,
        readonly statusBarManager: StatusBarManager,
        readonly statusBar: StatusBarItem,
        readonly config: AugmentConfigListener,
        readonly timelineReporter: ClientCompletionTimelineReporter
    ) {
        statusBarManager.setState(enabled);
        this.provider = new InlineCompletionProvider(
            completionsModel,
            new CompletionAcceptanceReporter(
                apiServer,
                new OnboardingSessionEventReporter(apiServer)
            ),
            statusBarManager,
            config,
            timelineReporter
        );

        this.document = this.createAndOpenFile("/example/file", "Hello World!");

        // Setup api server with common mock
        apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: any,
                _language: string,
                _context: any,
                _recentChanges: ReplacementText[],
                _fileEditEvents?: FileEditEvent[],
                _completionTimeoutMs?: number,
                _probeOnly?: boolean,
                _apiTiming?: APITiming
            ): Promise<CompletionResult> => {
                if (_apiTiming) {
                    _apiTiming.rpcStart = Date.now();
                    _apiTiming.rpcEnd = Date.now();
                }
                return this.apiCompletionResult;
            }
        );

        // Add a mock event listener
        this.mockEventListener = jest.fn();
        onCompletionRequest(this.mockEventListener);
    }

    public createAndOpenFile(fileName: string, contents: string): MutableTextDocument {
        const uri = Uri.file(fileName);
        mockFSUtils.writeFileUtf8(fileName, contents, true);
        return openTextDocument(uri);
    }

    assertCompletion(
        result: InlineCompletionItem[],
        opts: AssertCompletionOptions = {
            range: new Range(0, 0, 0, 0),
            wantCompletions: true,
        }
    ) {
        let expectCompletions: AugmentCompletion[] = [];
        let expectCompletionCount: number = 0;
        if (opts.wantCompletions) {
            expectCompletionCount = this.apiCompletionResult.completionItems.length;
            expectCompletions = this.apiCompletionResult.completionItems.map((item) => {
                return new AugmentCompletion(
                    opts.insertText ?? item.text,
                    item.suffixReplacementText,
                    item.skippedSuffix,
                    {
                        startOffset: opts.range?.start.character || 0,
                        endOffset: opts.range?.end.character || 0,
                    }
                );
            });
        }

        expect(result.length).toBe(expectCompletionCount);
        for (let i = 0; i < expectCompletionCount; i++) {
            expect(result[i]).toEqual({
                insertText: expectCompletions[i].completionText,
                range: opts.range,
                command: undefined,
                filterText: undefined,
            });
        }

        expect(this.mockEventListener).toHaveBeenCalledTimes(1);

        const eventCall = this.mockEventListener.mock.calls[0][0];
        expect(eventCall.occuredAt).toEqual(expect.any(Date));
        expect(eventCall.document).toEqual(this.document);
        expect(eventCall.requestId).toEqual("request.123");
        expect(eventCall.pathName).toEqual(this.document.uri.path);
        expect(eventCall.repoRoot).toEqual("");
        expect(eventCall.prefix).toEqual(opts.prefix ?? "");
        expect(eventCall.suffix).toEqual(opts.suffix ?? "Hello World!");
        expect(eventCall.completions).toEqual(expectCompletions);
    }

    dispose() {
        this.extension.dispose();
    }

    static async create(): Promise<InlineProviderTestKit> {
        const mockStatusBar: StatusBarItem = newMockStatusBar();
        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);

        const context = new ExtensionContext();
        const config = new AugmentConfigListener();
        const apiServer = new MockAPIServer();
        const auth = new AuthSessionStore(context, config);
        const recentCompletions = new RecentCompletions();
        const recentInstructions = new RecentItems<AugmentInstruction>(10);
        const recentNextEditLocations = new RecentItems<NextEditResultInfo>(10);
        const recentChats = new RecentItems<ChatRequest>(10);
        const nextEditWebViewEvent = new vscode.EventEmitter<NextEditWebViewMessage>();
        const extensionUpdateEvent = new vscode.EventEmitter<void>();
        const globalState = new AugmentGlobalState(context);
        const syncingStateTracker = new SyncingEnabledTracker();
        const mockAssetManager = new AssetManager(context);

        const extension = new AugmentExtension(
            context,
            globalState,
            config,
            apiServer,
            auth,
            recentCompletions,
            recentInstructions,
            recentNextEditLocations,
            recentChats,
            nextEditWebViewEvent,
            extensionUpdateEvent,
            new MainPanelWebviewProvider(context.extensionUri),
            new vscode.EventEmitter<MainPanelApp>(),
            new ActionsModel(globalState),
            syncingStateTracker,
            new vscode.EventEmitter<ChatExtensionMessage>(),
            new OnboardingSessionEventReporter(apiServer),
            mockAssetManager
        );
        const statusBar = new StatusBarManager();
        const metrics = new ClientMetricsReporter(apiServer);
        const completionsModel = new CompletionsModel(extension, config, metrics);
        const timelineReporter = new ClientCompletionTimelineReporter(apiServer);

        await extension.enable();

        return new InlineProviderTestKit(
            extension,
            completionsModel,
            apiServer,
            statusBar,
            mockStatusBar,
            config,
            timelineReporter
        );
    }
}

type AssertCompletionOptions = {
    range?: Range;
    wantCompletions?: boolean;
    insertText?: string;
    prefix?: string;
    suffix?: string;
};

describe("InlineCompletionProvider.provideInlineCompletionItems", () => {
    let kit: InlineProviderTestKit;

    beforeEach(async () => {
        jest.useFakeTimers();
        resetMockWorkspace();
        kit = await InlineProviderTestKit.create();
    });

    afterEach(() => {
        kit.dispose();
        resetMockWorkspace();
        jest.clearAllMocks();
        jest.useRealTimers();
    });

    it("should provide completions for a new invoke inline completion with auto completion config true", async () => {
        const timelineSpy = jest.spyOn(kit.timelineReporter, "reportCompletionTimeline");

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        kit.assertCompletion(result);

        const now = Date.now();
        expect(timelineSpy).toHaveBeenCalledTimes(1);
        expect(timelineSpy).toHaveBeenCalledWith("request.123", {
            emitTime: now,
            requestStart: now,
            rpcEnd: now,
            rpcStart: now,
        });
    });

    it("should provide completions for a new invoke inline completion with auto completion config false", async () => {
        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        kit.assertCompletion(result);
    });

    it("should provide completions for a new automatic inline completion with auto completion config true", async () => {
        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Automatic,
            },
            new CancellationToken()
        );

        kit.assertCompletion(result);
    });

    it("should return no completions for a new automatic inline completion with auto completion config false", async () => {
        mockWorkspaceConfigChange(
            generateMockWorkspaceConfig({
                completions: {
                    enableAutomaticCompletions: false,
                },
            })
        );

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Automatic,
            },
            new CancellationToken()
        );

        expect(result.length).toBe(0);
    });

    it("return previous completion without selected text", async () => {
        // Trigger an initial completion so the next request
        // can be re-used if appropriate
        await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        kit.mockEventListener.mockClear();

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        kit.assertCompletion(result);
    });

    it("should handle cancellation and not emit a completion request", async () => {
        const cancelToken = new CancellationToken();
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: any,
                _language: string,
                _context: any,
                _recentChanges: ReplacementText[]
            ): Promise<CompletionResult> => {
                /**
                 * Trigger cancellation before returning completions
                 */
                cancelToken.cancel();

                return kit.apiCompletionResult;
            }
        );

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            cancelToken
        );
        expect(result.length).toBe(0);
        expect(kit.mockEventListener).toHaveBeenCalledTimes(0);
    });

    it("return completions when text from a completion pop-up item matches in default mode", async () => {
        // Trigger an initial completion so the next request
        // can be re-used if appropriate
        await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        kit.mockEventListener.mockClear();

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                // For completions from a pop-up the text in the selected
                // completion info will be populated
                selectedCompletionInfo: {
                    text: kit.apiCompletionResult.completionItems[0].text,
                    range: new Range(0, 0, 0, 0),
                },
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        expect(result.length).toBe(1);
        expect(kit.mockEventListener).toHaveBeenCalledTimes(1);
    });

    it("return no completions for text from a completion pop-up item in default mode", async () => {
        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                // For completions from a pop-up the text in the selected
                // completion info will be populated
                selectedCompletionInfo: {
                    text: "example",
                    range: new Range(0, 0, 0, 0),
                },
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        expect(result.length).toBe(0);
        expect(kit.mockEventListener).toHaveBeenCalledTimes(1);
    });

    it("return previous completion from model when context has selected text", async () => {
        // Trigger an initial completion so the next request
        // can be re-used if appropriate
        await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        kit.mockEventListener.mockClear();

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: {
                    text: "example",
                    range: new Range(0, 0, 0, 0),
                },
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        kit.assertCompletion(result, {
            wantCompletions: true,
            range: new Range(0, 0, 0, 0),
        });
    });

    it("return previous completion from model when selected text matches", async () => {
        const selectedRange = new Range(0, 0, 0, 0);

        // Trigger an initial completion so the next request
        // can be re-used if appropriate
        await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        kit.mockEventListener.mockClear();

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: {
                    // Ensure the selected text matches the previous completion
                    text: kit.apiCompletionResult.completionItems[0].text,
                    range: selectedRange,
                },
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        kit.assertCompletion(result, {
            range: selectedRange,
            wantCompletions: true,
        });
    });

    it("handle completion include text before the completion start", async () => {
        const wordPrefix = "m";
        kit.document = kit.createAndOpenFile("/example/file", `  ${wordPrefix}`);
        // Start selected range at 2 (before the m)
        const selectedRange = new Range(0, 2, 0, 2);
        // Start position at 3 (after the m)
        kit.position = new Position(0, 3);

        kit.apiCompletionResult = {
            completionItems: [
                {
                    text: "odel = 'hi';",
                    suffixReplacementText: "",
                    skippedSuffix: "",
                },
            ],
            unknownBlobNames: [],
            checkpointNotFound: false,
        };

        // Trigger an initial completion so the next request
        // can be re-used if appropriate
        await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        kit.mockEventListener.mockClear();

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: {
                    // Ensure the selected text matches the previous completion
                    text: wordPrefix + kit.apiCompletionResult.completionItems[0].text,
                    range: selectedRange,
                },
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        kit.assertCompletion(result, {
            insertText: wordPrefix + kit.apiCompletionResult.completionItems[0].text,
            range: new Range(0, 2, 0, 3),
            prefix: `  m`,
            suffix: "",
            wantCompletions: true,
        });
    });

    it("handle completion where request is in middle of a completion", async () => {
        // Start after "console."
        const completionText = "console.log('hello world');";
        kit.apiCompletionResult = {
            completionItems: [
                {
                    text: completionText,
                    suffixReplacementText: "",
                    skippedSuffix: "",
                },
            ],
            unknownBlobNames: [],
            checkpointNotFound: false,
        };

        // Trigger an initial completion so the next request
        // can be re-used if appropriate
        await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        kit.mockEventListener.mockClear();

        // Make penging-completions think the document has `console.`
        const editEvent = kit.document.insert(0, completionText.slice(0, 8));
        publishTextDocumentChange(editEvent);

        const selectedRange = new Range(0, 8, 0, 8);
        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            new Position(0, 8),
            {
                selectedCompletionInfo: {
                    // Ensure the selected text matches the previous completion
                    text: "log('hello world');",
                    range: selectedRange,
                },
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );

        kit.assertCompletion(result, {
            insertText: "log('hello world');",
            range: new Range(0, 8, 0, 8),
            wantCompletions: true,
        });
    });

    it("handle skip completion error", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: any,
                _language: string,
                _context: any,
                _recentChanges: ReplacementText[]
            ): Promise<CompletionResult> => {
                expect(kit.statusBar.tooltip).toBe(generatingCompletion.tooltip);
                /**
                 * Trigger skip completion error
                 */
                throw new SkipCompletion("Injected error");
            }
        );

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        expect(result.length).toBe(0);
        expect(kit.mockEventListener).toHaveBeenCalledTimes(1);
        expect(kit.statusBar.tooltip).toBe(enabled.tooltip);
    });

    it("handle unexpected error", async () => {
        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: any,
                _language: string,
                _context: any,
                _recentChanges: ReplacementText[]
            ): Promise<CompletionResult> => {
                expect(kit.statusBar.tooltip).toBe(generatingCompletion.tooltip);

                /**
                 * Trigger unexpected error
                 */
                throw new Error("Injected error");
            }
        );

        const result = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        expect(result.length).toBe(0);
        expect(kit.mockEventListener).toHaveBeenCalledTimes(1);
        expect(kit.statusBar.tooltip).toBe(completionFailed.tooltip);

        kit.mockEventListener.mockClear();

        kit.apiServer.complete = jest.fn(
            async (
                _requestId: string,
                _prefix: string,
                _suffix: string,
                _path: string,
                _blobName: string | undefined,
                _completionLocation: any,
                _language: string,
                _context: any,
                _recentChanges: ReplacementText[]
            ): Promise<CompletionResult> => {
                expect(kit.statusBar.tooltip).toBe(generatingCompletion.tooltip);

                /**
                 * Trigger skip completion error
                 */
                throw new SkipCompletion("Injected error");
            }
        );

        const result2 = await kit.provider.provideInlineCompletionItems(
            kit.document,
            kit.position,
            {
                selectedCompletionInfo: undefined,
                triggerKind: InlineCompletionTriggerKind.Invoke,
            },
            new CancellationToken()
        );
        expect(result2.length).toBe(0);
        expect(kit.mockEventListener).toHaveBeenCalledTimes(1);
        expect(kit.statusBar.tooltip).toBe(enabled.tooltip);
    });
});
