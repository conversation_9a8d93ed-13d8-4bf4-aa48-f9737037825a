<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin require-restart="true">
  <id>com.augmentcode</id>
  <name>Augment</name>
  <vendor>Augment Computing</vendor>

  <dependencies>
    <plugin id="com.intellij.modules.platform"/>
    <plugin id="com.jetbrains.sh"/>
    <plugin id="org.jetbrains.plugins.terminal"/>
    <plugin id="com.intellij.modules.lang"/> <!-- to make incompatible with Gateway Client -->
    <module name="intellij.platform.collaborationTools"/> <!-- for OAuth support -->
  </dependencies>

  <resource-bundle>messages.AugmentBundle</resource-bundle>

  <extensions defaultExtensionNs="com.intellij">
    <notificationGroup
      id="augment.plugin.updates"
      displayType="STICKY_BALLOON"
      key="augment.notification.group.name"
      bundle="messages.AugmentBundle"
    />
    <applicationService
      serviceImplementation="com.augmentcode.intellij.idea.AugmentPluginUpdater"/>

    <toolWindow id="Augment" anchor="right"
                icon="AugmentIcons.StatusInitial"
                factoryClass="com.augmentcode.intellij.chat.AugmentChatToolWindowFactory"/>

    <postStartupActivity implementation="com.augmentcode.intellij.index.AugmentValidateIndexActivity"/>
    <postStartupActivity implementation="com.augmentcode.intellij.chat.AugmentChatProjectActivity"/>
    <postStartupActivity implementation="com.augmentcode.intellij.idea.AugmentPostStartupActivity"/>

    <applicationService
      serviceImplementation="com.augmentcode.intellij.settings.AugmentSettings"/>
    <applicationService
      serviceImplementation="com.augmentcode.intellij.settings.AugmentIntegrationsConfig"/>
    <applicationService
      serviceInterface="com.augmentcode.intellij.api.AugmentAPI"
      serviceImplementation="com.augmentcode.intellij.api.AugmentAPIImpl"/>
    <applicationService
      serviceInterface="com.augmentcode.intellij.api.HttpClientProvider"
      serviceImplementation="com.augmentcode.intellij.api.HttpClientProviderImpl"/>
    <applicationService
      serviceInterface="com.augmentcode.intellij.settings.PluginVersionProvider"
      serviceImplementation="com.augmentcode.intellij.settings.PluginVersionProviderImpl"/>
    <applicationConfigurable
      parentId="editor"
      id="com.augmentcode.intellij.settings"
      instance="com.augmentcode.intellij.settings.AugmentSettingsConfigurable"
      displayName="Augment"/>
    <statusBarWidgetFactory
      implementation="com.augmentcode.intellij.status.AugmentStatusBarWidgetFactory"
      id="AugmentStatusBarWidget"/>

    <projectService
      serviceInterface="com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager"
      serviceImplementation="com.augmentcode.intellij.syncing.AugmentRemoteSyncingManagerImpl"/>
    <projectService serviceInterface="com.augmentcode.intellij.webviews.AugmentMessagingService"
                    serviceImplementation="com.augmentcode.intellij.webviews.chat.ChatMessagingService"/>
    <projectService serviceImplementation="com.augmentcode.intellij.metrics.OnboardingSessionEventReporter"/>
    <projectService serviceImplementation="com.augmentcode.intellij.index.AugmentEditorHistoryService"/>

    <fileBasedIndex
      implementation="com.augmentcode.intellij.index.AugmentLocalIndex"/>
    <fileBasedIndex
      implementation="com.augmentcode.intellij.guidelines.AugmentWorkspaceGuidelinesIndex"/>

    <inline.completion.provider order="first"
      implementation="com.augmentcode.intellij.completion.AugmentCompletionProvider"/>

    <inline.completion.element.manipulator
      implementation="com.augmentcode.intellij.completion.AugmentCompletionElementManipulator"
      order="first"/>

    <httpRequestHandler implementation="com.augmentcode.intellij.auth.AugmentOAuthCallbackHandler"/>
    <notificationGroup id="augment.notifications" displayType="BALLOON" isLogByDefault="false"/>

    <editorFactoryListener implementation="com.augmentcode.intellij.index.AugmentEditorFocusListener"/>
    <editorFactoryListener implementation="com.augmentcode.intellij.chat.AugmentChatSelectionListener"/>
    <diff.DiffExtension implementation="com.augmentcode.intellij.listeners.SmartPasteDiffExtension"/>
  </extensions>

  <projectListeners>
    <listener class="com.augmentcode.intellij.listeners.AugmentVFSListener"
              topic="com.intellij.openapi.vfs.newvfs.BulkFileListener"/>
  </projectListeners>

  <applicationListeners>
    <listener class="com.augmentcode.intellij.completion.AugmentInlineCompletionListener"
              topic="com.intellij.codeInsight.inline.completion.InlineCompletionInstallListener"/>
  </applicationListeners>

  <actions>
    <action id="com.augmentcode.intellij.actions.ShowSettingsAction"
            class="com.augmentcode.intellij.actions.ShowSettingsAction" text="Plugin Settings"
            description="Show settings for the Augment plugin"/>
    <action id="com.augmentcode.intellij.actions.OpenSettingsWebviewAction"
            class="com.augmentcode.intellij.actions.OpenSettingsWebviewAction"
            icon="AllIcons.General.Settings"
            text="Tools Settings"
            description="Open Augment Tools Settings">
    </action>
    <action id="com.augmentcode.intellij.guidelines.OpenWorkspaceGuidelinesCommand"
            class="com.augmentcode.intellij.guidelines.OpenWorkspaceGuidelinesCommand"
            text="Edit Workspace Guidelines for Augment"
            description="Open the workspace guidelines file for editing">
    </action>
    <action id="com.augmentcode.intellij.actions.ExtensionStatusAction"
            class="com.augmentcode.intellij.actions.ExtensionStatusAction" text="Show Extension Status"
            description="Show the status of the extension. This can be helpful for debugging."/>
    <action id="com.augmentcode.intellij.actions.SignInAction" class="com.augmentcode.intellij.actions.SignInAction"
            text="Sign In" description="Sign in to Augment"/>
    <action id="com.augmentcode.intellij.actions.SignOutAction" class="com.augmentcode.intellij.actions.SignOutAction"
            text="Sign Out" description="Sign out of Augment"/>
    <action id="com.augmentcode.intellij.actions.ShowChatAction"
            class="com.augmentcode.intellij.actions.ShowChatAction" text="Show Chat"
            description="Show the Augment Chat panel">
      <keyboard-shortcut keymap="$default" first-keystroke="control alt I"/>
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta control I"/>
    </action>
    <action id="com.augmentcode.intellij.actions.ShowHistoryAction"
            class="com.augmentcode.intellij.actions.ShowHistoryAction" text="Show History"
            description="Show the Augment History panel">
      <keyboard-shortcut keymap="$default" first-keystroke="control alt 7"/>
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta control 7"/>
    </action>"
    <action id="com.augmentcode.intellij.actions.ToggleCompletionsAction"
            class="com.augmentcode.intellij.actions.ToggleCompletionsAction" text="Toggle Completions"
            description="Toggle automatic inline completions on/off">
      <keyboard-shortcut keymap="$default" first-keystroke="control alt 9"/>
      <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta control 9"/>
    </action>
    <action id="com.augmentcode.intellij.actions.GenerateSyncReport"
            class="com.augmentcode.intellij.actions.GenerateSyncReport"
            text="Generate Sync Report"
            description="Generate a report of synced files and stats">
    </action>
    <action id="com.augmentcode.intellij.actions.ManageAccountAction"
            class="com.augmentcode.intellij.actions.ManageAccountAction"
            text="Manage Account"
            description="Open the Augment account management page in browser">
    </action>
    <action id="com.augmentcode.intellij.actions.ReindexAction"
            class="com.augmentcode.intellij.actions.ReindexAction"
            text="Rebuild Augment Index"
            description="Rebuild the Augment local index. This can be helpful when the index gets out of sync.">
    </action>
  </actions>
</idea-plugin>
