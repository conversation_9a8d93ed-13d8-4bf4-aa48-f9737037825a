package com.augmentcode.intellij.chat

import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.CredentialsChangeListener
import com.augmentcode.intellij.auth.CredentialsMessageBusWrapper
import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.webviews.chat.ChatMessagingService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class AugmentChatProjectActivity(private val cs: CoroutineScope) : ProjectActivity {
  private val credMsgBus = CredentialsMessageBusWrapper(cs)

  override suspend fun execute(project: Project) {
    if (ApplicationManager.getApplication().isUnitTestMode) return

    val log = thisLogger()
    // Send initial guidelines state if feature is enabled
    val featureFlagManager = FeatureFlagManager.instance
    if (featureFlagManager.userGuidelinesEnabled()) {
      log.info("Sending initial guidelines state from project activity")
      sendInitialGuidelinesState(project)
    }

    credMsgBus.subscribe(
      project,
      object : CredentialsChangeListener {
        override fun onChange(credentials: AugmentCredentials?) {
          // Send guidelines state when credentials change
          if (credentials != null && featureFlagManager.userGuidelinesEnabled()) {
            sendInitialGuidelinesState(project)
            log.info("Sending initial guidelines state after credentials change")
          }
        }
      },
    )
  }

  /**
   * Sends the initial guidelines state to the webview.
   */
  private fun sendInitialGuidelinesState(project: Project) {
    val messagingService = ChatMessagingService.getInstance(project)
    val featureFlagManager = FeatureFlagManager.instance
    // Only send if the feature is enabled
    if (featureFlagManager.userGuidelinesEnabled()) {
      // Send the guidelines state to the webview
      DumbService.getInstance(project).runWhenSmart {
        cs.launch(Dispatchers.IO) {
          thisLogger().warn("Sending initial guidelines state on EDT from dumb service runWhenSmart")
          messagingService.sendGuidelinesStateToWebview()
        }
      }
    }
  }
}
