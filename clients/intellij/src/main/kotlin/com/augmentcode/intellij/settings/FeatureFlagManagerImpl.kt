package com.augmentcode.intellij.settings

import com.augmentcode.api.FeatureFlags
import com.augmentcode.api.SmartPastePrecomputeMode
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.utils.SemVer
import com.google.gson.Gson
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.extensions.PluginId
import kotlinx.coroutines.runBlocking
import org.jetbrains.annotations.VisibleForTesting

/**
 * Note about feature flag version check:
 * If the server sends back null or empty, that means our flag is automatically false.
 * If the server sends back 0.0.0, that means our flag is automatically true because
 * our local plugin version always resolves to at least 0.0.0
 * For any other values from the server we will actually do the comparison.
 * Note that stuff following the SemVer triple will be dropped, so 0.0.1-beta == 0.0.1
 */
@Service(Service.Level.APP)
class FeatureFlagManagerImpl : FeatureFlagManager {
  private val logger = thisLogger()
  private val gson = Gson()

  @VisibleForTesting
  override fun fetchAndCacheFeatureFlags(): Boolean {
    return runBlocking {
      featureFlagsFromApi() != null
    }
  }

  override fun forceCompletionEnabled(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijForceCompletionMinVersion)
  }

  override fun enableExternalSourcesInChat(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.enableExternalSourcesInChat == true
  }

  override fun shareServiceEnabled(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijShareMinVersion)
  }

  override fun useNewThreadsMenu(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijNewThreadsMenuMinVersion)
  }

  override fun enableChatMermaidDiagrams(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijEnableChatMermaidDiagramsMinVersion)
  }

  override fun smallSyncThreshold(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.smallSyncThreshold ?: FeatureFlags.DEFAULT_SMALL_SYNC_THRESHOLD
  }

  override fun bigSyncThreshold(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.bigSyncThreshold ?: FeatureFlags.DEFAULT_BIG_SYNC_THRESHOLD
  }

  override fun enableShowSummary(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.intellijShowSummary ?: false
  }

  override fun enableSmartPaste(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijSmartPasteMinVersion)
  }

  override fun smartPastePrecomputeMode(waitForNetwork: Boolean): SmartPastePrecomputeMode {
    return SmartPastePrecomputeMode.fromString(featureFlags(waitForNetwork)?.smartPastePrecomputeMode)
  }

  override fun chatWithToolsEnabled(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijChatWithToolsMinVersion)
  }

  override fun agentModeEnabled(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijAgentModeMinVersion)
  }

  override fun enableDesignSystemRichTextEditor(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijDesignSystemRichTextEditorMinVersion)
  }

  override fun enableBackgroundAgents(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijBackgroundAgentsMinVersion)
  }

  override fun askForSyncPermission(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijAskForSyncPermissionMinVersion)
  }

  override fun chatMultimodalEnabled(waitForNetwork: Boolean): Boolean {
    return currentPluginVersionIsAtLeast(featureFlags(waitForNetwork)?.intellijChatMultimodalMinVersion)
  }

  override fun memoriesParams(waitForNetwork: Boolean): String {
    return featureFlags(waitForNetwork)?.memoriesParams ?: "{}"
  }

  override fun guidelinesEnabled(waitForNetwork: Boolean): Boolean =
    featureFlags(waitForNetwork)?.enableGuidelines == true &&
      featureFlags(waitForNetwork)?.intellijEnableWorkspaceGuidelines ?: false

  override fun userGuidelinesEnabled(waitForNetwork: Boolean): Boolean =
    guidelinesEnabled(waitForNetwork) &&
      (featureFlags(waitForNetwork)?.intellijEnableUserGuidelines ?: false)

  override fun userGuidelinesInSettingsEnabled(waitForNetwork: Boolean): Boolean =
    userGuidelinesEnabled(waitForNetwork) &&
      (featureFlags(waitForNetwork)?.intellijUserGuidelinesInSettings ?: false)

  override fun getUserGuidelinesLengthLimit(waitForNetwork: Boolean): Int =
    featureFlags(waitForNetwork)?.userGuidelinesLengthLimit ?: FeatureFlags.DEFAULT_USER_GUIDELINES_LENGTH_LIMIT

  override fun getWorkspaceGuidelinesLengthLimit(waitForNetwork: Boolean): Int =
    featureFlags(waitForNetwork)?.workspaceGuidelinesLengthLimit ?: FeatureFlags.DEFAULT_WORKSPACE_GUIDELINES_LENGTH_LIMIT

  override fun enableHomespunGitignore(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.intellijEnableHomespunGitignore ?: false
  }

  override fun enableAgentAutoMode(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.enableAgentAutoMode ?: false
  }

  override fun promptEnhancerEnabled(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.intellijPromptEnhancerEnabled ?: false
  }

  override fun sentryEnabled(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.intellijEnableSentry ?: false
  }

  override fun webviewErrorSamplingRate(waitForNetwork: Boolean): Double {
    return featureFlags(waitForNetwork)?.intellijWebviewErrorSamplingRate ?: 0.0
  }

  override fun pluginErrorSamplingRate(waitForNetwork: Boolean): Double {
    return featureFlags(waitForNetwork)?.intellijPluginErrorSamplingRate ?: 0.0
  }

  override fun webviewTraceSamplingRate(waitForNetwork: Boolean): Double {
    return featureFlags(waitForNetwork)?.intellijWebviewTraceSamplingRate ?: 0.0
  }

  override fun pluginTraceSamplingRate(waitForNetwork: Boolean): Double {
    return featureFlags(waitForNetwork)?.intellijPluginTraceSamplingRate ?: 0.0
  }

  override fun enableWebviewPerformanceMonitoring(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.intellijEnableWebviewPerformanceMonitoring ?: false
  }

  // Sidecar feature flags
  override fun agentEditTool(waitForNetwork: Boolean): String {
    return featureFlags(waitForNetwork)?.agentEditTool ?: ""
  }

  override fun agentEditToolMinViewSize(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.agentEditToolMinViewSize ?: 0
  }

  override fun agentEditToolSchemaType(waitForNetwork: Boolean): String {
    return featureFlags(waitForNetwork)?.agentEditToolSchemaType ?: ""
  }

  override fun agentEditToolEnableFuzzyMatching(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.agentEditToolEnableFuzzyMatching ?: false
  }

  override fun agentEditToolFuzzyMatchSuccessMessage(waitForNetwork: Boolean): String {
    return featureFlags(waitForNetwork)?.agentEditToolFuzzyMatchSuccessMessage ?: ""
  }

  override fun agentEditToolFuzzyMatchMaxDiff(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.agentEditToolFuzzyMatchMaxDiff ?: 0
  }

  override fun agentEditToolFuzzyMatchMaxDiffRatio(waitForNetwork: Boolean): Double {
    return featureFlags(waitForNetwork)?.agentEditToolFuzzyMatchMaxDiffRatio ?: 0.0
  }

  override fun agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs ?: 0
  }

  override fun agentEditToolInstructionsReminder(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.agentEditToolInstructionsReminder ?: false
  }

  override fun agentEditToolShowResultSnippet(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.agentEditToolShowResultSnippet ?: true
  }

  override fun agentEditToolMaxLines(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.agentEditToolMaxLines ?: 200
  }

  override fun agentSaveFileToolInstructionsReminder(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.agentSaveFileToolInstructionsReminder ?: false
  }

  override fun grepSearchToolEnable(waitForNetwork: Boolean): Boolean {
    return featureFlags(waitForNetwork)?.grepSearchToolEnable ?: false
  }

  override fun grepSearchToolTimelimitSec(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.grepSearchToolTimelimitSec ?: 10
  }

  override fun grepSearchToolOutputCharsLimit(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.grepSearchToolOutputCharsLimit ?: 5000
  }

  override fun grepSearchToolNumContextLines(waitForNetwork: Boolean): Int {
    return featureFlags(waitForNetwork)?.grepSearchToolNumContextLines ?: 5
  }

  private fun featureFlags(waitForNetwork: Boolean): FeatureFlags? {
    return if (waitForNetwork) {
      runBlocking {
        featureFlagsFromApi()
      }
    } else {
      AugmentAPI.instance.getCachedModelInfo()?.featureFlags
    }
  }

  private suspend fun featureFlagsFromApi(): FeatureFlags? {
    try {
      return if (AugmentAPI.instance.available()) {
        AugmentAPI.instance.fetchModelInfo()?.featureFlags
      } else {
        null
      }
    } catch (ex: Exception) {
      logger.warn("Failed to get feature flags from API", ex)
      return null
    }
  }

  /**
   * returns true if the argument minVersionStr is less than or equal to the current plugin version
   * returns false if the argument is null or blank
   */
  internal fun currentPluginVersionIsAtLeast(minVersionStr: String?): Boolean {
    return try {
      if (minVersionStr.isNullOrBlank()) {
        return false
      }
      currentPluginVersion() >= SemVer(minVersionStr)
    } catch (e: Exception) {
      logger.warn("Failed evaluate feature flag from version string: $minVersionStr", e)
      false
    }
  }

  private fun currentPluginVersion(): SemVer {
    return service<PluginVersionProvider>().currentPluginVersion().let {
      try {
        SemVer(it?.takeIf { !it.isNullOrEmpty() } ?: "0.0.0")
      } catch (e: IllegalArgumentException) {
        logger.warn("Failed to parse plugin version:", e)
        SemVer("0.0.0")
      }
    }
  }
}

// Just for easy mocking purposes in test
interface PluginVersionProvider {
  // we return a String? instead of a SemVer so that we can put the
  // logic in the FeatureFlagManager and test it
  fun currentPluginVersion(): String?

  fun isBeta(): Boolean
}

class PluginVersionProviderImpl : PluginVersionProvider {
  override fun currentPluginVersion(): String? {
    val plugin = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
    return plugin?.version
  }

  override fun isBeta(): Boolean {
    val plugin = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
    return plugin?.url?.contains("beta") == true
  }
}
