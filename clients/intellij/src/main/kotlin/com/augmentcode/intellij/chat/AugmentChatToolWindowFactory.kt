package com.augmentcode.intellij.chat

import com.augmentcode.intellij.AugmentBundle
import com.augmentcode.intellij.utils.AugmentToolWindowType
import com.augmentcode.intellij.utils.TOOL_WINDOW_TYPE_KEY
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import icons.AugmentIcons

class AugmentChatToolWindowFactory : ToolWindowFactory, DumbAware {
  override fun shouldBeAvailable(project: Project): Boolean {
    return true
  }

  override fun createToolWindowContent(
    project: Project,
    toolWindow: ToolWindow,
  ) {
    if (project.isDisposed) {
      thisLogger().warn("Project is disposed, cannot create tool window")
      return
    }

    toolWindow.setIcon(AugmentIcons.StatusInitial)
    toolWindow.title = AugmentBundle.message("chat.toolwindow.title")
    toolWindow.stripeTitle = AugmentBundle.message("chat.toolwindow.title")
    toolWindow.setTitleActions(
      listOfNotNull(ActionManager.getInstance().getAction("com.augmentcode.intellij.actions.OpenSettingsWebviewAction")),
    )
    val contentManager = toolWindow.contentManager
    val chatToolWindow = AugmentChatToolWindow(project)
    val content =
      contentManager.factory.createContent(
        chatToolWindow,
        null,
        true,
      ).apply {
        // don't allow closing chat content so only the whole window can be hidden
        // to hide the chat
        isCloseable = false
      }
    content.putUserData(TOOL_WINDOW_TYPE_KEY, AugmentToolWindowType.CHAT)
    contentManager.addContent(content)
  }
}
