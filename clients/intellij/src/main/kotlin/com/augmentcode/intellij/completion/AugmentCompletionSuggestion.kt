package com.augmentcode.intellij.completion

import com.augmentcode.api.CompletionRequest
import com.augmentcode.api.RecencyInfo
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.DEFAULT_PREFIX_SIZE
import com.augmentcode.intellij.api.DEFAULT_SUFFIX_SIZE
import com.augmentcode.intellij.history.AugmentHistoryEntry
import com.augmentcode.intellij.history.AugmentHistoryModel
import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.index.AugmentEditorHistoryService
import com.augmentcode.intellij.index.AugmentRoot
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.status.StateDefinitions
import com.augmentcode.intellij.status.StateManager
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.codeInsight.inline.completion.InlineCompletionRequest
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionElement
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionSkipTextElement
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionSuggestion
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionVariant
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.TextRange
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.asFlow
import org.jetbrains.annotations.VisibleForTesting

// How long we display a status that no completions were found
const val NO_COMPLETION_STATUS_DURATION_MS: Long = 2000

val AugmentLastCompletionElementKey = Key<AugmentCompletionElement>("AugmentLastCompletionElement")

class AugmentCompletionSuggestion(
  private val inlineCompletionRequest: InlineCompletionRequest,
) : InlineCompletionSuggestion {
  private val logger = thisLogger()
  private var stateDisposal: () -> Unit = {}
  private val project = inlineCompletionRequest.editor.project
  private val historyModel: AugmentHistoryModel? = if (project != null) AugmentHistoryModel.getInstance(project) else null

  override suspend fun getVariants(): List<InlineCompletionVariant> {
    try {
      stateDisposal()
      inlineCompletionRequest.editor.project?.let { project ->
        stateDisposal = StateManager.getInstance(project).setState(StateDefinitions.GeneratingCompletion)
      }
      val completionList = getCompletion()
      if (completionList.isEmpty()) {
        inlineCompletionRequest.editor.project?.let { project ->
          val zeroDisposal = StateManager.getInstance(project).setState(StateDefinitions.NoCompletions)
          val cs = CoroutineScope(Dispatchers.IO)
          cs.launch {
            delay(NO_COMPLETION_STATUS_DURATION_MS)
            zeroDisposal()
          }
        }
      }
      return completionList
    } finally {
      stateDisposal()
    }
  }

  private suspend fun getCompletion(): List<InlineCompletionVariant> {
    val api = AugmentAPI.instance
    if (!api.available()) {
      return emptyList()
    }

    try {
      logger.debug("Retrieving new inline completion")
      val completionRequest = buildAugmentCompletionRequest() ?: return emptyList()
      val completionResult = api.complete(completionRequest) ?: return emptyList()

      val unknownMemories = completionResult.unknownMemoryNames
      if (unknownMemories != null && unknownMemories.isNotEmpty()) {
        // we can't run write actions here, so we first need to collect affected files in a read action
        // and then submit a reindexing job to the write thread for later.
        // once re-indexing is in process IntelliJ will stop calling this provider until re-indexing is done
        // this way we are avoiding an endless loop of re-indexing.
        logger.info("Server reported unknown blobs: ${unknownMemories.size}")
        AugmentBlobStateReader.requestInvalidation(inlineCompletionRequest.file.project, unknownMemories.toSet())
      }
      val completionItem = completionResult.completionItems?.firstOrNull() ?: return emptyList()

      if (completionItem.text.isBlank()) {
        return emptyList()
      }

      historyModel?.addCompletionAsync(
        AugmentHistoryEntry(
          completionRequest,
          completionResult,
          System.currentTimeMillis(),
        ),
      )

      // Stash completion in user data to retrieve them if rejected and report when shown to the user
      inlineCompletionRequest.editor.putUserData(
        AugmentLastCompletionElementKey,
        AugmentCompletionElement(0, completionItem.text, completionResult.requestId),
      )

      val textElement = AugmentCompletionElement(0, completionItem.text, completionResult.requestId)
      val elementList = mutableListOf<InlineCompletionElement>(textElement)
      if (completionItem.skippedSuffix != null && completionItem.skippedSuffix.isNotEmpty()) {
        if (completionItem.suffixReplacementText.isEmpty() ||
          completionItem.suffixReplacementText.startsWith(
            completionItem.skippedSuffix,
          )
        ) {
          elementList += InlineCompletionSkipTextElement(completionItem.skippedSuffix)
          val replacementText = completionItem.suffixReplacementText.removePrefix(completionItem.skippedSuffix)
          if (replacementText.isNotEmpty()) {
            elementList += AugmentCompletionElement(0, replacementText, completionResult.requestId)
          }
        }
      }
      val completeCompletionVariant =
        InlineCompletionVariant.build(
          elements = elementList.asFlow(),
        )

      return listOf(completeCompletionVariant)
    } catch (e: Exception) {
      val isCancellation = e is CancellationException || e.cause is CancellationException
      if (isCancellation) {
        // Completions can be cancelled by IntelliJ if the user types while we are waiting for a completion result.
        logger.debug("Completion request was cancelled: ${e.message}")
      } else {
        logger.warn("Completion request failed: ${e.message}", e)
      }
      return emptyList()
    }
  }

  @VisibleForTesting
  suspend fun buildAugmentCompletionRequest(): CompletionRequest? {
    val document = inlineCompletionRequest.document
    val file = inlineCompletionRequest.file

    // result will be pasted after this offset
    val offset = inlineCompletionRequest.endOffset

    val completionRequest = CompletionRequest()
    completionRequest.model = AugmentSettings.instance.modelName ?: ""

    // TextRange has end exclusive
    fun ensureDocumentRange(offset: Int): Int {
      if (document.textLength == 0) return 0
      if (offset < 0) return 0
      if (offset > document.textLength) return document.textLength
      return offset
    }

    val model = AugmentAPI.instance.currentModel() // will return the default one if settings are not set
    val characterOfContextPrefix = model?.suggestedPrefixCharCount ?: DEFAULT_PREFIX_SIZE
    val startPrefixOffset = ensureDocumentRange(offset - characterOfContextPrefix)
    // prompt is a prefix of the line
    val prefixWithRange = getTextFromDocument(document, startPrefixOffset, offset)
    completionRequest.prompt = prefixWithRange.text
    completionRequest.prefixBegin = prefixWithRange.range.startOffset

    val characterOfContextSuffix = model?.suggestedSuffixCharCount ?: DEFAULT_SUFFIX_SIZE
    val endSuffixOffset = ensureDocumentRange(inlineCompletionRequest.endOffset + characterOfContextSuffix)
    val suffixWithRange = getTextFromDocument(document, inlineCompletionRequest.endOffset, endSuffixOffset)
    completionRequest.suffix = suffixWithRange.text
    completionRequest.suffixEnd = suffixWithRange.range.endOffset

    completionRequest.cursorPosition = offset

    completionRequest.path = AugmentRoot.findRelativePath(file.project, file.virtualFile) ?: return null

    val blobState = AugmentBlobStateReader.read(file)
    completionRequest.blobName = blobState?.remoteName // can be null

    completionRequest.lang = AugmentAPI.instance.fetchModelInfo()?.findLanguage(file.virtualFile.extension)

    completionRequest.blobs = AugmentRemoteSyncingManager.getInstance(file.project).synchronizedBlobsPayload()
    completionRequest.recencyInfo = calculateRecencyInfo(file.project)

    return completionRequest
  }

  data class TextWithRange(val text: String, val range: TextRange)

  private fun getTextFromDocument(
    document: Document,
    startOffset: Int,
    endOffset: Int,
  ): TextWithRange {
    var textRange = TextRange(startOffset, endOffset)
    var text = document.getText(textRange)
    if (text.isEmpty()) return TextWithRange(text, textRange)

    // We are breaking a Unicode character at the start of the string
    if (text.first().isLowSurrogate()) {
      textRange = TextRange(startOffset + 1, endOffset)
      text = document.getText(textRange)
    }

    // We are breaking a Unicode character at the end of the string
    if (text.last().isHighSurrogate()) {
      textRange = TextRange(startOffset, endOffset - 1)
      text = document.getText(textRange)
    }

    return TextWithRange(text, textRange)
  }

  private suspend fun calculateRecencyInfo(project: Project): RecencyInfo =
    RecencyInfo().apply {
      recentChanges =
        coroutineScope {
          FileEditorManager.getInstance(project).allEditors.map { editor ->
            async {
              AugmentEditorHistoryService.getInstance(project).findReplacementsSinceLastUpload(editor)
            }
          }.awaitAll().flatten()
        }
    }
}
