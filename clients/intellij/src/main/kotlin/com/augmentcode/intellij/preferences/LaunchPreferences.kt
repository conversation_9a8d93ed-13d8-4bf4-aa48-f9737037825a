package com.augmentcode.intellij.preferences

import com.augmentcode.intellij.chat.AugmentWebviewChatServiceImpl
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.rpc.AugmentChatEntry
import com.augmentcode.rpc.ChatModelReply
import com.augmentcode.rpc.ChatModelReplyData
import com.augmentcode.rpc.ChatUserMessageRequest
import com.augmentcode.rpc.PreferenceInput
import com.augmentcode.rpc.PreferencePair
import com.augmentcode.rpc.PreferenceResultRequest
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.wm.ToolWindowManager
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.forEach
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

/**
 * Wrapper for launch of preferences webview and handling of result
 */
class PreferenceComparison(
  private val project: Project,
  private val cs: CoroutineScope,
  private val chatUserMessageRequest: ChatUserMessageRequest
): Disposable {
  private val logger = thisLogger()
  val chatService = AugmentWebviewChatServiceImpl(project)
  private lateinit var preferencesVirtualFile: AugmentPreferencesWebviewEditorVirtualFile
  private val loadedDeferred = CompletableDeferred<Unit>()
  private var resultDeferred = CompletableDeferred<PreferenceResultRequest>()

  init {
    Disposer.register(AugmentDisposable.getInstance(project), this)
  }

  override fun dispose() {
    if (!resultDeferred.isCompleted) {
      resultDeferred.cancel()
    }
    if (!loadedDeferred.isCompleted) {
      loadedDeferred.cancel()
    }

    // Close the preferences file if it's open
    invokeLater {
      if (::preferencesVirtualFile.isInitialized) {
        FileEditorManager.getInstance(project).closeFile(preferencesVirtualFile)
      }
    }
  }

  suspend fun launchComparison(): Flow<ChatModelReply> {
    logger.info("Launching preferences")

    // Create and register disposal of the preferences webview
    preferencesVirtualFile = AugmentPreferencesWebviewEditorVirtualFile(
      project,
      cs,
      buildPreferencesInput(chatUserMessageRequest),
      onLoaded = { loadedDeferred.complete(Unit) },
      onResultReceived = { resultDeferred.complete(it)}
    )

    // Open the preferences panel and return the deferred containing the selected response
    val editorManager = FileEditorManager.getInstance(project)
    invokeLater {
      logger.info("Opening preferences file")
      editorManager.openFile(preferencesVirtualFile, true, true)
    }

    // Wait for panel to load before streaming chat responses
    try {
      loadedDeferred.await()
    } catch (e: CancellationException) {
      logger.warn("Failed to load preferences")
      return emptyFlow() // todo(diehuxx): send error reply
    }

    // Stream chat responses
    val (modelIdA, modelIdB) = selectModelPair() ?: throw IllegalStateException("No models available for comparison") // todo(diehuxx): gracefully send error reply
    val (chatResponseA, _) = streamResponses(modelIdA, modelIdB)

    // Wait for user to submit result
    val result = try {
      resultDeferred.await()
    } catch (e: Exception) {
      logger.warn("Failed to get preference result")
      return flowOf(chatResponseA)
    }

    logger.info("Result received: $result")
    // TODO: Select chat response based on result
    // TODO: Send selected response to api
    invokeLater {
      logger.info("Closing preferences file")
      editorManager.closeFile(preferencesVirtualFile)
    }

    // Default to chat stream A for now until we get the selection logic working
    return flowOf(chatResponseA)
  }

  /**
   * Streams competing chat responses to preferences webview and returns the cached responses as lists
   */
  private suspend fun streamResponses(modelIdA: String, modelIdB: String): Pair<ChatModelReply, ChatModelReply> {
    // Create shared flows that can be consumed multiple times (by the preferences webview then the chat service)
    val chatResponseA = chatService.chatUserMessage(chatUserMessageRequest, modelIdA)
    val chatResponseB = chatService.chatUserMessage(chatUserMessageRequest, modelIdB)

    // Col
    var textA = ""
    var textB = ""
    var requestIdA = ""
    var requestIdB = ""
    val jobA = cs.launch {
      chatResponseA.collect { reply ->
        textA += reply.data.text
        requestIdA = reply.data.requestId
      }
    }
    val jobB = cs.launch {
      chatResponseB.collect { reply ->
        textB += reply.data.text
        requestIdB = reply.data.requestId
      }
      logger.info("Stream B collection completed")
    }

    // Wait for both to complete
    jobA.join()
    jobB.join()

    // Send both responses and done message to the preferences webview
    val chatReplyA = ChatModelReply.newBuilder()
      .setData(
        ChatModelReplyData.newBuilder()
          .setRequestId(requestIdA)
          .setText(textA)
          .setStreaming(false),
      )
      .setStream("A")
      .build()
    val chatReplyB = ChatModelReply.newBuilder()
      .setData(
        ChatModelReplyData.newBuilder()
          .setRequestId(requestIdB)
          .setText(textB)
          .setStreaming(false),
      )
      .setStream("B")
      .build()
    preferencesVirtualFile.postChatStreamingMessage(chatReplyA)
    preferencesVirtualFile.postChatStreamingMessage(chatReplyB)
    preferencesVirtualFile.postStreamDone()

    return Pair(chatReplyA, chatReplyB)
  }

  private fun selectModelPair(): Pair<String, String>? {
    val eloConfig = PluginStateService.instance.context?.flags?.eloModelConfiguration ?: return null

    val highPriorityThreshold = eloConfig.highPriorityThreshold ?: 0.0

    return if (Math.random() < highPriorityThreshold) {
      selectHighPriorityModels(eloConfig.highPriorityModels)
    } else {
      selectRegularBattleModels(eloConfig.regularBattleModels)
    }
  }

  private fun selectHighPriorityModels(highPriorityModels: List<Any>?): Pair<String, String>? {
    if (highPriorityModels.isNullOrEmpty()) return null

    // Check if it's an array of pairs or flat array
    return if (highPriorityModels.isNotEmpty() && highPriorityModels[0] is List<*>) {
      // Array of pairs - select a random pair
      val pairs = highPriorityModels.filterIsInstance<List<String>>()
      val randomPair = pairs.randomOrNull()
      if (randomPair != null && randomPair.size >= 2) {
        Pair(randomPair[0], randomPair[1])
      } else null
    } else {
      // Flat array - select two random models
      val models = highPriorityModels.filterIsInstance<String>()
      if (models.size >= 2) {
        val shuffled = models.shuffled()
        Pair(shuffled[0], shuffled[1])
      } else null
    }
  }

  private fun selectRegularBattleModels(regularBattleModels: List<String>?): Pair<String, String>? {
    if (regularBattleModels.isNullOrEmpty() || regularBattleModels.size < 2) return null

    val modelA = regularBattleModels.random()
    var modelB: String
    do {
      modelB = regularBattleModels.random()
    } while (modelB == modelA && regularBattleModels.size > 1)

    return Pair(modelA, modelB)
  }

  private fun buildPreferencesInput(chatUserMessageRequest: ChatUserMessageRequest): PreferenceInput {
    val text = chatUserMessageRequest.data.text
    return PreferenceInput.newBuilder()
      .setType("Chat")
      .setData(
        PreferencePair.newBuilder()
          .setA(
            AugmentChatEntry.newBuilder()
              .setMessage(text)
              .setResponse("")
              .build(),
          )
          .setB(
            AugmentChatEntry.newBuilder()
              .setMessage(text)
              .setResponse("")
              .build(),
          )
          .build(),
      )
      .build()
  }
}