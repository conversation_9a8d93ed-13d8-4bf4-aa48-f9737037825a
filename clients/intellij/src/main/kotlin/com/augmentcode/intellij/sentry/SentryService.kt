package com.augmentcode.intellij.sentry

import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.settings.PluginVersionProvider
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import io.sentry.Breadcrumb
import io.sentry.Sentry
import io.sentry.SentryLevel

/**
 * Application-level service for initializing and managing Sentry error tracking.
 *
 * This service supports context-aware metadata collection with the following architecture:
 * - Each project gets its own SentryProjectMetadataService instance (managed as IntelliJ singletons)
 * - Metadata is collected continuously by project services but only reported when errors occur
 * - Metadata is included as contextual breadcrumbs when:
 *   - Exceptions are captured via captureException() (optionally with project context)
 *   - Error-level breadcrumbs are added via addBreadcrumb() (optionally with project context)
 * - When project context is provided, only that project's metadata is included
 * - When no project context is provided, metadata from all open projects are included
 * - This approach reduces unnecessary network traffic and Sentry quota usage
 * - Provides relevant context only when debugging errors
 * - Leverages IntelliJ's service lifecycle management (no manual registration required)
 */
@Service(Service.Level.APP)
class SentryService() : Disposable {
  private val logger = thisLogger()
  private val versionProvider = service<PluginVersionProvider>()
  private val ideVersion = ApplicationInfo.getInstance().fullVersion
  private val ideName = ApplicationInfo.getInstance().fullApplicationName
  private val osName = System.getProperty("os.name")
  private val osVersion = System.getProperty("os.version")
  private val osArch = System.getProperty("os.arch")
  private val pluginVersion = versionProvider.currentPluginVersion()
  private val environment = if (pluginVersion?.contains("snapshot") == true) "development" else "production"

  private val isBeta = versionProvider.isBeta()

  /**
   * Tracks whether Sentry has been successfully initialized.
   */
  private var isInitialized = false

  companion object {
    private const val SENTRY_DSN = "https://<EMAIL>/4509262642872320"
    private const val WEBVIEW_SENTRY_DSN =
      "https://<EMAIL>/4509294232207360"
  }

  val instance: SentryService
    get() = service()

  init {
    initialize()
    logger.info("SentryService initialized")
  }

  /**
   * Execute a block of code with proper exception handling and logging.
   *
   * @param errorMessage The message to log if an exception occurs
   * @param block The code block to execute
   * @return The result of the block execution or null if an exception occurred
   */
  private fun <T> withExceptionHandling(
    errorMessage: String,
    block: () -> T,
  ): T? {
    return try {
      block()
    } catch (e: Exception) {
      logger.error(errorMessage, e)
      null
    }
  }

  /**
   * Creates a breadcrumb with the given parameters.
   *
   * @param message The breadcrumb message
   * @param category The breadcrumb category
   * @param level The breadcrumb level
   * @param type The breadcrumb type
   * @param data Additional data to add to the breadcrumb
   * @return The created breadcrumb
   */
  private fun createBreadcrumb(
    message: String,
    category: String = "default",
    level: SentryLevel = SentryLevel.INFO,
    type: String = "default",
    data: Map<String, String> = emptyMap(),
  ): Breadcrumb {
    return Breadcrumb().apply {
      this.message = message
      this.category = category
      this.level = level
      this.type = type

      // Add additional data
      data.forEach { (key, value) ->
        this.setData(key, value)
      }
    }
  }

  /**
   * Executes the given block only if Sentry is initialized.
   *
   * @param block The code block to execute if Sentry is initialized
   * @return The result of the block execution or null if Sentry is not initialized
   */
  private fun <T> ifInitialized(block: () -> T): T? {
    return if (isInitialized) {
      block()
    } else {
      logger.warn("Sentry is not initialized, cannot execute block")
      null
    }
  }

  /**
   * Determines whether Sentry should be enabled based on feature flags and environment.
   *
   * Sentry is enabled only when BOTH conditions are met:
   * 1. The `intellij_enable_sentry` feature flag is enabled (checked with network wait)
   * 2. The current environment is production (determined by plugin version not containing "snapshot")
   *
   * For development environments, developers can enable Sentry by:
   * - Using a production build of the plugin (version without "snapshot")
   * - OR configuring the `intellij_enable_sentry` feature flag to return true for development environments
   *   through the feature flag configuration system
   *
   * @return true if Sentry should be enabled, false otherwise
   */
  fun shouldEnableSentry(): Boolean {
    val featureFlagManager = FeatureFlagManager.instance
    val featureFlagEnabled = featureFlagManager.sentryEnabled(waitForNetwork = true)
    val isProductionEnvironment = environment == "production"

    if (!featureFlagEnabled) {
      logger.info("Sentry is disabled by feature flag (intellij_enable_sentry)")
      return false
    }

    if (!isProductionEnvironment) {
      logger.info("Sentry is disabled for development environment (plugin version contains 'snapshot')")
      return false
    }

    return true
  }

  /**
   * Initialize Sentry with the appropriate configuration.
   * This method is safe to call multiple times.
   */
  fun initialize() {
    if (!shouldEnableSentry()) {
      return
    }

    if (isInitialized) {
      logger.warn("Sentry is already initialized")
      return
    }

    withExceptionHandling("Failed to initialize Sentry") {
      val variant = if (isBeta) "beta" else "stable"

      // Configure and initialize Sentry
      configureSentry(variant)

      // Install the global exception handler after successful Sentry initialization
      service<SentryGlobalExceptionHandler>().install()

      logger.info("Sentry initialized successfully")

      isInitialized = true
    }
  }

  /**
   * Configure Sentry with the appropriate options.
   *
   * @param variant The plugin variant (beta or stable)
   */
  private fun configureSentry(variant: String) {
    Sentry.init { options ->
      // Get the FeatureFlagManager instance at the point of use using recommended pattern
      val featureFlagManager = FeatureFlagManager.instance

      // Set the DSN
      options.dsn = SENTRY_DSN

      // Set environment (production for now, could be made configurable)
      options.environment = environment

      // Set release version
      options.release = "${pluginVersion ?: "unknown"}-$variant"

      // Enable automatic session tracking for crash-free sessions
      options.isEnableAutoSessionTracking = true

      // Set sample rate for performance monitoring using feature flag
      options.tracesSampleRate = featureFlagManager.pluginTraceSamplingRate()

      // Set sample rate for error reporting using feature flag
      options.sampleRate = featureFlagManager.pluginErrorSamplingRate()

      // Disable debug mode in production
      options.isDebug = environment != "production"

      // Add IntelliJ version as a tag
      options.setTag("intellij.version", ideVersion)
      options.setTag("intellij.name", ideName)
      options.setTag("os.name", osName)
      options.setTag("os.version", osVersion)
      options.setTag("os.arch", osArch)

      // Keeping this line below, in case we want to implement beforeSend in the future
      // options.beforeSend = SentryOptions.BeforeSendCallback { event, _ -> filterSensitiveData(event) }
      // Disable sending default PII
      options.isSendDefaultPii = false
    }
  }

  /**
   * Creates a JSON string with Sentry configuration for the webview.
   * This configuration is injected into the webview and used by the Sentry initialization code.
   */
  fun createSentryConfigForWebview(): String {
    val featureFlagManager = FeatureFlagManager.instance
    val webviewErrorSampleRate = featureFlagManager.webviewErrorSamplingRate(waitForNetwork = true)
    val webviewTraceSampleRate = featureFlagManager.webviewTraceSamplingRate(waitForNetwork = true)
    val sentryEnabled = shouldEnableSentry()
    if (!sentryEnabled) {
      logger.info("Sentry is disabled for webview")
      return "{enabled: false}"
    }
    // Create the Sentry configuration JSON
    return (
      "{" +
        "enabled: ${shouldEnableSentry()}," +
        "dsn: '${WEBVIEW_SENTRY_DSN}'," +
        "release: 'augment-intellij-webview@$pluginVersion'," +
        "environment: 'production'," +
        "errorSampleRate: $webviewErrorSampleRate," +
        "tracesSampleRate: $webviewTraceSampleRate," +
        "sendDefaultPii: false," +
        // if not production, enable debug mode, sentry is disabled in dev mode by default
        "debug: ${environment != "production"}," +
        "tags: {" +
        "  'intellij.version': '$ideVersion'," +
        "  'plugin.version': '$pluginVersion'" +
        "}" +
        "}"
    )
  }

  /**
   * Capture an exception and send it to Sentry.
   * Includes current metrics as contextual data.
   *
   * @param throwable The exception to capture
   * @param project Optional project context. If provided, only metrics from this project are included.
   *                If null, metrics from all open projects are included.
   */
  fun captureException(
    throwable: Throwable,
    project: Project? = null,
  ) {
    ifInitialized {
      includeCurrentMetricsAsContext(project)
      Sentry.captureException(throwable)
    }
  }

  /**
   * Add a breadcrumb to track user actions or application events.
   * Includes current metrics as context for ERROR level breadcrumbs.
   *
   * @param message The breadcrumb message
   * @param category The breadcrumb category
   * @param level The breadcrumb level
   * @param project Optional project context. If provided, only metrics from this project are included.
   *                If null, metrics from all open projects are included.
   */
  fun addBreadcrumb(
    message: String,
    category: String = "default",
    level: SentryLevel = SentryLevel.INFO,
    project: Project? = null,
  ) {
    ifInitialized {
      // Include current metrics as context for error-level breadcrumbs
      if (level == SentryLevel.ERROR) {
        includeCurrentMetricsAsContext(project)
      }

      val breadcrumb = createBreadcrumb(message, category, level)
      Sentry.addBreadcrumb(breadcrumb)
    }
  }

  /**
   * Add a metrics breadcrumb to Sentry.
   */
  fun addMetricsBreadcrumb(
    metricName: String,
    value: Number,
    tags: Map<String, String> = emptyMap(),
  ) {
    ifInitialized {
      // Create data map with metric information
      val data =
        mutableMapOf(
          "metric_name" to metricName,
          "metric_value" to value.toString(),
        )

      // Add tags with prefix
      tags.forEach { (key, tagValue) ->
        data["tag_$key"] = tagValue
      }

      val breadcrumb =
        createBreadcrumb(
          message = "Metric: $metricName = $value",
          category = "metrics",
          level = SentryLevel.INFO,
          data = data,
        )

      Sentry.addBreadcrumb(breadcrumb)
    }
  }

  /**
   * Include current metadata as contextual breadcrumbs.
   * This method is called when capturing exceptions or error-level breadcrumbs.
   *
   * Uses IntelliJ's service singleton pattern to get SentryProjectMetadataService instances
   * directly from projects without maintaining manual registrations.
   *
   * @param project Optional project context. If provided, only metadata from this project are included.
   *                If null, metadata from all open projects are included as fallback.
   */
  private fun includeCurrentMetricsAsContext(project: Project? = null) {
    ifInitialized {
      if (project != null) {
        // Include metrics only for the specified project
        includeMetricsForProject(project)
      } else {
        // Fallback: include metrics from all open projects
        ProjectManager.getInstance().openProjects.forEach { openProject ->
          includeMetricsForProject(openProject)
        }
      }
    }
  }

  /**
   * Include metrics for a specific project.
   *
   * @param project The project to include metrics for
   */
  private fun includeMetricsForProject(project: Project) {
    try {
      SentryProjectMetadataService.getInstance(project).latestMetrics?.let { metrics ->
        addMetricsAsContextualBreadcrumbs(project.locationHash, metrics)
      }
    } catch (e: Exception) {
      logger.warn("Failed to include metrics context for project: ${project.name}", e)
    }
  }

  /**
   * Add metrics for a specific project as contextual breadcrumbs.
   */
  private fun addMetricsAsContextualBreadcrumbs(
    projectKey: String,
    metrics: MetricsData,
  ) {
    val projectTags = metrics.systemTags + ("project_key" to projectKey)

    // Add memory metrics as context
    metrics.memoryMetrics?.let { memory ->
      addMetricsGroup(
        metrics =
          listOf(
            "heap_available_mb" to memory.heapAvailableMB,
            "heap_utilized_mb" to memory.heapUtilizedMB,
          ),
        tags = projectTags,
      )
    }

    // Add repository metrics as context
    metrics.repositoryMetrics?.let { repo ->
      addMetricsGroup(
        metrics =
          listOf(
            "git_tracked_files_count" to repo.gitTrackedFiles,
            "indexed_files_count" to repo.indexedFiles,
          ),
        tags = projectTags,
      )
    }

    // Add webview metrics as context
    metrics.webviewMetrics?.let { webview ->
      addMetricsGroup(
        metrics =
          listOf(
            "webview_state_num_chars" to webview.webviewChatStateSize,
            "settings_state_num_chars" to webview.webviewSettingsStateSize,
          ),
        tags = projectTags,
      )
    }
  }

  /**
   * Add a group of metrics as breadcrumbs.
   *
   * @param metrics List of metric name to value pairs
   * @param tags Tags to add to each metric
   */
  private fun addMetricsGroup(
    metrics: List<Pair<String, Number>>,
    tags: Map<String, String>,
  ) {
    metrics.forEach { (name, value) ->
      addMetricsBreadcrumb(name, value, tags)
    }
  }

  /**
   * Dispose Sentry when the service is being disposed.
   * This is called automatically by the IntelliJ Platform when the application shuts down
   * or the plugin is unloaded. This also uninstalls the global exception handler.
   * CoroutineScope is automatically cancelled by IntelliJ Platform when application shuts down.
   * Project-specific metrics services are automatically disposed by IntelliJ's service lifecycle.
   */
  override fun dispose() {
    withExceptionHandling("Error disposing Sentry") {
      // Uninstall the global exception handler first
      service<SentryGlobalExceptionHandler>().uninstall()

      // Close Sentry
      Sentry.close()

      logger.info("Sentry disposed successfully")
    }
  }
}
