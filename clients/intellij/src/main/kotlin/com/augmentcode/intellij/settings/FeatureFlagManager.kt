package com.augmentcode.intellij.settings

import com.augmentcode.api.SmartPastePrecomputeMode
import com.intellij.openapi.components.service
import org.jetbrains.annotations.VisibleForTesting

interface FeatureFlagManager {
  companion object {
    val instance: FeatureFlagManager
      get() = service<FeatureFlagManagerImpl>()
  }

  /**
   * @return true if the feature flags were successfully fetched and cached
   */
  @VisibleForTesting
  fun fetchAndCacheFeatureFlags(): Boolean

  fun forceCompletionEnabled(waitForNetwork: Boolean = false): Boolean

  fun enableExternalSourcesInChat(waitForNetwork: Boolean = false): Boolean

  fun shareServiceEnabled(waitForNetwork: Boolean = false): Boolean

  fun smallSyncThreshold(waitForNetwork: Boolean = false): Int

  fun bigSyncThreshold(waitForNetwork: Boolean = false): Int

  fun enableShowSummary(waitForNetwork: Boolean = false): Boolean

  fun useNewThreadsMenu(waitForNetwork: Boolean = false): Boolean

  fun enableSmartPaste(waitForNetwork: Boolean = false): Boolean

  fun smartPastePrecomputeMode(waitForNetwork: Boolean = false): SmartPastePrecomputeMode

  fun chatWithToolsEnabled(waitForNetwork: Boolean = false): Boolean

  fun agentModeEnabled(waitForNetwork: Boolean = false): Boolean

  fun enableDesignSystemRichTextEditor(waitForNetwork: Boolean = false): Boolean

  fun enableChatMermaidDiagrams(waitForNetwork: Boolean = false): Boolean

  fun enableBackgroundAgents(waitForNetwork: Boolean = false): Boolean

  fun askForSyncPermission(waitForNetwork: Boolean = false): Boolean

  fun chatMultimodalEnabled(waitForNetwork: Boolean = false): Boolean

  fun memoriesParams(waitForNetwork: Boolean = false): String

  /**
   * Checks if the guidelines feature is enabled.
   * This is the main feature flag that controls both user and workspace guidelines.
   */
  fun guidelinesEnabled(waitForNetwork: Boolean = false): Boolean

  /**
   * Checks if the user guidelines feature is enabled specifically for IntelliJ.
   */
  fun userGuidelinesEnabled(waitForNetwork: Boolean = false): Boolean

  /**
   * Checks if the user guidelines feature is enabled in the settings page specifically for IntelliJ.
   */
  fun userGuidelinesInSettingsEnabled(waitForNetwork: Boolean = false): Boolean

  /**
   * Gets the maximum length allowed for user guidelines.
   */
  fun getUserGuidelinesLengthLimit(waitForNetwork: Boolean = false): Int

  /**
   * Gets the maximum length allowed for workspace guidelines.
   */
  fun getWorkspaceGuidelinesLengthLimit(waitForNetwork: Boolean = false): Int

  fun enableHomespunGitignore(waitForNetwork: Boolean = false): Boolean

  fun enableAgentAutoMode(waitForNetwork: Boolean = false): Boolean

  fun promptEnhancerEnabled(waitForNetwork: Boolean = false): Boolean

  /**
   * If true, error reporting to Sentry is enabled.
   */
  fun sentryEnabled(waitForNetwork: Boolean = false): Boolean

  /**
   * Gets the sampling rate for webview errors (0.0-1.0).
   */
  fun webviewErrorSamplingRate(waitForNetwork: Boolean = false): Double

  /**
   * Gets the sampling rate for plugin errors (0.0-1.0).
   */
  fun pluginErrorSamplingRate(waitForNetwork: Boolean = false): Double

  /**
   * Gets the sampling rate for webview traces (0.0-1.0).
   */
  fun webviewTraceSamplingRate(waitForNetwork: Boolean = false): Double

  /**
   * Gets the sampling rate for plugin traces (0.0-1.0).
   */
  fun pluginTraceSamplingRate(waitForNetwork: Boolean = false): Double

  /**
   * Checks if performance monitoring in webviews is enabled.
   */
  fun enableWebviewPerformanceMonitoring(waitForNetwork: Boolean = false): Boolean

  // Sidecar feature flags
  fun agentEditTool(waitForNetwork: Boolean = false): String

  fun agentEditToolMinViewSize(waitForNetwork: Boolean = false): Int

  fun agentEditToolSchemaType(waitForNetwork: Boolean = false): String

  fun agentEditToolEnableFuzzyMatching(waitForNetwork: Boolean = false): Boolean

  fun agentEditToolFuzzyMatchSuccessMessage(waitForNetwork: Boolean = false): String

  fun agentEditToolFuzzyMatchMaxDiff(waitForNetwork: Boolean = false): Int

  fun agentEditToolFuzzyMatchMaxDiffRatio(waitForNetwork: Boolean = false): Double

  fun agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs(waitForNetwork: Boolean = false): Int

  fun agentEditToolInstructionsReminder(waitForNetwork: Boolean = false): Boolean

  fun agentEditToolShowResultSnippet(waitForNetwork: Boolean = false): Boolean

  fun agentEditToolMaxLines(waitForNetwork: Boolean = false): Int

  fun agentSaveFileToolInstructionsReminder(waitForNetwork: Boolean = false): Boolean

  fun grepSearchToolEnable(waitForNetwork: Boolean = false): Boolean

  fun grepSearchToolTimelimitSec(waitForNetwork: Boolean = false): Int

  fun grepSearchToolOutputCharsLimit(waitForNetwork: Boolean = false): Int

  fun grepSearchToolNumContextLines(waitForNetwork: Boolean = false): Int
}
