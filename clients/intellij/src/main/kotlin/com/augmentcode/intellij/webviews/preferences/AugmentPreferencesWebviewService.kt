package com.augmentcode.intellij.webviews.preferences

import com.augmentcode.intellij.preferences.AugmentPreferencesWebviewEditorVirtualFile
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.utils.ProperThreadUsageChecker
import com.augmentcode.rpc.AugmentChatEntry
import com.augmentcode.rpc.PreferenceInput
import com.augmentcode.rpc.PreferenceNotifyRequest
import com.augmentcode.rpc.PreferencePair
import com.augmentcode.rpc.PreferencesInitializeResponse
import com.augmentcode.rpc.PreferencesLoadedRequest
import com.augmentcode.rpc.WebviewPreferencesServiceGrpcKt
import com.google.protobuf.Empty
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import kotlinx.coroutines.CoroutineScope

/**
 * Service that handles preferences webview
 */
@Service(Service.Level.PROJECT)
class AugmentPreferencesWebviewService(
  private val project: Project,
  private val cs: CoroutineScope,
) : WebviewPreferencesServiceGrpcKt.WebviewPreferencesServiceCoroutineImplBase() {
  private val logger = logger<AugmentPreferencesWebviewService>()

  private var preferencesVirtualFile: AugmentPreferencesWebviewEditorVirtualFile? = null
  private var preferencesInput: PreferenceInput? = null

  companion object {
    fun getInstance(project: Project): AugmentPreferencesWebviewService = project.service()
  }

  fun openPreferences(messageText: String) {
    ProperThreadUsageChecker.shouldNotBeOnEDT("openPreferences")
    val editorManager = FileEditorManager.getInstance(project)

    if (preferencesVirtualFile == null) {
      preferencesVirtualFile = AugmentPreferencesWebviewEditorVirtualFile(project, cs)
      preferencesInput = buildPreferencesInput(messageText)

      Disposer.register(AugmentDisposable.getInstance(project)) {
        invokeLater {
          logger.info("Closing preferences file")
          editorManager.closeFile(preferencesVirtualFile!!)
          preferencesVirtualFile = null
          preferencesInput = null
        }
      }
    }

    invokeLater {
      logger.info("Opening preferences file")
      editorManager.openFile(preferencesVirtualFile!!, true, true)
    }
  }

  override suspend fun preferenceNotify(request: PreferenceNotifyRequest): Empty {
    logger.info("Preference notification: ${request.message}")

    invokeLater {
      Notifications.Bus.notify(
        Notification(
          "augment.notifications",
          "Preference Panel",
          request.message,
          NotificationType.INFORMATION,
        ),
        project,
      )
    }

    return Empty.getDefaultInstance()
  }

  override suspend fun preferencesLoaded(request: PreferencesLoadedRequest): PreferencesInitializeResponse {
    logger.info("Preferences webview loaded")

    if (preferencesInput == null) {
      logger.warn("Preferences input is null")
      return PreferencesInitializeResponse.getDefaultInstance()
    }

    return PreferencesInitializeResponse.newBuilder()
      .setData(preferencesInput)
      .build()
  }

  private fun buildPreferencesInput(messageText: String): PreferenceInput {
    return PreferenceInput.newBuilder()
      .setType("Chat")
      .setData(
        PreferencePair.newBuilder()
          .setA(
            AugmentChatEntry.newBuilder()
              .setMessage(messageText)
              .setResponse("")
              .build(),
          )
          .setB(
            AugmentChatEntry.newBuilder()
              .setMessage(messageText)
              .setResponse("")
              .build(),
          )
          .build(),
      )
      .build()
  }
}
