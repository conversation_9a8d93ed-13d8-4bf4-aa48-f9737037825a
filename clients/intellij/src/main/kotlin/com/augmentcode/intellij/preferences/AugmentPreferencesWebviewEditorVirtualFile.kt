package com.augmentcode.intellij.preferences

import com.augmentcode.intellij.webviews.AugmentWebview
import com.augmentcode.intellij.webviews.AugmentWebviewStateKey
import com.augmentcode.intellij.webviews.WebviewFactory
import com.augmentcode.intellij.webviews.preferences.AugmentPreferencesWebviewService
import com.augmentcode.intellij.webviews.preferences.PreferencesMessagingService
import com.augmentcode.intellij.webviews.serializeProtoToJson
import com.augmentcode.rpc.ChatModelReply
import com.augmentcode.rpc.PreferenceInput
import com.augmentcode.rpc.PreferenceResultRequest
import com.augmentcode.rpc.copy
import com.intellij.ide.plugins.UIComponentFileEditor
import com.intellij.ide.plugins.UIComponentVirtualFile
import com.intellij.openapi.fileEditor.FileEditorManagerKeys
import com.intellij.openapi.project.Project
import kotlinx.coroutines.CoroutineScope
import java.awt.BorderLayout
import javax.swing.JPanel

class AugmentPreferencesWebviewEditorVirtualFile(
  private val project: Project,
  private val cs: CoroutineScope,
  val preferenceInput: PreferenceInput,
  val onLoaded: () -> Unit,
  val onResultReceived: (PreferenceResultRequest) -> Unit,
) : UIComponentVirtualFile(
    PREFERENCES_VIRTUAL_FILE_NAME,
    null,
  ) {
  companion object {
    const val PREFERENCES_VIRTUAL_FILE_NAME = "Augment Preferences"
    const val ENTRY_FILE_PATH = "preference.html"
  }

  val preferencesService: AugmentPreferencesWebviewService
  val messagingService: PreferencesMessagingService

  init {
    putUserData(FileEditorManagerKeys.FORBID_TAB_SPLIT, true)
    preferencesService = AugmentPreferencesWebviewService(project, cs, preferenceInput, onLoaded, onResultReceived)
    messagingService = PreferencesMessagingService(project, cs, preferencesService)
  }

  var webview: AugmentWebview? = null

  override fun createContent(editor: UIComponentFileEditor): Content {
    return Content {
      // Create a webview with a load handler for preferences
      webview =
        WebviewFactory.create(
          project,
          ENTRY_FILE_PATH,
          AugmentWebviewStateKey.PREFERENCES_STATE,
          messagingService,
          editor,
        )

      JPanel(BorderLayout()).apply {
        webview?.let { add(it.browser.component, BorderLayout.CENTER) }
      }
    }
  }

  fun postChatStreamingMessage(message: ChatModelReply) {
    webview?.postMessage(serializeProtoToJson(message))
  }

  fun postStreamDone() {
    webview?.postMessage("{ type: 'chat-stream-done' }") // todo(diehuxx): move this into chat protos so we aren't hardcoding a string
  }
}
