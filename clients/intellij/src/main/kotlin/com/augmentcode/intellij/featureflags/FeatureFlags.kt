package com.augmentcode.intellij.featureflags

import com.augmentcode.api.EloModelConfiguration
import com.augmentcode.api.ModelConfig
import com.augmentcode.intellij.utils.SemVer
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.intellij.ide.plugins.PluginManager
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.extensions.PluginId

data class FeatureFlags(
  val chatMultimodalEnabled: <PERSON>olean,
  val enableCompletionsHistory: Boolean,
  val agentModeEnabled: Boolean,
  val guidelinesEnabled: Boolean,
  val userGuidelinesEnabled: <PERSON>olean,
  val promptEnhancerEnabled: Boolean,
  val enableChatMermaidDiagrams: <PERSON>olean,
  val enableDesignSystemRichTextEditor: Boolean,
  val smartPastePrecomputeMode: String,
  val enableSmartPaste: <PERSON><PERSON>an,
  val useNewThreadsMenu: <PERSON>olean,
  val enableExternalSourcesInChat: Boolean,
  val shareServiceEnabled: <PERSON>olean,
  val enableHomespunGitignore: Boolean,
  val memoriesParams: String,
  val userGuidelinesLengthLimit: Int,
  val workspaceGuidelinesLengthLimit: Int,
  val userGuidelinesInSettingsEnabled: Boolean,
  val chatWithToolsEnabled: Boolean,
  val sentryEnabled: Boolean,
  val webviewErrorSamplingRate: Double,
  val pluginErrorSamplingRate: Double,
  val webviewTraceSamplingRate: Double,
  val pluginTraceSamplingRate: Double,
  val maxUploadSizeBytes: Int,
  val bypassLanguageFilter: Boolean,
  val additionalChatModels: Map<String, String>,
  val enableAgentAutoMode: Boolean,
  val preferenceCollectionAllowed: Boolean,
  val eloModelConfiguration: EloModelConfiguration,
) {
  companion object {
    // Create a FeatureFlags consisting of default values with overrides from the ModelConfig flags
    fun fromModelConfig(modelConfig: ModelConfig): FeatureFlags {
      val flagsFromAPI = modelConfig.featureFlags
      return DefaultFeatureFlags.copyWithOverrides(flagsFromAPI)
    }

    // Return true if the current version is at least the min version. False if either are undefined.
    internal fun isMinVersionAtLeast(
      currentVersionStr: String?,
      minVersionStr: String?,
    ): Boolean {
      if (minVersionStr.isNullOrBlank() || currentVersionStr.isNullOrBlank()) {
        return false
      }

      try {
        val minVersion = SemVer(minVersionStr)
        val currentVersion = SemVer(currentVersionStr)
        return currentVersion >= minVersion
      } catch (e: IllegalArgumentException) {
        thisLogger().warn("Failed to parse version: $currentVersionStr or $minVersionStr", e)
        return false
      }
    }
  }

  // Create a copy of the FeatureFlags object with values overridden if defined in the APIFeatureFlags
  // object if defined
  private fun copyWithOverrides(flagsFromAPI: com.augmentcode.api.FeatureFlags?): FeatureFlags {
    val pluginVersionStr = getCurrentPluginVersion()
    return FeatureFlags(
      chatMultimodalEnabled =
        flagsFromAPI?.intellijChatMultimodalMinVersion?.let { isMinVersionAtLeast(pluginVersionStr, it) }
          ?: chatMultimodalEnabled,
      enableCompletionsHistory =
        flagsFromAPI?.intellijCompletionsHistoryMinVersion?.let {
          isMinVersionAtLeast(
            pluginVersionStr,
            it,
          )
        } ?: enableCompletionsHistory,
      agentModeEnabled =
        flagsFromAPI?.intellijAgentModeMinVersion?.let { isMinVersionAtLeast(pluginVersionStr, it) }
          ?: agentModeEnabled,
      guidelinesEnabled = flagsFromAPI?.enableGuidelines ?: guidelinesEnabled,
      userGuidelinesEnabled = flagsFromAPI?.intellijEnableUserGuidelines ?: userGuidelinesEnabled,
      promptEnhancerEnabled = flagsFromAPI?.intellijPromptEnhancerEnabled ?: promptEnhancerEnabled,
      enableChatMermaidDiagrams =
        flagsFromAPI?.intellijEnableChatMermaidDiagramsMinVersion?.let {
          isMinVersionAtLeast(
            pluginVersionStr,
            it,
          )
        } ?: enableChatMermaidDiagrams,
      enableDesignSystemRichTextEditor =
        flagsFromAPI?.intellijDesignSystemRichTextEditorMinVersion?.let {
          isMinVersionAtLeast(
            pluginVersionStr,
            it,
          )
        } ?: enableDesignSystemRichTextEditor,
      smartPastePrecomputeMode = flagsFromAPI?.smartPastePrecomputeMode ?: smartPastePrecomputeMode,
      enableSmartPaste =
        flagsFromAPI?.intellijSmartPasteMinVersion?.let { isMinVersionAtLeast(pluginVersionStr, it) }
          ?: enableSmartPaste,
      useNewThreadsMenu =
        flagsFromAPI?.intellijNewThreadsMenuMinVersion?.let { isMinVersionAtLeast(pluginVersionStr, it) }
          ?: useNewThreadsMenu,
      enableExternalSourcesInChat = flagsFromAPI?.enableExternalSourcesInChat ?: enableExternalSourcesInChat,
      shareServiceEnabled =
        flagsFromAPI?.intellijShareMinVersion?.let { isMinVersionAtLeast(pluginVersionStr, it) }
          ?: shareServiceEnabled,
      enableHomespunGitignore = flagsFromAPI?.intellijEnableHomespunGitignore ?: enableHomespunGitignore,
      memoriesParams = flagsFromAPI?.memoriesParams ?: memoriesParams,
      userGuidelinesLengthLimit = flagsFromAPI?.userGuidelinesLengthLimit ?: userGuidelinesLengthLimit,
      workspaceGuidelinesLengthLimit = flagsFromAPI?.workspaceGuidelinesLengthLimit ?: workspaceGuidelinesLengthLimit,
      userGuidelinesInSettingsEnabled =
        flagsFromAPI?.intellijUserGuidelinesInSettings
          ?: userGuidelinesInSettingsEnabled,
      chatWithToolsEnabled =
        flagsFromAPI?.intellijChatWithToolsMinVersion?.let { isMinVersionAtLeast(pluginVersionStr, it) }
          ?: chatWithToolsEnabled,
      sentryEnabled = flagsFromAPI?.intellijEnableSentry ?: sentryEnabled,
      webviewErrorSamplingRate = flagsFromAPI?.intellijWebviewErrorSamplingRate ?: webviewErrorSamplingRate,
      pluginErrorSamplingRate = flagsFromAPI?.intellijPluginErrorSamplingRate ?: pluginErrorSamplingRate,
      webviewTraceSamplingRate = flagsFromAPI?.intellijWebviewTraceSamplingRate ?: webviewTraceSamplingRate,
      pluginTraceSamplingRate = flagsFromAPI?.intellijPluginTraceSamplingRate ?: pluginTraceSamplingRate,
      maxUploadSizeBytes = flagsFromAPI?.maxUploadSizeBytes ?: maxUploadSizeBytes,
      bypassLanguageFilter = flagsFromAPI?.bypassLanguageFilter ?: bypassLanguageFilter,
      additionalChatModels = flagsFromAPI?.additionalChatModelsMap() ?: additionalChatModels,
      enableAgentAutoMode = flagsFromAPI?.enableAgentAutoMode ?: enableAgentAutoMode,
      preferenceCollectionAllowed =
        flagsFromAPI?.intellijPreferenceCollectionAllowedMinVersion?.let {
          isMinVersionAtLeast(pluginVersionStr, it)
        } ?: preferenceCollectionAllowed,
      eloModelConfiguration = parseEloModelConfiguration(flagsFromAPI?.eloModelConfiguration) ?: eloModelConfiguration,
    )
  }

  private fun parseEloModelConfiguration(eloModelConfiguration: String?): EloModelConfiguration? {
    if (eloModelConfiguration.isNullOrBlank()) {
      return null
    }
    try {
      return Gson().fromJson(eloModelConfiguration, EloModelConfiguration::class.java)
    } catch (e: JsonSyntaxException) {
      thisLogger().warn("Failed to parse ELO model configuration: $eloModelConfiguration", e)
      return null
    }
  }

  // Get the plugins current version
  private fun getCurrentPluginVersion(): String? {
    val plugin = PluginManager.getInstance().findEnabledPlugin(PluginId.getId("com.augmentcode"))
    if (plugin == null) {
      thisLogger().warn("Failed to lookup plugin version, failed to get augment plugin details")
      return null
    }
    return plugin.version
  }
}
