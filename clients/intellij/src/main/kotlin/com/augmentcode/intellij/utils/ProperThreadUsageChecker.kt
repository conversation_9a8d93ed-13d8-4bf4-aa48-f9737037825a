package com.augmentcode.intellij.utils

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.thisLogger

/**
 * Utility class for checking proper thread usage.
 * In future we can integrate this with Sentry or metrics or chaos monkey
 */
class ProperThreadUsageChecker {
  companion object {
    fun shouldNotBeOnEDT(operation: String) {
      if (ApplicationManager.getApplication().isDispatchThread) {
        thisLogger().warn("This operation ($operation) must not be on the EDT")
      }
    }
  }
}
