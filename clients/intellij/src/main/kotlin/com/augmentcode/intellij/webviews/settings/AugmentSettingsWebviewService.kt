package com.augmentcode.intellij.webviews.settings

import com.augmentcode.api.RemoteToolId
import com.augmentcode.api.RevokeToolAccessStatus
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.guidelines.GuidelinesService
import com.augmentcode.intellij.settings.AugmentIntegrationsConfig
import com.augmentcode.intellij.settings.AugmentSettingsWebviewEditorVirtualFile
import com.augmentcode.intellij.settings.FeatureFlagManager
import com.augmentcode.intellij.sidecar.SidecarService
import com.augmentcode.intellij.utils.AugmentDisposable
import com.augmentcode.intellij.utils.ProperThreadUsageChecker
import com.augmentcode.rpc.*
import com.google.protobuf.Empty
import com.intellij.ide.BrowserUtil
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.annotations.VisibleForTesting

/**
 * Service that handles settings webview messages
 */
@Service(Service.Level.PROJECT)
class AugmentSettingsWebviewService(
  private val project: Project,
  private val cs: CoroutineScope,
) : WebviewSettingsServiceGrpcKt.WebviewSettingsServiceCoroutineImplBase() {
  private val logger = logger<AugmentSettingsWebviewService>()

  private var settingsVirtualFile: AugmentSettingsWebviewEditorVirtualFile? = null

  companion object {
    fun getInstance(project: Project): AugmentSettingsWebviewService = project.service()
  }

  override suspend fun toolConfigLoaded(request: ToolConfigLoadedRequest): ToolConfigInitializeResponse {
    logger.info("Settings webview loaded")

    // Get the guidelines states from the GuidelinesService
    val guidelinesService = GuidelinesService.getInstance(project)
    val guidelinesStates = guidelinesService.getGuidelinesStates()

    // Build and return the response with guidelines included
    return ToolConfigInitializeResponse.newBuilder()
      .setData(
        SettingsInitializeResponseData.newBuilder()
          .addAllToolConfigs(emptyList<ToolSettings>())
          .addAllHostTools(project.serviceOrNull<SidecarService>()?.getToolStatusForSettingsPanel()?.toolsList ?: emptyList())
          .setEnableDebugFeatures(ApplicationManager.getApplication().isInternal)
          .setEnableAgentMode(FeatureFlagManager.instance.agentModeEnabled())
          .setSettingsComponentSupported(
            SettingsComponentSupported.newBuilder()
              .setWorkspaceContext(false)
              .setMcpServerList(true)
              .setMcpServerImport(false)
              .setOrientation(false)
              .setRemoteTools(true)
              .setUserGuidelines(FeatureFlagManager.instance.userGuidelinesInSettingsEnabled())
              .setTerminal(false)
              .setRules(false)
              .build(),
          )
          .setGuidelines(guidelinesStates)
          .setEnableInitialOrientation(false) // not yet supported
          .build(),
      )
      .build()
  }

  override suspend fun toolConfigGetDefinitions(request: ToolConfigGetDefinitionsRequest): ToolConfigDefinitionsResponse {
    logger.info("Tool config definitions requested")

    // For now, return empty data - this will be expanded as we implement tool configuration
    return ToolConfigDefinitionsResponse.newBuilder()
      .setData(
        ToolConfigDefinitionsResponseData.newBuilder()
          .addAllHostTools(
            project.serviceOrNull<SidecarService>()?.getToolStatusForSettingsPanel(request.data.useCache)?.toolsList ?: emptyList(),
          )
          .build(),
      )
      .build()
  }

  override suspend fun toolConfigSave(request: ToolConfigSaveRequest): Empty {
    logger.info("Tool config save requested for tool: ${request.data.toolName}")

    // TODO: Implement MCP tool config saving

    return Empty.getDefaultInstance()
  }

  /**
   * Open the URL in the browser for the user to authenticate.
   * This is similar to the VS Code implementation in settings-panel.ts.
   */
  override suspend fun toolConfigStartOAuth(request: ToolConfigStartOAuthRequest): ToolConfigStartOAuthResponse {
    logger.info("Tool config OAuth requested with URL: ${request.data.authUrl}")

    var success = false
    try {
      val url = request.data.authUrl
      // Use withContext to ensure we're on the UI thread when opening the browser
      withContext(Dispatchers.Main) {
        BrowserUtil.browse(url)
      }
      success = true
    } catch (e: Exception) {
      logger.error("Error opening URL: ${e.message}", e)
    }

    return ToolConfigStartOAuthResponse.newBuilder()
      .setData(
        ToolConfigStartOAuthResponseData.newBuilder()
          .setOk(success)
          .build(),
      )
      .build()
  }

  /**
   * Revoke access for a tool.
   * This is similar to the VS Code implementation in settings-panel.ts.
   */
  override suspend fun toolConfigRevokeAccess(request: ToolConfigRevokeAccessRequest): ToolConfigDefinitionsResponse {
    logger.info("Tool config revoke access requested for tool: ${request.data.toolId}")

    try {
      // Get the tool definitions to find the tool by its RemoteToolId
      val hostTools = project.serviceOrNull<SidecarService>()?.getToolStatusForSettingsPanel()?.toolsList ?: emptyList()
      val tool =
        hostTools.find { t ->
          t.identifier.hostName == request.data.toolId.hostName &&
            t.identifier.toolId == request.data.toolId.toolId
        }

      // TODO use constant or enum or something for remoteToolHost
      if (tool != null && tool.identifier.hostName == "remoteToolHost") {
        val toolId = tool.identifier.toolId
        logger.info("Revoking access for remote tool: ${tool.definition.name} ($toolId)")
        val apiToolId = RemoteToolId.values().find { it.value == toolId.toInt() } ?: RemoteToolId.Unknown
        val result = AugmentAPI.instance.revokeToolAccess(apiToolId)
        logger.info("Revoke access result: ${result.status}")
        when (result.status) {
          RevokeToolAccessStatus.Success.value -> {
            logger.info("Successfully revoked access for ${tool.definition.name} ($toolId).")
          }
          RevokeToolAccessStatus.NotActive.value -> {
            logger.info("Tool ${tool.definition.name} ($toolId) has no access to revoke.")
          }
          RevokeToolAccessStatus.Unimplemented.value -> {
            logger.warn("Revoking access is not implemented for ${tool.definition.name} ($toolId).")
            showNotification("Not Implemented", "Revoking access is not implemented for ${tool.definition.name}.", NotificationType.WARNING)
          }
          RevokeToolAccessStatus.NotFound.value -> {
            logger.warn("Tool not found: ${tool.definition.name} ($toolId).")
            showNotification("Tool Not Found", "The tool ${tool.definition.name} was not found.", NotificationType.ERROR)
          }
          RevokeToolAccessStatus.Failed.value -> {
            logger.warn("Failed to revoke access for ${tool.definition.name} ($toolId).")
            showNotification("Failed to Revoke Access", "Failed to revoke access for ${tool.definition.name}.", NotificationType.ERROR)
          }
          else -> {
            logger.warn("Unknown status (${result.status}) when revoking access for ${tool.definition.name} ($toolId).")
            showNotification(
              "Unknown Status",
              "Unknown status (${result.status}) when revoking access for ${tool.definition.name}.",
              NotificationType.ERROR,
            )
          }
        }
      } else {
        logger.warn("Tool not found: ${request.data.toolId.hostName} ${request.data.toolId.toolId}")
        showNotification("Tool Not Found", "The tool ${tool?.definition?.name} was not found.", NotificationType.ERROR)
      }
    } catch (e: Exception) {
      logger.error("Error revoking access: ${e.message}", e)
    }

    return this.toolConfigGetDefinitions(
      ToolConfigGetDefinitionsRequest.newBuilder().setData(
        ToolConfigGetDefinitionsRequestData.newBuilder().setUseCache(false).build(),
      ).build(),
    )
  }

  override suspend fun getStoredMCPServers(request: GetStoredMCPServersRequest): GetStoredMCPServersResponse {
    return AugmentIntegrationsConfig.instance.mcpServers
  }

  override suspend fun setStoredMCPServers(request: SetStoredMCPServersRequest): Empty {
    AugmentIntegrationsConfig.instance.mcpServers = GetStoredMCPServersResponse.newBuilder().addAllData(request.dataList).build()

    // Convert to the format expected by the sidecar
    project.serviceOrNull<SidecarService>()?.setMcpServers(AugmentIntegrationsConfig.instance.mcpServersForSidecar)

    return Empty.getDefaultInstance()
  }

  override suspend fun executeInitialOrientation(request: ExecuteInitialOrientationRequest): Empty {
    logger.info("Execute initial orientation requested")

    // TODO: Implement initial orientation execution

    return Empty.getDefaultInstance()
  }

  override suspend fun updateUserGuidelines(request: UpdateUserGuidelinesRequest): Empty {
    logger.info("Update user guidelines requested")

    val guidelinesService = GuidelinesService.getInstance(project)
    guidelinesService.updateUserGuidelines(
      request.data,
    )
    return Empty.getDefaultInstance()
  }

  /**
   * Handle sign out request from the settings webview.
   * This is similar to the VS Code implementation in settings-panel.ts.
   */
  override suspend fun signOut(request: SignOutRequest): Empty {
    logger.info("Sign out requested from settings webview")

    // Clear the OAuth state (sign out)
    AugmentOAuthState.instance.clear()
    closeSettingsFile()

    return Empty.getDefaultInstance()
  }

  @VisibleForTesting
  fun closeSettingsFile() {
    val editorManager = FileEditorManager.getInstance(project)

    if (settingsVirtualFile == null) {
      /**
       * Some reason settingsVirtualFile is null, but the file is still open.  So
       * this will look to see if its open and close it.
       */
      editorManager.openFiles.forEach { file ->
        if (file.name == AugmentSettingsWebviewEditorVirtualFile.SETTINGS_VIRTUAL_FILE_NAME) {
          invokeLater {
            editorManager.closeFile(file)
          }
        }
      }
      return
    }
    // snapshot the file reference so we don't have a race condition in the invokeLater
    val fileToClose = settingsVirtualFile
    invokeLater {
      editorManager.closeFile(fileToClose!!)
    }
    settingsVirtualFile = null
  }

  /**
   * Creates the settings webview if it does not exist and opens the virtual file in the editor.
   * If a section is provided, navigates to that section in the settings.
   *
   * @param section Optional section to navigate to in the settings
   */
  fun openSettingsWebview(section: String? = null) {
    ProperThreadUsageChecker.shouldNotBeOnEDT("openSettingsWebview")
    val editorManager = FileEditorManager.getInstance(project)

    // Initialize the messaging service to handle WebViewMessageType.signOut messages

    if (settingsVirtualFile == null) {
      // Create the file with a handler to navigate to the specified section if provided
      settingsVirtualFile = AugmentSettingsWebviewEditorVirtualFile(project, cs, section)

      Disposer.register(AugmentDisposable.getInstance(project)) {
        closeSettingsFile()
      }
    }

    // using fully qualified class name to avoid ambiguity with com.intellij.openapi.application.invokeLater
    // TODO: A lint rule for it, since team is ok with it.
    com.intellij.openapi.application.invokeLater {
      editorManager.openFile(settingsVirtualFile!!, true, true)
      if (section != null) {
        // Navigate to the specified section if the file already exists
        settingsVirtualFile!!.navigateToSection(section)
      }
    }
  }

  private fun showNotification(
    title: String,
    content: String,
    notificationType: NotificationType,
  ) {
    com.intellij.openapi.application.invokeLater {
      Notifications.Bus.notify(
        Notification(
          "augment.notifications",
          title,
          content,
          notificationType,
        ),
        project,
      )
    }
  }
}
