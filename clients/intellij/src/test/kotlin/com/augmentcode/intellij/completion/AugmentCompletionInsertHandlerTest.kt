package com.augmentcode.intellij.completion

import com.augmentcode.api.ResolveCompletionsRequest
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.generateCompletionResult
import com.augmentcode.intellij.mock.*
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.codeInsight.inline.completion.InlineCompletionEvent
import com.intellij.codeInsight.inline.completion.InlineCompletionHandler
import com.intellij.codeInsight.inline.completion.session.InlineCompletionContext
import com.intellij.codeInsight.inline.completion.testInlineCompletion
import com.intellij.testFramework.TestDataPath
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import io.mockk.spyk
import io.mockk.verify
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentCompletionInsertHandlerBasicTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.completionURL = "http://test-server"
    AugmentSettings.instance.apiToken = "test-token"
  }

  @Suppress("UnstableApiUsage")
  @Test
  fun testAfterInsertionWhenAPIReady() =
    myFixture.testInlineCompletion {
      val gson = GsonUtil.createApiGson()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/record-onboarding-session-event" -> {
              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/completion" -> {
              respond(
                content = """{"completion_items":[{"text":"Hello from Augment!\")"}]}""",
                status = HttpStatusCode.OK,
                headers = headersOf("Content-Type", "application/json"),
              )
            }
            "/resolve-completions" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val resolveRequest = gson.fromJson(requestBody, ResolveCompletionsRequest::class.java)

              // Verify the resolution data
              assertEquals(1, resolveRequest.resolutions.size)
              val resolution = resolveRequest.resolutions.first()
              assertEquals(0, resolution.acceptedIdx)
              assertTrue(resolution.emitTimeSec <= resolution.resolveTimeSec)

              respond(
                content = """{"success": true}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      augmentHelpers().registerMockEngine(mockEngine)

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      val context = InlineCompletionContext.getOrNull(fixture.editor)
      assertEquals("Hello from Augment!\")", context?.textToInsert())
      insert()
      myFixture.checkResultByFile("simple.expected.txt")

      // Wait for completion to be processed
      waitForAssertion({
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/resolve-completions" })
      })
    }
}

@RunWith(JUnit4::class)
@TestDataPath("\$CONTENT_ROOT/src/test/testData")
class AugmentCompletionInsertHandlerInsertTest : AugmentBasePlatformTestCase() {
  override fun getTestDataPath() = "src/test/testData/completion"

  override fun runInDispatchThread(): Boolean = false // to use testInlineCompletion

  @Test
  @Suppress("UnstableApiUsage")
  fun testAfterInsertionWithFollowUpCompletion() {
    myFixture.testInlineCompletion {
      val completionText = "Hello from"
      val completionResult = generateCompletionResult(completionText)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(completionResult),
        testRootDisposable,
      )

      myFixture.configureByFile("simple.txt")
      InlineCompletionHandler.registerTestHandler(AugmentCompletionProvider(), testRootDisposable)
      callInlineCompletion()
      delay() // so everything is processed
      insert()

      // Now allow the secondary inline completion call to proceed and accept it
      val secondCompletion = " Augment!\")"
      val secondCompletionResult = generateCompletionResult(secondCompletion)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(secondCompletionResult),
        testRootDisposable,
      )

      delay() // so everything is processed
      insert()
      myFixture.checkResultByFile("simple.expected.txt")
    }
  }

  @Test
  @Suppress("UnstableApiUsage")
  fun testCompletionWithTriggerOnAllEditsSettingOff() {
    myFixture.testInlineCompletion {
      val completionText = "Hello"
      val completionResult = generateCompletionResult(completionText)
      application.registerOrReplaceServiceInstance(
        AugmentAPI::class.java,
        @Suppress("DEPRECATION")
        MockAugmentAPI(completionResult),
        testRootDisposable,
      )

      val augmentProvider = spyk(AugmentCompletionProvider())
      myFixture.configureByText("demo.txt", "")
      InlineCompletionHandler.registerTestHandler(augmentProvider, testRootDisposable)

      // Trigger an editor change event
      verify(timeout = 1000, exactly = 0) { augmentProvider.isEnabled(any<InlineCompletionEvent.DirectCall>()) }
    }
  }
}
