package com.augmentcode.intellij.index.ignore

import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.intellij.openapi.application.runWriteAction
import com.intellij.openapi.vfs.VfsUtil
import com.intellij.testFramework.PsiTestUtil
import junit.framework.TestCase.assertEquals
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import org.junit.Test
import java.io.File
import java.nio.file.Files

class PathFilterServiceSymlinkTest : AugmentHeavyPlatformTestCase() {
  @Test
  fun testSymlinkHandling() {
    // scenario: Create a top level directory foo, and symlink in foo that points back to foo.
    // Put a .gitignore file in foo. Get the path filter for foo. Make sure that the path filter is only one
    // entry in the path filter because, we skipped the symlink.

    val tempDir = getVirtualFile(createTempDirectory())
    PsiTestUtil.addContentRoot(module, tempDir)
    val pathFilterService = PathFilterService(project, augmentHelpers().createCoroutineScope(Dispatchers.IO))

    // Create parent foo, containing a .gitignore, and a symlink back to foo.
    // foo
    // ├── .gitignore
    // └── symlink -> foo
    val fooDir =
      runWriteAction {
        val fooDir = tempDir.createChildDirectory(this, "foo")
        fooDir.createChildData(this, ".gitignore").also {
          VfsUtil.saveText(it, "*.log")
        }
        fooDir
      }
    val fooPath = File(fooDir.path).toPath()

    // Create symlink foo/symlink -> foo
    runWriteAction {
      val symlinkPath = File(fooDir.path, "symlink")
      Files.createSymbolicLink(symlinkPath.toPath(), fooPath)
    }

    // Refresh VFS to pick up the symlink
    VfsUtil.markDirtyAndRefresh(false, true, true, tempDir)

    // Check that the path filter is only one entry
    val filter =
      runBlocking {
        pathFilterService.getPathFilter(fooDir)
      }
    val stacks = filter.getStacks()
    assertEquals(listOf(""), stacks.keys.toList())
  }

  @Test
  fun testRubrikSymlinkPerformance() {
    // scenario: Create top level directory foo, and a hundred child directories of foo called fooChild0 through fooChild99
    // Put a .gitignore file in each of them. Then, put a symlink in each of them back to foo.
    // If we skip symlinks when creating the path filter, it will take linear time to create the path filter and
    // accept the file.
    // If we fail to skip symlinks, it will cause exponential fan out up the max symlink depth,
    // which seems to be 32 on my (diehuxx) MacOS 14 machine. This will cause both creation of the path filter
    // and running through ignore rules to take a very long time.
    // Why are we writing such an esoteric test? Because this particular scenario caused a bad bug at Rubrik.
    // Rubrik's codebase has a similar set up to this test: many directories with .gitignore files,
    // and symlinks back to the parent directory. This is essentially an integration test to make sure we don't
    // regress at Rubrik.

    val tempDir = getVirtualFile(createTempDirectory())
    PsiTestUtil.addContentRoot(module, tempDir)
    val pathFilterService = PathFilterService(project, augmentHelpers().createCoroutineScope(Dispatchers.IO))

    // Create parent foo, containing a .gitignore, and a hundred child directories.
    // foo
    // ├── .gitignore
    // ├── test.txt
    // ├── fooChild0
    // │   ├── .gitignore
    // │   └── symlink -> foo
    // ├── fooChild1
    // │   ├── .gitignore
    // │   └── symlink -> foo
    // ├── fooChild2
    // │   ├── .gitignore
    // │   └── symlink -> foo
    // (and so on up to fooChild99)
    val fooDir =
      runWriteAction {
        val fooDir = tempDir.createChildDirectory(this, "foo")
        fooDir.createChildData(this, ".gitignore").also {
          VfsUtil.saveText(it, "*.log")
        }
        fooDir
      }
    val testFile =
      runWriteAction {
        fooDir.createChildData(this, "test.txt").also {
          VfsUtil.saveText(it, "test content")
        }
      }
    val fooPath = File(fooDir.path).toPath()

    for (i in 0..99) {
      runWriteAction {
        val fooChildDir = fooDir.createChildDirectory(this, "fooChild$i")
        fooChildDir.createChildData(this, ".gitignore").also {
          VfsUtil.saveText(it, "*.log")
        }
        // and create a symlink back to foo call fooChild$i/symlink -> foo
        val symlinkPath = File(fooChildDir.path, "symlink")
        Files.createSymbolicLink(symlinkPath.toPath(), fooPath)
      }
    }

    // Refresh VFS to pick up the symlinks
    VfsUtil.markDirtyAndRefresh(false, true, true, tempDir)

    // Check that the file is accepted within a short timeout
    var isAccepted: Boolean
    runBlocking {
      try {
        withTimeout(100) {
          isAccepted = pathFilterService.isAccepted(testFile)
        }
        assertTrue("File should be accepted", isAccepted)
      } catch (e: TimeoutCancellationException) {
        fail("isAccepted timed out. Recursive symlink may have caused circular path filter")
      }
    }
  }
}
