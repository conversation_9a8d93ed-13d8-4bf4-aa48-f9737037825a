package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.openapi.util.Disposer
import kotlinx.coroutines.Dispatchers
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

/**
 * This test is fairly unique - it tests the lifecycle of the plugin state service
 * which is an application level service.
 *
 * To ensure the connection is closed, we have to do some assertions in the
 * teardown (i.e. after the application is disposed).
 */
@RunWith(JUnit4::class)
class PluginStateServiceTest : AugmentBasePlatformTestCase() {
  @Test
  fun testShouldRegisterAndUnregisterEventListeners() {
    val cs = augmentHelpers().createCoroutineScope(Dispatchers.IO)
    val service = PluginStateService(cs)

    var stateChanges = 0
    service.subscribe(
      project.messageBus.connect(service),
      object : PluginStateListener {
        override fun onStateChange(
          context: PluginContext?,
          state: PluginState,
        ) {
          stateChanges++
        }
      },
    )

    waitForAssertion({
      assertEquals(PluginState.SIGN_IN_REQUIRED, service.state)
      assertEquals(null, service.context)
    })

    // Save credentials to trigger a credentials message
    stateChanges = 0
    AugmentOAuthState.instance.saveCredentials(AugmentCredentials("access-token", "http://test-server"))

    waitForAssertion({
      assertEquals(PluginState.ENABLED, service.state)
      assertEquals(
        PluginContext(
          FeatureFlags.fromModelConfig(HttpUtil.defaultMockModelConfig),
          AugmentModel.fromModelConfig(HttpUtil.defaultMockModelConfig),
        ),
        service.context,
      )
      assert(stateChanges > 0)
    })

    // Change settings to trigger a settings message
    stateChanges = 0
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "" // Not completion URL will lead to not available

    waitForAssertion({
      assertEquals(PluginState.SIGN_IN_REQUIRED, service.state)
      assertEquals(null, service.context)
      assert(stateChanges > 0)
    })

    // Dispose of the service to remove the message bus connection
    Disposer.dispose(service)
    stateChanges = 0

    // Further updates should not lead to any state changes
    AugmentSettings.instance.apiToken = "test-token-2"
    AugmentSettings.instance.completionURL = "http://test-server"

    // Clear credentials to trigger a credentials message
    AugmentOAuthState.instance.clear()

    assertEquals(0, stateChanges)
  }
}
