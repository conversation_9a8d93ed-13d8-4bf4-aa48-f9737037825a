package com.augmentcode.intellij.utils

import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.waitForAssertion
import com.intellij.openapi.util.Disposer
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.util.concurrent.atomic.AtomicInteger

@RunWith(JUnit4::class)
class CoalescingExecutorTest : AugmentBasePlatformTestCase() {
  @Test
  fun testCoalescing() {
    val executorRunCount = AtomicInteger(0)
    val deferred = CompletableDeferred<Unit>()
    val executor =
      CoalescingExecutor(augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) {
        executorRunCount.incrementAndGet()
        deferred.await()
      }

    executor.trigger()

    waitForAssertion({
      assertEquals(1, executorRunCount.get())
    })

    // Should not run again until the first run is finished
    executor.trigger()
    executor.trigger()
    executor.trigger()

    assertEquals(1, executorRunCount.get())

    // Finish the first run
    deferred.complete(Unit)

    waitForAssertion({
      assertEquals(2, executorRunCount.get())
    })

    // Disposing the executor should prevent any further runs
    Disposer.dispose(executor)
    executor.trigger()

    assertEquals(2, executorRunCount.get())
  }

  @Test
  fun testCoalescingDisposalWhileRunning() {
    val executorRunCount = AtomicInteger(0)
    val deferred = CompletableDeferred<Unit>()
    val executor =
      CoalescingExecutor(augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) {
        executorRunCount.incrementAndGet()
        deferred.await()
      }

    executor.trigger()

    waitForAssertion({
      assertEquals(1, executorRunCount.get())
    })

    // Should not run again until the first run is finished
    executor.trigger()
    executor.trigger()
    executor.trigger()

    assertEquals(1, executorRunCount.get())

    Disposer.dispose(executor)

    // Finish the first run
    deferred.complete(Unit)

    waitForAssertion({
      assertEquals(1, executorRunCount.get())
    })
  }

  @Test
  fun testCoalescingResettingWhileRunning() {
    val executorRunCount = AtomicInteger(0)
    val executorCompleteCount = AtomicInteger(0)
    val deferreds = mutableListOf<CompletableDeferred<Unit>>()
    val executor =
      CoalescingExecutor(augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) {
        executorRunCount.incrementAndGet()

        val deferred = CompletableDeferred<Unit>()
        deferreds.add(deferred)
        deferred.await()

        executorCompleteCount.incrementAndGet()
      }

    executor.trigger()

    waitForAssertion({
      assertEquals(1, executorRunCount.get())
    }, timeoutMs = 10000)

    // Should not run again until the first run is finished
    executor.trigger()
    executor.trigger()
    executor.trigger()

    assertEquals(1, executorRunCount.get())
    assertEquals(0, executorCompleteCount.get())

    executor.reset()

    assertEquals(1, executorRunCount.get())
    assertEquals(0, executorCompleteCount.get())

    // Should run again since we reset, even though the first runs deferred is still pending
    // This ensures the mutex is released when previous work is cancelled
    executor.trigger()

    waitForAssertion({
      assertEquals(2, executorRunCount.get())
      assertEquals(0, executorCompleteCount.get())
    })

    // Trigger a re-run
    executor.trigger()
    executor.trigger()

    // Complete our deferreds
    deferreds.forEach { it.complete(Unit) }

    // Wait for the re-run to finish
    waitForAssertion({
      assertEquals(3, executorRunCount.get())
    })

    // Complete any remaining deferreds
    deferreds.forEach { it.complete(Unit) }
  }
}
