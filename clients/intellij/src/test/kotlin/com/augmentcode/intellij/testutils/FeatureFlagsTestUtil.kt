package com.augmentcode.intellij.testutils

import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.AugmentAPIImpl
import com.augmentcode.intellij.api.HttpClientProvider
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.settings.FeatureFlagManager
import com.intellij.openapi.Disposable
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.plugins.HttpTimeout
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import java.net.URI

/**
 * Utility class for setting up feature flags in tests.
 * This implementation avoids using the deprecated MockAugmentAPI class.
 */
object FeatureFlagsTestUtil {
  /**
   * Sets up feature flags for testing by mocking the API response.
   *
   * @param disposable The disposable to register the mock HTTP client with
   * @param featureFlags A map of feature flag names to their values
   */
  fun setupFeatureFlags(
    disposable: Disposable,
    featureFlags: Map<String, Any>,
  ) {
    // Set up test credentials
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"

    // Create a JSON string with the feature flags
    val featureFlagsJson =
      featureFlags.entries.joinToString(
        prefix = "{",
        postfix = "}",
        separator = ",",
      ) { (key, value) ->
        when (value) {
          is String -> "\"$key\": \"$value\""
          is Boolean, is Number -> "\"$key\": $value"
          else -> "\"$key\": \"${value}\""
        }
      }

    // Setup mock HTTP client with feature flags
    val mockEngine =
      MockEngine { request ->
        when (request.url.encodedPath) {
          "/get-models" -> {
            respond(
              content = """{
              "models": [
                {
                  "name": "test-model",
                  "display_name": "Test Model",
                  "feature_flags": $featureFlagsJson
                }
              ],
              "default_model_name": "test-model",
              "feature_flags": $featureFlagsJson
            }""",
              status = HttpStatusCode.OK,
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }

          else -> {
            respond(
              content = "{}",
              status = HttpStatusCode.OK,
              headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
          }
        }
      }

    // Register the mock HTTP client
    application.registerOrReplaceServiceInstance(
      HttpClientProvider::class.java,
      object : HttpClientProvider {
        override fun clientFor(uri: URI): HttpClient {
          return HttpClient(mockEngine) {
            install(HttpTimeout)
          }
        }
      },
      disposable,
    )
    // Clear the model cache in AugmentAPI to ensure we get fresh data
    clearAugmentAPICache()
    FeatureFlagManager.instance.fetchAndCacheFeatureFlags()
  }

  /**
   * Clears the model cache in AugmentAPI to ensure we get fresh data.
   * This is necessary because the AugmentAPI caches model info, including feature flags.
   */
  private fun clearAugmentAPICache() {
    // Get the AugmentAPI instance and clear its cache
    val apiInstance = AugmentAPI.instance
    if (apiInstance is AugmentAPIImpl) {
      // The clearModelCache method is marked with @VisibleForTesting
      apiInstance.clearModelCache()
    }
  }
}
