package com.augmentcode.intellij.webviews.chat

import com.augmentcode.intellij.index.AugmentBlobStateReader
import com.augmentcode.intellij.mock.PathFilterServiceMocks
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.HttpUtil
import com.intellij.psi.PsiElement
import com.intellij.testFramework.IndexingTestUtil
import com.intellij.testFramework.fixtures.BasePlatformTestCase
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class FindSymbolTest : BasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
    HttpUtil.mockServerSync(testRootDisposable)
  }

  @Test
  fun testFindSymbolMatchingSearchScope() {
    PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

    val file =
      myFixture.configureByText(
        "foo.java",
        """
        class Foo {
            public void fooMethod() {
            }
        }
        """.trimIndent(),
      )
    IndexingTestUtil.waitUntilIndexesAreReady(project)
    val blobState = AugmentBlobStateReader.read(file)
    assertNotNull(blobState)
    assertEquals("foo.java", blobState?.relativePath)

    val messagingService = ChatMessagingService.getInstance(project)
    val responses =
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
              "type":"async-wrapper",
              "requestId":"abc-123",
              "error":null,
              "baseMsg": {
                  "type": "find-symbol-request",
                  "data": {
                      "query": "Foo",
                      "searchScope": {
                          "files": [
                              {
                                  "pathName": "foo.java"
                              }
                          ]
                      }
                  }
              }
          }
          """.trimIndent(),
        ).toList()
      }

    assertEquals(1, responses.size)
    assertEquals(
      """
      {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
              "data": [
                  {
                      "file": {
                          "repoRoot": "",
                          "pathName": "foo.java",
                          "range": {
                              "start": 1,
                              "stop": 4
                          },
                          "fullRange": {
                              "startLineNumber": 0,
                              "startColumn": 0,
                              "endLineNumber": 3,
                              "endColumn": 1
                          }
                      }
                  }
              ],
              "type": "find-symbol-response"
          },
          "type": "async-wrapper"
      }
      """.trimIndent().toPrettyJson(),
      responses.first().toPrettyJson(),
    )
  }

  @Test
  fun testFindSymbolRegexWithNullTextRange() {
    PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

    // Create a fake PSI element that returns null for textRange
    myFixture.configureByText(
      "foo.java",
      """
      class Foo {
          public void fooMethod() {
          }
      }
      """.trimIndent(),
    )
    // Mock PSI elements to return null text range
    mockk<PsiElement> {
      every { parent } returns myFixture.file
      every { textRange } returns null
      every { text } returns "testFakeMethod"
    }

    val messagingService = ChatMessagingService.getInstance(project)
    val responses =
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
              "type":"async-wrapper",
              "requestId":"abc-123",
              "error":null,
              "baseMsg": {
                  "type": "find-symbol-request",
                  "data": {
                      "query": "test.*",
                      "searchScope": {
                          "files": []
                      }
                  }
              }
          }
          """.trimIndent(),
        ).toList()
      }

    assertEquals(1, responses.size)
    // expect empty response
    assertEquals(
      """
      {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
              "data": [],
              "type": "find-symbol-response"
          },
          "type": "async-wrapper"
      }
      """.trimIndent().toPrettyJson(),
      responses.first().toPrettyJson(),
    )
  }

  @Test
  fun testFindSymbolInWrongFile() {
    PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

    val file =
      myFixture.configureByText(
        "foo.java",
        """
        class Foo {
            public void fooMethod() {
            }
        }
        """.trimIndent(),
      )
    IndexingTestUtil.waitUntilIndexesAreReady(project)
    val blobState = AugmentBlobStateReader.read(file)
    assertNotNull(blobState)
    assertEquals("foo.java", blobState?.relativePath)

    val messagingService = ChatMessagingService.getInstance(project)
    val responses =
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
              "type":"async-wrapper",
              "requestId":"abc-123",
              "error":null,
              "baseMsg": {
                  "type": "find-symbol-request",
                  "data": {
                      "query": "Foo",
                      "searchScope": {
                          "files": [
                              {
                                  "pathName": "bar.java"
                              }
                          ]
                      }
                  }
              }
          }
          """.trimIndent(),
        ).toList()
      }

    assertEquals(1, responses.size)
    // expect empty response since the symbol is not in the file
    // specified in the search scope
    assertEquals(
      """
      {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
              "data": [],
              "type": "find-symbol-response"
          },
          "type": "async-wrapper"
      }
      """.trimIndent().toPrettyJson(),
      responses.first().toPrettyJson(),
    )
  }

  @Test
  fun testFindSymbolEmptySearchScope() {
    PathFilterServiceMocks.mockAlwaysAccept(project, testRootDisposable)

    val file =
      myFixture.configureByText(
        "foo.java",
        """
        class Foo {
            public void fooMethod() {
            }
        }
        """.trimIndent(),
      )
    IndexingTestUtil.waitUntilIndexesAreReady(project)
    val blobState = AugmentBlobStateReader.read(file)
    assertNotNull(blobState)
    assertEquals("foo.java", blobState?.relativePath)

    val messagingService = ChatMessagingService.getInstance(project)
    val responses =
      runBlocking {
        messagingService.processMessageFromWebview(
          """
          {
              "type":"async-wrapper",
              "requestId":"abc-123",
              "error":null,
              "baseMsg": {
                  "type": "find-symbol-request",
                  "data": {
                      "query": "Foo",
                      "searchScope": {
                          "files": []
                      }
                  }
              }
          }
          """.trimIndent(),
        ).toList()
      }

    assertEquals(1, responses.size)
    assertEquals(
      """
      {
          "requestId": "abc-123",
          "error": "",
          "baseMsg": {
              "data": [
                  {
                      "file": {
                          "repoRoot": "",
                          "pathName": "foo.java",
                          "range": {
                              "start": 1,
                              "stop": 4
                          },
                          "fullRange": {
                              "startLineNumber": 0,
                              "startColumn": 0,
                              "endLineNumber": 3,
                              "endColumn": 1
                          }
                      }
                  }
              ],
              "type": "find-symbol-response"
          },
          "type": "async-wrapper"
      }
      """.trimIndent().toPrettyJson(),
      responses.first().toPrettyJson(),
    )
  }
}
