package com.augmentcode.intellij.pluginstate

import com.augmentcode.intellij.auth.AugmentCredentials
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.featureflags.FeatureFlags
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.mockOAuthState
import com.augmentcode.intellij.testutils.waitForAssertionAsync
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import java.util.Collections

@RunWith(JUnit4::class)
class PluginStateExecutorTest : AugmentBasePlatformTestCase() {
  @Test
  fun testUpdateStateSignedOut() =
    runTest {
      var ctx: PluginContext? = null
      var state: PluginState? = null

      val executor =
        PluginStateExecutor(augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          ctx = c
          state = s
        }
      executor.updateState()

      waitForAssertionAsync({
        assertEquals(state, PluginState.SIGN_IN_REQUIRED)
        assertEquals(ctx, null)
      })
    }

  @Test
  fun testUpdateStateSignedIn() =
    runTest {
      AugmentOAuthState.instance.saveCredentials(AugmentCredentials("access-token", "http://test-server"))

      var ctx: PluginContext? = null
      var state: PluginState? = null

      val executor =
        PluginStateExecutor(augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          ctx = c
          state = s
        }
      executor.updateState()

      waitForAssertionAsync({
        assertEquals(PluginState.ENABLED, state)
        assertEquals(
          PluginContext(
            FeatureFlags.fromModelConfig(HttpUtil.defaultMockModelConfig),
            AugmentModel.fromModelConfig(HttpUtil.defaultMockModelConfig),
          ),
          ctx,
        )
      })
    }

  @Test
  fun testUpdateStateMultipleCalls() =
    runTest {
      mockOAuthState(AugmentCredentials("access-token", "http://test-server"), testRootDisposable)

      // This deferred is used to control when the /get-models request is responded to
      val deferred = CompletableDeferred<Unit>()
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> {
              runBlocking {
                deferred.await()
              }
              HttpUtil.respondGetModels(this)
            }

            // These endpoints might be called but are safe to ignore
            "/report-error" -> HttpUtil.respondOK(this)
            "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
            else -> throw IllegalStateException("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      var ctx: PluginContext? = null
      var state: PluginState? = null

      val states = Collections.synchronizedList(mutableListOf<PluginState>())
      val executor =
        PluginStateExecutor(augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          synchronized(states) {
            states.add(s)
          }
          ctx = c
          state = s
        }

      executor.updateState()

      waitForAssertionAsync({
        assertEquals(PluginState.INITIALIZING, state)
        assertEquals(null, ctx)
      })

      // Calling update 5 times should result in an extra run of the executor
      // once it's current execution is done.
      executor.updateState()
      executor.updateState()
      executor.updateState()
      executor.updateState()
      executor.updateState()

      // Make sure the first call to /get-models was made
      waitForAssertionAsync({
        assertEquals(listOf(PluginState.INITIALIZING), synchronized(states) { states })
      })
      // Ensure state is what we expect
      assertEquals(PluginState.INITIALIZING, state)
      assertEquals(null, ctx)

      // Allow the current execution to finish
      deferred.complete(Unit)

      // We should see a second call to /get-models because the executor should have
      // been triggered again due to the updateState() calls
      waitForAssertionAsync({
        assertEquals(
          listOf(
            PluginState.INITIALIZING,
            PluginState.ENABLED,
            PluginState.INITIALIZING,
            PluginState.ENABLED,
          ),
          synchronized(states) {
            states
          },
        )
      })

      // The state should have updated to Enabled
      assertEquals(PluginState.ENABLED, state)
      assertEquals(
        PluginContext(
          FeatureFlags.fromModelConfig(HttpUtil.defaultMockModelConfig),
          AugmentModel.fromModelConfig(HttpUtil.defaultMockModelConfig),
        ),
        ctx,
      )
    }

  @Test
  fun testUpdateStateMultipleCallsWithFailure() =
    runTest {
      AugmentOAuthState.instance.saveCredentials(AugmentCredentials("access-token", "http://test-server"))

      // This deferred is used to control when the /get-models request is responded to
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> {
              respond(
                content = "{}",
                status = HttpStatusCode.InternalServerError,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            // These endpoints might be called but are safe to ignore
            "/report-error" -> HttpUtil.respondOK(this)
            "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
            else -> throw IllegalStateException("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      var ctx: PluginContext? = null
      var state: PluginState? = null

      val executor =
        PluginStateExecutor(augmentHelpers().createCoroutineScope(Dispatchers.IO), testRootDisposable) { c, s ->
          ctx = c
          state = s
        }
      executor.updateState()

      waitForAssertionAsync({
        assertEquals(PluginState.GET_MODEL_INFO_FAILED, state)
        assertEquals(null, ctx)
      })
    }
}
