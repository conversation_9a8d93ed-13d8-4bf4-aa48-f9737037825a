package com.augmentcode.intellij.testutils

import com.augmentcode.api.FeatureFlags
import com.augmentcode.api.Language
import com.augmentcode.api.Model
import com.augmentcode.api.ModelConfig
import com.augmentcode.intellij.api.AugmentAPI
import com.augmentcode.intellij.api.AugmentAPIImpl
import com.augmentcode.intellij.api.HttpClientProvider
import com.augmentcode.intellij.auth.AugmentOAuthService
import com.augmentcode.intellij.auth.AugmentOAuthState
import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.status.StateManager
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.intellij.openapi.Disposable
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.testFramework.replaceService
import com.intellij.testFramework.unregisterService
import com.intellij.util.application
import io.ktor.client.HttpClient
import io.ktor.client.engine.mock.MockEngine
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkAll
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.runBlocking
import java.net.URI
import java.util.concurrent.CompletableFuture
import kotlin.coroutines.CoroutineContext

class AugmentTestHelpers(private val project: Project, private val testRootDisposable: Disposable) {
  companion object {
    // NOTE: This is a static method as we want to run it after all tests are completed and the testRootDisposal
    // has removed all registered mock services
    fun cleanup() {
      // Prevent tests impacting over tests with cached model info
      (AugmentAPI.instance as AugmentAPIImpl).clearModelCache()
    }
  }

  fun setUp() {
    cleanup()
    resetGlobalServices()
    registerMockEngine(baseMockEngine()) // Ensure the API is mocked
    preventOAuthBrowserFlow()

    // Run this in setup only since if a test mocks AugmentRemoteSyncingManager
    // and they don't allow for reset() to be mocked (i.e. not a relaxed mock)
    // then the teardown can fail.
    runBlocking {
      AugmentRemoteSyncingManager.getInstance(project).reset()
    }
  }

  fun tearDown() {
    resetGlobalServices()
  }

  fun resetGlobalServices() {
    // Clean up mocks
    unmockkAll()

    // TODO: Instead of this, can we replace the AugmentSettings service
    // This is currently using persistent state.
    AugmentSettings.instance.modelName = ""
    AugmentSettings.instance.apiToken = ""
    AugmentSettings.instance.completionURL = ""
    AugmentSettings.instance.inlineCompletionEnabled = true

    AugmentOAuthState.instance.clear()
    StateManager.getInstance(project).reset()
    PluginStateService.instance.reset()

    unregisterServiceIfNeeded(PathFilterService::class.java) // to clean it lazily
  }

  fun registerMockEngine(mockEngine: MockEngine) {
    application.registerOrReplaceServiceInstance(
      HttpClientProvider::class.java,
      object : HttpClientProvider {
        override fun clientFor(uri: URI): HttpClient {
          return HttpClient(mockEngine) {
            // Install the HttpTimeout plugin that AugmentHttpClient expects
            install(io.ktor.client.plugins.HttpTimeout)
          }
        }
      },
      testRootDisposable,
    )
  }

  // Override this if you want to change the mock engine implementation for all
  // your tests
  fun baseMockEngine(): MockEngine {
    return MockEngine { request ->
      when (request.url.encodedPath) {
        "/get-models" -> HttpUtil.respondGetModels(this)
        "/record-onboarding-session-event" -> HttpUtil.respondOK(this)
        else -> throw IllegalStateException("Unexpected request to ${request.url.encodedPath}")
      }
    }
  }

  fun createCoroutineScope(context: CoroutineContext): CoroutineScope {
    val cs = CoroutineScope(context)
    Disposer.register(testRootDisposable) {
      cs.cancel("Test disposed")
    }
    return cs
  }

  fun unregisterServiceIfNeeded(serviceInterface: Class<*>) {
    if (project.getService(serviceInterface) != null) {
      try {
        project.unregisterService(serviceInterface)
      } catch (e: Exception) {
        // Ignore exceptions during cleanup
      }
    }
  }

  private fun preventOAuthBrowserFlow() {
    val mockOAuthService = mockk<AugmentOAuthService>(relaxed = true)
    every { mockOAuthService.authorize() } returns CompletableFuture.completedFuture(null)

    mockkObject(AugmentOAuthService.Companion)
    every { AugmentOAuthService.instance } returns mockOAuthService

    application.replaceService(AugmentOAuthService::class.java, mockOAuthService, testRootDisposable)
  }

  fun createModelConfig(flags: FeatureFlags = FeatureFlags()): ModelConfig {
    return ModelConfig().apply {
      defaultModelName = "default-model"
      supportedLanguages =
        listOf(
          Language().apply {
            name = "Text"
            extensions = setOf(".txt")
          },
          Language().apply {
            name = "Go"
            extensions = setOf(".go")
          },
          Language().apply {
            name = "XML"
            extensions = setOf(".xml")
          },
          Language().apply {
            name = "Java"
            extensions = setOf(".java")
          },
        )
      availableModels =
        listOf(
          Model().apply {
            name = defaultModelName
            suggestedPrefixCharCount = 4096
            suggestedSuffixCharCount = 4096
          },
          Model().apply {
            name = "alternative-model"
            suggestedPrefixCharCount = 9192
            suggestedSuffixCharCount = 9192
          },
        )
      featureFlags = flags
    }
  }
}
