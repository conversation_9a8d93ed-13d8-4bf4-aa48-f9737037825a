#!/bin/bash

set -euo pipefail

declare -rA ENV_TO_CLUSTER=(
	[DEV]=gcp-agent0
	[STAGING]=gcp-prod-agent0
	[PROD]=gcp-prod-agent0
)

usage() {
	cat <<-EOF
	Usage:
	  $(basename "$0") <command> [flags]

	**This binary is only meant to be run from bazel.** Most flags are mandatory and cannot be overridden. Both
	the "outie" image and "innie" image are pushed by default, however only the "outie" image should really be
	needed -- the outie embeds its own copie of the innie image.

	Commands:
	  names       Print both names.
	  outie-name  Print the outie image name.
	  innie-name  Print the innie image name.
	  push        Push both images using crane.
	  push-outie  Push just the outie image using crane.
	  push-innie  Push just the innie image using crane.
	  copy        Copy an existing image with a new tag (uses --source-tag and --tag).

	Deployment Flags:
	  --env=enum       Services environment (DEV, STAGING, PROD).
	  --tag=str        Behavior depends on --env:
	                     DEV:           [required] Typically dev-{user} or "DEV" for the environment default.
	                     STAGING|PROD:  Defaults to "STAGING" or "PROD". Only set if you know what you're doing.
	  --source-tag=str Source tag to copy from (only used with 'copy' command). Defaults to "STAGING".

	Build-System Flags:
	  --oci-outie=path   Augment Remote Agent OCI Image directory (outie).
	  --oci-innie=path   Augment Remote Agent OCI Image directory (innie)
	  --jsonnet=path     Path to the (go-)jsonnet executable.
	  --clusters=path    Path to cluster definitions (infra/cfg/clusters/clusters.jsonnet).
	  --crane=path       Path to crane binary, for pushing.

	Env to Cluster Mapping:
	EOF
	for env in "${!ENV_TO_CLUSTER[@]}"; do
		printf "  %-7s -> %s\n" "$env" "${ENV_TO_CLUSTER[$env]}"
	done
}

main() {
	if [[ -z "${BAZEL_REAL:-}" && -z "${BUILD_WORKSPACE_DIRECTORY:-}" && -z "${RUNFILES_DIR:-}" ]]; then
		echo "ERROR: This script is meant to be run from Bazel." >&2
		usage >&2
		exit 1
	fi

	eval set -- $(getopt -n "$0" -o 'h' --long 'env:,tag:,source-tag:,dest-tag:,oci-innie:,oci-outie:,jsonnet:,clusters:,crane:,help,usage' -- "$@")
	declare -i _NO_VIRT=0

	while true; do
		case "$1" in
		--)
			shift
			break
			;;
		--env)
			declare -r _ENV="${2^^}" # toupper
			shift 2
			continue
			;;
		--tag)
			declare -r _TAG="$2"
			shift 2
			continue
			;;
		--source-tag)
			declare -r _SOURCE_TAG="$2"
			shift 2
			continue
			;;
		--oci-outie)
			declare -r _OCI_OUTIE="$2"
			shift 2
			continue
			;;
		--oci-innie)
			declare -r _OCI_INNIE="$2"
			shift 2
			continue
			;;
		--jsonnet)
			declare -r _JSONNET="$2"
			shift 2
			continue
			;;
		--clusters)
			declare -r _CLUSTERS="$2"
			shift 2
			continue
			;;
		--crane)
			declare -r _CRANE="$2"
			shift 2
			continue
			;;
		-h | --help | --usage)
			usage
			return 0
			;;
		*)
			err_exit "Invalid Option: %s." "$1"
			;;
		esac
	done

	if (( $# > 1 )); then
		err_exit "Invalid number of positional arguments."
	elif [[ "$_ENV" == "DEV" ]]; then
		if [[ -z "${_TAG:-}" ]]; then
			err_exit "$(tput setaf 4)--tag$(tput setaf 1) is required for $(tput setaf 4)%s$(tput setaf 1)." "$_ENV"
		fi
	elif [[ "$_ENV" == "STAGING" ]]; then
		[[ "${_TAG:-}" ]] || declare -r _TAG="$_ENV"
	elif [[ "$_ENV" == "PROD" ]]; then
		[[ "${_TAG:-}" ]] || declare -r _TAG="$_ENV"
	else
		err_exit "Invalid environment: %s." "$_ENV"
	fi

	# Set default source tag for copy command
	[[ "${_SOURCE_TAG:-}" ]] || declare -r _SOURCE_TAG="STAGING"

	declare -r outie_image_name="$(_image_name_outie)"
	declare -r innie_image_name="$(_image_name_innie)"

	declare -r cmd="${1:-names}"
	case "$cmd" in
		names)
			_print_name "Outie Image" "$outie_image_name"
			_print_name "Innie Image" "$innie_image_name"
			;;
		outie-name)
			_print_name "Outie Image" "$outie_image_name"
			;;
		innie-name)
			_print_name "Innie Image" "$innie_image_name"
			;;
		push)
			_push "$_OCI_OUTIE" "$outie_image_name"
			_push "$_OCI_INNIE" "$innie_image_name"
			;;
		push-outie)
			_push "$_OCI_OUTIE" "$outie_image_name"
			;;
		push-innie)
			_push "$_OCI_INNIE" "$innie_image_name"
			;;
		copy)
			_copy
			;;
		*)
			err_exit "Invalid command: %s." "$cmd"
			;;
	esac
}

err_exit() {
	declare -r fmt="$1"; shift
	usage; echo
	printf -- "$(tput bold; tput setaf 1)ERROR: ${fmt}$(tput sgr0)\n" "$@" >&2
	exit 1
}

run() {
	declare -ra cmd=("$@")
	printf "Running: $(tput bold; tput setaf 2)%s$(tput sgr0)\n" "${cmd[*]}" >&2
	"${cmd[@]}"
}

_image_name_outie() {
	_image_name "augment-remote-agent-virt"
}
_image_name_innie() {
	_image_name "augment-remote-agent"
}
_image_name() {
	declare -r basename="$1"
	declare -r cluster="${ENV_TO_CLUSTER[$_ENV]}"
	declare -r registry="$("$_JSONNET" -Se "(import '$_CLUSTERS').cluster('$cluster').services_env['$_ENV'].agent_image_registry")"
	echo -n "$registry/$basename:$_TAG"
}
_print_name() {
	declare -r label="$1" image="$2"
	if [[ -t 1 ]]; then
		printf "%s: $(tput bold; tput setaf 2)%s$(tput sgr0)\n" "$label" "$image"
	else
		printf "%s" "$image"
	fi
}

_push() {
	declare -r oci_image="$1" img_name="$2"
	declare -ra cmd=(
		crane push "$oci_image" "$img_name"
	)
	run "${cmd[@]}"
}

_copy() {
	# For STAGING -> PROD copy, use the staging registry but with the exact source tag
	if [[ "$_ENV" == "PROD" && "$_SOURCE_TAG" == STAGING* ]]; then
		declare -r source_outie_image="us-central1-docker.pkg.dev/agent-sandbox-prod/agents-staging-us-central1/augment-remote-agent-virt:$_SOURCE_TAG"
		declare -r source_innie_image="us-central1-docker.pkg.dev/agent-sandbox-prod/agents-staging-us-central1/augment-remote-agent:$_SOURCE_TAG"
	else
		# Build source image names with source tag in current environment
		declare -r source_outie_image="$(_image_name_with_tag "augment-remote-agent-virt" "$_SOURCE_TAG")"
		declare -r source_innie_image="$(_image_name_with_tag "augment-remote-agent" "$_SOURCE_TAG")"
	fi

	# Build destination image names with destination tag
	declare -r dest_outie_image="$(_image_name_outie)"
	declare -r dest_innie_image="$(_image_name_innie)"

	# Copy both images (handles cross-registry copying)
	_print_name "Copying Outie" "$source_outie_image -> $dest_outie_image"
	declare -ra outie_cmd=(
		crane copy "$source_outie_image" "$dest_outie_image"
	)
	run "${outie_cmd[@]}"

	_print_name "Copying Innie" "$source_innie_image -> $dest_innie_image"
	declare -ra innie_cmd=(
		crane copy "$source_innie_image" "$dest_innie_image"
	)
	run "${innie_cmd[@]}"
}

_image_name_with_tag() {
	declare -r basename="$1"
	declare -r tag="$2"
	declare -r cluster="${ENV_TO_CLUSTER[$_ENV]}"
	declare -r registry="$("$_JSONNET" -Se "(import '$_CLUSTERS').cluster('$cluster').services_env['$_ENV'].agent_image_registry")"
	echo -n "$registry/$basename:$tag"
}

crane() {
	"$_CRANE" "$@"
}

main "$@"
