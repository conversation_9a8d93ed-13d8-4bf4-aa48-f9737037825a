#!/usr/sbin/nft -f

# TODO(mattm): destroy.
table inet filter; delete table inet filter
table ip nat; delete table ip nat

define innie2 = **********

# Define bogon address ranges
define bogons = {
	0.0.0.0/8,         # RFC 1122 "This host on this network"
	10.0.0.0/8,        # RFC 1918 private space
	**********/10,     # RFC 6598 Carrier-grade NAT
	*********/8,       # RFC 1122 localhost
	***********/16,    # RFC 3927 link-local
	**********/12,     # RFC 1918 private space
	*********/24,      # RFC 6890 IETF Protocol Assignments
	*********/24,      # RFC 5737 TEST-NET-1
	***********/16,    # RFC 1918 private space
	**********/15,     # RFC 2544 benchmarking
	************/24,   # RFC 5737 TEST-NET-2
	***********/24,    # RFC 5737 TEST-NET-3
	*********/4,       # RFC 5771 multicast
	240.0.0.0/4,       # RFC 1112 reserved
	***************/32 # RFC 919 limited broadcast
}

table inet filter {
	chain input {
		type filter hook input priority filter; policy drop

		ct state invalid counter drop
		ct state {established, related} counter accept
		iif lo counter accept

		iif "eth0" counter jump input_WAN
		iif "br0"  counter jump input_VMs

		pkttype host limit rate 5/second counter reject with icmpx type admin-prohibited
		counter drop
	}
	chain input_WAN {
	}
	chain input_VMs {
		tcp dport 19532 counter accept comment "systemd-journal-remote receiver"
	}
	chain output {
		type filter hook output priority filter; policy accept
		ct state invalid counter drop
		counter accept
	}
}
table ip nat {
	chain prerouting {
		type nat hook prerouting priority dstnat; policy accept

		tcp dport 2222 counter dnat to $innie2
		tcp dport 9999 counter dnat to $innie2
	}
}
table inet filter {
	chain forward {
		type filter hook forward priority filter; policy drop
		iif "eth0" oif "br0"  counter jump forward_WAN_to_VMs
		iif "br0"  oif "eth0" counter jump forward_VMs_to_WAN
		counter drop
	}
	chain forward_WAN_to_VMs {
		ct state invalid counter drop
		ct state {established, related} counter accept
		tcp dport 2222 counter accept
		tcp dport 9999 counter accept
		counter drop
	}
	chain forward_VMs_to_WAN {
		ct state invalid counter drop
		ct state {established, related} counter accept
		# Block outbound traffic to bogon addresses
		ip daddr $bogons counter drop comment "Block bogon addresses"
		counter accept
	}
}
table ip nat {
	chain postrouting {
		type nat hook postrouting priority srcnat; policy accept
		oif "eth0" counter masquerade
	}
}
