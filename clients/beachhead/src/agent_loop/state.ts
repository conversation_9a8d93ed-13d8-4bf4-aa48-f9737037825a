import { limitChatHistoryTruncate } from "@augment-internal/sidecar-libs/src/chat/chat-truncation";
import {
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNode,
    ChatResultNodeType,
    Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { v4 as uuidv4 } from "uuid";

import { ChatResult } from "../augment-api";
import { getLogger } from "../logging";
import { ChangedFile, RemoteAgentStatus } from "../remote-agent-manager/types";

export type ExchangeState = {
    exchange: Exchange;
    // Whether the exchange was completed, or forcibly terminated.
    completed: boolean;
    /** The index of the exchange in the chat history. */
    sequenceId: number;
    /** ISO string format of when the exchange was finished. */
    finishedAt: string;
    /** Files changed during this exchange */
    changedFiles: ChangedFile[];
};

/**
 * Manages the state for the agent, including request nodes and chat history.
 *
 * All *finished* exchanges are stored in chatHistory.
 * The tool calls from the last exchange are stored in toolCalls.
 *
 * All *unfinished* exchanges are stored in requestNodes, requestId, responseChunks.
 * Tool call results are stored in requestNodes.
 * LLM call results are stored in responseChunks.
 *
 * The loop should call in order:
 * 1. sendUserChat (if valid status). Then loop over:
 *   a. beginRequest
 *   b. pushResponseChunk (multiple times) - LLM call
 *   c. finishResponse
 *   d. exit if no tool calls (status = agentCompleted)
 *   e. pushToolCallResult (multiple times) - Tool calls.
 *
 * TODO(jeff): If we wanted, we could enforce the order of calls using a builder pattern
 * with each state as a separate class that only has the methods for transitions
 * from that state.
 */
export class AgentState {
    private _remoteAgentId: string | undefined = undefined;
    private _conversationId: string = uuidv4();
    private _requestNodes: ChatRequestNode[] = [];
    private _requestId: string | undefined = undefined;
    private _responseChunks: ChatResult[] = [];
    private _toolCalls: ChatResultNode[] = [];
    private _chatHistory: ExchangeState[] = [];
    private _status: RemoteAgentStatus = RemoteAgentStatus.agentStarting;
    private _lastProcessedServiceUpdateSequenceId: number = 0;
    private _lastUploadedChatExchangeSequenceId: number = 0;
    private _changedFiles: ChangedFile[] = [];
    private readonly _userGuidelines: string = "";
    private readonly _workspaceGuidelines: string = "";
    private readonly _agentMemories: string = "";
    private _modelId?: string = undefined;

    private _logger = getLogger("AgentState");

    public setModelId(modelId?: string) {
        this._modelId = modelId;
    }

    public get modelId(): string | undefined {
        return this._modelId;
    }

    constructor(
        remoteAgentId: string | undefined = undefined,
        userGuidelines: string = "",
        workspaceGuidelines: string = "",
        agentMemories: string = "",
        modelId: string = ""
    ) {
        this._remoteAgentId = remoteAgentId;
        this._userGuidelines = userGuidelines;
        this._workspaceGuidelines = workspaceGuidelines;
        this._agentMemories = agentMemories;
        this._modelId = modelId;
    }

    /**
     * Creates an AgentState instance from a JSON string with type validation.
     *
     * @param jsonString The JSON string representation of an AgentState
     * @returns A new AgentState instance with properties from the JSON
     * @throws Error if the JSON is invalid or missing required properties
     */
    static fromJson(jsonString: string): AgentState {
        try {
            const parsedData = JSON.parse(jsonString) as Record<string, unknown>;
            const state = new AgentState();

            if (typeof parsedData._conversationId === "string") {
                state._conversationId = parsedData._conversationId;
            } else if (parsedData._conversationId !== undefined) {
                throw new Error("Invalid _conversationId type in state data");
            }

            if (Array.isArray(parsedData._requestNodes)) {
                state._requestNodes = parsedData._requestNodes as ChatRequestNode[];
            } else if (parsedData._requestNodes !== undefined) {
                throw new Error("Invalid _requestNodes type in state data");
            }

            if (typeof parsedData._requestId === "string" || parsedData._requestId === undefined) {
                state._requestId = parsedData._requestId;
            } else if (parsedData._requestId !== null) {
                throw new Error("Invalid _requestId type in state data");
            }

            if (Array.isArray(parsedData._responseChunks)) {
                state._responseChunks = parsedData._responseChunks as ChatResult[];
            } else if (parsedData._responseChunks !== undefined) {
                throw new Error("Invalid _responseChunks type in state data");
            }

            if (Array.isArray(parsedData._toolCalls)) {
                state._toolCalls = parsedData._toolCalls as ChatResultNode[];
            } else if (parsedData._toolCalls !== undefined) {
                throw new Error("Invalid _toolCalls type in state data");
            }

            if (Array.isArray(parsedData._chatHistory)) {
                state._chatHistory = parsedData._chatHistory as ExchangeState[];
            } else if (parsedData._chatHistory !== undefined) {
                throw new Error("Invalid _chatHistory type in state data");
            }

            if (typeof parsedData._status === "number") {
                // Ensure the status is a valid RemoteAgentStatus enum value
                if (
                    Object.values(RemoteAgentStatus).includes(
                        parsedData._status as RemoteAgentStatus
                    )
                ) {
                    state._status = parsedData._status as RemoteAgentStatus;
                } else {
                    throw new Error(`Invalid _status value: ${parsedData._status}`);
                }
            } else if (parsedData._status !== undefined) {
                throw new Error("Invalid _status type in state data");
            }
            if (typeof parsedData._lastProcessedServiceUpdateSequenceId === "number") {
                state._lastProcessedServiceUpdateSequenceId =
                    parsedData._lastProcessedServiceUpdateSequenceId;
            } else if (parsedData._lastProcessedServiceUpdateSequenceId !== undefined) {
                throw new Error("Invalid _lastProcessedServiceUpdateSequenceId type in state data");
            }
            if (typeof parsedData._lastUploadedChatExchangeSequenceId === "number") {
                state._lastUploadedChatExchangeSequenceId =
                    parsedData._lastUploadedChatExchangeSequenceId;
            } else if (parsedData._lastUploadedChatExchangeSequenceId !== undefined) {
                throw new Error("Invalid _lastUploadedSequenceId type in state data");
            }
            if (Array.isArray(parsedData._changedFiles)) {
                state._changedFiles = parsedData._changedFiles as ChangedFile[];
            } else if (parsedData._changedFiles !== undefined) {
                throw new Error("Invalid _changedFiles type in state data");
            }

            return state;
        } catch (error) {
            if (error instanceof SyntaxError) {
                throw new Error(`Failed to parse agent state JSON: ${error.message}`);
            }
            throw error;
        }
    }

    get conversationId(): string {
        return this._conversationId;
    }

    /**
     * The request nodes for the current *unfinished* exchange.
     * TODO(jeff): currently not readonly because Exchange takes a mutable array.
     */
    get requestNodes(): ChatRequestNode[] {
        return this._requestNodes;
    }

    /**
     * The request message for the current *unfinished* exchange.
     */
    get requestMessage(): string {
        return this._requestNodes
            .filter((node) => node.type === ChatRequestNodeType.TEXT)
            .map((node) => node.text_node?.content || "")
            .join("");
    }

    /**
     * The request ID for the current *unfinished* exchange.
     */
    get requestId(): string | undefined {
        return this._requestId;
    }

    /**
     * The response text for the current *unfinished* exchange.
     */
    get responseText(): string {
        const responseText = this._responseChunks.map((chunk) => chunk.text).join("");

        // If we have a MAIN_TEXT_FINISHED node, use its contents instead
        // NOTE(mpauly): This is consistent with how we handle things on the IDE side in conversation-model.ts.
        const allResponseNodes = this._responseChunks.flatMap((chunk) => chunk.nodes ?? []);
        const mainTextFinishedContent = allResponseNodes.find(
            (node) => node.type === ChatResultNodeType.MAIN_TEXT_FINISHED
        )?.content;

        if (mainTextFinishedContent) {
            return mainTextFinishedContent;
        } else {
            return responseText;
        }
    }

    /**
     * The response nodes for the current *unfinished* exchange.
     * TODO: currently not readonly because Exchange takes a mutable array.
     */
    get responseNodes(): ChatResultNode[] {
        return this._responseChunks.flatMap(
            (chunk) =>
                chunk.nodes?.filter(
                    (node) =>
                        node.type === ChatResultNodeType.RAW_RESPONSE ||
                        node.type === ChatResultNodeType.TOOL_USE
                ) || []
        );
    }

    /**
     * The current *unfinished* exchange.
     * TODO(jeff): we can remove this once we are using the chat streaming endpoint.
     */
    get currentOpenExchange(): Exchange | undefined {
        if (!this.requestId) {
            return undefined;
        }
        return {
            request_message: this.requestMessage,
            response_text: this.responseText,
            request_id: this.requestId,
            request_nodes: this.requestNodes,
            response_nodes: this.responseNodes,
        };
    }

    /**
     * The tool calls from the last *finished* exchange.
     */
    get toolCalls(): readonly ChatResultNode[] {
        return this._toolCalls;
    }

    /**
     * The chat history, including all *finished* exchanges, to be returned to the user.
     * TODO: currently not readonly because Exchange takes a mutable array.
     */
    get chatHistoryForDisplay(): readonly ExchangeState[] {
        return this._chatHistory;
    }

    /**
     * The chat history, including all *finished* exchanges, to be sent for chat calls.
     * TODO: currently not readonly because augment-api takes a mutable array.
     * TODO: currently this is O(n) and computed on every call mostly because
     * limitChatHistoryTruncate takes the entire exchange.
     */
    get chatHistoryForAPI(): Exchange[] {
        const chatHistory = this._chatHistory
            .filter((exchange) => exchange.completed)
            .map((exchange) => ({
                // Strip the request message and response text for consistency with the old implementation
                // NOTE(mpauly): I'm not 100% sure if this is necessary, but the chat.proto does say
                // that if the structured is present, the string messages should be empty.
                ...exchange.exchange,
                request_message: "",
                response_text: "",
            }));
        return limitChatHistoryTruncate(chatHistory);
    }

    /**
     * The current status of the agent.
     */
    get status(): RemoteAgentStatus {
        return this._status;
    }

    /**
     * The last processed sequence ID for agent updates.
     */
    get lastProcessedServiceUpdateSequenceId(): number {
        return this._lastProcessedServiceUpdateSequenceId;
    }

    /**
     * The last uploaded sequence ID for completed exchange.
     */
    get lastUploadedSequenceId(): number {
        return this._lastUploadedChatExchangeSequenceId;
    }

    /**
     * Sets the request nodes to the user's chat message.
     */
    sendUserChat(message: ChatRequestNode[]) {
        if (
            this.status !== RemoteAgentStatus.agentIdle &&
            this.status !== RemoteAgentStatus.agentStarting
        ) {
            this._logger.error(
                "Tried to send user chat when agent is in invalid state: %s",
                this.status
            );

            // To attempt to recover, reset the request nodes and continue.
            this._requestNodes = [];
        }

        this._status = RemoteAgentStatus.agentRunning;
        this._requestNodes.push(...message);
    }

    /**
     * The request nodes are finalized, and begin to send a request.
     */
    beginRequest(requestId: string) {
        this._requestId = requestId;
    }

    /**
     * Pushes a response chunk to the current response.
     */
    pushResponseChunk(chunk: ChatResult) {
        this._responseChunks.push(chunk);
    }

    /**
     * The response chunks for the current *unfinished* exchange.
     */
    get responseChunks(): ChatResult[] {
        return this._responseChunks;
    }

    /**
     * Pushes the current request and response chunks to the chat history,
     * sets tool calls that need to be run, and clears the request and response nodes.
     */
    finishResponse() {
        if (this.requestId === undefined) {
            // This should never be reached if called correctly.
            throw new Error("No request ID set in finishResponse");
        }

        this.pushExchange(true /* completed */);
    }

    /**
     * Pushes changed files to the current exchange.
     */
    pushChangedFiles(changedFiles: ChangedFile[]) {
        this._changedFiles.push(...changedFiles);
    }

    /**
     * Pushes a tool call result to the current request.
     */
    pushToolCallResult(index: number, toolResult: ToolUseResponse) {
        const toolCallNode = this._toolCalls[index];
        if (!toolCallNode.tool_use) {
            throw new Error("No tool use in tool call node");
        }

        const newRequestNode: ChatRequestNode = {
            id: index,
            type: ChatRequestNodeType.TOOL_RESULT,
            tool_result_node: {
                tool_use_id: toolCallNode.tool_use.tool_use_id,
                content: toolResult.text,
                is_error: toolResult.isError,
            },
        };
        this._requestNodes.push(newRequestNode);
    }

    /*
     * Sets the last processed service update sequence ID.
     */
    setLastProcessedServiceUpdateSequenceId(sequenceId: number) {
        this._lastProcessedServiceUpdateSequenceId = sequenceId;
    }

    /*
     * Sets the last uploaded sequence ID of a completed exchange.
     */
    setLastUploadedSequenceId(sequenceId: number) {
        this._lastUploadedChatExchangeSequenceId = sequenceId;
    }

    get userGuidelines(): string {
        return this._userGuidelines;
    }

    get workspaceGuidelines(): string {
        return this._workspaceGuidelines;
    }

    get agentMemories(): string {
        return this._agentMemories;
    }

    /**
     * An interruption occurred, we move the state to a completed state.
     * This should be used for user interrupts or restarts.
     * In either case, a new message needs to be issued to continue.
     * This method is safe to call (repeatedly) if the agent is already in a completed state.
     */
    interrupt() {
        // NOTE: as a matter of design, in order to make sure any actions that have been
        // already displayed to the user are not lost, we only roll the state forward.
        // We can then later omit, elide, or truncate nodes from chatHistoryForAPI if needed.

        // We simply push the exchange to the history if the next request is either
        // being built, or already sent. This covers all the cases:
        // 1. If there are any tool calls, we simply omit the corresponding result
        //    from the next request and the backend will add a "fake" tool call result
        //    in between that says the tool call was interrupted.
        // 2. For any completed tool calls, we also omit the corresponding results.
        // 3. If the user sent a message, or we are in the middle of a request,
        //    we omit the entire exchange (including the user's message).
        // Note that we will still show the entire history to the user, but omit
        // exchanges that were not completed from chatHistoryForAPI, so the user
        // may be confused why the agent is missing context.
        //
        // However, this is the same behavior as the IDE agent.
        // TODO(jeff): We can consider whether we want to change this behavior, and
        // give the agent context about (2) and (3). We'd need to be careful to not
        // send empty responses if the chatStream API can't handle those.
        if (this.requestNodes.length > 0) {
            // If we made it in here, this.isUserChatValid() must also be false.
            this.pushExchange(false /* completed */);
        }

        this._status = RemoteAgentStatus.agentIdle;
    }

    /**
     * Pushes the current request and response chunks to the chat history,
     * sets tool calls that need to be run, and clears the request and response nodes.
     *
     * This call always succeeds, regardless of the state of the agent.
     *
     * @param completed signals whether the exchange was completed, or forcibly terminated.
     */
    private pushExchange(completed: boolean) {
        const newExchange: Exchange = {
            request_message: this.requestMessage,
            response_text: this.responseText,
            request_id: this.requestId || "",
            request_nodes: this._requestNodes,
            response_nodes: this.responseNodes,
        };
        this._toolCalls = this.responseNodes.filter(
            (node) => node.type === ChatResultNodeType.TOOL_USE
        );
        this._chatHistory.push({
            exchange: newExchange,
            completed: completed,
            // sequence id is monotonic starting from 1
            sequenceId: 1 + this._chatHistory.length,
            finishedAt: new Date().toISOString(),
            changedFiles: this._changedFiles,
        });
        if (this._toolCalls.length === 0) {
            this._status = RemoteAgentStatus.agentIdle;
        }

        this._requestNodes = [];
        this._requestId = undefined;
        this._responseChunks = [];
        this._changedFiles = [];
    }
}
