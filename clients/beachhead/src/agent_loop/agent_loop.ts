import {
    Cha<PERSON><PERSON><PERSON>,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ChatR<PERSON><PERSON> } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import {
    BackoffParams,
    retryWithBackoff,
} from "@augment-internal/sidecar-libs/src/utils/promise-utils";
import { truncateMiddle } from "@augment-internal/sidecar-libs/src/utils/strings";

import { APIServerImplWithErrorReporting as APIServerImpl } from "../augment-api";
import { EventTimer } from "../event-timer";
import { APIError } from "../exceptions";
import { getLogger } from "../logging";
import {
    AgentWorkspaceStreamResponse,
    AgentWorkspaceUpdate,
    RemoteAgentChatHistoryResponse,
    RemoteAgentExchange,
    RemoteAgentStatus,
} from "../remote-agent-manager/types";
import { SSHConnectionFinder } from "../ssh-connections";
import { APIStatus } from "../utils/types";
import { BeachheadWorkspaceManager } from "../workspace-manager";
import { AsyncQueue } from "./async-queue";
import { AgentState, ExchangeState } from "./state";
import { StatePersistence } from "./state-persistence";

export function makeMessageNodes(message: string): ChatRequestNode[] {
    return [
        {
            id: 1,
            type: ChatRequestNodeType.TEXT,
            text_node: {
                content: message,
            },
        },
    ];
}

// Helper function to create a response chunk. This is useful for injecting messages into the chat stream.
export function makeResponseChunk(text: string, id: number = 1): ChatResult {
    return {
        text: text,
        nodes: [
            {
                id: id,
                type: ChatResultNodeType.RAW_RESPONSE,
                content: text,
            },
        ],
    };
}

export type ToolRunId = { requestId: string; toolUseId: string };

// TODO(AU-8996): Get these constants from feature flags

/** How often to report the chat history when streaming */
const REPORT_STREAMED_CHAT_EVERY_CHUNK = 3;

/** Max total size of changed files to send in a single chat update. If this update is exceeded, we
 * will skip sending changed files. */
const MAX_TOTAL_CHANGED_FILES_SIZE_BYTES = 2 * 1024 * 1024; // 2 MB

/** Max number of skipped paths to send when the total changed files size is exceeded */
const MAX_CHANGED_FILES_SKIPPED_PATHS = 1000;

/** How often to update the agent status (particularly active SSH connections) when idle */
const IDLE_STATUS_UPDATE_INTERVAL_MS = 60 * 1000; // 1 minute

/** Backoff parameters for retrying API calls */
const BACKOFF_PARAMS: BackoffParams = {
    initialMS: 500,
    mult: 2,
    maxMS: 30 * 1000, // The delay between retries should not exceed 30 seconds
    maxTotalMs: 5 * 60 * 1000, // The total time spent retrying should not exceed 5 minutes
    canRetry: (e: unknown) => {
        // Retry most errors
        // TODO: Should we use the normal retry logic here instead of this
        // special logic that does extra retries?
        if (e instanceof APIError) {
            // No point in retrying if we hit a 413
            if (e.status === APIStatus.augmentTooLarge) {
                return false;
            }
        }
        return true;
    },
};

/**
 * Handles the agent loop execution, managing the conversation flow between
 * the user, LLM, and tools.
 */
export class AgentLoop {
    // Max allowed size of a tool response
    private static readonly maxToolResponseBytes = 64 * 1024;

    // Maximum number of iterations in a single turn for the agent loop
    // TODO(AU-8996): Get this from a feature flag
    private static readonly maxIterations = 100;

    private apiServer: APIServerImpl;
    private toolsModel: ToolsModel;
    private state: AgentState;
    private chatQueue: AsyncQueue<ChatRequestNode[]> = new AsyncQueue<ChatRequestNode[]>();
    private agentID;
    private hasPendingInterrupt: boolean = false;
    private runningTool: ToolRunId | undefined = undefined;
    private logger = getLogger("AgentLoop");
    private addCurrentOpenExchangeToHistory = false;
    private workspaceManager: BeachheadWorkspaceManager;
    private statePersistence?: StatePersistence;
    private sshConnectionFinder: SSHConnectionFinder;

    /**
     * Creates a new AgentLoop instance.
     *
     * @param apiServer The API server implementation to use for chat requests
     * @param toolsModel The tools model to use for tool calls
     * @param addCurrentOpenExchangeToHistory Whether to add the current open exchange to chat history
     * @param workspaceManager The workspace manager to update after each turn
     * @param statePersistence Optional state persistence manager to save and load agent state
     * @param initialState Optional initial state to use instead of creating a new one
     * @param sshConnectionFinder The SSH connection finder to check for active SSH connections
     */
    constructor(
        apiServer: APIServerImpl,
        toolsModel: ToolsModel,
        workspaceManager: BeachheadWorkspaceManager,
        addCurrentOpenExchangeToHistory: boolean = false,
        agentID: string,
        sshConnectionFinder: SSHConnectionFinder,
        statePersistence?: StatePersistence,
        initialState?: AgentState
    ) {
        this.apiServer = apiServer;
        this.toolsModel = toolsModel;
        if (initialState) {
            this.state = initialState;
        } else {
            this.state = new AgentState();
        }
        this.addCurrentOpenExchangeToHistory = addCurrentOpenExchangeToHistory;
        this.workspaceManager = workspaceManager;
        this.statePersistence = statePersistence;
        this.state = initialState ?? new AgentState();
        this.agentID = agentID;
        this.sshConnectionFinder = sshConnectionFinder;
    }

    setModelId(modelId?: string) {
        this.state.setModelId(modelId);
    }

    /*
     * Returns the chat history for the agent and last complete chat exchange sequence id.
     *
     * @param incrementalOnly If true, only returns the chat history since the last call to this method
     */
    chatHistory(incrementalOnly: boolean = false): [RemoteAgentChatHistoryResponse, number] {
        const chatHistory: RemoteAgentExchange[] = [];
        this.state.chatHistoryForDisplay.forEach((exchangeState: ExchangeState) => {
            // When in incremental mode, we only return the chat history for exchanges that have not been uploaded yet
            if (incrementalOnly && exchangeState.sequenceId <= this.state.lastUploadedSequenceId) {
                return;
            }

            const { exchange, sequenceId, finishedAt, changedFiles } = exchangeState;

            // Check if changed files are too large
            // Get the length of the UTF-8 encoded JSON string, which is what will be sent over the network
            var changedFilesTooLarge: boolean;
            var changedFilesSkippedPaths: string[] = [];
            const changedFilesSize = new TextEncoder().encode(JSON.stringify(changedFiles)).length;
            if (changedFilesSize > MAX_TOTAL_CHANGED_FILES_SIZE_BYTES) {
                this.logger.info(
                    `Skipping %d changed files for sequence ID %d due to size: %d bytes is greater than max of %d bytes`,
                    changedFiles.length,
                    sequenceId,
                    changedFilesSize,
                    MAX_TOTAL_CHANGED_FILES_SIZE_BYTES
                );
                changedFilesTooLarge = true;
                changedFilesSkippedPaths = changedFiles
                    .slice(0, MAX_CHANGED_FILES_SKIPPED_PATHS)
                    .map((file) => file.old_path || file.new_path);
            } else {
                this.logger.debug(
                    `Sending %d changed files for sequence ID %d, size: %d bytes`,
                    changedFiles.length,
                    sequenceId,
                    changedFilesSize
                );
                changedFilesTooLarge = false;
            }

            chatHistory.push({
                exchange: exchange,
                changed_files: changedFilesTooLarge ? [] : changedFiles,
                sequence_id: sequenceId,
                finished_at: finishedAt,
                changed_files_skipped: changedFilesSkippedPaths,
                changed_files_skipped_count: changedFilesTooLarge ? changedFiles.length : 0,
            });
        });

        // Get the sequence ID of the last completed exchange
        let lastCompletedSequenceID = 0;
        const exchangeCount = this.state.chatHistoryForDisplay.length;
        if (exchangeCount > 0) {
            lastCompletedSequenceID =
                this.state.chatHistoryForDisplay[exchangeCount - 1].sequenceId;
        }

        // Add current open exchange if requested
        if (this.addCurrentOpenExchangeToHistory && this.state.currentOpenExchange) {
            chatHistory.push({
                exchange: this.state.currentOpenExchange,
                changed_files: [],
                // Sequence ID is monotonic starting from 1
                sequence_id: 1 + exchangeCount,
                // This is a hack, because backend use finished_at to decide order and
                // control overwrite. We call chatHistory in a fire & forget manner
                // in streaming. The asychronous coroutine could have this ts race
                // with the complete exchange. The hack of 1 minute in the past here
                // would make sure the complete exchange always win.
                finished_at: new Date(Date.now() - 60 * 1000).toISOString(),
                changed_files_skipped: [],
                changed_files_skipped_count: 0,
            });
        }
        return [{ chat_history: chatHistory }, lastCompletedSequenceID];
    }

    async interrupt() {
        // First set the interrupt flag so that we don't begin any new work.
        this.logger.info("Interrupt requested at agent loop");
        this.hasPendingInterrupt = true;

        // Cancel any currently running tool.
        if (this.runningTool) {
            const runningTool = this.runningTool;
            this.runningTool = undefined;
            await this.toolsModel.cancelToolRun(runningTool.requestId, runningTool.toolUseId);
        }
    }

    status(): RemoteAgentStatus {
        return this.state.status;
    }

    /**
     * Checks if an interrupt has been requested and handles it.
     * Generally should be called before starting a section.
     */
    private checkInterrupt(): boolean {
        if (this.hasPendingInterrupt) {
            this.logger.info("Interrupting agent loop");
            this.hasPendingInterrupt = false;
            this.state.interrupt();
            return true;
        }
        return false;
    }

    /**
     * Cancels a pending interrupt. Should be called when we know that the interrupt
     * is not needed anymore, for example when the next chat requset arrives.
     */
    private cancelPendingInterrupt() {
        this.hasPendingInterrupt = false;
    }

    /**
     * Runs the agent loop continuously, processing messages from the chat queue.
     *
     * @returns A promise that resolves when the agent loop completes
     */
    async runLoop(startingNodes: ChatRequestNode[]): Promise<void> {
        this.logger.info("Starting agent loop for conversation %s", this.state.conversationId);

        // If an initial message is provided, use it
        if (startingNodes.length > 0) {
            this.chatQueue.push(startingNodes);
        } else if (this.state.requestNodes.length > 0) {
            // We shouldn't resume with an incomplete exchange, so this indicates a crash in the
            // previous run. Append an exchange explaining the crash but don't automatically resume.
            this.logger.error(
                "Agent loop started with incomplete exchange, indicating a crash. Request nodes: %s",
                JSON.stringify(this.state.requestNodes)
            );

            this.state.beginRequest(this.apiServer.createRequestId());
            const injectedChunkId = this.state.responseChunks.length + 1;
            this.state.pushResponseChunk(
                makeResponseChunk(
                    "The remote agent crashed due to an error in the model call. Would you like to continue?",
                    injectedChunkId
                )
            );
            this.state.finishResponse();

            await this.reportChatHistory();
        } else if (this.state.chatHistoryForDisplay.length > 0) {
            const previousExchange =
                this.state.chatHistoryForDisplay[this.state.chatHistoryForDisplay.length - 1]
                    .exchange;
            const prevToolCalls = (previousExchange.response_nodes ?? []).filter(
                (node) => node.type === ChatResultNodeType.TOOL_USE
            );
            if (prevToolCalls.length > 0) {
                // The final completed exchange should never have a tool call (we continue the agent
                // loop until there are no tool calls), so this indicates a crash in the tool call.
                // In this case, append an exchange explaing the crash but don't automatically
                // resume.
                this.logger.error(
                    "Agent loop started with tool calls in the previous exchange, indicating a crash. Tool calls: %s",
                    JSON.stringify(prevToolCalls)
                );

                for (let i = 0; i < prevToolCalls.length; i++) {
                    this.state.pushToolCallResult(i, {
                        // Supplying the text is required when isError is true otherwise subsequent
                        // chat requests wil fail
                        text: "Remote agent error.",
                        isError: true,
                    });
                }
                this.state.beginRequest(this.apiServer.createRequestId());
                this.state.pushResponseChunk(
                    makeResponseChunk(
                        "The remote agent crashed due to an error in the tool call. Would you like to continue?"
                    )
                );
                this.state.finishResponse();

                await this.reportChatHistory();
            }
        }

        // Start the streaming updates in parallel with the main loop
        // We don't await this promise as it's meant to run concurrently
        // Error is thrown if we keep failing to connect to the stream,
        // which will propagate up and terminate the agent loop.
        void this.startStreamingUpdates();

        while (true) {
            // While waiting for a message, periodically update the SSH activity
            const idleStatusUpdateController = this.startPeriodicIdleStatusUpdate();
            const message = await this.chatQueue.pop();

            // Stop the periodic SSH activity update
            idleStatusUpdateController.abort();

            // Process the message
            const eventTimer = new EventTimer();
            try {
                await this.run(message, eventTimer);
            } catch (error: unknown) {
                this.logger.info(
                    "Agent loop completed one iteration, event times: %s",
                    eventTimer.getEventTimesKVString()
                );
                this.logger.error("Error running agent loop: %s", error);
                throw error;
            }
            this.logger.info(
                "Agent loop completed one iteration, event times: %s",
                eventTimer.getEventTimesKVString()
            );
        }
    }

    /**
     * Runs the agent loop with the given message.
     * NOTE: agent state is persisted after any change is made to AgentState
     * @param message The message to submit to the conversation
     * @returns A promise that resolves when the agent loop completes
     */
    async run(message: ChatRequestNode[], eventTimer: EventTimer): Promise<void> {
        eventTimer.start();

        this.logger.info(
            "Running basic agent loop for conversation %s with message: %s",
            this.state.conversationId,
            JSON.stringify(message)
        );
        this.state.sendUserChat(message);
        await this.statePersistence?.save(this.state);
        eventTimer.event_finished("statePersistence.save");
        await this.reportAgentStatus(RemoteAgentStatus.agentRunning);
        eventTimer.event_finished("reportAgentStatus");

        // Iterate until the model responds without a tool call or we timeout
        for (let iter = 0; iter < AgentLoop.maxIterations; iter++) {
            // Make a chat call to the API server
            if (this.checkInterrupt()) {
                await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
                eventTimer.event_finished("reportAgentStatus");
                return;
            }
            this.state.beginRequest(this.apiServer.createRequestId());
            const beforeTimestamp = this.workspaceManager.getCurrentTimestamp();
            eventTimer.event_finished("workspaceManager.getCurrentTimestamp");

            this.logger.info("Calling chat with request ID: %s", this.state.requestId);
            if (this.state.requestId === undefined) {
                // This should never be reached, but just for type checking.
                throw new Error("No request ID set");
            }
            const chatRequestId = this.state.requestId;
            const chatStream = await this.apiServer.chatStream(
                this.state.requestId, // requestId
                "", // message
                this.state.chatHistoryForAPI, // chatHistory
                { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] }, // blobs
                [], // userGuidedBlobs
                [], // externalSourceIds
                this.state.modelId, // model
                { commits: [], workingDirectory: [] }, // VCSChange
                [], // recentChanges
                undefined, // contextCodeExchangeRequestId
                undefined, // selectedCode
                undefined, // prefix
                undefined, // suffix
                undefined, // pathName
                undefined, // language
                undefined, // sessionId
                undefined, // disableAutoExternalSources
                this.state.userGuidelines, // userGuidelines
                this.state.workspaceGuidelines, // workspaceGuidelines
                (await this.toolsModel.getToolDefinitions()).map((tool) => tool.definition), // toolDefinitions
                this.state.requestNodes, // nodes
                ChatMode.remoteAgent, // mode
                this.state.agentMemories // agentMemories
            );
            eventTimer.event_finished("apiServer.chatStream");
            let chunksCount = 0;

            // Process the streaming response
            if (this.checkInterrupt()) {
                await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
                eventTimer.event_finished("reportAgentStatus");
                return;
            }
            for await (const chunk of chatStream) {
                eventTimer.event_finished("chatStream.iteration");
                if (this.checkInterrupt()) {
                    await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
                    eventTimer.event_finished("reportAgentStatus");
                    return;
                }

                this.logger.debug("text: %s", chunk.text);
                if (chunk.nodes) {
                    this.logger.debug("nodes: %s", JSON.stringify(chunk.nodes));
                }

                this.state.pushResponseChunk(chunk);
                chunksCount++;
                if (chunksCount % REPORT_STREAMED_CHAT_EVERY_CHUNK === 0) {
                    // Only report and save the state every N chunks in order to not cause performance issues
                    void this.reportChatHistory(true);
                    await this.statePersistence?.save(this.state);
                    eventTimer.event_finished("statePersistence.save");
                }
            }

            // Add exchange to history
            this.logger.info("Response text: %s", this.state.responseText);
            this.state.finishResponse();
            await this.reportChatHistory(false);
            eventTimer.event_finished("reportChatHistory");
            await this.statePersistence?.save(this.state);
            eventTimer.event_finished("statePersistence.save");

            if (this.state.status === RemoteAgentStatus.agentIdle) {
                this.logger.info("No tool call node found in response: Exiting agent loop");
                await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
                eventTimer.event_finished("reportAgentStatus");
                return;
            }

            // Process each tool call
            for (let i = 0; i < this.state.toolCalls.length; i++) {
                if (this.checkInterrupt()) {
                    await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
                    eventTimer.event_finished("reportAgentStatus");
                    return;
                }

                const toolCallNode = this.state.toolCalls[i];
                if (!toolCallNode.tool_use) {
                    throw new Error("Tool call node is missing tool_use");
                }
                this.logger.info(
                    "Tool call (%s): %s",
                    toolCallNode.tool_use.tool_name,
                    JSON.stringify(toolCallNode.tool_use.input_json)
                );

                // Call the tool
                const toolInput = JSON.parse(toolCallNode.tool_use.input_json) as Record<
                    string,
                    any
                >;
                this.runningTool = {
                    requestId: chatRequestId,
                    toolUseId: toolCallNode.tool_use.tool_use_id,
                };
                const toolResult = clipToolResult(
                    await this.toolsModel.callTool(
                        this.runningTool.requestId,
                        this.runningTool.toolUseId,
                        toolCallNode.tool_use.tool_name,
                        toolInput,
                        this.state.chatHistoryForAPI,
                        this.state.conversationId
                    ),
                    AgentLoop.maxToolResponseBytes
                );
                eventTimer.event_finished("toolsModel.callTool");
                this.logger.info("Tool result: %s", cropLines(toolResult.text));

                // Add tool result to request nodes for next request
                this.state.pushToolCallResult(i, toolResult);
                await this.reportChatHistory(true);
                eventTimer.event_finished("reportChatHistory");
                await this.statePersistence?.save(this.state);
                eventTimer.event_finished("statePersistence.save");

                // Update workspace after tool call
                await this.updateWorkspace();
                eventTimer.event_finished("updateWorkspace");
            }
            const changes = await this.workspaceManager.getChangesSince(beforeTimestamp);
            this.logger.info("Changes since last request: %s", JSON.stringify(changes));
            this.state.pushChangedFiles(changes);
            eventTimer.event_finished("workspaceManager.getChangesSince");
        }

        this.logger.info(
            "Agent loop exceeded max iterations (%d): Exiting agent loop",
            AgentLoop.maxIterations
        );

        // Add a message explaining that the conversation has been paused
        this.state.beginRequest(this.apiServer.createRequestId()); // Since a request ID is expected we provide one even though there's not a model call
        this.state.pushResponseChunk(
            makeResponseChunk(
                "Your conversation has been paused after reaching the maximum number of iterations. Would you like to continue?"
            )
        );
        this.state.finishResponse();
        await this.statePersistence?.save(this.state);
        eventTimer.event_finished("statePersistence.save");
        await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
        eventTimer.event_finished("reportAgentStatus");
    }

    /**
     * Updates the workspace by scanning for changes
     */
    private async updateWorkspace(): Promise<void> {
        try {
            await this.workspaceManager.update();
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error("Error updating workspace: %s", errorMessage);
        }
    }

    /**
     * Reports the chat history to the service.
     *
     * If ignore_failure is set to true, attempts to report the history a single time and ignores
     * any errors. Used when reporting chat history as part of a larger operation that should not
     * be interrupted by a failure to report chat history.
     */
    private async reportChatHistory(ignore_failure: boolean = false): Promise<void> {
        const [chatHistoryResponse, lastCompletedChatExchangeSeqId] = this.chatHistory(true);

        if (ignore_failure) {
            try {
                await this.apiServer.reportRemoteAgentChatHistory(
                    this.agentID,
                    chatHistoryResponse.chat_history
                );
            } catch {
                this.logger.warn(
                    "Failed to report chat history for sequence ID %d with %d exchanges",
                    lastCompletedChatExchangeSeqId,
                    chatHistoryResponse.chat_history.length
                );
                return;
            }
        } else {
            await retryWithBackoff(
                async () =>
                    this.apiServer.reportRemoteAgentChatHistory(
                        this.agentID,
                        chatHistoryResponse.chat_history
                    ),
                this.logger,
                BACKOFF_PARAMS
            );
        }

        // Only completed exchange sequence id is considered uploaded.
        // the second comparison here is to handle the case where new partial exchange is constantly
        // added to the chat history
        this.state.setLastUploadedSequenceId(lastCompletedChatExchangeSeqId);
        this.logger.info(
            "Successfully reported chat history for sequence ID %d with %d exchanges",
            lastCompletedChatExchangeSeqId,
            chatHistoryResponse.chat_history.length
        );
    }

    /**
     * Reports that the beachhead is up and alive
     */
    public async reportAlive(): Promise<void> {
        if (await this.statePersistence?.checkSavedStateExists()) {
            // Use the idle status, since we will be in this state anyway until we
            // actually enter the agent loop with a command to process, however
            // briefly
            await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
        } else {
            // TODO: Should we do this?
            // await this.reportAgentStatus(RemoteAgentStatus.agentStarting);
        }
    }

    /**
     * Reports the agent status to the service
     */
    private async reportAgentStatus(status: RemoteAgentStatus): Promise<void> {
        const hasActiveSSHConnection: boolean = this.sshConnectionFinder.hasActiveSSHConnections();
        await retryWithBackoff(
            async () =>
                this.apiServer.agentWorkspaceReportStatus(
                    this.agentID,
                    status,
                    hasActiveSSHConnection
                ),
            this.logger,
            BACKOFF_PARAMS
        );
    }

    /**
     * Process a list of instruction (chat or interrupt) updates from the service
     */
    async processInstructionUpdates(updates: AgentWorkspaceUpdate[]): Promise<void> {
        for (const update of updates) {
            // 1. handle interrupt
            if (update.update.Interrupt) {
                this.logger.info("Received interrupt from service");
                await this.interrupt();
                await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
            } else if (update.update.ChatRequest) {
                // 2. handle chat
                this.logger.info("Received chat request");
                // if there are unconsumed pending interrupts, cancel so that chat
                // won't send and get immediately interruptted
                if (this.hasPendingInterrupt) {
                    // cancel the pending interrupt
                    this.cancelPendingInterrupt();
                }
                // if chat is ongoing, interrupt
                if (this.state.status === RemoteAgentStatus.agentRunning) {
                    await this.interrupt();
                }
                const modelId = update.update.ChatRequest?.request_details.model_id;
                this.state.setModelId(modelId);
                if (
                    update.update.ChatRequest?.request_details.request_nodes &&
                    update.update.ChatRequest.request_details.request_nodes.length > 0
                ) {
                    this.chatQueue.push(update.update.ChatRequest.request_details.request_nodes);
                } else {
                    this.logger.error(
                        "No request nodes in chat request with sequence ID: %s",
                        update.sequence_id
                    );
                }
            } else {
                this.logger.error("Unknown update type: %s", JSON.stringify(update));
            }
            // 3. update processed sequence ID
            this.state.setLastProcessedServiceUpdateSequenceId(update.sequence_id);
        }
    }

    // When set to true, the agent has encountered a critical error and will:
    // - Stop processing any further updates
    // - Report FAILED status to the server
    private agentLoopFailed = false;

    /**
     * Process updates from the workspace stream
     * @param stream The workspace stream to process
     */
    private async processWorkspaceStream(
        stream: AsyncIterable<AgentWorkspaceStreamResponse>
    ): Promise<void> {
        for await (const response of stream) {
            // Skip if there are no updates
            if (!response.updates || response.updates.length === 0) {
                continue;
            }

            // Convert stream updates directly to workspace updates
            const workspaceUpdates = response.updates.map((update) => ({
                sequence_id: update.sequence_id,
                update: {
                    // These will be undefined if the properties don't exist, which is fine
                    Interrupt: update.interrupt,
                    ChatRequest: update.chat_request,
                },
            }));

            // Process the updates using the existing method
            try {
                await this.processInstructionUpdates(workspaceUpdates);
            } catch (error) {
                // If there's an error processing updates, log it and exit
                this.logger.error("Error processing stream update: %s", error);

                // Mark the agent loop as failed and break out of the loop
                this.agentLoopFailed = true;
                break;
            }
        }
    }

    /**
     * Start streaming updates from the server
     * @returns A promise that resolves when the streaming is complete
     */
    private async startStreamingUpdates(): Promise<void> {
        this.logger.info("Starting streaming updates");
        let currentBackoffMs = BACKOFF_PARAMS.initialMS; // Declare and initialize local variable

        // Keep trying to connect to the stream with backoff
        while (!this.agentLoopFailed) {
            try {
                // Connect to the workspace stream
                this.logger.info("Connecting to workspace stream...");
                const stream = await this.apiServer.agentWorkspaceStream(
                    this.agentID,
                    this.state.lastProcessedServiceUpdateSequenceId
                );
                this.logger.info("Successfully connected to workspace stream");
                currentBackoffMs = BACKOFF_PARAMS.initialMS; // Reset backoff on successful connection

                // Process the stream
                await this.processWorkspaceStream(stream);

                // If we get here, it means the stream completed normally
                // This is unexpected, so log it and retry
                this.logger.warn("Workspace stream completed unexpectedly, will reconnect");
            } catch (error) {
                // TODO(AU-10152): Is it possible to avoid this termination error and keep the stream alive longer?
                if (error instanceof Error && error.message === "terminated") {
                    this.logger.info("Workspace stream terminated, will reconnect");
                    continue;
                }

                // Log the error and retry with backoff
                // If the agent loop has failed, the main loop will exit on the next iteration
                this.logger.warn("Workspace stream error, will retry with backoff: %s", error);

                // Delay before retrying with current backoff value
                await new Promise((resolve) => setTimeout(resolve, currentBackoffMs));

                // Increase backoff for next attempt
                currentBackoffMs = Math.min(
                    currentBackoffMs * BACKOFF_PARAMS.mult,
                    BACKOFF_PARAMS.maxMS
                );
            }
        }

        // If we get here, the agent loop has failed
        this.logger.error("Agent loop has failed, exiting streaming updates");

        // Report the failed status
        await this.reportAgentStatus(RemoteAgentStatus.agentFailed);

        // Rethrow the error to terminate the agent loop
        throw new Error("Agent loop has failed");
    }

    private startPeriodicIdleStatusUpdate(): AbortController {
        const controller = new AbortController();
        const signal = controller.signal;
        void (async () => {
            while (!signal.aborted) {
                await new Promise((resolve) => setTimeout(resolve, IDLE_STATUS_UPDATE_INTERVAL_MS));
                if (signal.aborted) {
                    break;
                }
                await this.reportAgentStatus(RemoteAgentStatus.agentIdle);
            }
        })();
        return controller;
    }
}

// Crops a string to a maximum number of lines and adds an ellipsis if needed
function cropLines(text: string, maxLines: number = 5): string {
    const lines = text.split("\n");
    const croppedLines = lines.slice(0, maxLines).join("\n");
    return croppedLines + (lines.length > maxLines ? "\n..." : "");
}

// Clips tool result text to a maximum number of bytes
// Since the underlying truncateMiddle function adds a truncation message if the text is truncated,
// the total length of the result text can be slightly above maxBytes.
export function clipToolResult(toolResult: ToolUseResponse, maxBytes: number): ToolUseResponse {
    return {
        ...toolResult,
        text: truncateMiddle(toolResult.text, maxBytes),
    };
}
