import express from "express";
import http from "http";

import { Agent<PERSON><PERSON> } from "./agent_loop/agent_loop";
import { getLogger } from "./logging";
import { RemoteAgentStatus } from "./remote-agent-manager/types";
import { SSHManager } from "./ssh";
import { WorkspaceSetupStatusTracker } from "./workspace-setup-status-tracker";

// TODO(AU-10559): Remove all that we can from the http server

export function startServer(
    port: number,
    agentLoop: AgentLoop,
    workspaceSetupStatusTracker: WorkspaceSetupStatusTracker,
    ssh: SSHManager
) {
    const app = express();
    const server = http.createServer(app);

    const logger = getLogger("Beachhead");

    // Add middleware to parse JSON and URL-encoded bodies
    app.use(express.json({ strict: false }));
    app.use(express.urlencoded({ extended: true }));

    // Add health check endpoint
    app.get("/health", (req: express.Request, res: express.Response) => {
        res.status(200).json({
            status: "OK",
            agent_working: agentLoop.status() === RemoteAgentStatus.agentRunning,
        });
    });

    app.get("/chat-history", (req: express.Request, res: express.Response) => {
        const [chatHistoryResponse, _] = agentLoop.chatHistory();
        res.status(200).json(chatHistoryResponse);
    });

    app.post("/quit", (req: express.Request, res: express.Response) => {
        res.status(200).end();
        // Close the server gracefully
        server.close(() => {
            logger.info("HTTP server closed, exiting process");
            process.exit(0);
        });

        // Optional: Set a timeout in case graceful shutdown takes too long
        setTimeout(() => {
            logger.error("Forced exit after timeout");
            process.exit(1);
        }, 5000);
    });

    app.post("/interrupt", (_req: express.Request, res: express.Response) => {
        void agentLoop
            .interrupt()
            .then(() => {
                res.status(200).json({ status: "OK" });
            })
            .catch((error) => {
                const errorStr = error instanceof Error ? error.message : String(error);
                logger.error(`Error in interrupt endpoint: ${errorStr}`);
                res.status(500).json({ error: errorStr });
            });
    });

    // Deprecated
    app.post("/chat", (_req: express.Request, res: express.Response) => {
        res.status(410).end();
    });

    app.get("/workspace-report-status", (req: express.Request, res: express.Response) => {
        try {
            // Return the response to the caller
            res.status(200).json(workspaceSetupStatusTracker.report_status());
        } catch (error) {
            const errorStr = error instanceof Error ? error.message : String(error);
            logger.error(`Error in workspace-report-status endpoint: ${errorStr}`);
            res.status(500).json({ error: errorStr });
        }
    });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    app.post("/ssh/authorized_keys", async (req: express.Request, res: express.Response) => {
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            if (!req.body || typeof req.body !== "object" || !Array.isArray(req.body.public_keys)) {
                return res.status(400).json({
                    error: "Request body must be a JSON object with a 'public_keys' array",
                    body_value: JSON.stringify(req.body),
                });
            }
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
            await ssh.appendAuthorizedKeys(req.body.public_keys);
            res.status(200).json(await ssh.sshConfig());
        } catch (error) {
            const errorStr = error instanceof Error ? error.message : String(error);
            logger.error(`Error in SSH authorized_keys endpoint: ${errorStr}`);
            res.status(500).json({ error: errorStr });
        }
    });

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    app.get("/ssh/config", async (_req: express.Request, res: express.Response) => {
        res.status(200).json(await ssh.sshConfig());
    });

    // Start the server
    server.listen(port, () => {
        logger.info(`Web server listening on port ${port}`);
    });

    // Setup graceful shutdown
    process.on("SIGTERM", () => {
        logger.info("SIGTERM signal received: closing HTTP server");
        server.close(() => {
            logger.info("HTTP server closed");
        });
    });
}
