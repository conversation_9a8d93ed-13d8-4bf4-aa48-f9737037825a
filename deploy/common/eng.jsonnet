// list of all engineers
//
// Please add yourself to this list in alphabetical order to get access to services
// username: Use your primary augmentcode.com email address prefix
// gcp_access: 'full' if GCP dev VM access is required, null otherwise
// piiAccess: If 'full', the user is able to view raw PII in the prod BigQuery analytics dataset. If
//            'masked', the user will see a hash instead of the real value. Users with 'full' should
//            also have a business justification in a comment.
[
  {
    fullname: '<PERSON><PERSON><PERSON>',
    username: 'aarash',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: '<PERSON>',
    username: 'adam',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: '<PERSON>',
    username: 'alex',
    gcp_access: 'full',
    github: 'ding-alex',
    piiAccess: 'masked',
  },
  {
    fullname: '<PERSON><PERSON>',
    username: 'alyah',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: '<PERSON>',
    username: 'andre',
    gcp_access: 'full',
    github: 'a2chang',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Andrew Johnson',
    username: 'andrew',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Anshuman Pandey',
    username: 'anshuman',
    gcp_access: null,
    github: 'anshuman-augment',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Arun Chaganty',
    username: 'arun',
    gcp_access: 'full',
    github: 'arunchaganty',
    piiAccess: 'masked',
  },
  {
    fullname: 'Aswin Karumbunathan',
    username: 'aswin',
    gcp_access: 'full',
    github: 'aswink',
    piiAccess: 'masked',
  },
  {
    fullname: 'Bin Gao',
    username: 'bin',
    gcp_access: 'full',
    github: 'gaobin415',
    piiAccess: 'masked',
  },
  {
    fullname: 'Bo Nielson',
    username: 'bo',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Brandon Davis',
    username: 'brandon',
    gcp_access: 'full',
    github: 'bddavis',
    // Full PII access needed for agents UXR onboarding.
    piiAccess: 'full',
  },
  {
    fullname: 'Brennan McAdams',
    username: 'brennan',
    gcp_access: null,
    github: 'brennanmcadams',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Cameron Custer',
    username: 'cam',
    gcp_access: 'full',
    github: 'cameroncuster',
    // Full PII access needed for managing customers across external services (Stripe)
    piiAccess: 'full',
  },
  {
    fullname: 'Carl Case',
    username: 'carl',
    gcp_access: 'full',
    github: 'cbcase',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chris Kelly',
    username: 'chris',
    gcp_access: 'full',
    github: 'amateurhuman',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chris Campana',
    username: 'chris.campana',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Claire Garcia',
    username: 'claire',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Dave Clay',
    username: 'clay',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Constantine (Costa) Sapuntzakis',
    username: 'costa',
    gcp_access: 'external',
    github: 'csapuntz',
    // Trying to identify fraudulent users
    piiAccess: 'full',
    // Give extra k8s and GCP permissions in dev clusters
    glassbreakerDev: true,
    // Give extra k8s and GCP permissions in prod clusters
    glassbreakerProd: true,
  },
  {
    fullname: 'David Maskasky',
    username: 'david',
    gcp_access: 'full',
    github: 'dmaskasky',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'David Stephenson',
    username: 'des',
    gcp_access: 'full',
    github: 'D-E-Stephenson',
    // Full PII access needed for fraud detection and self-serve to enterprise user management.
    piiAccess: 'full',
  },
  {
    fullname: 'Devang Jhabakh Jai',
    username: 'devang',
    gcp_access: 'full',
    github: 'devangjhabakh',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Diane Huxley',
    username: 'diane',
    gcp_access: 'full',
    github: 'diehuxx',
    piiAccess: 'masked',
  },
  {
    fullname: 'Scott Dietzen',
    username: 'dietz',
    gcp_access: null,
    github: 'scott-dietzen',
    piiAccess: 'masked',
  },
  {
    fullname: 'Dirk Meister',
    username: 'dirk',
    gcp_access: 'external',
    github: 'dmeister',
    piiAccess: 'masked',
    glassbreakerDev: true,
    glassbreakerProd: true,
  },
  {
    fullname: 'Xuanyi Dong',
    username: 'dxy',
    gcp_access: 'full',
    github: 'D-X-Y',
    piiAccess: 'masked',
  },
  {
    fullname: 'Edward Sanchez',
    username: 'edward',
    gcp_access: 'full',
    github: 'edwardsanchez',
    piiAccess: 'masked',
  },
  {
    fullname: 'Eric Hou',
    username: 'eric',
    gcp_access: 'full',
    github: 'ultraeric',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Evan Driscoll',
    username: 'evan',
    gcp_access: 'external',
    github: 'evan0aug',
    piiAccess: 'masked',
  },
  {
    fullname: 'Fedor Korotkov',
    username: 'fedorkorotkov',
    gcp_access: 'full',
    github: 'fkorotkov',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Frank Nunley',
    username: 'frank',
    gcp_access: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Guy Gur-Ari',
    username: 'guy',
    gcp_access: 'full',
    github: 'guygurari',
    piiAccess: 'masked',
  },
  {
    fullname: 'Harry Dang',
    username: 'harry',
    gcp_access: 'full',
    github: 'Harry-Dang',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Igor Ostrovsky',
    username: 'igor',
    gcp_access: 'full',
    github: 'igor0',
    piiAccess: 'masked',
  },
  {
    fullname: 'Itamar Marom-Yochai',
    username: 'itamar',
    gcp_access: 'full',
    github: 'itamarom',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jacqueline Speiser',
    username: 'jacqueline',
    gcp_access: 'full',
    github: 'jspeiser',
    // Temporary PII access to help with the auth spanner migration.
    piiAccess: 'full',
    glassbreakerDev: true,
    glassbreakerProd: true,
  },
  {
    fullname: 'Jared Williams',
    username: 'jared',
    gcp_access: 'full',
    github: 'jareddvw',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jeffrey Shen',
    username: 'jeff',
    gcp_access: 'full',
    github: 'jeffdshen',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jiayi Wei',
    username: 'jiayi',
    gcp_access: 'full',
    github: 'MrVPlusOne',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Joel Galenson',
    username: 'joel',
    gcp_access: 'full',
    github: 'jgalenson',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jon Engler',
    username: 'jon',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Jon McLachlan',
    username: 'jonmclachlan',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Julie Bey',
    username: 'julie',
    gcp_access: null,
    github: 'juliebey',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Justin Spears',
    username: 'justin',
    gcp_access: 'full',
    github: 'jspears',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Justin Xu',
    username: 'justinxu',
    gcp_access: 'full',
    github: 'justinxu421',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Kye Kim',
    username: 'kye',
    gcp_access: 'full',
    github: 'ghkim101',
    piiAccess: 'masked',
  },
  {
    fullname: 'Liam Lindner',
    username: 'liam',
    gcp_access: 'full',
    github: 'refactornator',
    piiAccess: 'masked',
  },
  {
    fullname: 'Lior Neumann',
    username: 'lior',
    gcp_access: 'full',
    github: 'liornm',
    piiAccess: 'masked',
  },
  {
    fullname: 'Luke Paulsen',
    username: 'luke',
    gcp_access: 'full',
    github: 'augmentluke',
    piiAccess: 'masked',
  },
  {
    fullname: 'Marc MacIntyre',
    username: 'marcmac',
    gcp_access: 'external',
    github: 'marcmac',
    piiAccess: 'masked',
    glassbreakerDev: true,
  },
  {
    fullname: 'Mark Pariente',
    username: 'markp',
    gcp_access: 'full',
    github: 'mrpdaemon',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Markus Rabe',
    username: 'markus',
    gcp_access: 'full',
    github: 'MarkusRabe',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chris Marty',
    username: 'marty',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt Gaunt-Seo',
    username: 'matt',
    gcp_access: 'full',
    github: 'gauntface',
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt McClernan',
    username: 'matt.mcclernan',
    gcp_access: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Matt Monaco',
    username: 'mattm',
    gcp_access: 'full',
    github: 'mmonaco',
    piiAccess: 'masked',
  },
  {
    fullname: 'Matthew So',
    username: 'matts',
    gcp_access: 'full',
    github: 'matthew-so',
    piiAccess: 'masked',
  },
  {
    fullname: 'Max Hahn',
    username: 'maxhahn',
    gcp_access: 'full',
    github: 'augment-mhahn',
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt Ball',
    username: 'mb',
    gcp_access: null,
    github: 'matt-ball',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Mike Mao',
    username: 'mike',
    gcp_access: 'full',
    github: 'mike-meow',
    piiAccess: 'masked',
  },
  {
    fullname: 'Mirolsav Gavrilov',
    username: 'miroslav',
    gcp_access: 'full',
    github: 'mgavrilov-augmentcode',
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt Legrand',
    username: 'ml',
    gcp_access: 'full',
    github: 'insprd',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Molisha Shah',
    username: 'molisha',
    gcp_access: 'full',
    github: 'molishashah',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Guy Mograbi',
    username: 'moogi',
    gcp_access: 'full',
    github: 'moogi-augment',
    // Full PII access needed for next-edit adoption success.
    piiAccess: 'full',
  },
  {
    fullname: 'Matt Pauly',
    username: 'mpauly',
    gcp_access: 'full',
    github: 'mtpauly',
    // Full PII access needed for managing remote agents rollout.
    piiAccess: 'full',
  },
  {
    fullname: 'Navtej Sadhal',
    username: 'navtej',
    gcp_access: 'full',
    github: 'navtej-ac',
    piiAccess: 'masked',
  },
  {
    fullname: 'Nikita Sirohi',
    username: 'nikita',
    gcp_access: 'full',
    github: 'nikita-sirohi',
    // Temporary PII access to do review and data deletions
    piiAccess: 'full',
  },
  {
    fullname: 'Oliver Schober',
    username: 'oliver',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Omer Bensaadon',
    username: 'omer',
    gcp_access: null,
    github: 'omerbensaadon',
    piiAccess: 'masked',
  },
  {
    fullname: 'Omendra Singh Rathor',
    username: 'osr',
    gcp_access: 'full',
    github: 'osr-augment',
    piiAccess: 'masked',
  },
  {
    fullname: 'Pranay Agrawal',
    username: 'pranay',
    gcp_access: 'full',
    github: 'pranayagra',
    piiAccess: 'masked',
  },
  {
    fullname: 'Ran Halprin',
    username: 'ran',
    gcp_access: 'full',
    github: 'ranhalprin',
    piiAccess: 'masked',
  },
  {
    fullname: 'Rich Hankins',
    username: 'rich',
    gcp_access: 'full',
    github: 'richhankins',
    piiAccess: 'full',
  },
  {
    fullname: 'Robert Kitaoka',
    username: 'rob',
    gcp_access: 'full',
    github: 'rob-aug',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Sachal Jogi',
    username: 'sach',
    gcp_access: null,
    github: null,
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Siyao Li',
    username: 'siyao',
    gcp_access: 'full',
    github: 'siyaoL1',
    piiAccess: 'masked',
  },
  {
    fullname: 'Sophie Reynolds',
    username: 'sophie',
    gcp_access: 'full',
    github: 'shreynolds',
    piiAccess: 'masked',
  },
  {
    fullname: 'Surbhi Jain',
    username: 'surbhi',
    gcp_access: 'full',
    github: 'surbhiijain',
    // Full PII access needed for managing customers across external services (Stripe)
    piiAccess: 'full',
  },
  {
    fullname: 'Syl Giuliani',
    username: 'syl',
    gcp_access: null,
    github: 'sylg',
    // Full PII access needed for managing customer relations.
    piiAccess: 'full',
  },
  {
    fullname: 'Tamuz Hod',
    username: 'tamuz',
    gcp_access: 'full',
    github: 'TamuzHod',
    piiAccess: 'masked',
  },
  {
    fullname: 'Tenzin Low',
    username: 'tenzin',
    gcp_access: 'full',
    github: 'tenzinhl',
    piiAccess: 'masked',
  },
  {
    fullname: 'Todd Hausman',
    username: 'todd',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Tongfei Chen',
    username: 'tongfei',
    gcp_access: 'full',
    github: 'ctongfei',
    piiAccess: 'masked',
  },
  {
    fullname: 'Vaibhav Agrawal',
    username: 'vaibhav',
    gcp_access: 'full',
    github: 'vaibagra',
    piiAccess: 'masked',
  },
  {
    fullname: 'Vinay Perneti',
    username: 'vinay',
    gcp_access: 'full',
    github: 'vperneti',
    piiAccess: 'masked',
  },
  {
    fullname: 'Vincent Wong',
    username: 'vincentwong',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Viktor Passichenko',
    username: 'vpas',
    gcp_access: 'full',
    github: 'vpas',
    piiAccess: 'masked',
  },
  {
    fullname: 'Xiaolei Zhu',
    username: 'xiaolei',
    gcp_access: 'full',
    github: 'virtualzx-nad',
    // Trying to identify suspicious users
    piiAccess: 'full',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Zheren Dong',
    username: 'zheren',
    gcp_access: 'full',
    github: 'Edwardong',
    piiAccess: 'masked',
  },
  {
    fullname: 'Zhewei Shi',
    username: 'zhewei',
    gcp_access: 'full',
    github: 'zheweishi',
    piiAccess: 'masked',
  },
  {
    fullname: 'Zhuoran Shen',
    username: 'zhuoran',
    gcp_access: 'full',
    github: 'cmsflash',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
]
