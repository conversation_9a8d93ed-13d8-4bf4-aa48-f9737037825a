local config = (import 'deploy/tenants/namespace_configs/dev-defaults.jsonnet');

local githubOauthSecretOverride = {
  name: 'dev-mike-github-processor-oauth-app-secret',
  version: 'latest',
};

config
.withDeployFlags({
  useSharedDevRequestInsightBigquery: false,
  exportFullData: true,
  remoteAgentImageTag: '{namespace}',
  githubOauthSecretOverride: githubOauthSecretOverride,
})
.withFakeFeatureFlags({
  remote_agents_workspace_outbound_enabled: true,
  enable_github_processor: true,
})
